package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 微信视频号小店商品
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopProductDTO {

    /**
     * 商品spuid
     */
    @SerializedName("product_id")
    private Long productId;
    /**
     * skuID
     */
    @SerializedName("sku_id")
    private Long skuId;
    /**
     * sku数量
     */
    @SerializedName("sku_cnt")
    private Long num;
    /**
     * 商品标题
     */
    @SerializedName("title")
    private String productName;
    /**
     * 商品编码
     */
    @SerializedName("sku_code")
    private String skuErpCode;
    /**
     * 订单实付金额，单位为分
     */
    @SerializedName("real_price")
    private Integer payment;

    /**
     * 市场单价，单位为分
     */
    @SerializedName("market_price")
    private Integer unitPrice;

}
