package com.xtc.marketing.adapterservice.rpc.oms;

import com.google.common.collect.Maps;
import com.google.gson.JsonElement;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.OmsBaseRequest;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.OmsBaseResponse;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.OmsModifyAddressNotifyCmd;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.SyncInvoiceApplyCmd;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.SyncRefundCmd;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.SyncTradeCmd;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.net.SocketTimeoutException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.function.Consumer;

/**
 * OMS系统rpc
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class OmsRpc {

    /**
     * 域名
     */
    private static final String DOMAIN = "http://retailers.eebbk.com";
    /**
     * http 请求类
     */
    private static final RestTemplate REST_TEMPLATE = new RestTemplate();

    static {
        // 设置字符串的转换使用UTF-8编码
        REST_TEMPLATE.getMessageConverters().stream()
                .filter(converter -> converter.getClass().equals(StringHttpMessageConverter.class))
                .forEach(converter -> ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8));
        // 设置请求超时时间，单位：毫秒
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(3000);
        requestFactory.setReadTimeout(3000);
        REST_TEMPLATE.setRequestFactory(requestFactory);
    }

    /**
     * 修改地址通知
     *
     * @param cmd 参数
     */
    public void notifyModifyAddress(OmsModifyAddressNotifyCmd cmd) {
        this.call(cmd);
    }

    /**
     * 同步订单
     *
     * @param cmd 参数
     */
    public void syncTrade(SyncTradeCmd cmd) {
        this.call(cmd);
    }

    /**
     * 同步退款单
     *
     * @param cmd 参数
     */
    public void syncRefund(SyncRefundCmd cmd) {
        this.call(cmd);
    }

    /**
     * 同步发票申请
     *
     * @param cmd 参数
     * @return 响应结果
     */
    public OmsBaseResponse<Void> syncInvoiceApply(SyncInvoiceApplyCmd cmd) {
        return this.call(cmd, true);
    }

    /**
     * 执行远程调用
     *
     * @param request 请求
     * @param <T>     响应数据类型
     * @return 响应结果
     */
    private <T> OmsBaseResponse<T> call(OmsBaseRequest<T> request) {
        return this.call(request, false);
    }

    /**
     * 执行远程调用
     *
     * @param request       请求
     * @param ignoreFailure 忽略响应失败，不抛出异常，由方法调用方处理
     * @param <T>           响应数据类型
     * @return 响应结果
     */
    private <T> OmsBaseResponse<T> call(OmsBaseRequest<T> request, boolean ignoreFailure) {
        // 构建请求实体
        RequestEntity<?> requestEntity = this.initRequestEntity(request);
        // 日志记录
        String responseStr = "";
        String requestInfoLog = String.format("OMS系统RPC %s url: %s, body: %s",
                requestEntity.getMethod(), requestEntity.getUrl(), requestEntity.getBody());
        log.info(requestInfoLog);
        try {
            // 发起请求
            ResponseEntity<String> response = REST_TEMPLATE.exchange(requestEntity, String.class);
            responseStr = response.getBody();
            log.info("{}, response: {}", requestInfoLog, responseStr);
            // 解析响应结果
            OmsBaseResponse<T> result = this.paresResponse(request, responseStr);
            // 忽略响应失败，不抛出异常，由方法调用方处理
            if (result != null && ignoreFailure) {
                return result;
            }
            // 响应失败抛出异常
            if (result == null || result.isFailure()) {
                throw this.remoteException();
            }
            return result;
        } catch (Exception e) {
            if (e.getCause() instanceof SocketTimeoutException) {
                responseStr = "请求超时";
            }
            if (e instanceof HttpStatusCodeException) {
                responseStr = ((HttpStatusCodeException) e).getResponseBodyAsString();
            }
            throw this.rpcSysException(responseStr, e);
        }
    }

    /**
     * 解析响应结果
     *
     * @param request     请求
     * @param responseStr 响应结果字符串
     * @param <T>         响应数据类型
     * @return 响应结果
     */
    private <T> OmsBaseResponse<T> paresResponse(OmsBaseRequest<T> request, String responseStr) {
        OmsBaseResponse<JsonElement> baseResponse = GsonUtil.jsonToBean(responseStr, OmsBaseResponse.class, JsonElement.class);
        // 当响应数据类型为 Void 时，表示丢弃响应数据并返回 null，保证方法返回值的正确性
        if (request.getResponseClass() == Void.class) {
            OmsBaseResponse<T> voidResponse = new OmsBaseResponse<>();
            voidResponse.setSuccess(baseResponse.getSuccess());
            voidResponse.setCode(baseResponse.getCode());
            voidResponse.setDesc(baseResponse.getDesc());
            voidResponse.setData(null);
            return voidResponse;
        }
        Type type;
        if (baseResponse.getData().isJsonArray()) {
            // 针对数组数据，需要返回的 T 类型当作是 List<T> 的一个整体，需要构建 List 类型的 Type
            type = GsonUtil.buildType(List.class, request.getResponseClass());
        } else {
            // 默认直接返回 T 类型，没有外层嵌套
            type = GsonUtil.buildType(request.getResponseClass());
        }
        return GsonUtil.jsonToBean(responseStr, OmsBaseResponse.class, type);
    }

    /**
     * 初始化请求
     *
     * @param request 请求
     * @return 请求实体
     */
    private RequestEntity<?> initRequestEntity(OmsBaseRequest<?> request) {
        // 构建请求 url
        UriComponentsBuilder uriBuilder = UriComponentsBuilder.fromHttpUrl(DOMAIN + request.getApiPath());
        // GET 请求将属性的名称作为请求参数 key，属性的 getXxx 方法作为请求参数 value
        if (request.getRequestMethod() == HttpMethod.GET) {
            Map<String, Object> fieldMap = this.getFieldMap(request,
                    errorFields -> log.warn("OMS系统调用异常，组装失败的请求参数 [{}]", errorFields));
            fieldMap.forEach(uriBuilder::queryParam);
        }
        URI uri = uriBuilder.build().toUri();
        // 构建请求实体
        RequestEntity.BodyBuilder requestBuilder = RequestEntity.method(request.getRequestMethod(), uri);
        // POST、PUT 请求设置 json 类型的 body 参数
        if (request.getRequestMethod() == HttpMethod.POST || request.getRequestMethod() == HttpMethod.PUT) {
            return requestBuilder.contentType(MediaType.APPLICATION_JSON).body(GsonUtil.objectToJson(request));
        }
        return requestBuilder.build();
    }

    /**
     * 获取对象所有属性的名称和值的集合
     *
     * @param object      对象
     * @param errorFields 异常处理
     * @return 属性的名称和值的集合
     */
    private Map<String, Object> getFieldMap(Object object, Consumer<String> errorFields) {
        Map<String, Object> map = Maps.newHashMapWithExpectedSize(object.getClass().getDeclaredFields().length);
        StringJoiner errorJoiner = new StringJoiner(",");
        Arrays.stream(object.getClass().getDeclaredFields())
                .forEach(field -> {
                    // key：属性名称，value：属性 getXxx 方法的返回值
                    String fieldName = field.getName();
                    try {
                        // 获取属性的 getXxx 方法并执行
                        String methodName = "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1);
                        Method method = object.getClass().getMethod(methodName);
                        Object fieldValue = method.invoke(object);
                        map.put(fieldName, fieldValue);
                    } catch (Exception e) {
                        // 收集异常的属性名称
                        errorJoiner.add(fieldName);
                    }
                });
        if (errorJoiner.length() > 0) {
            errorFields.accept(errorJoiner.toString());
        }
        return map;
    }

    /**
     * 远程接口异常
     *
     * @return 异常
     */
    private RemoteException remoteException() {
        return new RemoteException("RPC返回异常状态码");
    }

    /**
     * 远程调用异常
     *
     * @param responseStr 响应结果
     * @param e           异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, Exception e) {
        String msg = String.format("OMS系统调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
    }

    public static void main(String[] args) {
        OmsRpc omsRpc = new OmsRpc();
        OmsModifyAddressNotifyCmd cmd = OmsModifyAddressNotifyCmd.builder().tradeId("xxx").build();
        omsRpc.notifyModifyAddress(cmd);
    }

}
