package com.xtc.marketing.adapterservice.rpc.tiktok.tiktokdto.enums;

import lombok.Getter;

/**
 * 抖音平台快递公司
 */
@Getter
public enum TikTokLogisticsCompany {
    /**
     * 圆通
     */
    YTO("圆通", "yuantong", "", "https://lf3-cm.ecombdstatic.com/obj/logistics-davinci/template/v2/yuantong_76_130.xml"),
    /**
     * 京东
     */
    JD("京东", "jd", "ed-m-0001", "https://lf6-cm.ecombdstatic.com/obj/logistics-davinci/template/template_jd76.xml"),
    /**
     * 顺丰
     */
    SF("顺丰", "shunfeng", "2", "https://lf3-cm.ecombdstatic.com/obj/logistics-davinci/template/v2/shunfeng_76_130.xml"),
    /**
     * EMS
     */
    EMS("EMS", "ems", "", "https://lf3-cm.ecombdstatic.com/obj/logistics-davinci/template/v2/ems_76_130.xml"),
    ;

    /**
     * 快递公司名称
     */
    private final String name;

    /**
     * 快递公司代码
     */
    private final String code;

    /**
     * 快递产品类型
     */
    private final String type;

    /**
     * 电子面单模板地址（电子面单）
     */
    private final String printTemplateUrl;

    TikTokLogisticsCompany(String name, String code, String type, String printTemplateUrl) {
        this.name = name;
        this.code = code;
        this.type = type;
        this.printTemplateUrl = printTemplateUrl;
    }
}
