package com.xtc.marketing.adapterservice.rpc.ems.emsdto.command;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmsCreateOrderCmd {

    /**
     * 订单信息
     */
    @SerializedName("OrderNormal")
    private EmsOrderNormalCmd orderNormal;

}
