package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.notify;

import com.google.gson.JsonObject;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Base64;
import java.util.StringJoiner;

/**
 * 微信视频号小店通知消息
 * <p><a href="https://developers.weixin.qq.com/doc/channels/API/basics/message_push.html">文档</a></p>
 */
@Slf4j
@Getter
@Setter
@ToString
@NoArgsConstructor
public class WechatChannelsShopNotify {

    /**
     * 字符集
     */
    private static final Charset CHARSET = StandardCharsets.UTF_8;

    /**
     * 小店UserName（账号信息-原始ID）
     */
    private String toUserName;
    /**
     * 微信小店ID
     */
    private String appid;
    /**
     * 推送配置token
     */
    private String token;
    /**
     * AES密钥
     */
    private byte[] aesKey;
    /**
     * 时间戳（秒）
     */
    private String timestamp;
    /**
     * 随机数
     */
    private String nonce;
    /**
     * url签名
     */
    private String signature;
    /**
     * 消息签名
     */
    private String msgSignature;
    /**
     * 消息appid
     */
    private String msgAppId;
    /**
     * 消息
     */
    private String message;
    /**
     * 消息字节长度
     */
    private Integer messageBytesLength;
    /**
     * 加密消息
     */
    private String encryptMessage;
    /**
     * 加密消息的随机字符串（16位）
     */
    private String randomStr;

    public WechatChannelsShopNotify(String toUserName) {
        this.toUserName = toUserName;
        this.init();
    }

    public WechatChannelsShopNotify(String toUserName, String message) {
        this.toUserName = toUserName;
        this.message = message;
        this.init();
    }

    public WechatChannelsShopNotify(String toUserName, String encryptMessage, String msgSignature) {
        this.toUserName = toUserName;
        this.encryptMessage = encryptMessage;
        this.msgSignature = msgSignature;
        this.init();
    }

    /**
     * 初始化配置
     */
    public void init() {
        NotifyConfig config = NotifyConfig.getByToUserName(this.toUserName);
        this.appid = config.getAppid();
        this.token = config.getToken();
        this.aesKey = this.buildAesKey(config.getAesKey());
    }

    /**
     * 验证消息的 appid
     *
     * @return 执行结果
     */
    public boolean verifyMsgAppid() {
        if (StringUtils.isAnyBlank(this.appid, this.msgAppId)) {
            return false;
        }
        return this.appid.equals(this.msgAppId);
    }

    /**
     * 验证消息推送服务器配置
     *
     * @param timestamp 时间戳
     * @param nonce     随机数
     * @param signature 签名
     * @return 执行结果
     */
    public boolean verifyNotifyServer(String timestamp, String nonce, String signature) {
        if (StringUtils.isBlank(this.nonce)) {
            this.nonce = nonce;
        }
        if (StringUtils.isBlank(this.timestamp)) {
            this.timestamp = timestamp;
        }
        if (StringUtils.isBlank(this.signature)) {
            this.signature = signature;
        }

        String plaintext = this.sortAndJoinString(this.timestamp, this.nonce, this.token);
        String sha1Hex = sha1Hex(plaintext);
        return sha1Hex.equals(this.signature);
    }

    /**
     * 验证消息推送
     *
     * @param timestamp 时间戳
     * @param nonce     随机数
     * @return 执行结果
     */
    public boolean verifyNotifyMessage(String timestamp, String nonce) {
        if (StringUtils.isBlank(this.nonce)) {
            this.nonce = nonce;
        }
        if (StringUtils.isBlank(this.timestamp)) {
            this.timestamp = timestamp;
        }

        String plaintext = this.sortAndJoinString(this.timestamp, this.nonce, this.token, this.encryptMessage);
        String sha1Hex = sha1Hex(plaintext);
        return sha1Hex.equals(this.msgSignature);
    }

    /**
     * 生成返回消息
     *
     * @return 返回消息
     */
    public String buildReturnMessage() {
        String encryptedMessage = this.encryptMessage();
        String timestamp = this.buildTimestamp();
        String nonce = this.buildNonce();
        String msgSignature = this.buildMsgSignature();

        // 组装 json 结果
        JsonObject returnMessage = new JsonObject();
        returnMessage.addProperty("Encrypt", encryptedMessage);
        returnMessage.addProperty("MsgSignature", msgSignature);
        returnMessage.addProperty("TimeStamp", timestamp);
        returnMessage.addProperty("Nonce", nonce);
        return returnMessage.toString();
    }

    /**
     * 加密消息
     *
     * @return 加密消息
     */
    public String encryptMessage() {
        if (StringUtils.isNotBlank(this.encryptMessage)) {
            return this.encryptMessage;
        }
        if (StringUtils.isBlank(this.message)) {
            return null;
        }
        // 初始化
        byte[] randomStrBytes = this.buildRandomStr().getBytes(CHARSET);
        byte[] messageBytes = this.getMessage().getBytes(CHARSET);
        this.messageBytesLength = messageBytes.length;
        byte[] networkBytesOrder = this.buildNetworkBytesOrder(this.messageBytesLength);
        byte[] appidBytes = this.appid.getBytes(CHARSET);
        // 组合加密消息 = randomStrBytes + networkBytesOrder + messageBytes + appidBytes
        byte[] encryptMessageBytes = new byte[0];
        encryptMessageBytes = ArrayUtils.addAll(encryptMessageBytes, randomStrBytes);
        encryptMessageBytes = ArrayUtils.addAll(encryptMessageBytes, networkBytesOrder);
        encryptMessageBytes = ArrayUtils.addAll(encryptMessageBytes, messageBytes);
        encryptMessageBytes = ArrayUtils.addAll(encryptMessageBytes, appidBytes);
        // 使用自定义的填充方式对明文进行补位填充
        byte[] padBytes = AesUtil.pkcs7Encode(encryptMessageBytes.length);
        encryptMessageBytes = ArrayUtils.addAll(encryptMessageBytes, padBytes);
        // 加密
        byte[] encryptBytes = AesUtil.encrypt(this.aesKey, encryptMessageBytes);
        this.encryptMessage = base64Encode(encryptBytes);
        return this.encryptMessage;
    }

    /**
     * 解密消息
     *
     * @return 解密消息
     */
    public String decryptMessage() {
        if (StringUtils.isNotBlank(this.message)) {
            return this.message;
        }
        if (StringUtils.isBlank(this.encryptMessage)) {
            return null;
        }
        // 解密
        byte[] encryptBytes = base64Decode(this.encryptMessage);
        byte[] decryptMessageBytes = AesUtil.decrypt(this.aesKey, encryptBytes);
        // 去除补位字符
        decryptMessageBytes = AesUtil.pkcs7Decode(decryptMessageBytes);
        // 16 位随机字符串
        byte[] randomStrBytes = Arrays.copyOfRange(decryptMessageBytes, 0, 16);
        this.randomStr = new String(randomStrBytes, CHARSET);
        // 网络字节序
        byte[] networkBytesOrder = Arrays.copyOfRange(decryptMessageBytes, 16, 20);
        this.messageBytesLength = this.recoverNetworkBytesOrder(networkBytesOrder);
        // 消息内容
        byte[] messageBytes = Arrays.copyOfRange(decryptMessageBytes, 20, 20 + this.messageBytesLength);
        this.message = new String(messageBytes, CHARSET);
        // 消息appid
        byte[] appidBytes = Arrays.copyOfRange(decryptMessageBytes, 20 + this.messageBytesLength, decryptMessageBytes.length);
        this.msgAppId = new String(appidBytes, CHARSET);
        return this.message;
    }

    /**
     * 获取AES密钥
     *
     * @param aesKey AES密钥字符串
     * @return AES密钥
     */
    public byte[] buildAesKey(String aesKey) {
        if (this.aesKey != null) {
            return this.aesKey;
        }
        // Base64_Decode(EncodingAESKey + "=") EncodingAESKey 尾部填充一个字符的 "=", 用 Base64_Decode 生成 32 个字节的 AESKey
        this.aesKey = base64Decode(aesKey + "=");
        return this.aesKey;
    }

    /**
     * 获取时间戳（秒）
     *
     * @return 时间戳（秒）
     */
    public String buildTimestamp() {
        if (StringUtils.isNotBlank(this.timestamp)) {
            return this.timestamp;
        }
        long epochSecond = LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8"));
        this.timestamp = String.valueOf(epochSecond);
        return this.timestamp;
    }

    /**
     * 获取随机数
     *
     * @return 随机数
     */
    public String buildNonce() {
        if (StringUtils.isNotBlank(this.nonce)) {
            return this.nonce;
        }
        // 生成范围 0~4294967295 之间的数字
        int random = (int) (Math.random() * 4294967295L);
        this.nonce = String.valueOf(random);
        return this.nonce;
    }

    /**
     * 获取消息签名
     *
     * @return 签名
     */
    public String buildMsgSignature() {
        if (StringUtils.isNotBlank(this.msgSignature)) {
            return this.msgSignature;
        }
        String plaintext = this.timestamp + this.nonce + this.token + this.encryptMessage;
        this.msgSignature = this.sha1Hex(plaintext);
        return this.msgSignature;
    }

    /**
     * 获取随机字符串（16位）
     *
     * @return 随机字符串（16位）
     */
    public String buildRandomStr() {
        if (StringUtils.isNotBlank(this.randomStr)) {
            return this.randomStr;
        }
        this.randomStr = RandomStringUtils.randomAlphanumeric(16);
        return this.randomStr;
    }

    /**
     * 生成 4 个字节的网络字节序
     *
     * @param sourceNumber 源数字
     * @return 4 个字节的网络字节序
     */
    private byte[] buildNetworkBytesOrder(int sourceNumber) {
        byte[] orderBytes = new byte[4];
        orderBytes[3] = (byte) (sourceNumber & 0xFF);
        orderBytes[2] = (byte) (sourceNumber >> 8 & 0xFF);
        orderBytes[1] = (byte) (sourceNumber >> 16 & 0xFF);
        orderBytes[0] = (byte) (sourceNumber >> 24 & 0xFF);
        return orderBytes;
    }

    /**
     * 还原4个字节的网络字节序
     *
     * @param orderBytes 字节序
     * @return 源数字
     */
    private int recoverNetworkBytesOrder(byte[] orderBytes) {
        int sourceNumber = 0;
        for (int i = 0; i < 4; i++) {
            sourceNumber <<= 8;
            sourceNumber |= orderBytes[i] & 0xff;
        }
        return sourceNumber;
    }

    /**
     * 排序并拼接字符串
     *
     * @param strings 字符串数组
     * @return 拼接字符串
     */
    private String sortAndJoinString(String... strings) {
        StringJoiner plaintext = new StringJoiner("");
        Arrays.stream(strings).sorted().forEach(plaintext::add);
        String joinString = plaintext.toString();
        log.info("排序并拼接字符串 {}", joinString);
        return joinString;
    }

    /**
     * SHA1加密
     *
     * @param plaintext 明文
     * @return 密文
     */
    private String sha1Hex(String plaintext) {
        return DigestUtils.sha1Hex(plaintext);
    }

    /**
     * base64 编码
     *
     * @param src 源数据
     * @return 编码字符串
     */
    private String base64Encode(byte[] src) {
        return Base64.getEncoder().encodeToString(src);
    }

    /**
     * base64 解码
     *
     * @param base64 编码字符串
     * @return 源数据
     */
    private byte[] base64Decode(String base64) {
        return Base64.getDecoder().decode(base64);
    }

    /**
     * 消息通知配置
     */
    @Getter
    public enum NotifyConfig {
        /**
         * 小天才店铺
         */
        XTC("gh_4fda64b8edde", "wxb3b7a4e891a4f0ce",
                "vGzUAqOCWLTANTcC9LtqxIjNWBjvYOv6", "n4drydWGMXqcqvgNGSgEzFpQwcRD44yfhh8atmLMBzc"),
        /**
         * 步步高店铺
         */
        BBK("gh_6398c1d7ac08", "wx6aaf372e0815ec47",
                "KaUm5KyjSGsborxKquCPzA5ZdLELsXbz", "hnOALQYbUMJBkBcx5koMUngfH9WLPKT3BxKYknEqV0v"),
        /**
         * 深圳启鲸小天才
         */
        SZQJ_XTC("gh_23a27da36d7b", "wxa02982a06e019892",
                "Kud67bvXOySw3fCpqBWniUBp2p2woPHQ", "sTNGmwCAlTMFtl8Hefe5fx1vnqJGOicQQSKAXKWBlni"),
        ;

        /**
         * 小店UserName（账号信息-原始ID）
         */
        private final String toUserName;
        /**
         * 微信小店ID（账号信息-微信小店ID）
         */
        private final String appid;
        /**
         * 推送配置token
         */
        private final String token;
        /**
         * AES密钥
         */
        private final String aesKey;

        NotifyConfig(String toUserName, String appid, String token, String aesKey) {
            this.toUserName = toUserName;
            this.appid = appid;
            this.token = token;
            this.aesKey = aesKey;
        }

        public static NotifyConfig getByToUserName(String toUserName) {
            for (NotifyConfig config : NotifyConfig.values()) {
                if (config.toUserName.equals(toUserName)) {
                    return config;
                }
            }
            throw new IllegalArgumentException("消息通知配置不存在 " + toUserName);
        }
    }

    /**
     * 测试用例根据文档开发，切勿随意修改
     */
    public static void main(String[] args) {
        // 测试接收通知消息
        Runnable testReceive = () -> {
            WechatChannelsShopNotify notify = new WechatChannelsShopNotify(
                    "gh_4fda64b8edde",
                    "+qdx1OKCy+5JPCBFWw70tm0fJGb2Jmeia4FCB7kao+/Q5c/ohsOzQHi8khUOb05JCpj0JB4RvQMkUyus8TPxLKJGQqcvZqzDpVzazhZv6JsXUnnR8XGT740XgXZUXQ7vJVnAG+tE8NUd4yFyjPy7GgiaviNrlCTj+l5kdfMuFUPpRSrfMZuMcp3Fn2Pede2IuQrKEYwKSqFIZoNqJ4M8EajAsjLY2km32IIjdf8YL/P50F7mStwntrA2cPDrM1kb6mOcfBgRtWygb3VIYnSeOBrebufAlr7F9mFUPAJGj04=",
                    "046e02f8204d34f8ba5fa3b1db94908f3df2e9b3"
            );
            notify.setAppid("wxba5fad812f8e6fb9");
            notify.setToken("AAAAA");

            String aesKeyStr = "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" + "=";
            byte[] aesKey = notify.base64Decode(aesKeyStr);
            notify.setAesKey(aesKey);

            boolean verifyNotifyServer = notify.verifyNotifyServer("1714036504", "1514711492",
                    "f464b24fc39322e44b38aa78f5edd27bd1441696");
            System.out.println("verifyNotifyServer: " + verifyNotifyServer);
            Assert.isTrue(verifyNotifyServer, "test failed!");

            notify.setTimestamp(null);
            notify.setNonce(null);
            boolean verifyNotifyMessage = notify.verifyNotifyMessage("1714112445", "415670741");
            System.out.println("verifyNotifyMessage: " + verifyNotifyMessage);
            Assert.isTrue(verifyNotifyMessage, "test failed!");

            String decryptMessage = notify.decryptMessage();
            System.out.println("decryptMessage: " + decryptMessage);

            System.out.println("appId: " + notify.getAppid());
            System.out.println("msgAppId: " + notify.getMsgAppId());
            System.out.println("verifyAppid: " + notify.verifyMsgAppid());
            Assert.isTrue(notify.verifyMsgAppid(), "test failed!");

            String encryptMessage = notify.encryptMessage();
            System.out.println("encryptMessage: " + encryptMessage);

            System.out.println(notify);
        };
        testReceive.run();

        // 测试回包给微信服务器
        Runnable testReturn = () -> {
            WechatChannelsShopNotify notify = new WechatChannelsShopNotify(
                    "gh_6398c1d7ac08",
                    "{\"demo_resp\":\"good luck\"}"
            );
            notify.setAppid("wxba5fad812f8e6fb9");
            notify.setToken("AAAAA");

            String aesKeyStr = "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA" + "=";
            byte[] aesKey = notify.base64Decode(aesKeyStr);
            notify.setAesKey(aesKey);

            notify.setTimestamp("1713424427");
            notify.setNonce("415670741");
            notify.setRandomStr("707722b803182950");

            String returnMessage = notify.buildReturnMessage();
            System.out.println("returnMessage: " + returnMessage);

            String encryptMessage = "ELGduP2YcVatjqIS+eZbp80MNLoAUWvzzyJxgGzxZO/5sAvd070Bs6qrLARC9nVHm48Y4hyRbtzve1L32tmxSQ==";
            boolean verifyEncryptMessage = encryptMessage.equals(notify.encryptMessage());
            System.out.println("verifyEncryptMessage: " + verifyEncryptMessage);
            Assert.isTrue(verifyEncryptMessage, "test failed!");

            String msgSignature = "1b9339964ed2e271e7c7b6ff2b0ef902fc94dea1";
            boolean verifyMsgSignature = msgSignature.equals(notify.buildMsgSignature());
            System.out.println("verifyMsgSignature: " + verifyMsgSignature);
            Assert.isTrue(verifyMsgSignature, "test failed!");

            System.out.println(notify);
        };
        testReturn.run();
    }

}
