package com.xtc.marketing.adapterservice.rpc.jd;

import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.etms.TraceQueryJsf.response.get.TraceDTO;
import com.jd.open.api.sdk.domain.etms.TraceQueryJsf.response.get.TraceQueryResultDTO;
import com.jd.open.api.sdk.domain.etms.WaybillJosService.response.receive.WaybillResultInfoDTO;
import com.jd.open.api.sdk.request.JdRequest;
import com.jd.open.api.sdk.request.etms.LdopReceiveTraceGetRequest;
import com.jd.open.api.sdk.request.etms.LdopWaybillReceiveRequest;
import com.jd.open.api.sdk.response.AbstractResponse;
import com.jd.open.api.sdk.response.etms.LdopReceiveTraceGetResponse;
import com.jd.open.api.sdk.response.etms.LdopWaybillReceiveResponse;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.rmi.RemoteException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 京东物流RPC（京东商城-京东快递API）
 * <p><a href="https://jos.jd.com/apilist?apiGroupId=64&apiId=14047&apiName=jingdong.ldop.waybill.receive">宙斯平台-京东快递API</a></p>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class JdLogisticsRpc {

    /**
     * 缓存 client 对象，京东平台
     */
    private static final Map<String, JdClient> CLIENT_CACHE_JD = new ConcurrentHashMap<>();

    /**
     * 物流下单
     * <p><a href="https://jos.jd.com/apilist?apiGroupId=64&apiId=14047&apiName=jingdong.ldop.waybill.receive">京东快递下单接口</a></p>
     *
     * @param account 账号
     * @param request 参数
     * @return 执行结果
     */
    public WaybillResultInfoDTO createOrder(LogisticsAccountDO account, LdopWaybillReceiveRequest request) {
        if (ObjectUtils.anyNull(request.getWeight(), request.getVloumn())) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "参数不合法，重量和体积必填");
        }
        LdopWaybillReceiveResponse response = this.call(account, request);
        return response.getReceiveorderinfoResult();
    }

    /**
     * 查询快递路由
     * <p><a href="https://jos.jd.com/apilist?apiGroupId=64&apiId=13543&apiName=jingdong.ldop.receive.trace.get">查询快递路由</a></p>
     *
     * @param account 账号
     * @param request 参数
     * @return 快递路由
     */
    public List<TraceDTO> searchRoutes(LogisticsAccountDO account, LdopReceiveTraceGetRequest request) {
        LdopReceiveTraceGetResponse response = this.call(account, request);
        return Optional.of(response)
                .map(LdopReceiveTraceGetResponse::getQuerytraceResult)
                .map(TraceQueryResultDTO::getData)
                .orElse(Collections.emptyList());
    }

    /**
     * 执行远程调用
     *
     * @param account 账号
     * @param request 参数
     * @param <T>     返回值类型
     * @return 执行结果
     */
    private <T extends AbstractResponse> T call(LogisticsAccountDO account, JdRequest<T> request) {
        String responseStr = "";
        try {
            log.info("京东平台参数: {}", request.getAppJsonParams());
            JdClient client = this.getJdClient(account);
            T response = client.execute(request);

            responseStr = GsonUtil.objectToJson(response);
            log.info("京东平台返回值: {}", responseStr);

            if (!"0".equals(response.getCode())) {
                throw new RemoteException("RPC返回异常状态码");
            }
            return response;
        } catch (Exception e) {
            String msg = String.format("京东平台调用异常 response: %s", responseStr);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
        }
    }

    /**
     * 获取请求客户端，京东平台
     *
     * @param account 账号
     * @return 客户端
     */
    private JdClient getJdClient(LogisticsAccountDO account) {
        String key = account.getBizAccount();
        if (CLIENT_CACHE_JD.get(key) == null) {
            JdClient client = new DefaultJdClient(account.getApiUrl(), account.getAppAccessToken(),
                    account.getClientCode(), account.getClientSecret(), 500, 3000);
            CLIENT_CACHE_JD.put(key, client);
        }
        return CLIENT_CACHE_JD.get(key);
    }

}
