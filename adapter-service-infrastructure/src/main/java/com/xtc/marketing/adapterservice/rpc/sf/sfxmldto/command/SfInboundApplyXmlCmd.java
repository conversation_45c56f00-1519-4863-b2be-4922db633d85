package com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.command;

import com.beust.jcommander.internal.Lists;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.SfRequestXmlDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 顺丰仓库，入库申请单
 */
@Setter
@Getter
@ToString
@XStreamAlias("PurchaseOrder")
public class SfInboundApplyXmlCmd extends SfRequestXmlDTO {

    /**
     * 供应商代码
     */
    @XStreamAlias("VendorCode")
    private String vendorCode;

    /**
     * 仓库编码
     */
    @XStreamAlias("WarehouseCode")
    private String warehouseCode;

    /**
     * 入库单号（业务方单号）
     */
    @XStreamAlias("ErpOrder")
    private String orderId;

    /**
     * 入库单号生成时间（业务方单号生成时间）
     */
    @XStreamAlias("OrderDate")
    private String orderDate;

    /**
     * 客户订单类型，默认：10
     */
    @XStreamAlias("ErpOrderType")
    private String erpOrderType = "10";

    /**
     * 顺丰订单类型，默认：采购入库
     * 10：采购入库
     * 20：退货入库
     * 30：调拨入库
     * 40：赠品入库
     * 50：换货入库
     * 60：其它入库
     * 如果超出以上6种类型，请单独联系业务部门。
     */
    @XStreamAlias("SFOrderType")
    private String sfOrderType = "采购入库";

    /**
     * 预计收货时间（格式：YYYY-MM-DD HH24:MI:SS）
     */
    @XStreamAlias("ScheduledReceiptDate")
    private String scheduledReceiptDate;

    /**
     * 货物集合
     */
    @XStreamAlias("Items")
    private List<SfInboundApplyXmlCmd.Item> items;

    @Setter
    @Getter
    @ToString
    @XStreamAlias("Item")
    public static class Item {

        /**
         * skuId
         */
        @XStreamAlias("SkuNo")
        private String skuId;

        /**
         * 数量
         */
        @XStreamAlias("Qty")
        private Integer quantity;

        /**
         * 库存状态，默认：10
         * 10:正品
         * 20:残品
         */
        @XStreamAlias("InventoryStatus")
        private String inventoryStatus = "10";

    }

    @Override
    public String toRequestXml(String requestBodyXml, String companyCode) {
        return "<PurchaseOrderRequest>"
                + "<CompanyCode>"
                + companyCode
                + "</CompanyCode>"
                + "<PurchaseOrders>"
                + requestBodyXml
                + "</PurchaseOrders>"
                + "</PurchaseOrderRequest>";
    }

    private static final List<String> RESPONSE_PARSE_ELEMENTS =
            Lists.newArrayList("PurchaseOrderResponse", "PurchaseOrders", "PurchaseOrder");

    @Override
    public List<String> getResponseParseElements() {
        return RESPONSE_PARSE_ELEMENTS;
    }

}
