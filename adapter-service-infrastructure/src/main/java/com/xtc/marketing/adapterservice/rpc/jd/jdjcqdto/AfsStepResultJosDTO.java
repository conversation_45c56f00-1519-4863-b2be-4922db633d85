package com.xtc.marketing.adapterservice.rpc.jd.jdjcqdto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 售后服务单全流程消息
 */
@Getter
@Setter
@ToString
public class AfsStepResultJosDTO {

    /**
     * 订单号
     */
    private String orderId;
    /**
     * 服务单号
     */
    private String afsServiceId;
    /**
     * 商家id
     */
    private String buId;
    /**
     * 操作日期
     */
    private String operationDate;
    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 步骤类型
     */
    private String stepType;
    /**
     * 售后分类ID
     */
    private Long afsCategoryId;
    /**
     * 操作XID
     */
    private String operationXID;
    /**
     * 操作人
     */
    private String operationPin;
    /**
     * 渠道
     */
    private Integer channel;
    /**
     * 商品名称
     */
    private String wareName;
    /**
     * 组织ID
     */
    private String orgId;
    /**
     * 分类来源
     */
    private Integer categorySource;
    /**
     * 审核结果
     */
    private Integer approveResult;
    /**
     * 身份
     */
    private String identity;
    /**
     * 售后结果类型
     */
    private String afsResultType;
    /**
     * SKU UUID
     */
    private String skuUuid;
    /**
     * 商品ID
     */
    private Long wareId;
    /**
     * 操作名称
     */
    private String operationName;
    /**
     * 售后申请ID
     */
    private Long afsApplyId;
    /**
     * 客户期望
     */
    private String customerExpect;
    /**
     * 平台来源
     */
    private Integer platformSrc;
    /**
     * 公司ID
     */
    private Integer companyId;
    /**
     * 服务数量
     */
    private Integer serviceCount;
    /**
     * 客户XID
     */
    private String customerXID;
    /**
     * 客户PIN
     */
    private String customerPin;

}