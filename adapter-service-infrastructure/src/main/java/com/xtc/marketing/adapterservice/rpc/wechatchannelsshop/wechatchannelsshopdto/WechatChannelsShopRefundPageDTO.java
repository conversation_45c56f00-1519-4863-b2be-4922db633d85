package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 微信视频号小店退款列表
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopRefundPageDTO extends WechatChannelsShopBaseDTO {

    /**
     * 售后单号列表
     */
    @SerializedName("after_sale_order_id_list")
    private List<String> afterSaleOrderIdList;
    /**
     * 是否还有数据
     */
    @SerializedName("has_more")
    private Boolean hasMore;
    /**
     * 翻页参数
     */
    @SerializedName("next_key")
    private String nextKey;

    @Override
    public String getResponseDataKey() {
        return "";
    }

}
