package com.xtc.marketing.adapterservice.rpc.pdd.pdddto.enums;

import lombok.Getter;

/**
 * 拼多多平台快递公司
 */
@Getter
public enum PddLogisticsCompany {

    /**
     * 顺丰
     */
    SF("顺丰", 44L, "https://file-link.pinduoduo.com/sf_one", "{\"TIMED-DELIVERY\":{\"value\":\"%s\"}}"),
    /**
     * 圆通
     */
    YTO("圆通", 85L, "https://file-link.pinduoduo.com/yto_one", null),
    /**
     * EMS
     */
    EMS("EMS", 118L, "https://file-link.pinduoduo.com/ems_one", null),
    /**
     * 京东
     */
    JD("京东", 120L, "https://file-link.pinduoduo.com/jd_std", "{\"PRODUCT_TYPE\":{\"value\":\"%s\"}}"),
    ;

    /**
     * 公司名称
     */
    private final String name;
    /**
     * 公司代码
     */
    private final Long code;
    /**
     * 电子面单，模板url
     */
    private final String templateUrl;
    /**
     * 物流服务配置
     */
    private final String logisticsService;

    PddLogisticsCompany(String name, Long code, String templateUrl, String logisticsService) {
        this.name = name;
        this.code = code;
        this.templateUrl = templateUrl;
        this.logisticsService = logisticsService;
    }

    public String getLogisticsService(String logisticsType) {
        return String.format(logisticsService, logisticsType);
    }
}
