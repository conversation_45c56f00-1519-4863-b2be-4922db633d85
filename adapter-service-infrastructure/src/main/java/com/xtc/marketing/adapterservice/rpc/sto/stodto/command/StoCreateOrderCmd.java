package com.xtc.marketing.adapterservice.rpc.sto.stodto.command;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 申通下单参数
 */
@Getter
@Setter
@ToString
public class StoCreateOrderCmd {

    /**
     * 订单号（客户系统自己生成，唯一）
     */
    private String orderNo;
    /**
     * 订单来源（订阅服务时填写的来源编码）
     */
    private String orderSource;
    /**
     * 获取面单的类型（00-普通、03-国际、01-代收、02-到付、04-生鲜），默认普通业务
     */
    private String billType = "00";
    /**
     * 订单类型（01-普通订单、02-调度订单）默认01-普通订单
     */
    private String orderType = "01";
    /**
     * 寄件人信息
     */
    private StoSenderCmd sender;
    /**
     * 收件人信息
     */
    private StoReceiverCmd receiver;
    /**
     * 货物信息
     */
    private StoCargoCmd cargo;
    /**
     * 客户信息
     */
    @SerializedName("customer")
    private StoCustomerCmd stoCustomerCmd;
    /**
     * 支付方式（1-现付；2-到付；3-月结）
     */
    private String payModel = "3";

}
