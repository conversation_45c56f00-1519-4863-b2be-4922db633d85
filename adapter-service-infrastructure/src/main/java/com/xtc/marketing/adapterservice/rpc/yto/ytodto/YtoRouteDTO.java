package com.xtc.marketing.adapterservice.rpc.yto.ytodto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class YtoRouteDTO {

    /**
     * 运单号
     */
    @SerializedName("waybill_No")
    private String wayBillNo;

    /**
     * 走件产生时间 yyyy-MM-dd HH:mm:ss
     */
    @SerializedName("upload_Time")
    private String uploadTime;

    /**
     * 物流状态，固定为：GOT 已揽收;ARRIVAL 已收入;DEPARTURE 已发出;
     * SENT_SCAN 派件;INBOUND 自提柜入柜;SIGNED 签收成功;FAILED 签收失败;
     * FORWARDING 转寄;TMS_RETURN 退回;
     */
    @SerializedName("infoContent")
    private String infoContent;

    /**
     * 物流信息
     */
    @SerializedName("processInfo")
    private String processInfo;

    /**
     * 当前操作城市
     */
    @SerializedName("city")
    private String city;

    /**
     * 当前操作区或者县
     */
    @SerializedName("district")
    private String district;

    /**
     * 重量，单位：kg
     */
    @SerializedName("weight")
    private String weight;

}
