package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.query;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 微信视频号小店订单分页参数
 */
@Getter
@Setter
@ToString
public class WechatChannelsShopOrderPageQry {

    /**
     * 订单更新时间范围
     */
    @SerializedName("update_time_range")
    private TimeRange updateTimeRange;
    /**
     * 订单状态
     */
    private Integer status;
    /**
     * 买家身份标识
     */
    private String openid;
    /**
     * 分页参数，上一页请求返回
     */
    @SerializedName("next_key")
    private String nextKey;
    /**
     * 每页数量(不超过100)
     */
    @SerializedName("page_size")
    private Integer pageSize;

    /**
     * 时间范围
     */
    @Getter
    @Setter
    @ToString
    public static class TimeRange {

        /**
         * 开始时间，秒级时间戳（距离end_time不可超过7天）
         */
        @SerializedName("start_time")
        private Long startTime;
        /**
         * 结束时间，秒级时间戳（距离start_time不可超过7天）
         */
        @SerializedName("end_time")
        private Long endTime;

    }

}
