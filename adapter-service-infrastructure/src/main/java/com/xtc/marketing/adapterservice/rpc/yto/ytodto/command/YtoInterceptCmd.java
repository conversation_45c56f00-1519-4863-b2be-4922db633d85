package com.xtc.marketing.adapterservice.rpc.yto.ytodto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class YtoInterceptCmd {

    /**
     * 单号
     */
    private String wayBillNo;

    /**
     * 收件方姓名
     */
    private String receiverName;

    /**
     * 收件方手机号
     */
    private String receiverTel;

    /**
     * 收件方省份
     */
    private String receiveProvName;

    /**
     * 收件方城市
     */
    private String receiveCityName;

    /**
     * 收件方区县
     */
    private String receiveCountyName;

    /**
     * 收件方地址
     */
    private String receiveAddress;

    /**
     * 拦截备注
     */
    private String wantedDesc;

}
