package com.xtc.marketing.adapterservice.rpc.oms.omsdto.command;

import com.google.gson.annotations.Expose;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.OmsBaseRequest;
import lombok.*;
import org.springframework.http.HttpMethod;

import java.util.List;

/**
 * 订单同步参数
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SyncTradeCmd extends OmsBaseRequest<Void> {

    /**
     * 平台id
     */
    private String platformId;
    /**
     * 订单号集合
     */
    private List<String> tradeIds;
    /**
     * 直接处理订单数据（默认：单个订单直接处理，多个订单异步处理）
     */
    private Boolean processTrade;
    /**
     * 拉取订单详情（默认：true）
     */
    private Boolean pullTradeDetail;
    /**
     * 事件（仅在请求url做标识，并且 Gson 序列化与反序列化都不处理）
     */
    @Expose
    private String event;

    @Override
    public String getApiPath() {
        String eventParam = event == null ? "" : "?event=" + event;
        return "/api/trade/import-sync" + eventParam;
    }

    @Override
    public HttpMethod getRequestMethod() {
        return HttpMethod.POST;
    }

    @Override
    public Class<Void> getResponseClass() {
        return Void.class;
    }

}
