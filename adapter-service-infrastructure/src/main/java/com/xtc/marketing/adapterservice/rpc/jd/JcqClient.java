package com.xtc.marketing.adapterservice.subscribe.ability.client;

import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.subscribe.ability.domainservice.SubscribeLogDomainService;
import com.xtc.marketing.adapterservice.subscribe.ability.domainservice.SubscribePushOmsDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 京东消息拉取客户端
 * 支持开启/关闭拉取线程，拉取到数据后直接处理
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class JcqClient {

    private final SubscribePushOmsDomainService subscribePushOmsDomainService;

    /**
     * 连接地址
     */
    public static final String CONNECT_URL = "https://jcq-shared-004.cn-north-1.jdcloud.com";
    /**
     * 用户accessKey
     */
    private static final String ACCESS_KEY = "JDC_B4739AAC938DA0765C61ADF4AC9C";
    /**
     * 用户secretKey
     */
    private static final String SECRET_KEY = "B0546A667B90813150464A0C3C79E9D2";
    /**
     * 拉取服务线程池缓存
     */
    private static final Map<String, JdMessagePuller> MESSAGE_PULLERS = new ConcurrentHashMap<>();

    /**
     * 清除消息拉取器缓存
     *
     * @param shop 店铺
     * @return 被清除的拉取器，为null说明没有缓存
     */
    public JdMessagePuller removeMessagePuller(ShopDO shop) {
        JdMessagePuller puller = MESSAGE_PULLERS.remove(shop.getShopCode());
        if (puller != null) {
            puller.stop();
        }
        return puller;
    }

    /**
     * 获取消息拉取器
     *
     * @param shop     店铺
     * @param bizParam 业务参数
     * @return 消息拉取器
     */
    public JdMessagePuller getMessagePuller(ShopDO shop, String bizParam) {
        return MESSAGE_PULLERS.computeIfAbsent(shop.getShopCode(), k -> new JdMessagePuller(shop, bizParam));
    }

    /**
     * 启动消息拉取
     *
     * @param shop     店铺
     * @param bizParam 业务参数
     * @return 拉取器状态信息
     */
    public String startPull(ShopDO shop, String bizParam) {
        JdMessagePuller puller = getMessagePuller(shop, bizParam);
        boolean started = puller.start();
        return String.format("京东消息拉取器状态 - 店铺: %s, 启动: %s, 运行中: %s", 
                shop.getShopCode(), started, puller.isRunning());
    }

    /**
     * 停止消息拉取
     *
     * @param shop 店铺
     * @return 停止状态信息
     */
    public String stopPull(ShopDO shop) {
        JdMessagePuller puller = removeMessagePuller(shop);
        boolean existed = puller != null;
        return String.format("京东消息拉取器状态 - 店铺: %s, 已存在: %s, 已停止: %s", 
                shop.getShopCode(), existed, existed);
    }

    /**
     * 京东消息拉取器内部类
     */
    public class JdMessagePuller {
        
        /**
         * 店铺信息
         */
        private final ShopDO shop;
        
        /**
         * 业务参数
         */
        private final String bizParam;
        
        /**
         * 拉取服务线程池
         */
        private ScheduledThreadPoolExecutor pullService;
        
        /**
         * 运行状态标识
         */
        private final AtomicBoolean running = new AtomicBoolean(false);
        
        /**
         * 拉取间隔（毫秒）
         */
        private static final long PULL_INTERVAL = 200L;
        
        /**
         * 初始延迟（毫秒）
         */
        private static final long INITIAL_DELAY = 200L;

        public JdMessagePuller(ShopDO shop, String bizParam) {
            this.shop = shop;
            this.bizParam = bizParam;
        }

        /**
         * 启动拉取服务
         *
         * @return 是否启动成功
         */
        public boolean start() {
            if (running.compareAndSet(false, true)) {
                try {
                    // 创建拉取服务线程池
                    pullService = new ScheduledThreadPoolExecutor(1, 
                            new JdPullThreadFactory("jd-pull-service-" + shop.getShopCode()));
                    
                    // 启动定时拉取任务
                    pullService.scheduleAtFixedRate(new PullTask(), INITIAL_DELAY, PULL_INTERVAL, TimeUnit.MILLISECONDS);
                    
                    log.info("京东消息拉取器启动成功 - 店铺: {}, 拉取间隔: {}ms", shop.getShopCode(), PULL_INTERVAL);
                    return true;
                } catch (Exception e) {
                    running.set(false);
                    log.error("京东消息拉取器启动失败 - 店铺: {}, 错误: {}", shop.getShopCode(), e.getMessage(), e);
                    return false;
                }
            }
            return false; // 已经在运行中
        }

        /**
         * 停止拉取服务
         */
        public void stop() {
            if (running.compareAndSet(true, false)) {
                try {
                    if (pullService != null && !pullService.isShutdown()) {
                        pullService.shutdown();
                        // 等待任务完成，最多等待5秒
                        if (!pullService.awaitTermination(5, TimeUnit.SECONDS)) {
                            pullService.shutdownNow();
                        }
                    }
                    log.info("京东消息拉取器停止成功 - 店铺: {}", shop.getShopCode());
                } catch (Exception e) {
                    log.error("京东消息拉取器停止异常 - 店铺: {}, 错误: {}", shop.getShopCode(), e.getMessage(), e);
                }
            }
        }

        /**
         * 检查是否运行中
         *
         * @return 是否运行中
         */
        public boolean isRunning() {
            return running.get() && pullService != null && !pullService.isShutdown();
        }

        /**
         * 拉取任务
         */
        private class PullTask implements Runnable {
            @Override
            public void run() {
                try {
                    // 检查运行状态
                    if (!running.get()) {
                        return;
                    }
                    
                    // 设置日志追踪ID
                    SubscribeLogDomainService.logTraceId();
                    
                    // 拉取消息（占位方法）
                    String messageData = pullMessage();
                    
                    // 如果有消息数据，直接处理
                    if (messageData != null && !messageData.trim().isEmpty()) {
                        log.debug("京东消息拉取成功 - 店铺: {}, 消息: {}", shop.getShopCode(), messageData);
                        
                        // 直接处理业务（占位方法）
                        processBiz(messageData);
                    }
                    
                } catch (Exception e) {
                    log.error("京东消息拉取任务执行异常 - 店铺: {}, 错误: {}", shop.getShopCode(), e.getMessage(), e);
                }
            }
        }

        /**
         * 拉取消息（占位方法）
         * 实际实现时需要调用京东的消息接口
         *
         * @return 消息数据，无消息时返回null
         */
        private String pullMessage() {
            // TODO: 实现实际的消息拉取逻辑
            // 示例：调用京东消息接口，获取消息数据
            log.trace("执行消息拉取 - 店铺: {}, AppKey: {}", shop.getShopCode(), shop.getAppKey());
            
            // 模拟无消息的情况
            return null;
        }

        /**
         * 处理业务数据（占位方法）
         * 实际实现时需要根据消息类型路由到不同的处理器
         *
         * @param messageData 消息数据
         */
        private void processBiz(String messageData) {
            try {
                // TODO: 实现实际的业务处理逻辑
                log.info("处理京东消息 - 店铺: {}, 消息: {}", shop.getShopCode(), messageData);
                
                // 示例：解析消息并路由到对应的处理器
                // 1. 解析消息内容，确定消息类型
                // 2. 根据消息类型获取对应的数据处理器
                // 3. 构建业务数据对象
                // 4. 调用处理器处理消息
                
                // 模拟处理逻辑
                SubscribePushOmsDomainService.BizData bizData = SubscribePushOmsDomainService.BizData.builder()
                        .platformId(extractPlatformId(bizParam))
                        .tradeId(extractTradeId(messageData))
                        .build();
                
                // 根据消息类型选择处理器
                subscribePushOmsDomainService.tradePushToOms(bizData);
                
            } catch (Exception e) {
                log.error("京东消息处理失败 - 店铺: {}, 消息: {}, 错误: {}", 
                        shop.getShopCode(), messageData, e.getMessage(), e);
            }
        }

        /**
         * 从业务参数中提取平台ID（示例方法）
         */
        private String extractPlatformId(String bizParam) {
            // TODO: 实际解析逻辑
            return "JD_PLATFORM";
        }

        /**
         * 从消息数据中提取订单ID（示例方法）
         */
        private String extractTradeId(String messageData) {
            // TODO: 实际解析逻辑
            return "TRADE_" + System.currentTimeMillis();
        }
    }

    /**
     * 京东拉取线程工厂
     */
    private static class JdPullThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        JdPullThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r, namePrefix + "-" + threadNumber.getAndIncrement());
            // 设置为守护线程
            t.setDaemon(true);
            if (t.getPriority() != Thread.NORM_PRIORITY) {
                t.setPriority(Thread.NORM_PRIORITY);
            }
            return t;
        }
    }
} 