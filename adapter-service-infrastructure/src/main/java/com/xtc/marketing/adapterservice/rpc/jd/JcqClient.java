package com.xtc.marketing.adapterservice.rpc.jd;

import com.xtc.marketing.adapterservice.rpc.jd.jdjcqdto.JcqPullResultDTO;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/**
 * 京东消息拉取客户端
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class JcqClient {

    /**
     * 连接地址
     */
    public static final String CONNECT_URL = "https://jcq-shared-004.cn-north-1.jdcloud.com";
    /**
     * 用户accessKey
     */
    private static final String ACCESS_KEY = "JDC_B4739AAC938DA0765C61ADF4AC9C";
    /**
     * 用户secretKey
     */
    private static final String SECRET_KEY = "B0546A667B90813150464A0C3C79E9D2";
    /**
     * 拉取服务线程池缓存
     */
    private static final Map<String, JdMessagePuller> MESSAGE_PULLERS = new ConcurrentHashMap<>();

    /**
     * 消息消费者
     */
    @Setter
    private Consumer<JcqPullResultDTO> messageConsumer;

    /**
     * 启动消息拉取
     *
     * @param groupId 消费组ID
     * @param topic   主题名称
     * @return 拉取器状态信息
     */
    public String startPull(String groupId, String topic) {
        String key = groupId + "_" + topic;
        JdMessagePuller puller = MESSAGE_PULLERS.computeIfAbsent(key, k -> new JdMessagePuller(groupId, topic));
        boolean started = puller.start();
        return String.format("京东消息拉取器状态 - groupId: %s, topic: %s, 启动: %s, 运行中: %s",
                groupId, topic, started, puller.isRunning());
    }

    /**
     * 停止消息拉取
     *
     * @param groupId 消费组ID
     * @param topic   主题名称
     * @return 停止状态信息
     */
    public String stopPull(String groupId, String topic) {
        String key = groupId + "_" + topic;
        JdMessagePuller puller = MESSAGE_PULLERS.remove(key);
        boolean existed = puller != null;
        if (existed) {
            puller.stop();
        }
        return String.format("京东消息拉取器状态 - groupId: %s, topic: %s, 已存在: %s, 已停止: %s",
                groupId, topic, existed, existed);
    }

    /**
     * 京东消息拉取器内部类
     */
    public class JdMessagePuller {

        /**
         * 运行状态标识
         */
        private final AtomicBoolean running = new AtomicBoolean(false);
        /**
         * 拉取间隔（毫秒）
         */
        private static final long PULL_INTERVAL = 200L;
        /**
         * 初始延迟（毫秒）
         */
        private static final long INITIAL_DELAY = 200L;

        /**
         * 拉取服务线程池
         */
        private ScheduledThreadPoolExecutor pullService;
        /**
         * 消费组ID
         */
        private String groupId;
        /**
         * 主题名称
         */
        private String topic;

        /**
         * 生成 JdMessagePuller
         *
         * @param groupId 消费组ID
         * @param topic   主题名称
         */
        public JdMessagePuller(String groupId, String topic) {
            this.groupId = groupId;
            this.topic = topic;
        }

        /**
         * 启动拉取服务
         *
         * @return 是否启动成功
         */
        public boolean start() {
            if (running.compareAndSet(false, true)) {
                try {
                    // 创建拉取服务线程池
                    pullService = new ScheduledThreadPoolExecutor(1, new JdPullThreadFactory("jd-pull-service"));
                    // 启动定时拉取任务
                    pullService.scheduleAtFixedRate(new PullTask(), INITIAL_DELAY, PULL_INTERVAL, TimeUnit.MILLISECONDS);
                    log.info("京东消息拉取器启动成功 - 拉取间隔: {}ms", PULL_INTERVAL);
                    return true;
                } catch (Exception e) {
                    running.set(false);
                    log.error("京东消息拉取器启动失败 - 错误: {}", e.getMessage(), e);
                    return false;
                }
            }
            // 已经在运行中
            return false;
        }

        /**
         * 停止拉取服务
         */
        public void stop() {
            if (running.compareAndSet(true, false)) {
                try {
                    if (pullService != null && !pullService.isShutdown()) {
                        pullService.shutdown();
                        // 等待任务完成，最多等待5秒
                        if (!pullService.awaitTermination(5, TimeUnit.SECONDS)) {
                            pullService.shutdownNow();
                        }
                    }
                    log.info("京东消息拉取器停止成功");
                } catch (Exception e) {
                    log.error("京东消息拉取器停止异常 - 错误: {}", e.getMessage(), e);
                }
            }
        }

        /**
         * 检查是否运行中
         *
         * @return 是否运行中
         */
        public boolean isRunning() {
            return running.get() && pullService != null && !pullService.isShutdown();
        }

        /**
         * 拉取任务
         */
        private class PullTask implements Runnable {

            @Override
            public void run() {
                try {
                    // 检查运行状态
                    if (!running.get()) {
                        return;
                    }
                    // 拉取消息
                    String messageData = pullMessage();
                    // 如果有消息数据，直接处理
                    if (StringUtils.isNotBlank(messageData)) {
                        log.debug("京东消息拉取成功 - 消息: {}", messageData);
                        // 直接处理业务
                        processBiz(messageData);
                    }
                } catch (Exception e) {
                    log.error("京东消息拉取任务执行异常 - 错误: {}", e.getMessage(), e);
                }
            }

        }

        /**
         * 拉取消息（占位方法）
         * 实际实现时需要调用京东的消息接口
         *
         * @return 消息数据，无消息时返回null
         */
        private String pullMessage() {
            // TODO: 实现实际的消息拉取逻辑
            // 示例：调用京东消息接口，获取消息数据
            log.trace("执行消息拉取");

            // 模拟无消息的情况
            return null;
        }

        /**
         * 处理业务数据
         * 使用设置的消息消费者处理消息
         *
         * @param messageData 消息数据
         */
        private void processBiz(String messageData) {
            try {
                log.info("处理京东消息 - 消息: {}", messageData);
                // 如果设置了消息消费者，则使用消费者处理
                if (messageConsumer != null) {
                    log.debug("使用消息消费者处理消息");
                    JcqPullResultDTO pullResult = GsonUtil.jsonToBean(messageData, JcqPullResultDTO.class);
                    messageConsumer.accept(pullResult);
                } else {
                    log.warn("未设置消息消费者，无法处理消息");
                }
            } catch (Exception e) {
                log.error("京东消息处理失败 - 消息: {}, 错误: {}", messageData, e.getMessage(), e);
            }
        }

    }

    /**
     * 京东拉取线程工厂
     */
    private static class JdPullThreadFactory implements ThreadFactory {

        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        JdPullThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }

        @Override
        public Thread newThread(@NotNull Runnable runnable) {
            Thread thread = new Thread(runnable, namePrefix + "-" + threadNumber.getAndIncrement());
            // 设置为守护线程
            thread.setDaemon(true);
            if (thread.getPriority() != Thread.NORM_PRIORITY) {
                thread.setPriority(Thread.NORM_PRIORITY);
            }
            return thread;
        }

    }

}