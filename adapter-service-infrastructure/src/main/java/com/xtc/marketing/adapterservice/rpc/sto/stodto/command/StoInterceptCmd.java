package com.xtc.marketing.adapterservice.rpc.sto.stodto.command;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 申通拦截参数
 */
@Getter
@Setter
@ToString
public class StoInterceptCmd {

    /**
     * 运单号
     */
    private String waybillNo;
    /**
     * 退改类型，无特殊需求请使用固定值“DEFAULT”，系统会根据客户网点配置计算出最优退回方式，推荐使用
     */
    private final String interceptForAct = "DEFAULT";
    /**
     * 退改收件人姓名
     */
    private String receiverName;
    /**
     * 退改收件人电话
     */
    private String receiverTel;
    /**
     * 退改收件人省
     */
    private String receiverProvince;
    /**
     * 退改收件人城市
     */
    private String receiverCity;
    /**
     * 退改收件人区
     */
    private String receiverArea;
    /**
     * 退改收件人详细地址
     */
    private String receiverAddress;
    /**
     * 退回原因
     */
    private String interceptReason;

}
