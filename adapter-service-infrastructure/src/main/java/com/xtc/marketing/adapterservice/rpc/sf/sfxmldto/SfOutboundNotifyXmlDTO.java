package com.xtc.marketing.adapterservice.rpc.sf.sfxmldto;

import com.google.common.collect.Lists;
import com.thoughtworks.xstream.annotations.XStreamAlias;
import com.thoughtworks.xstream.annotations.XStreamConverter;
import com.thoughtworks.xstream.annotations.XStreamImplicit;
import com.xtc.marketing.adapterservice.rpc.sf.util.IntFromStringConverter;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 顺丰仓库，出库通知
 */
@Getter
@Setter
@ToString
@XStreamAlias("SaleOrder")
public class SfOutboundNotifyXmlDTO extends SfNotifyXmlDTO {

    /**
     * 仓库编码
     */
    @XStreamAlias("WarehouseCode")
    private String warehouseCode;

    /**
     * 出库单号（业务方单号）
     */
    @XStreamAlias("ErpOrder")
    private String orderId;

    /**
     * 顺丰出库单号（顺丰单号）
     */
    @XStreamAlias("ShipmentId")
    private String shipmentId;

    /**
     * 运单号
     */
    @XStreamAlias("WayBillNo")
    private String waybillNo;

    /**
     * 出库单类型
     */
    @XStreamAlias("ErpOrderType")
    private String orderType;

    /**
     * 顺丰出库单类型
     */
    @XStreamAlias("SfOrderType")
    private String shipmentOrderType;

    /**
     * 出库单状态
     * <pre>
     *     1400:已取消
     *     2300:等待工作
     *     2700:包装完成
     *     2900:发货确认
     *     3900:订单已完成
     * </pre>
     */
    @XStreamAlias("DataStatus")
    private String orderStatus;

    /**
     * 发货时间（格式：YYYY-MM-DD HH24:MI:SS）
     */
    @XStreamAlias("ActualShipDateTime")
    private String actualShipTime;

    /**
     * 箱子集合
     */
    @XStreamAlias("Containers")
    private List<Container> containers;

    @Getter
    @Setter
    @ToString
    @XStreamAlias("Container")
    public static class Container {

        /**
         * 箱子货物集合
         */
        @XStreamAlias("ContainerItems")
        private List<ContainerItem> containerItems;

    }

    @Getter
    @Setter
    @ToString
    @XStreamAlias("Item")
    public static class ContainerItem {

        /**
         * 商品编号
         */
        @XStreamAlias("SkuNo")
        private String skuId;

        /**
         * 库存状态
         * 10:正品
         * 20:残品
         */
        @XStreamAlias("InventoryStatus")
        private String inventoryStatus;

        /**
         * 出库数量
         */
        @XStreamConverter(value = IntFromStringConverter.class)
        @XStreamAlias("ActualQty")
        private Integer actualQuantity;

        /**
         * 序列号集合（产品条码）
         */
        @XStreamAlias("SerialNumbers")
        private SerialNumbers serialNumbers;

    }

    @Getter
    @Setter
    @ToString
    @XStreamAlias("SerialNumbers")
    public static class SerialNumbers {

        /**
         * 序列号集合（产品条码）
         */
        @XStreamImplicit(itemFieldName = "SerialNumber")
        private List<String> serialNumber;

    }

    private static final List<String> NOTIFY_PARSE_ELEMENTS =
            Lists.newArrayList("SaleOrderOutboundDetailRequest", "SaleOrders", "SaleOrder");

    @Override
    public List<String> getNotifyParseElements() {
        return NOTIFY_PARSE_ELEMENTS;
    }

    @Override
    public String getNotifyResponse() {
        return this.buildNotifyResponse("SALE_ORDER_OUTBOUND_DETAIL_SERVICE",
                "SaleOrderOutboundDetailResponse");
    }

    /**
     * 获取出库单状态文本
     *
     * @return 出库单状态文本
     */
    public String getOrderStatusText() {
        switch (this.orderStatus) {
            case "1400":
                return "已取消";
            case "2300":
                return "等待工作";
            case "2700":
                return "包装完成";
            case "2900":
                return "发货确认";
            case "3900":
                return "订单已完成";
            default:
                return this.orderStatus;
        }
    }

}
