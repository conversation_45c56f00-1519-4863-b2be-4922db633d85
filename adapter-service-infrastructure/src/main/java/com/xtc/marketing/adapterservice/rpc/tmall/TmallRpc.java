package com.xtc.marketing.adapterservice.rpc.tmall;

import com.google.common.collect.ImmutableMap;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.TaobaoRequest;
import com.taobao.api.TaobaoResponse;
import com.taobao.api.domain.Refund;
import com.taobao.api.domain.Trade;
import com.taobao.api.request.*;
import com.taobao.api.response.*;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;

import java.net.SocketTimeoutException;
import java.rmi.RemoteException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 天猫商城RPC
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TmallRpc {

    /**
     * 缓存 client 对象
     */
    private static final Map<String, TaobaoClient> CLIENT_CACHE = new ConcurrentHashMap<>();

    /**
     * 订单列表字段
     */
    public static final String FIELDS_ORDER_LIST = "tid,type,status,step_trade_status,modified,pay_time,payment," +
            "buyer_memo,seller_memo,orders,rx_audit_status,rx_has_send_good,receipt_type,send_time";
    /**
     * 订单详情字段
     */
    public static final String FIELDS_ORDER_DETAIL = "oaid,trade_attr,coupon_fee,store_code,shipper,is_sh_ship," +
            "invoice_no,invoice_type,invoice_kind,modified,received_payment,alipay_point,step_trade_status,pcc_af," +
            "step_paid_fee,alipay_no,invoice_name,buyer_nick,post_fee,title,buyer_alipay_no,buyer_message,buyer_memo," +
            "seller_memo,type,created,sid,tid,status,buyer_rate,total_fee,payment,discount_fee,pay_time,consign_time," +
            "point_fee,pic_path,num_iid,num,price,receiver_name,receiver_state,receiver_city,receiver_district," +
            "receiver_address,receiver_zip,receiver_mobile,receiver_phone,orders.title,orders.pic_path,orders.price," +
            "orders.num,orders.iid,orders.num_iid,orders.sku_id,orders.status,orders.oid,orders.total_fee,tmall_coupon_fee," +
            "orders.payment,orders.discount_fee,orders.outer_iid,orders.outer_sku_id,orders.sku_properties_name," +
            "orders.refund_status,orders.status,orders.refund_id,promotion,promotion_details,buyer_open_uid," +
            "receipt_type,send_time,omnichannel_param,tcps_code,omni_tags,gov_sn_check,gov_subsidy_amount," +
            "use_gov_subsidy,gov_subsidy_amount_exact,gov_store,use_gov_subsidy_new,gov_subsidy_amount_new," +
            "orders.use_gov_subsidy_new,orders.gov_subsidy_amount_new,orders.gov_sn_check";
    /**
     * 退款列表字段
     */
    public static final String FIELDS_REFUND = "refund_id,tid,oid,total_fee,buyer_nick,created,modified," +
            "order_status,status,good_status,has_good_return,refund_fee,payment,reason,desc,title,num," +
            "company_name,sid,outer_id,sku";
    /**
     * 评价列表字段
     */
    public static final String FIELDS_COMMENT = "tid,oid,role,nick,result,created,rated_nick,item_title,item_price," +
            "content,reply,valid_score,num_iid,logistics_service_score,ouid";

    /**
     * 中转服务器地址
     */
    private static final String PROXY_DOMAIN = "http://************:30002";
    /**
     * API：查询订单分页列表
     */
    private static final String API_ORDER_PAGE = "/api/tmall/order/page";
    /**
     * API：查询订单详情
     */
    private static final String API_ORDER_DETAIL = "/api/tmall/order/detail";
    /**
     * API：查询订单的应开发票金额
     */
    private static final String API_ORDER_INVOICE_AMOUNT = "/api/tmall/order/invoice-amount";
    /**
     * API：订单备注
     */
    private static final String API_ORDER_REMARK = "/api/tmall/order/remark";
    /**
     * API：查询退款单分页列表
     */
    private static final String API_REFUND_PAGE = "/api/tmall/refund/page";
    /**
     * API：查询退款单详情
     */
    private static final String API_REFUND_DETAIL = "/api/tmall/refund/detail";

    /**
     * 订单列表查询
     *
     * @param shop    店铺
     * @param request 参数
     * @return 订单列表
     */
    public TradesSoldIncrementGetResponse pageOrders(ShopDO shop, TradesSoldIncrementGetRequest request) {
        return this.call(shop, request, API_ORDER_PAGE);
    }

    /**
     * 订单详情查询
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 订单详情
     */
    public Trade getOrder(ShopDO shop, String orderNo) {
        TradeFullinfoGetRequest request = new TradeFullinfoGetRequest();
        request.setFields(FIELDS_ORDER_DETAIL);
        request.setTid(Long.parseLong(orderNo));
        TradeFullinfoGetResponse response = this.call(shop, request, API_ORDER_DETAIL);
        return response.getTrade();
    }

    /**
     * 退款单列表查询
     *
     * @param shop    店铺
     * @param request 参数
     * @return 退款单列表
     */
    public RefundsReceiveGetResponse pageRefunds(ShopDO shop, RefundsReceiveGetRequest request) {
        return this.call(shop, request, API_REFUND_PAGE);
    }

    /**
     * 退款单详情查询
     *
     * @param shop    店铺
     * @param request 参数
     * @return 退款单
     */
    public Refund getRefund(ShopDO shop, RefundGetRequest request) {
        RefundGetResponse response = this.call(shop, request, API_REFUND_DETAIL);
        return response.getRefund();
    }

    /**
     * 评价列表查询
     *
     * @param shop    店铺
     * @param request 参数
     * @return 评价列表
     */
    public TraderatesGetResponse pageComments(ShopDO shop, TraderatesGetRequest request) {
        return this.call(shop, request);
    }

    /**
     * 物流发货
     *
     * @param shop    店铺
     * @param request 参数
     * @return 执行结果
     */
    public boolean orderShipping(ShopDO shop, AlibabaAscpLogisticsOfflineSendRequest request) {
        AlibabaAscpLogisticsOfflineSendResponse response = this.call(shop, request);
        return response.getResult().getSuccess();
    }

    /**
     * 取消物流发货
     *
     * @param shop     店铺
     * @param orderNo  订单号
     * @param refundId 退款单号
     * @return 执行结果
     */
    public boolean orderShippingCancel(ShopDO shop, String orderNo, String refundId) {
        AliyunFbtRefundErpInterceptRequest request = new AliyunFbtRefundErpInterceptRequest();
        AliyunFbtRefundErpInterceptRequest.CancelGoodsDTO cancelGoodsDTO = new AliyunFbtRefundErpInterceptRequest.CancelGoodsDTO();
        cancelGoodsDTO.setTid(Long.parseLong(orderNo));
        cancelGoodsDTO.setRefundId(Long.parseLong(refundId));
        cancelGoodsDTO.setStatus("SUCCESS");
        request.setParam(cancelGoodsDTO);
        AliyunFbtRefundErpInterceptResponse response = this.call(shop, request);
        return response.isSuccess();
    }

    /**
     * 虚拟发货
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 执行结果
     */
    public boolean orderDummyShipping(ShopDO shop, String orderNo) {
        LogisticsDummySendRequest request = new LogisticsDummySendRequest();
        request.setTid(Long.parseLong(orderNo));
        LogisticsDummySendResponse rsp = this.call(shop, request);
        return rsp.getShipping().getIsSuccess();
    }

    /**
     * 备注
     *
     * @param shop    店铺
     * @param request 参数
     * @return 执行结果
     */
    public boolean orderRemark(ShopDO shop, TradeMemoUpdateRequest request) {
        TradeMemoUpdateResponse response = this.call(shop, request, API_ORDER_REMARK);
        return response.isSuccess();
    }

    /**
     * 解密
     *
     * @param shop    店铺
     * @param request 参数
     * @return 执行结果
     */
    public List<TopOaidDecryptResponse.Receiver> orderDecrypt(ShopDO shop, TopOaidDecryptRequest request) {
        TopOaidDecryptResponse response = this.call(shop, request);
        return response.getReceiverList();
    }

    /**
     * 查询发票申请
     *
     * @param shop    店铺
     * @param request 参数
     * @return 发票申请
     */
    public List<AlibabaEinvoiceApplyGetResponse.Apply> getInvoiceApply(ShopDO shop, AlibabaEinvoiceApplyGetRequest request) {
        AlibabaEinvoiceApplyGetResponse response = this.call(shop, request);
        return response.getApplyList();
    }

    /**
     * 查询订单应开票金额
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 订单应开票金额
     */
    public TradeInvoiceAmountGetResponse getInvoiceAmount(ShopDO shop, String orderNo) {
        TradeInvoiceAmountGetRequest request = new TradeInvoiceAmountGetRequest();
        request.setTid(Long.parseLong(orderNo));
        return this.call(shop, request, API_ORDER_INVOICE_AMOUNT);
    }

    /**
     * 退货确认入仓
     *
     * @param shop     店铺
     * @param refundId 退款单号
     */
    public void refundGoodsToWarehouse(ShopDO shop, String refundId) {
        NextoneLogisticsWarehouseUpdateRequest request = new NextoneLogisticsWarehouseUpdateRequest();
        request.setRefundId(Long.parseLong(refundId));
        request.setWarehouseStatus(1L);
        NextoneLogisticsWarehouseUpdateResponse response = this.call(shop, request);
        Optional.of(response)
                .map(NextoneLogisticsWarehouseUpdateResponse::getSucceed)
                .filter(BooleanUtils::isTrue)
                .orElseThrow(() -> this.rpcSysException(response));
    }

    /**
     * 上传发票文件
     *
     * @param shop    店铺
     * @param request 请求参数
     * @return 执行结果
     */
    public boolean uploadInvoiceFile(ShopDO shop, AlibabaEinvoiceDetailUploadRequest request) {
        AlibabaEinvoiceDetailUploadResponse alibabaEinvoiceDetailUploadResponse = this.call(shop, request);
        return alibabaEinvoiceDetailUploadResponse.isSuccess();
    }

    /**
     * 生成电子面单
     *
     * @param shop    店铺
     * @param request 电子面单请求参数
     * @return 电子面单
     */
    public CainiaoWaybillIiGetResponse.WaybillCloudPrintResponse createLogisticsOrder(ShopDO shop, CainiaoWaybillIiGetRequest request) {
        CainiaoWaybillIiGetResponse response = this.call(shop, request);
        return Optional.of(response)
                .map(CainiaoWaybillIiGetResponse::getModules)
                .filter(CollectionUtils::isNotEmpty)
                .map(items -> items.get(0))
                .orElseThrow(() -> this.rpcSysException(response));
    }

    /**
     * 取消电子面单
     *
     * @param shop    店铺
     * @param request 取消电子面单请求参数
     * @return 执行结果
     */
    public boolean cancelLogisticsOrder(ShopDO shop, CainiaoWaybillIiCancelRequest request) {
        CainiaoWaybillIiCancelResponse response = this.call(shop, request);
        return Optional.of(response)
                .map(CainiaoWaybillIiCancelResponse::getCancelResult)
                .orElseThrow(() -> this.rpcSysException(response));
    }

    /**
     * 执行远程调用
     *
     * @param shop    店铺
     * @param request 请求参数
     * @param <T>     返回值类型
     * @return 返回值
     */
    private <T extends TaobaoResponse> T call(ShopDO shop, TaobaoRequest<T> request) {
        log.info("天猫参数: {}", GsonUtil.objectToJson(request));
        String responseStr = "";
        try {
            // 执行请求
            TaobaoClient client = this.getClient(shop);
            T response = client.execute(request, shop.getAppSessionKey());
            // 解析响应结果
            responseStr = GsonUtil.objectToJson(response);
            log.info("天猫返回值: {}", responseStr);
            if (!response.isSuccess()) {
                throw new RemoteException("RPC返回异常状态码");
            }
            return response;
        } catch (Exception e) {
            throw this.rpcSysException(responseStr, e);
        }
    }

    /**
     * 执行远程调用
     *
     * @param shop    店铺
     * @param request 请求
     * @param api     中转接口
     * @param <T>     返回值类型
     * @return 执行结果
     */
    private <T extends TaobaoResponse> T call(ShopDO shop, TaobaoRequest<T> request, String api) {
        String url = PROXY_DOMAIN + api;
        String responseStr = "";
        try {
            // 生成请求参数
            Map<String, Object> body = ImmutableMap.of("shop", shop, "requestParam", GsonUtil.objectToJson(request));
            // 执行请求
            responseStr = HttpUtil.post(url, body);
            // 解析响应结果
            if (StringUtils.isBlank(responseStr)) {
                throw remoteException();
            }
            return GsonUtil.jsonToBean(responseStr, request.getResponseClass());
        } catch (Exception e) {
            if (e instanceof HttpStatusCodeException) {
                responseStr = ((HttpStatusCodeException) e).getResponseBodyAsString();
            }
            if (e.getCause() instanceof SocketTimeoutException) {
                responseStr = "请求超时";
            }
            throw this.rpcSysException(responseStr, url, e);
        }
    }

    /**
     * 获取请求客户端
     *
     * @param shop 店铺
     * @return 客户端
     */
    private TaobaoClient getClient(ShopDO shop) {
        String key = shop.getShopCode();
        if (CLIENT_CACHE.get(key) == null) {
            TaobaoClient client = new DefaultTaobaoClient(shop.getApiUrl(), shop.getAppKey(), shop.getAppSecret());
            CLIENT_CACHE.put(key, client);
        }
        return CLIENT_CACHE.get(key);
    }

    /**
     * 远程接口异常
     *
     * @return 异常
     */
    private RemoteException remoteException() {
        return new RemoteException("RPC返回异常状态码");
    }

    /**
     * 远程调用异常
     *
     * @param response 响应
     * @return 异常
     */
    private SysException rpcSysException(Object response) {
        String responseStr = GsonUtil.objectToJson(response);
        String msg = String.format("天猫平台调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg);
    }

    /**
     * 远程调用异常
     *
     * @param responseStr 响应结果
     * @param e           异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, Exception e) {
        String msg = String.format("天猫平台调用异常 response: %s", responseStr);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
    }

    /**
     * 远程调用异常
     *
     * @param responseStr 响应结果
     * @param url         url
     * @param e           异常
     * @return 异常
     */
    private SysException rpcSysException(String responseStr, String url, Exception e) {
        String msg = String.format("天猫平台调用异常 response: %s, url: %s", responseStr, url);
        return SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
    }

    public static void main(String[] args) {
        TmallRpc rpc = new TmallRpc();
        ShopDO shop = ShopDO.builder()
                .shopCode("TMALL_XTC")
                .apiUrl("https://gw.api.taobao.com/router/rest")
                .appKey("xxxxxx")
                .appSecret("xxxxxx")
                .appSessionKey("xxxxxx")
                .build();
        rpc.getOrder(shop, "xxxxxx");
    }

}
