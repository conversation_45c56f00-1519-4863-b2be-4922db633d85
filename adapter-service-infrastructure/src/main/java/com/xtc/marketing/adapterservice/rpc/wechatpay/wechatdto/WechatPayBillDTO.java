package com.xtc.marketing.adapterservice.rpc.wechatpay.wechatdto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 微信支付账单
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class WechatPayBillDTO {

    /**
     * 哈希类型，说明：哈希类型
     */
    @SerializedName("hash_type")
    private String hashType;

    /**
     * 哈希值，说明：原始账单（gzip需要解压缩）的摘要值，用于校验文件的完整性
     */
    @SerializedName("hash_value")
    private String hashValue;

    /**
     * 下载地址，说明：供下一步请求账单文件的下载地址，该地址超过时间后失效
     */
    @SerializedName("download_url")
    private String downloadUrl;

}
