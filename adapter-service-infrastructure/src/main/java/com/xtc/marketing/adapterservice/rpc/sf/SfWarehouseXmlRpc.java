package com.xtc.marketing.adapterservice.rpc.sf;

import com.google.common.collect.Lists;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.*;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.command.*;
import com.xtc.marketing.adapterservice.rpc.sf.sfxmldto.query.SfStocksXmlQry;
import com.xtc.marketing.adapterservice.rpc.sf.util.XStreamUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpServerErrorException;

import java.net.SocketTimeoutException;
import java.nio.charset.StandardCharsets;
import java.rmi.RemoteException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 顺丰仓库RPC
 * <pre>
 *     测试地址：https://portal-gateway.sit.sf-express.com:55443/scc-portal-api-service/omsPortalService/sendRequest
 *     接入码：ldmqUWpPZdRm3lsBGNiHuw==
 *     校验码：SOoaG23JhBC7NoS1HU3uX8VqYLMe5ruO
 *     货主：W7698089145
 *     仓库：755DCG
 * </pre>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SfWarehouseXmlRpc {

    /**
     * 生产环境
     */
    private static final String DOMAIN = "http://bsp-oisp.sf-express.com/bsp-wms/OmsCommons";
    /**
     * 接入编码
     */
    private static final String ACCESS_CODE = "ldmqUWpPZdRm3lsBGNiHuw==";
    /**
     * client secret
     */
    private static final String CLIENT_SECRET = "qFpubwOAQ2a8CmMR2TcfT47tHWX1w02w";
    /**
     * 校验码
     */
    private static final String CHECK_WORK = "MQTjKpNXhR4MfT2JM43yplW14XRo5luW";
    /**
     * 货主编码
     */
    private static final String COMPANY_CODE = "W7698089145";
    /**
     * 接口地址：拦截出库
     */
    private static final String URL_OUT_BOUND_INTERCEPT = "http://bsp-ois.sf-express.com/bsp-ois/sfexpressService";
    /**
     * 请求头：拦截出库
     */
    private static final String HEAD_OUT_BOUND_INTERCEPT = "gdtcx,I01LV8y9tA84mJT0AmA9H07Oe1ANUJXd";
    /**
     * 校验码：拦截出库
     */
    private static final String CHECK_WORK_OUT_BOUND_INTERCEPT = "I01LV8y9tA84mJT0AmA9H07Oe1ANUJXd";

    /**
     * API：查询库存
     */
    private static final String API_QUERY_STOCKS = "RT_INVENTORY_QUERY_SERVICE";
    /**
     * API：申请入库
     */
    private static final String API_INBOUND_APPLY = "PURCHASE_ORDER_SERVICE";
    /**
     * API：取消入库
     */
    private static final String API_INBOUND_CANCEL = "CANCEL_PURCHASE_ORDER_SERVICE";
    /**
     * API：申请出库
     */
    private static final String API_OUTBOUND_APPLY = "SALE_ORDER_SERVICE";
    /**
     * API：取消出库
     */
    private static final String API_OUTBOUND_CANCEL = "CANCEL_SALE_ORDER_SERVICE";
    /**
     * API：拦截出库
     */
    private static final String API_OUTBOUND_INTERCEPT = "WantedOrderService";

    /**
     * 查询库存
     *
     * @param qry 参数
     * @return 库存
     */
    public List<SfStockXmlDTO.Stock> queryStocks(SfStocksXmlQry qry) {
        SfStockXmlDTO sfStockXmlDTO = this.call(API_QUERY_STOCKS, qry, SfStockXmlDTO.class);
        return Optional.ofNullable(sfStockXmlDTO.getStocks())
                .orElse(Collections.emptyList())
                .stream()
                .map(SfStockXmlDTO.StockResponse::getStock)
                .collect(Collectors.toList());
    }

    /**
     * 申请入库
     *
     * @param cmd 参数
     * @return 执行结果
     */
    public SfInboundApplyXmlDTO applyInbound(SfInboundApplyXmlCmd cmd) {
        return this.call(API_INBOUND_APPLY, cmd, SfInboundApplyXmlDTO.class);
    }

    /**
     * 取消入库
     *
     * @param orderId 入库单号（业务方单号）
     * @return 执行结果
     */
    public SfInboundCancelXmlDTO cancelInbound(String orderId) {
        SfInboundCancelXmlCmd cmd = SfInboundCancelXmlCmd.builder().orderId(orderId).build();
        return this.call(API_INBOUND_CANCEL, cmd, SfInboundCancelXmlDTO.class);
    }

    /**
     * 出库申请
     *
     * @param cmd 参数
     * @return 平台出库单号
     */
    public SfOutboundApplyXmlDTO applyOutbound(SfOutboundApplyXmlCmd cmd) {
        return this.call(API_OUTBOUND_APPLY, cmd, SfOutboundApplyXmlDTO.class);
    }

    /**
     * 取消出库
     *
     * @param orderId 出库单号（业务方单号）
     * @return 执行结果
     */
    public SfOutboundCancelXmlDTO cancelOutbound(String orderId) {
        SfOutboundCancelXmlCmd cmd = SfOutboundCancelXmlCmd.builder().orderId(orderId).build();
        return this.call(API_OUTBOUND_CANCEL, cmd, SfOutboundCancelXmlDTO.class);
    }

    /**
     * 拦截出库
     *
     * @param cmd 参数
     */
    public void interceptOutbound(SfOutboundInterceptXmlCmd cmd) {
        this.call(URL_OUT_BOUND_INTERCEPT, API_OUTBOUND_INTERCEPT, cmd, SfResponseXmlDTO.class);
    }

    /**
     * 转换通知数据字符串转换为 bean 对象
     *
     * @param data 通知数据
     * @return 转换后 bean 对象
     */
    public <T extends SfNotifyXmlDTO> T convertToNotifyBean(String data, Class<T> clazz) {
        try {
            Element rootElement = DocumentHelper.parseText(data).getRootElement();
            Element bodyElement = rootElement.element("Body");
            Element bizResult = clazz.newInstance().getNotifyParseElements().stream()
                    .reduce(bodyElement, Element::element, (e1, e2) -> e2);
            return XStreamUtil.toBean(bizResult.asXML(), clazz);
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "顺丰仓库通知解析异常", e);
        }
    }

    /**
     * 接口调用
     *
     * @param apiCode       api 代码
     * @param request       请求参数
     * @param responseClass 响应结果类型
     * @param <T>           继承 SfRequestBaseXmlDTO 的类型
     * @param <R>           继承 SfBaseXmlDTO 的类型
     * @return 响应结果
     */
    private <T extends SfRequestXmlDTO, R extends SfResponseXmlDTO> R call(String apiCode, T request, Class<R> responseClass) {
        return this.call(DOMAIN, apiCode, request, responseClass);
    }

    /**
     * 接口调用
     *
     * @param url           请求地址
     * @param apiCode       api 代码
     * @param request       请求参数
     * @param responseClass 响应结果类型
     * @param <T>           继承 SfRequestBaseXmlDTO 的类型
     * @param <R>           继承 SfBaseXmlDTO 的类型
     * @return 响应结果
     */
    private <T extends SfRequestXmlDTO, R extends SfResponseXmlDTO> R call(String url, String apiCode,
                                                                           T request, Class<R> responseClass) {
        // 获取请求报文，构建请求参数封装
        String requestXml = Optional.of(this.toRequestXml(apiCode, request)).map(this::removeNewline).get();
        MultiValueMap<String, Object> body = BooleanUtils.isTrue(request.otherXml())
                ? this.buildOtherBody(requestXml) : this.buildBody(requestXml);

        // 发送请求，处理异常
        String response = "";
        Element responseBody;
        try {
            response = Optional.ofNullable(HttpUtil.postForForm(url, body)).map(this::removeNewline)
                    .orElseThrow(() -> new RemoteException("顺丰响应报文为空"));
            log.info("SfWarehouseXmlRpc response: {}", response);

            Element rootElement = DocumentHelper.parseText(response).getRootElement();
            String responseHead = rootElement.elementText("Head");
            if (!"OK|PART".contains(responseHead)) {
                throw new RemoteException(rootElement.elementText("ERROR"));
            }
            responseBody = rootElement.element("Body");
        } catch (Exception e) {
            if (e instanceof HttpServerErrorException) {
                response = ((HttpServerErrorException) e).getResponseBodyAsString();
            }
            if (e.getCause() instanceof SocketTimeoutException) {
                response = "请求超时";
            }
            String msg = String.format("顺丰仓库调用异常 response: %s", response);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, response, msg, e);
        }

        // 解析响应结果
        return this.parseResponse(responseBody, request.getResponseParseElements(), responseClass);
    }

    /**
     * 解析响应结果
     *
     * @param responseBody          响应报文
     * @param responseParseElements 响应结果的 xml 元素解析层次
     * @param responseClass         响应结果类型
     * @param <R>                   继承 SfBaseXmlDTO 的类型
     * @return 响应结果
     */
    private <R extends SfResponseXmlDTO> R parseResponse(Element responseBody,
                                                         List<String> responseParseElements,
                                                         Class<R> responseClass) {
        try {
            // 获取业务结果
            Element bizResult = responseParseElements.stream()
                    .reduce(responseBody, Element::element, (e1, e2) -> e2);
            // 将 xml 字符串转换为 bean 对象
            R resultDTO = XStreamUtil.toBean(bizResult.asXML(), responseClass);
            if (resultDTO.isFailure()) {
                throw new RemoteException(resultDTO.getNote());
            }
            return resultDTO;
        } catch (Exception e) {
            String msg;
            if (e instanceof RemoteException) {
                msg = String.format("顺丰仓库响应结果失败 message: %s", e.getMessage());
            } else {
                msg = String.format("顺丰仓库响应结果解析异常 responseBody: %s", responseBody.asXML());
            }
            throw SysException.of(SysErrorCode.S_RPC_ERROR, msg, msg, e);
        }
    }

    /**
     * 获取 xml 请求报文
     *
     * @param apiCode api 代码
     * @param request 请求参数
     * @param <T>     继承 SfRequestBaseXmlDTO 的类型
     * @return xml 请求报文
     */
    private <T extends SfRequestXmlDTO> String toRequestXml(String apiCode, T request) {
        if (BooleanUtils.isTrue(request.otherXml())) {
            return "<Request service=\"" + apiCode + "\">"
                    + "<Head>" + HEAD_OUT_BOUND_INTERCEPT + "</Head>"
                    + "<Body>" + request.toRequestXml("") + "</Body>"
                    + "</Request>";
        }
        return "<?xml version=\"1.0\" encoding=\"utf-8\"?>"
                + "<Request service=\"" + apiCode + "\" lang=\"zh-CN\">"
                + "<Head>"
                + "<AccessCode>" + ACCESS_CODE + "</AccessCode>"
                + "<Checkword>" + CHECK_WORK + "</Checkword>"
                + "</Head>"
                + "<Body>" + request.toRequestXml(COMPANY_CODE) + "</Body>"
                + "</Request>";
    }

    /**
     * 构建请求参数封装
     *
     * @param requestXml xml格式请求体
     * @return 请求参数封装
     */
    private MultiValueMap<String, Object> buildOtherBody(String requestXml) {
        try {
            String msgData = requestXml + SfWarehouseXmlRpc.CHECK_WORK_OUT_BOUND_INTERCEPT;

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("xml", requestXml);
            body.add("verifyCode", this.buildMsgDigest(msgData));
            return body;
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "顺丰仓库调用异常，组装参数异常", e);
        }
    }

    /**
     * 构建请求参数封装
     *
     * @param requestXml xml格式请求体
     * @return 请求参数封装
     */
    private MultiValueMap<String, Object> buildBody(String requestXml) {
        try {
            String msgData = HttpUtil.urlEncode(requestXml + SfWarehouseXmlRpc.CLIENT_SECRET);

            MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
            body.add("logistics_interface", requestXml);
            body.add("data_digest", this.buildMsgDigest(msgData));
            return body;
        } catch (Exception e) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, "顺丰仓库调用异常，组装参数异常", e);
        }
    }

    /**
     * 生成签名
     *
     * @param msgData 请求数据
     * @return 签名
     */
    private String buildMsgDigest(String msgData) throws NoSuchAlgorithmException {
        byte[] md5 = this.md5Encode(msgData);
        return HttpUtil.base64Encode(md5);
    }

    /**
     * md5加密
     *
     * @param encryptStr 加密字符串
     * @return 密文字节
     */
    private byte[] md5Encode(String encryptStr) throws NoSuchAlgorithmException {
        MessageDigest md5 = MessageDigest.getInstance("MD5");
        md5.update(encryptStr.getBytes(StandardCharsets.UTF_8));
        return md5.digest();
    }

    /**
     * 去除换行符
     *
     * @param str 字符串
     * @return 去除换行符后的字符串
     */
    private String removeNewline(String str) {
        return str.replaceAll("[\n\r]", "");
    }

    public static void main(String[] args) {
        SfWarehouseXmlRpc rpc = new SfWarehouseXmlRpc();
        SfStocksXmlQry qry = new SfStocksXmlQry();
        qry.setWarehouseCode("010DCA");
        qry.setSkus(Lists.newArrayList(SfStocksXmlQry.Sku.builder().skuId("6921734923981").build()));
        List<SfStockXmlDTO.Stock> stocks = rpc.queryStocks(qry);
        log.info("{}", GsonUtil.objectToJson(stocks));
    }

}
