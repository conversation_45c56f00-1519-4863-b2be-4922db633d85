package com.xtc.marketing.adapterservice.rpc.tenserpay.tenserpaydto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class TenserPayOrderDTO extends TenserPayBaseDTO {

    /**
     * 订单支付成功状态
     */
    public static final String STATUS_SUCCESS = "S";

    /**
     * 收款方ID
     */
    private String payeeId;

    /**
     * 平台订单号
     */
    private String orderNo;

    /**
     * 支付渠道订单号（微信账单里的交易单号）
     */
    private String payChannelOrderNo;

    /**
     * 商户订单号
     */
    private String outOrderNo;

    /**
     * 交易状态
     * S：支付成功
     */
    private String status;

    /**
     * 状态描述
     */
    private String description;

    /**
     * 平台支付完成时间
     * 用户完成支付时间，格式：yyyyMMddHHmmss
     */
    private String successTime;

    /**
     * 付款方的ID
     */
    private String payerId;

    /**
     * 实际支付的金额，以元为单位
     */
    private String totalAmount;

    /**
     * 支付成功标识
     *
     * @return 支付成功标识
     */
    public boolean success() {
        return STATUS_SUCCESS.equals(this.status);
    }

    /**
     * 支付失败标识
     *
     * @return 支付失败标识
     */
    public boolean failure() {
        return !success();
    }

}
