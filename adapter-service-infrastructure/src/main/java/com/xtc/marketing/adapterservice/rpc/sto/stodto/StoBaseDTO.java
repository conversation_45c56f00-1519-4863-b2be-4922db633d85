package com.xtc.marketing.adapterservice.rpc.sto.stodto;

import com.google.gson.JsonElement;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class StoBaseDTO {

    /**
     * 是否成功
     */
    private Boolean success;
    /**
     * 错误编码
     */
    private String errorCode;
    /**
     * 错误信息
     */
    private String errorMsg;
    /**
     * 查询结果
     */
    private JsonElement data;

    /**
     * 请求失败
     *
     * @return 执行结果
     */
    public boolean isFailure() {
        return success == null || !success;
    }

}
