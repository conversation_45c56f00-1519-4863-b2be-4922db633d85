package com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.enums;

import com.google.common.collect.ImmutableMap;
import lombok.Getter;

import java.util.Map;

/**
 * 微信视频号平台快递公司
 */
@Getter
public enum WechatChannelsShopLogisticsCompany {
    /**
     * 顺丰
     */
    SF("SF",
            ImmutableMap.of("WECHAT_CHANNELS_SHOP_XTC", "6632212328", "WECHAT_CHANNELS_SHOP_BBK", "6632077193",
                    "WECHAT_CHANNELS_SHOP_SZQJ_XTC", "6634282917")),
    /**
     * 圆通
     */
    YTO("YTO",
            ImmutableMap.of("WECHAT_CHANNELS_SHOP_XTC", "6631981286", "WECHAT_CHANNELS_SHOP_BBK", "6632132933", "WECHAT_CHANNELS_SHOP_SZQJ_XTC", "6634335250")),
    /**
     * EMS
     */
    EMS("EMS",
            ImmutableMap.of("WECHAT_CHANNELS_SHOP_XTC", "**********", "WECHAT_CHANNELS_SHOP_BBK", "**********",
                    "WECHAT_CHANNELS_SHOP_SZQJ_XTC", "**********")),
    /**
     * 京东
     */
    JD("JD",
            ImmutableMap.of("WECHAT_CHANNELS_SHOP_XTC", "**********", "WECHAT_CHANNELS_SHOP_BBK", "**********"));

    /**
     * 快递公司id（发货接口使用）
     */
    private final String deliveryId;
    /**
     * 电子面单账号id
     */
    private final Map<String, String> accountId;

    WechatChannelsShopLogisticsCompany(String deliveryId, Map<String, String> accountId) {
        this.deliveryId = deliveryId;
        this.accountId = accountId;
    }
}
