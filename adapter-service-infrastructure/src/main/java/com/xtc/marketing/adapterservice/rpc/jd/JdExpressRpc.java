package com.xtc.marketing.adapterservice.rpc.jd;

import com.lop.open.api.sdk.DefaultDomainApiClient;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCheckPreCreateOrderV1.CommonCheckPreCreateOrderResponse;
import com.lop.open.api.sdk.domain.ECAP.CommonCreateOrderApi.commonCreateOrderV1.CommonCreateOrderResponse;
import com.lop.open.api.sdk.domain.ECAP.CommonModifyCancelOrderApi.commonCancelOrderV1.CommonModifyCancelOrderResponse;
import com.lop.open.api.sdk.domain.ECAP.CommonQueryOrderApi.commonGetOrderStatusV1.CommonOrderStatusResponse;
import com.lop.open.api.sdk.domain.ECAP.CommonQueryOrderApi.commonGetOrderTraceV1.CommonOrderTraceDetail;
import com.lop.open.api.sdk.domain.ECAP.CommonQueryOrderApi.commonGetOrderTraceV1.CommonOrderTraceRequest;
import com.lop.open.api.sdk.domain.ECAP.CommonQueryOrderApi.commonGetOrderTraceV1.CommonOrderTraceResponse;
import com.lop.open.api.sdk.plugin.LopPlugin;
import com.lop.open.api.sdk.plugin.factory.OAuth2PluginFactory;
import com.lop.open.api.sdk.request.DomainAbstractRequest;
import com.lop.open.api.sdk.request.ECAP.*;
import com.lop.open.api.sdk.response.AbstractResponse;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.rpc.jd.jdexpressdto.JdExpressResponseDTO;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.rmi.RemoteException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 京东快递RPC
 * <p><a href="https://open.jdl.com/#/devSupport/53209">通过SDK调用API</a></p>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class JdExpressRpc {

    /**
     * 设置缓存减少对象生成
     */
    private static final Map<String, DefaultDomainApiClient> CLIENT_CACHE = new ConcurrentHashMap<>();

    /**
     * 下单
     * <p><a href="https://open.jdl.com/#/open-business-document/api-doc/267/842">下单前置校验</a></p>
     * <p><a href="https://open.jdl.com/#/open-business-document/api-doc/267/841">下单接口</a></p>
     *
     * @param account    账号
     * @param request    下单请求
     * @param preRequest 预下单请求
     * @return 响应结果
     */
    public CommonCreateOrderResponse createOrder(LogisticsAccountDO account,
                                                 EcapV1OrdersCreateLopRequest request,
                                                 EcapV1OrdersPrecheckLopRequest preRequest) {
        // 下单前置校验
        CommonCheckPreCreateOrderResponse preCreateOrderResponse = this.call(account, preRequest, CommonCheckPreCreateOrderResponse.class);
        if ("超区".equals(preCreateOrderResponse.getShipmentInfo().getStartStationName())
                || "超区".equals(preCreateOrderResponse.getShipmentInfo().getEndStationName())) {
            throw SysException.of(SysErrorCode.S_RPC_ERROR, GsonUtil.objectToJson(preCreateOrderResponse));
        }
        // 下单
        return this.call(account, request, CommonCreateOrderResponse.class);
    }

    /**
     * 取消下单
     * <p><a href="https://open.jdl.com/#/open-business-document/api-doc/267/846">取消下单</a></p>
     *
     * @param account 账号
     * @param request 参数
     * @return 执行结果
     */
    public boolean cancelOrder(LogisticsAccountDO account, EcapV1OrdersCancelLopRequest request) {
        CommonModifyCancelOrderResponse response = this.call(account, request, CommonModifyCancelOrderResponse.class);
        return Optional.ofNullable(response)
                .map(CommonModifyCancelOrderResponse::getResultType)
                //  2：取消失败，3：拦截失败
                .map(resultType -> resultType != 2 && resultType != 3)
                .orElse(false);
    }

    /**
     * 查询订单详情
     * <p><a href="https://open.jdl.com/#/open-business-document/api-doc/267/844">订单状态查询</a></p>
     *
     * @param account 账号
     * @param request 参数
     * @return 订单详情
     */
    public CommonOrderStatusResponse getOrder(LogisticsAccountDO account, EcapV1OrdersStatusGetLopRequest request) {
        return this.call(account, request, CommonOrderStatusResponse.class);
    }

    /**
     * 查询路由
     * <p><a href="https://open.jdl.com/#/open-business-document/api-doc/267/1039">查询运单全程跟踪</a></p>
     * <p><a href="https://open.jdl.com/#/open-business-document/access-guide/267/54128">环节编码</a></p>
     *
     * @param account 账号
     * @param request 参数
     * @return 路由列表
     */
    public List<CommonOrderTraceDetail> routes(LogisticsAccountDO account, EcapV1OrdersTraceQueryLopRequest request) {
        CommonOrderTraceResponse response = this.call(account, request, CommonOrderTraceResponse.class);
        return Optional.ofNullable(response)
                .map(CommonOrderTraceResponse::getTraceDetails)
                .orElse(Collections.emptyList());
    }

    /**
     * 执行远程调用
     *
     * @param account       账号
     * @param request       参数
     * @param responseClass 业务数据类型
     * @param <T>           业务数据类型
     * @param <R>           响应类型
     * @return 业务数据
     */
    public <T, R extends AbstractResponse> T call(LogisticsAccountDO account,
                                                  DomainAbstractRequest<R> request,
                                                  Class<T> responseClass) {
        // 配置请求参数
        LopPlugin lopPlugin = OAuth2PluginFactory.produceLopPlugin(account.getClientCode(),
                account.getClientSecret(), account.getAppAccessToken());
        request.addLopPlugin(lopPlugin);

        String responseStr = "";
        try {
            log.info("京东快递参数: {}", GsonUtil.objectToJson(request.getBodyObject()));
            R response = this.getClient(account).execute(request);

            responseStr = GsonUtil.objectToJson(response);
            log.info("京东快递返回值: {}", responseStr);

            JdExpressResponseDTO<T> responseDTO = GsonUtil.jsonToBean(responseStr, JdExpressResponseDTO.class, responseClass);
            if (responseDTO.failure()) {
                throw remoteException();
            }
            return responseDTO.getBizData();
        } catch (Exception e) {
            String msg = String.format("京东快递调用异常 response: %s", responseStr);
            throw SysException.of(SysErrorCode.S_RPC_ERROR, responseStr, msg, e);
        }
    }

    /**
     * 获取请求客户端
     * <p>设置缓存减少对象生成</p>
     *
     * @param account 账号
     * @return 客户端
     */
    private DefaultDomainApiClient getClient(LogisticsAccountDO account) {
        String key = account.getClientCode();
        CLIENT_CACHE.computeIfAbsent(key, k -> new DefaultDomainApiClient(account.getApiUrl(), 500, 3000));
        return CLIENT_CACHE.get(key);
    }

    /**
     * 远程接口异常
     *
     * @return 异常
     */
    private RemoteException remoteException() {
        return new RemoteException("RPC返回异常状态码");
    }

    public static void main(String[] args) {
        JdExpressRpc rpc = new JdExpressRpc();
        LogisticsAccountDO account = LogisticsAccountDO.builder()
                .apiUrl("https://uat-oauth.jdl.com")
                .clientCode("xxxxxx")
                .clientSecret("xxxxxx")
                .appAccessToken("xxxxxx")
                .build();

        CommonOrderTraceRequest query = new CommonOrderTraceRequest();
        query.setWaybillCode("JDV015832875166");

        EcapV1OrdersTraceQueryLopRequest request = new EcapV1OrdersTraceQueryLopRequest();
        request.setCommonOrderTraceRequest(query);

        rpc.routes(account, request);
    }

}
