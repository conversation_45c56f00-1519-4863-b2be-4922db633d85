<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xtc.marketing.adapterservice</groupId>
        <artifactId>adapter-service-parent</artifactId>
        <version>1.0.0</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>adapter-service-infrastructure</artifactId>
    <packaging>jar</packaging>
    <name>${project.artifactId}</name>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <!--Project modules-->
        <dependency>
            <groupId>com.xtc.marketing.adapterservice</groupId>
            <artifactId>adapter-service-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xtc.marketing.adapterservice</groupId>
            <artifactId>adapter-service-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xtc.marketing.adapterservice</groupId>
            <artifactId>adapter-service-common</artifactId>
        </dependency>
        <!--Project modules End-->

        <!--Spring Boot-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-data-27</artifactId>
        </dependency>
        <!--Spring Boot End-->

        <!--order-service modules-->
        <dependency>
            <groupId>com.xtc.marketing.orderservice</groupId>
            <artifactId>order-service-client</artifactId>
        </dependency>
        <!--order-service modules End-->

        <!--dividend-center modules-->
        <dependency>
            <groupId>com.xtc.dividend</groupId>
            <artifactId>dividend-center-client</artifactId>
        </dependency>
        <!--dividend-center modules End-->

        <!--Open sdk-->
        <dependency>
            <groupId>com.taobao</groupId>
            <artifactId>taobao-sdk-java-auto</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tiktok</groupId>
            <artifactId>doudian-sdk-java</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sf</groupId>
            <artifactId>sf-csim-express-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>open-api-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jdcloud</groupId>
            <artifactId>jcq-java-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>lop-opensdk-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>jdcloudprint</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>EcapSDK</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>IntegratedSupplyChain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pdd</groupId>
            <artifactId>pop-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kuaishou</groupId>
            <artifactId>open_kwaishop_sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xiaohongshu</groupId>
            <artifactId>fe-platform-sdk</artifactId>
        </dependency>
        <!--Open sdk End-->

        <!--Misc-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.javen205</groupId>
            <artifactId>IJPay-WxPay</artifactId>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
        </dependency>
        <dependency>
            <groupId>org.dom4j</groupId>
            <artifactId>dom4j</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <!--Misc End-->
    </dependencies>
</project>
