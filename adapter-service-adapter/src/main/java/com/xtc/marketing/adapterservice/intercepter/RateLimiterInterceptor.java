package com.xtc.marketing.adapterservice.intercepter;

import com.xtc.marketing.adapterservice.annotation.RateLimiter;
import com.xtc.marketing.adapterservice.cache.RedissonUtil;
import com.xtc.marketing.adapterservice.config.RateLimiterConfig;
import com.xtc.marketing.adapterservice.config.RequestBodyReaderWrapper;
import com.xtc.marketing.adapterservice.constant.SystemConstant;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.util.AnnotationCatcher;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.HttpUtil;
import com.xtc.marketing.adapterservice.util.Md5Encoder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.time.Duration;
import java.util.Map;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
@Component
public class RateLimiterInterceptor implements HandlerInterceptor {

    private final RedissonUtil redissonUtil;
    private final RateLimiterConfig rateLimiterConfigs;

    /**
     * 企业微信告警接口
     */
    private static final String WECHAT_WORK_WARN_API = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=66b40bd7-6c5c-4905-b30d-1106b936accc";
    /**
     * 企业微信告警接口参数
     */
    private static final String WECHAT_WORK_WARN_API_PARAM = "{\"msgtype\":\"text\",\"text\":{\"content\":\"%s\",\"mentioned_list\": [\"20261647\",\"20246072\"]}}";

    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        // 过滤资源请求
        if (handler instanceof ResourceHttpRequestHandler) {
            return true;
        }

        // 获取流量限流器注解
        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        RateLimiter rateLimiter = AnnotationCatcher.catchAnnotation(method, RateLimiter.class);
        if (rateLimiter == null || StringUtils.isBlank(rateLimiter.limitParam())) {
            return true;
        }

        // 获取注解的限流参数
        Map<String, Object> allParams = new RequestBodyReaderWrapper(request).getAllParams();
        String limitId = Optional.ofNullable(allParams.get(rateLimiter.limitParam())).map(Object::toString).orElse(null);
        if (StringUtils.isBlank(limitId)) {
            throw SysException.of(SysErrorCode.S_PARAM_ERROR, "缺少必要参数 " + rateLimiter.limitParam());
        }

        // 限制访问频率
        this.rateLimit(limitId, rateLimiter, method);

        // 限制访问次数
        this.limitCount(limitId, rateLimiter.limitParam());

        return true;
    }

    /**
     * 限制访问频率
     *
     * @param limitId     限制id
     * @param rateLimiter 限流器注解
     * @param method      调用方法
     */
    private void rateLimit(String limitId, RateLimiter rateLimiter, Method method) {
        if (rateLimiter.rate() < 1) {
            return;
        }
        String bizName = SystemConstant.SYSTEM_NAME + ":rate-limiter:"
                + method.getDeclaringClass().getName() + ":" + method.getName();
        int rateInterval = rateLimiter.rateInterval() > 0 ? rateLimiter.rateInterval() : 1;
        boolean tryAcquire = redissonUtil.rateLimiter(bizName, limitId, rateLimiter.rate(), rateInterval);
        if (!tryAcquire) {
            throw SysException.of(SysErrorCode.S_RATELIMITER_RateLimiting);
        }
    }

    /**
     * 限制访问次数
     *
     * @param limitId    限制id
     * @param limitParam 限制参数
     */
    private void limitCount(String limitId, String limitParam) {
        RateLimiterConfig.LimitConfig limitConfig = rateLimiterConfigs.getLimitConfig(limitId);
        if (limitConfig == null) {
            return;
        }
        // 固定窗口限流：限制当天访问次数，过期时间24小时
        String fixedWindowKey = SystemConstant.SYSTEM_NAME + ":fixed-window-limiter:" + DateUtil.nowDate() + ":" + limitId;
        long limitCount = redissonUtil.incr(fixedWindowKey, Duration.ofHours(24));
        // 限流计数
        if (limitConfig.getLimitCount() > 0 && limitCount > limitConfig.getLimitCount()) {
            this.sendWechatWorkWarn("%s 服务限流参数 %s[%s] 计数超过限制值 %s",
                    SystemConstant.SYSTEM_NAME, limitParam, limitId, limitConfig.getLimitCount());
            throw SysException.of(SysErrorCode.S_RATELIMITER_AccessDenied);
        }
        // 告警计数
        if (limitConfig.getWarnCount() > 0 && limitCount > limitConfig.getWarnCount()) {
            this.sendWechatWorkWarn("%s 服务限流参数 %s[%s] 计数超过告警值 %s",
                    SystemConstant.SYSTEM_NAME, limitParam, limitId, limitConfig.getWarnCount());
        }
    }

    /**
     * 发送企业微信告警，限制 1 小时发送一条
     *
     * @param content 内容，支持 String.format(content, args)
     * @param args    内容模板参数
     */
    private void sendWechatWorkWarn(String content, Object... args) {
        if (StringUtils.isBlank(content)) {
            return;
        }
        try {
            // 内容格式化
            String contentFormat = args != null && args.length > 0 ? String.format(content, args) : content;
            // 重复发送限制
            Duration expire = Duration.ofHours(1);
            String contentKey = SystemConstant.SYSTEM_NAME + ":wechat-work-warn:" + Md5Encoder.encode(contentFormat);
            long incr = redissonUtil.incr(contentKey, expire);
            if (incr > 1) {
                log.warn("发送企业微信告警失败 - 已发送过通知 {} 秒内只能发送一次", expire.getSeconds());
                return;
            }
            // 发送企业微信告警
            HttpUtil.post(WECHAT_WORK_WARN_API, String.format(WECHAT_WORK_WARN_API_PARAM, contentFormat));
        } catch (Exception e) {
            log.warn("发送企业微信告警失败 - {}", e.getMessage(), e);
        }
    }

}
