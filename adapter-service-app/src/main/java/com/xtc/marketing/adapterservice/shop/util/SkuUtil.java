package com.xtc.marketing.adapterservice.shop.util;

import com.google.common.base.Splitter;
import com.xtc.marketing.adapterservice.util.BeanCopier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * sku工具类
 */
@Slf4j
public class SkuUtil {

    private SkuUtil() {
    }

    /**
     * 根据 skuIds 克隆多个订单明细
     *
     * @param items      订单明细集合
     * @param getSkuFunc 获取skuId的方法
     * @param setSkuFunc 设置skuId的方法
     * @param <T>        订单明细类型
     * @return 克隆后的订单明细集合
     */
    public static <T> List<T> splitSkuIdsAndCloneItem(List<T> items, Function<T, String> getSkuFunc, BiConsumer<T, String> setSkuFunc) {
        if (items == null) {
            return Collections.emptyList();
        }
        return items.stream()
                .map(item -> splitSkuIdsAndCloneItem(item, getSkuFunc, setSkuFunc))
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    /**
     * 根据 skuIds 克隆多个订单明细
     *
     * @param item       订单明细
     * @param getSkuFunc 获取skuId的方法
     * @param setSkuFunc 设置skuId的方法
     * @param <T>        订单明细类型
     * @return 克隆后的订单明细集合
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> splitSkuIdsAndCloneItem(T item, Function<T, String> getSkuFunc, BiConsumer<T, String> setSkuFunc) {
        String skuIds = getSkuFunc.apply(item);
        return splitSkuIds(skuIds).stream()
                .map(skuId -> {
                    try {
                        T newInstance = (T) BeanUtils.instantiateClass(item.getClass());
                        return BeanCopier.copy(item, () -> newInstance, (s, t) -> setSkuFunc.accept(t, skuId));
                    } catch (Exception e) {
                        log.warn("splitSkuIds error. skuId: {}, item: {}", skuId, item, e);
                        return null;
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * 拆分 skuIds 字符串
     *
     * @param skuIds skuIds字符串
     * @return skuId集合
     */
    public static List<String> splitSkuIds(String skuIds) {
        if (skuIds == null) {
            return Collections.emptyList();
        }
        return Splitter.on(",").omitEmptyStrings().trimResults().splitToList(skuIds);
    }

}
