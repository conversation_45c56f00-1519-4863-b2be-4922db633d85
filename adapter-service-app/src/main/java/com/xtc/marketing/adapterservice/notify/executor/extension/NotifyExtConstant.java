package com.xtc.marketing.adapterservice.notify.executor.extension;

/**
 * 通知扩展点常量
 */
public class NotifyExtConstant {

    private NotifyExtConstant() {
    }

    /* 业务 */
    /**
     * 业务：顺丰
     */
    public static final String BIZ_ID_SF = "SF";
    /**
     * 业务：微信视频号小店
     */
    public static final String BIZ_ID_WECHAT_CHANNELS_SHOP = "WECHAT_CHANNELS_SHOP";
    /**
     * 业务：天猫
     */
    public static final String BIZ_ID_TMALL = "TMALL";
    /**
     * 业务：抖音
     */
    public static final String BIZ_ID_TIKTOK = "TIKTOK";
    /**
     * 业务：快手
     */
    public static final String BIZ_ID_KUAISHOU = "KUAISHOU";
    /**
     * 业务：小红书
     */
    public static final String BIZ_ID_XIAOHONGSHU = "XIAOHONGSHU";

    /* 用例 */
    /**
     * 用例：仓库
     */
    public static final String USE_CASE_WAREHOUSE = "WAREHOUSE";
    /**
     * 用例：物流
     */
    public static final String USE_CASE_LOGISTICS = "LOGISTICS";
    /**
     * 用例：店铺
     */
    public static final String USE_CASE_SHOP = "SHOP";

    /* 场景 */
    /**
     * 场景：入库
     */
    public static final String SCENARIO_INBOUND = "inbound";
    /**
     * 场景：出库
     */
    public static final String SCENARIO_OUTBOUND = "outbound";
    /**
     * 场景：国补通知
     */
    public static final String SCENARIO_NATIONAL_SUBSIDY = "national_subsidy";
    /**
     * 场景：消息通知
     */
    public static final String SCENARIO_NOTIFY = "notify";
    /**
     * 场景：退款单变更
     */
    public static final String SCENARIO_REFUND = "refund";
    /**
     * 场景：地址变更
     */
    public static final String SCENARIO_MODIFY_ADDRESS = "modify_address";

}
