package com.xtc.marketing.adapterservice.shop.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import com.kuaishou.merchant.open.api.domain.express.GetEbillOrderDTO;
import com.kuaishou.merchant.open.api.domain.order.*;
import com.kuaishou.merchant.open.api.domain.refund.MerchantRefundDetailDataView;
import com.kuaishou.merchant.open.api.domain.refund.MerchantRefundListDataView;
import com.kuaishou.merchant.open.api.request.express.OpenExpressEbillGetRequest;
import com.kuaishou.merchant.open.api.request.order.OpenOrderCursorListRequest;
import com.kuaishou.merchant.open.api.request.order.OpenSellerOrderGoodsDeliverRequest;
import com.kuaishou.merchant.open.api.request.refund.OpenSellerOrderRefundPcursorListRequest;
import com.kuaishou.merchant.open.api.response.oauth.KsAccessTokenResponse;
import com.xtc.marketing.adapterservice.exception.BizErrorCode;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.kuaishou.KuaishouRpc;
import com.xtc.marketing.adapterservice.rpc.kuaishou.enums.KuaishouLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.converter.KuaishouShopConverter;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.adapterservice.shop.util.SkuUtil;
import com.xtc.marketing.adapterservice.util.BeanCopier;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = ShopExtConstant.USE_CASE, scenario = ShopExtConstant.SCENARIO_KUAISHOU)
public class KuaishouShopExt implements ShopExtPt {

    private final KuaishouRpc kuaishouRpc;
    private final KuaishouShopConverter kuaishouShopConverter;

    @Override
    public ShopDO getShopToken(ShopDO shop) {
        // 初始化
        ShopDO newToken = BeanCopier.copy(shop, ShopDO::new);
        // 刷新 AccessToken
        KsAccessTokenResponse ksAccessTokenResponse = kuaishouRpc.refreshAccessToken(shop);
        if (ksAccessTokenResponse == null) {
            return newToken;
        }
        // 设置新的 token 数据
        newToken.setAppAccessToken(ksAccessTokenResponse.getAccessToken());
        newToken.setAppRefreshToken(ksAccessTokenResponse.getRefreshToken());
        newToken.setAppExpireTime(LocalDateTime.now().plusSeconds(ksAccessTokenResponse.getExpiresIn()));
        return newToken;
    }

    @Override
    public PageResponse<OrderDTO> pageOrders(ShopDO shop, OrderPageQry qry) {
        // 初始化响应结果
        PageResponse<OrderDTO> response = PageResponse.of(Collections.emptyList(), -1, qry.getPageSize(), qry.getPageIndex());
        // 默认设置下一页没有数据
        response.setHasNext(false);
        response.setNextKey(KuaishouRpc.NO_MORE);
        // 如果请求参数标识下一页没有数据，则直接返回响应结果，用于终止循环分页查询
        if (KuaishouRpc.NO_MORE.equals(qry.getNextKey())) {
            return response;
        }
        // 初始化请求参数
        OpenOrderCursorListRequest request = new OpenOrderCursorListRequest();
        request.setBeginTime(DateUtil.toEpochMilli(qry.getUpdateTimeStart()));
        request.setEndTime(DateUtil.toEpochMilli(qry.getUpdateTimeEnd()));
        request.setPageSize(qry.getPageSize());
        if (BooleanUtils.isTrue(qry.getUseHasNext())) {
            // 获取第一页数据要传空字符串
            request.setCursor(StringUtils.defaultIfBlank(qry.getNextKey(), ""));
        }
        // 发起请求
        OrderListData orderListData = kuaishouRpc.pageOrders(shop, request);
        if (orderListData == null || ArrayUtils.isEmpty(orderListData.getOrderList())) {
            return response;
        }
        // 转换统一的订单数据，并设置到响应结果中
        List<OrderDTO> orderList = Stream.of(orderListData.getOrderList())
                .map(platformOrder -> {
                    OrderDTO orderDTO = kuaishouShopConverter.toAdapterOrderDTO(platformOrder);
                    this.splitSkuIdsAndCloneItem(platformOrder.getOrderItemInfo(), orderDTO, platformOrder.getOrderCpsInfo());
                    return orderDTO;
                })
                .collect(Collectors.toList());
        response.setData(orderList);
        if (BooleanUtils.isTrue(qry.getUseHasNext())) {
            response.setHasNext(ObjectUtils.notEqual(KuaishouRpc.NO_MORE, orderListData.getCursor()));
            response.setNextKey(orderListData.getCursor());
        }
        return response;
    }

    @Override
    public OrderDTO getOrder(ShopDO shop, OrderGetQry qry) {
        OrderDetail platformOrder = kuaishouRpc.getOrder(shop, qry.getOrderNo());
        if (platformOrder == null) {
            return null;
        }
        OrderDTO orderDTO = kuaishouShopConverter.toAdapterOrderDTO(platformOrder);
        orderDTO.setOriginOrderData(GsonUtil.objectToJson(platformOrder));
        this.splitSkuIdsAndCloneItem(platformOrder.getOrderItemInfo(), orderDTO, platformOrder.getOrderCpsInfo());
        return orderDTO;
    }

    @Override
    public boolean orderShipping(ShopDO shop, OrderShippingCmd cmd) {
        OrderDetail platformOrder = kuaishouRpc.getOrder(shop, cmd.getOrderNo());
        boolean hasShipping = this.checkOrderHasShipping(platformOrder);
        if (hasShipping) {
            return true;
        }
        OpenSellerOrderGoodsDeliverRequest request = new OpenSellerOrderGoodsDeliverRequest();
        // 填充发货条码
        if (CollectionUtils.isNotEmpty(cmd.getBarcodes())) {
            this.fillShippingBarcode(platformOrder, request, cmd);
        }
        // 获取快递公司编码
        Integer expressCode = KuaishouLogisticsCompany.valueOf(cmd.getLogisticsCompany().name()).getCode();
        request.setOrderId(Long.parseLong(cmd.getOrderNo()));
        request.setExpressCode(expressCode);
        request.setExpressNo(cmd.getWaybillNo());
        return kuaishouRpc.orderShipping(shop, request);
    }

    @Override
    public boolean orderShippingCancel(ShopDO shop, OrderShippingCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderDummyShipping(ShopDO shop, OrderDummyShippingCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderRemark(ShopDO shop, OrderRemarkCmd cmd) {
        return kuaishouRpc.orderRemark(shop, cmd.getOrderNo(), cmd.getRemark());
    }

    @Override
    public OrderDecryptDTO orderDecrypt(ShopDO shop, OrderDecryptCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<InvoiceApplyDTO> pageInvoiceApply(ShopDO shop, InvoiceApplyPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public InvoiceApplyDTO getInvoiceApply(ShopDO shop, InvoiceApplyGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceFile(ShopDO shop, OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceBase64(ShopDO shop, InvoiceUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public InvoiceAmountDTO getInvoiceAmount(ShopDO shop, String orderNo) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<CommentDTO> pageComments(ShopDO shop, CommentPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<RefundDTO> pageRefunds(ShopDO shop, RefundPageQry qry) {
        // 初始化响应结果
        PageResponse<RefundDTO> response = PageResponse.of(Collections.emptyList(), -1, qry.getPageSize(), qry.getPageIndex());
        // 默认设置下一页没有数据
        response.setHasNext(false);
        response.setNextKey(KuaishouRpc.NO_MORE);
        // 如果请求参数标识下一页没有数据，则直接返回响应结果，用于终止循环分页查询
        if (KuaishouRpc.NO_MORE.equals(qry.getNextKey())) {
            return response;
        }
        // 初始化请求参数
        OpenSellerOrderRefundPcursorListRequest request = new OpenSellerOrderRefundPcursorListRequest();
        request.setBeginTime(DateUtil.toEpochMilli(qry.getUpdateTimeStart()));
        request.setEndTime(DateUtil.toEpochMilli(qry.getUpdateTimeEnd()));
        request.setPageSize(qry.getPageSize());
        if (BooleanUtils.isTrue(qry.getUseHasNext())) {
            // 获取第一页数据要传空字符串
            request.setPcursor(StringUtils.defaultIfBlank(qry.getNextKey(), ""));
        }
        // 发起请求
        MerchantRefundListDataView refundListData = kuaishouRpc.pageRefunds(shop, request);
        if (refundListData == null || CollectionUtils.isEmpty(refundListData.getRefundOrderInfoList())) {
            return response;
        }
        // 转换统一的退款单数据，并设置到响应结果中
        List<RefundDTO> refundDTOList = refundListData.getRefundOrderInfoList().stream()
                .map(kuaishouShopConverter::toAdapterRefundDTO)
                .collect(Collectors.toList());
        response.setData(refundDTOList);
        response.setTotalCount(refundListData.getTotalSize().intValue());
        if (BooleanUtils.isTrue(qry.getUseHasNext())) {
            response.setHasNext(ObjectUtils.notEqual(KuaishouRpc.NO_MORE, refundListData.getPcursor()));
            response.setNextKey(refundListData.getPcursor());
        }
        return response;
    }

    @Override
    public RefundDTO getRefund(ShopDO shop, RefundGetQry qry) {
        MerchantRefundDetailDataView refundDetailData = kuaishouRpc.getRefund(shop, qry.getRefundId());
        return Optional.ofNullable(refundDetailData)
                .map(originRefundOrder -> {
                    RefundDTO refundDTO = kuaishouShopConverter.toAdapterRefundDTO(originRefundOrder);
                    refundDTO.setOriginData(GsonUtil.objectToJson(originRefundOrder));
                    return refundDTO;
                })
                .orElse(null);
    }

    @Override
    public ShopLogisticsOrderDTO createLogisticsOrder(ShopDO shop, ShopLogisticsOrderCreateCmd cmd) {
        KuaishouLogisticsCompany logisticsCompany = KuaishouLogisticsCompany.valueOf(cmd.getLogisticsCompany().name());
        List<String> receiverList = GsonUtil.jsonToList(cmd.getReceiverOaid(), String.class);
        cmd.setReceiverName(receiverList.get(0));
        cmd.setReceiverMobile(receiverList.get(1));
        cmd.setReceiverAddress(receiverList.get(2));
        OpenExpressEbillGetRequest request = kuaishouShopConverter.toOpenExpressEbillGetRequest(cmd, shop, logisticsCompany);
        GetEbillOrderDTO order = kuaishouRpc.createLogisticsOrder(shop, request);
        JsonObject orderDetailJson = new JsonObject();
        orderDetailJson.addProperty("signature", order.getSignature());
        orderDetailJson.addProperty("encryptedData", order.getPrintData());
        orderDetailJson.addProperty("key", order.getKey());
        orderDetailJson.addProperty("templateURL", logisticsCompany.getTemplateUrl());
        orderDetailJson.addProperty("ver", order.getVersion());
        return ShopLogisticsOrderDTO.builder()
                .wayBillNo(order.getWaybillCode())
                .orderDetail(orderDetailJson.toString())
                .build();
    }

    @Override
    public boolean cancelLogisticsOrder(ShopDO shop, ShopLogisticsOrderCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void refundGoodsToWarehouse(ShopDO shop, RefundGoodsToWarehouseCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void barcodeUpload(ShopDO shop, BarcodeUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    /**
     * 根据订单明细里的 skuIds 拆分明细，并且转换成统一订单明细，最后设置到统一订单数据中
     *
     * @param orderItemInfo 订单明细
     * @param orderDTO      统一订单数据
     * @param orderCpsInfo  达人信息
     */
    private void splitSkuIdsAndCloneItem(OrderItemInfo orderItemInfo, OrderDTO orderDTO, OrderCpsInfo orderCpsInfo) {
        OrderItemDTO items = kuaishouShopConverter.toAdapterOrderItemDTO(orderItemInfo, orderDTO.getOrderNo(), orderCpsInfo);
        List<OrderItemDTO> splitSkuItems = SkuUtil.splitSkuIdsAndCloneItem(items, OrderItemDTO::getSkuErpCode, OrderItemDTO::setSkuErpCode);
        // 设置达人id
        boolean xtcShop = "f19179fd3586f5bf14bf572487180a9a".equals(orderDTO.getSellerId());
        String authorId = xtcShop ? "1590545112" : "2671499319";
        String authorName = xtcShop ? "小天才旗舰店" : "步步高旗舰店";
        splitSkuItems.stream()
                .filter(item -> StringUtils.isBlank(item.getAuthorId()))
                .forEach(item -> {
                    item.setAuthorId(authorId);
                    item.setAuthorName(authorName);
                });

        orderDTO.setItems(splitSkuItems);
    }

    /**
     * 检查订单已发货
     *
     * @param orderDetail 订单
     * @return 执行结果
     */
    private boolean checkOrderHasShipping(OrderDetail orderDetail) {
        // 快手平台订单状态，0:未知状态，10：待付款，30：已付款/待发货 40：已发货，50：已签收，70：订单成功，80：订单失败/订单关闭
        int orderStatus = orderDetail.getOrderBaseInfo().getStatus();
        if (orderStatus == 40 || orderStatus == 50 || orderStatus == 70) {
            log.warn("订单已发货，无需重复发货 {} {}", orderDetail.getOrderBaseInfo().getOid(), orderStatus);
            return true;
        }
        if (orderStatus != 30) {
            // 例：平台的订单状态不符合推送发货状态的条件 277901543019 orderState: 10
            String msg = String.format("%s %s orderState: %s",
                    BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrDesc(),
                    orderDetail.getOrderBaseInfo().getOid(), orderStatus);
            throw new BizException(BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrCode(), msg);
        }
        return false;
    }

    /**
     * 填充条码
     *
     * @param platformOrder 平台订单
     * @param request       平台参数
     * @param cmd           条码
     */
    private void fillShippingBarcode(OrderDetail platformOrder, OpenSellerOrderGoodsDeliverRequest request, OrderShippingCmd cmd) {
        OrderDeliveryInfo orderDeliveryInfo = platformOrder.getOrderDeliveryInfo();
        if (orderDeliveryInfo == null
                || orderDeliveryInfo.getSerialNumberInfo() == null
                || CollectionUtils.isEmpty(orderDeliveryInfo.getSerialNumberInfo().getSerialType())) {
            return;
        }
        // serialType（商品码类型）值包含[1]时，发货必传SN码 包含[2]，发货必传IMEI码
        List<Integer> serialType = orderDeliveryInfo.getSerialNumberInfo().getSerialType();
        List<String> imeiList = Lists.newArrayListWithCapacity(cmd.getBarcodes().size());
        List<String> barcodes = Lists.newArrayListWithCapacity(cmd.getBarcodes().size());
        cmd.getBarcodes().forEach(barcode -> {
            if (serialType.contains(1)) {
                barcodes.add(barcode.getBarcode());
            }
            if (serialType.contains(2)) {
                imeiList.add(barcode.getImei());
            }
        });
        if (CollectionUtils.isNotEmpty(imeiList)) {
            request.setImeiList(imeiList);
        }
        if (CollectionUtils.isNotEmpty(barcodes)) {
            request.setSerialNumberList(barcodes);
        }
    }

}
