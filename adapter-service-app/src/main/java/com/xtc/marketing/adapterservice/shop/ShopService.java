package com.xtc.marketing.adapterservice.shop;

import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import org.springframework.web.multipart.MultipartFile;

public interface ShopService {

    /**
     * 查询店铺 AccessToken
     *
     * @param shopCode 店铺代码
     * @return 店铺 AccessToken
     */
    ShopAccessTokenDTO getAccessToken(String shopCode);

    /**
     * 刷新店铺 token 数据
     */
    void refreshToken();

    /**
     * 分页查询订单列表
     *
     * @param qry 参数
     * @return 订单分页列表
     */
    PageResponse<OrderDTO> pageOrders(OrderPageQry qry);

    /**
     * 查询订单
     *
     * @param qry 参数
     * @return 订单
     */
    OrderDTO getOrder(OrderGetQry qry);

    /**
     * 订单发货
     *
     * @param cmd 参数
     * @return 执行结果
     */
    boolean orderShipping(OrderShippingCmd cmd);

    /**
     * 订单取消发货
     *
     * @param cmd 参数
     * @return 执行结果
     */
    boolean orderShippingCancel(OrderShippingCancelCmd cmd);

    /**
     * 订单无需物流发货
     *
     * @param cmd 参数
     * @return 执行结果
     */
    boolean orderDummyShipping(OrderDummyShippingCmd cmd);

    /**
     * 订单备注
     *
     * @param cmd 参数
     * @return 执行结果
     */
    boolean orderRemark(OrderRemarkCmd cmd);

    /**
     * 订单解密
     *
     * @param cmd 参数
     * @return 解密数据
     */
    OrderDecryptDTO orderDecrypt(OrderDecryptCmd cmd);

    /**
     * 分页查询发票申请列表
     *
     * @param qry 参数
     * @return 发票申请分页列表
     */
    PageResponse<InvoiceApplyDTO> pageInvoiceApply(InvoiceApplyPageQry qry);

    /**
     * 查询发票申请
     *
     * @param qry 参数
     * @return 发票申请
     */
    InvoiceApplyDTO getInvoiceApply(InvoiceApplyGetQry qry);

    /**
     * 上传发票 - 文件流
     *
     * @param cmd         参数
     * @param invoiceFile 发票文件
     * @return 执行结果
     */
    boolean uploadInvoiceFile(OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile);

    /**
     * 上传发票 - base64字符串
     *
     * @param cmd 参数
     * @return 执行结果
     */
    boolean uploadInvoiceBase64(InvoiceUploadCmd cmd);

    /**
     * 查询发票开票金额
     *
     * @param qry 参数
     * @return 发票开票金额
     */
    InvoiceAmountDTO getInvoiceAmount(InvoiceAmountGetQry qry);

    /**
     * 分页查询评价列表
     *
     * @param qry 参数
     * @return 评价分页列表
     */
    PageResponse<CommentDTO> pageComments(CommentPageQry qry);

    /**
     * 分页查询退款单列表
     *
     * @param qry 参数
     * @return 退款单分页列表
     */
    PageResponse<RefundDTO> pageRefunds(RefundPageQry qry);

    /**
     * 查询退款单
     *
     * @param qry 参数
     * @return 退款单
     */
    RefundDTO getRefund(RefundGetQry qry);

    /**
     * 生成电子面单
     *
     * @param cmd 参数
     * @return 电子面单
     */
    ShopLogisticsOrderDTO createLogisticsOrder(ShopLogisticsOrderCreateCmd cmd);

    /**
     * 取消电子面单
     *
     * @param cmd 参数
     */
    boolean cancelLogisticsOrder(ShopLogisticsOrderCancelCmd cmd);

    /**
     * 退货确认入仓
     *
     * @param cmd 参数
     */
    void refundGoodsToWarehouse(RefundGoodsToWarehouseCmd cmd);

    /**
     * 条码上传
     *
     * @param cmd 参数
     */
    void barcodeUpload(BarcodeUploadCmd cmd);

}
