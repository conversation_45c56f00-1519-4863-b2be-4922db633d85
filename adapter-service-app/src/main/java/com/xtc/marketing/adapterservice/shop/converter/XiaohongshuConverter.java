package com.xtc.marketing.adapterservice.shop.converter;

import com.xiaohongshu.fls.opensdk.entity.afterSale.response.GetAfterSaleInfoResponse;
import com.xiaohongshu.fls.opensdk.entity.afterSale.response.ListAfterSaleInfosResponse;
import com.xiaohongshu.fls.opensdk.entity.express.ElectronicBillItem;
import com.xiaohongshu.fls.opensdk.entity.express.request.ElectronicBillOrdersCreateRequest;
import com.xiaohongshu.fls.opensdk.entity.invoice.request.ConfirmInvoiceRequest;
import com.xiaohongshu.fls.opensdk.entity.invoice.response.GetInvoiceListResponse;
import com.xiaohongshu.fls.opensdk.entity.order.Response.GetOrderDetailResponse;
import com.xiaohongshu.fls.opensdk.entity.order.Response.OrderReceiverInfo;
import com.xiaohongshu.fls.opensdk.entity.order.Response.OrderSimpleDetail;
import com.xtc.marketing.adapterservice.rpc.xiaohongshu.enums.XiaohongshuLogisticsCompany;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.InvoiceApplyDTO;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.OrderItemDTO;
import com.xtc.marketing.adapterservice.shop.dto.RefundDTO;
import com.xtc.marketing.adapterservice.shop.dto.command.InvoiceUploadCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsCargoCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsOrderCreateCmd;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTitleTypeEnum;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTypeEnum;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.MoneyUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.time.LocalDateTime;
import java.util.List;

@Mapper(
        componentModel = "spring",
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface XiaohongshuConverter {

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderId")
    @Mapping(target = "orderType", source = "orderType")
    @Mapping(target = "orderState", source = "orderStatus")
    @Mapping(target = "sellerMemo", source = "sellerRemark")
    @Mapping(target = "buyerMemo", source = "customerRemark")
    @Mapping(target = "receiverProvince", source = "receiverProvinceName")
    @Mapping(target = "receiverCity", source = "receiverCityName")
    @Mapping(target = "receiverDistrict", source = "receiverDistrictName")
    // 时间
    @Mapping(target = "updateTime", source = "updateTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "createdTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "paidTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "latestShippingTime", source = "promiseLastDeliveryTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "shippingTime", source = "deliveryTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "completedTime", source = "finishTime", qualifiedByName = "toLocalDateTime")
    OrderDTO toAdapterOrderDTO(OrderSimpleDetail source);

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "source.orderId")
    @Mapping(target = "orderType", source = "source.orderType")
    @Mapping(target = "orderState", source = "source.orderStatus")
    @Mapping(target = "sellerId", source = "source.shopId")
    @Mapping(target = "sellerName", source = "source.shopName")
    @Mapping(target = "sellerMemo", source = "source.sellerRemark")
    @Mapping(target = "buyerId", source = "source.userId")
    @Mapping(target = "buyerName", source = "source.userId")
    @Mapping(target = "buyerMemo", source = "source.customerRemark")
    @Mapping(target = "receiverName", source = "source.openAddressId")
    @Mapping(target = "receiverMobile", source = "source.openAddressId")
    @Mapping(target = "receiverProvince", source = "source.receiverProvinceName")
    @Mapping(target = "receiverCity", source = "source.receiverCityName")
    @Mapping(target = "receiverDistrict", source = "source.receiverDistrictName")
    @Mapping(target = "receiverAddress", source = "source.openAddressId")
    // 金额和时间
    @Mapping(target = "priceTotal", source = "source.merchantActualReceiveAmount")
    @Mapping(target = "payment", source = "source.merchantActualReceiveAmount")
    @Mapping(target = "shippingPayment", source = "source.totalShippingFree")
    @Mapping(target = "discount", expression = "java(source.getMerchantActualReceiveAmount() - source.getTotalPayAmount())")
    @Mapping(target = "updateTime", source = "source.updateTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "orderTime", source = "source.createdTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "payTime", source = "source.paidTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "latestShippingTime", source = "source.promiseLastDeliveryTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "shippingTime", source = "source.deliveryTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "completedTime", source = "source.finishTime", qualifiedByName = "toLocalDateTime")
    // 子对象转换
    @Mapping(target = "items", source = "source.skuList")
    // 按收件人、电话、地址排序，不可改变顺序
    @Mapping(target = "cipherTexts", expression = "java(java.util.Arrays.asList(receiver.getReceiverName(), receiver.getReceiverPhone(), receiver.getReceiverAddress()))")
    OrderDTO toAdapterOrderDTO(GetOrderDetailResponse source, OrderReceiverInfo receiver);

    /**
     * 转换统一订单明细
     *
     * @param source 平台订单明细
     * @return 统一订单明细
     */
    @Mapping(target = "refundState", source = "skuAfterSaleStatus")
    @Mapping(target = "productId", source = "skuId")
    @Mapping(target = "productName", source = "skuName")
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "skuName", source = "skuName")
    @Mapping(target = "skuErpCode", source = "erpcode")
    @Mapping(target = "num", source = "skuQuantity")
    @Mapping(target = "unitPrice", expression = "java((int) source.getSkuDetailList().get(0).getRawPricePerSku())")
    @Mapping(target = "priceTotal", source = "totalPaidAmount")
    @Mapping(target = "discount", expression = "java((int) source.getSkuDetailList().get(0).getRedDiscountPerSku())")
    @Mapping(target = "payment", expression = "java((int) source.getSkuDetailList().get(0).getPaidAmountPerSku())")
    @Mapping(target = "authorId", source = "source.kolId")
    @Mapping(target = "authorName", source = "source.kolName")
    OrderItemDTO toAdapterOrderItemDTO(GetOrderDetailResponse.OrderSkuDTOV3 source);

    /**
     * 转换统一退款数据
     *
     * @param refund 平台退款数据
     * @return 退款数据
     */
    // 基础属性
    @Mapping(target = "serviceNo", source = "returnsId")
    @Mapping(target = "serviceType", source = "returnType")
    @Mapping(target = "serviceState", source = "status")
    @Mapping(target = "serviceStateDesc", source = "desc")
    @Mapping(target = "applyReason", source = "reasonNameZh")
    @Mapping(target = "buyerId", source = "userId")
    @Mapping(target = "buyerName", source = "userId")
    // 金额和时间
    @Mapping(target = "payment", source = "expectedRefundAmountYuan")
    @Mapping(target = "refundTime", source = "applyTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "updateTime", source = "updatedAt", qualifiedByName = "toLocalDateTime")
    RefundDTO toAdapterRefundDTO(ListAfterSaleInfosResponse.OpenAPIAfterSaleBasicInfo refund);

    /**
     * 转换统一退款数据
     *
     * @param refund 平台退款数据
     * @return 退款数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "afterSaleInfo.orderId")
    @Mapping(target = "refundState", source = "afterSaleInfo.refundStatus")
    @Mapping(target = "serviceNo", source = "afterSaleInfo.returnsId")
    @Mapping(target = "serviceType", source = "afterSaleInfo.returnType")
    @Mapping(target = "serviceState", source = "afterSaleInfo.status")
    @Mapping(target = "applyReason", source = "afterSaleInfo.reasonNameZh")
    @Mapping(target = "buyerId", source = "afterSaleInfo.userId")
    @Mapping(target = "buyerName", source = "afterSaleInfo.userId")
    @Mapping(target = "returnWaybillNo", source = "logisticsInfo.afterSale.expressNo")
    @Mapping(target = "returnExpressCompany", source = "logisticsInfo.afterSale.expressCompanyName")
    @Mapping(target = "productId", expression = "java(refund.getAfterSaleInfo().getSkus().get(0).getSkuId())")
    @Mapping(target = "productName", expression = "java(refund.getAfterSaleInfo().getSkus().get(0).getSkuName())")
    @Mapping(target = "skuId", expression = "java(refund.getAfterSaleInfo().getSkus().get(0).getSkuId())")
    @Mapping(target = "skuName", expression = "java(refund.getAfterSaleInfo().getSkus().get(0).getSkuName())")
    @Mapping(target = "skuErpCode", expression = "java(refund.getAfterSaleInfo().getSkus().get(0).getSkuERPCode())")
    @Mapping(target = "num", expression = "java(refund.getAfterSaleInfo().getSkus().get(0).getBoughtCount())")
    // 金额和时间
    @Mapping(target = "refundApplyAmount", source = "afterSaleInfo.expectedRefundAmountYuan")
    @Mapping(target = "refundAmount", source = "afterSaleInfo.refundAmountYuan")
    @Mapping(target = "unitPrice", expression = "java(refund.getAfterSaleInfo().getSkus().get(0).getPrice().intValue())")
    @Mapping(target = "payment", expression = "java(refund.getAfterSaleInfo().getSkus().get(0).getPrice().intValue())")
    @Mapping(target = "refundTime", source = "afterSaleInfo.refundTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "returnTime", source = "logisticsInfo.afterSale.fillExpressNoTime", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "updateTime", source = "afterSaleInfo.updatedAt", qualifiedByName = "toLocalDateTime")
    @Mapping(target = "createTime", source = "afterSaleInfo.applyTime", qualifiedByName = "toLocalDateTime")
    RefundDTO toAdapterRefundItemDTO(GetAfterSaleInfoResponse refund);

    /**
     * 转换平台电子面单参数
     *
     * @param shopLogisticsOrderCreateCmd 统一电子面单参数
     * @param shop                        店铺
     * @param logisticsCompany            物流公司
     * @return 平台电子面单参数
     */
    @Mapping(target = "sender.name", source = "shopLogisticsOrderCreateCmd.senderName")
    @Mapping(target = "sender.phone", source = "shopLogisticsOrderCreateCmd.senderPhone")
    @Mapping(target = "sender.address.province", source = "shopLogisticsOrderCreateCmd.senderProvince")
    @Mapping(target = "sender.address.city", source = "shopLogisticsOrderCreateCmd.senderCity")
    @Mapping(target = "sender.address.district", source = "shopLogisticsOrderCreateCmd.senderDistrict")
    @Mapping(target = "sender.address.town", source = "shopLogisticsOrderCreateCmd.senderTown", defaultValue = "")
    @Mapping(target = "sender.address.detail", source = "shopLogisticsOrderCreateCmd.senderAddress")
    @Mapping(target = "cpCode", source = "logisticsCompany.code")
    @Mapping(target = "customerCode", source = "logisticsCompany.monthlyCard")
    @Mapping(target = "brandCode", source = "logisticsCompany.brandCode")
    @Mapping(target = "productCode", source = "logisticsCompany.type")
    @Mapping(target = "sellerName", source = "shop.shopName")
    @Mapping(target = "billVersion", constant = "2")
    @Mapping(target = "branchCode", source = "logisticsCompany.branchCode")
    @Mapping(target = "tradeOrderInfoList", expression = "java(java.util.Collections.singletonList(toElectronicBillTradeOrderInfo(shopLogisticsOrderCreateCmd, shop, logisticsCompany)))")
    ElectronicBillOrdersCreateRequest toElectronicBillOrdersCreateRequest(
            ShopLogisticsOrderCreateCmd shopLogisticsOrderCreateCmd,
            ShopDO shop,
            XiaohongshuLogisticsCompany logisticsCompany
    );

    /**
     * 转换平台电子面单参数
     *
     * @param shopLogisticsOrderCreateCmd 统一电子面单参数
     * @param shop                        店铺
     * @param logisticsCompany            物流公司
     * @return 平台电子面单参数
     */
    @Mapping(target = "recipient.openAddressId", source = "shopLogisticsOrderCreateCmd.receiverAddress")
    @Mapping(target = "orderInfo.orderChannelsType", constant = "XIAO_HONG_SHU")
    @Mapping(target = "orderInfo.xhsOrderId", source = "shopLogisticsOrderCreateCmd.shopOrderNo")
    @Mapping(target = "orderInfo.tradeOrderList", expression = "java(java.util.Collections.singletonList(shopLogisticsOrderCreateCmd.getShopOrderNo()))")
    @Mapping(target = "orderInfo.xhsOrderList", expression = "java(java.util.Collections.singletonList(shopLogisticsOrderCreateCmd.getShopOrderNo()))")
    @Mapping(target = "packageInfo.id", expression = "java(java.util.UUID.randomUUID().toString().replace(\"-\", \"\"))")
    @Mapping(target = "packageInfo.goodsDescription", expression = "java(org.apache.commons.lang3.StringUtils.left(shopLogisticsOrderCreateCmd.getMainCargoName(), 20))")
    @Mapping(target = "packageInfo.totalPackagesCount", constant = "0L")
    @Mapping(target = "packageInfo.items", source = "shopLogisticsOrderCreateCmd.cargos")
    @Mapping(target = "templateId", source = "logisticsCompany.templateId")
    @Mapping(target = "objectId", source = "shopLogisticsOrderCreateCmd.logisticsOrderId", defaultExpression = "java(java.util.UUID.randomUUID().toString().replace(\"-\", \"\"))")
    ElectronicBillOrdersCreateRequest.ElectronicBillTradeOrderInfo toElectronicBillTradeOrderInfo(
            ShopLogisticsOrderCreateCmd shopLogisticsOrderCreateCmd,
            ShopDO shop,
            XiaohongshuLogisticsCompany logisticsCompany
    );

    /**
     * 转换平台电子面单参数
     *
     * @param source 统一电子面单参数
     * @return 平台电子面单参数
     */
    @Mapping(target = "name", source = "name")
    @Mapping(target = "count", source = "quantity")
    ElectronicBillItem toElectronicBillItem(ShopLogisticsCargoCmd source);

    /**
     * 转换小红书发票数据为统一发票数据
     *
     * @param source 平台发票列表
     * @return 统一发票数据
     */
    List<InvoiceApplyDTO> toInvoiceApplyList(List<GetInvoiceListResponse.InvoiceRecord> source);

    /**
     * 转换小红书发票数据为统一发票数据
     *
     * @param source 平台发票
     * @return 统一发票数据
     */
    @Mapping(target = "invoiceTitle", source = "title")
    @Mapping(target = "orderNo", source = "refNo")
    @Mapping(target = "invoiceAmount", source = "invoiceTotalAmt", qualifiedByName = "yuanToCent")
    @Mapping(target = "taxNo", source = "taxNo")
    @Mapping(target = "bankName", source = "taxBankName")
    @Mapping(target = "bankAccount", source = "taxBankAccount")
    @Mapping(target = "invoiceType", source = "invoiceType", qualifiedByName = "toAdapterInvoiceTypeEnum")
    @Mapping(target = "invoiceTitleType", source = "titleType", qualifiedByName = "toTitleType")
    InvoiceApplyDTO toInvoiceApplyDTO(GetInvoiceListResponse.InvoiceRecord source);

    /**
     * 转换小红书发票数据为统一发票数据
     *
     * @param cmd 参数
     * @return 统一发票数据
     */
    @Mapping(target = "xhsInvoiceNo", source = "xhsInvoiceNo")
    @Mapping(target = "refNo", source = "cmd.orderNo")
    @Mapping(target = "invoiceType", source = "cmd.invoiceType", qualifiedByName = "toInvoiceType")
    @Mapping(target = "invoiceNo", source = "cmd.blueInvoiceNo")
    @Mapping(target = "file", expression = "java(java.util.Base64.getDecoder().decode(cmd.getInvoiceFileBase()))")
    ConfirmInvoiceRequest toConfirmInvoiceRequest(InvoiceUploadCmd cmd, String xhsInvoiceNo);

    /**
     * 设置默认达人信息
     *
     * @param order 订单
     */
    @AfterMapping
    default void setAuthor(@MappingTarget OrderDTO order) {
        if (order == null) {
            return;
        }
        if (CollectionUtils.isEmpty(order.getItems())) {
            return;
        }
        boolean xtcShop = "66b48f1ec2125e0015fd5d3f".equals(order.getSellerId());
        String authorId = xtcShop ? "6054b6420000000001005d22" : "643e01ccfde23e0001a33360";
        String authorName = xtcShop ? "小天才官方旗舰店" : "步步高学习机旗舰店";
        order.getItems().stream()
                .filter(item -> StringUtils.isBlank(item.getAuthorId()))
                .forEach(item -> {
                    item.setAuthorId(authorId);
                    item.setAuthorName(authorName);
                });
    }

    /**
     * 元转分
     *
     * @param yuan 元
     * @return 分
     */
    @Named("yuanToCent")
    default Integer yuanToCent(String yuan) {
        if (yuan == null) {
            return null;
        }
        return MoneyUtil.yuanToCent(yuan);
    }

    /**
     * 转换发票类型
     *
     * @param invoiceType 发票类型
     * @return 发票类型枚举
     */
    @Named("toAdapterInvoiceTypeEnum")
    default InvoiceTypeEnum toAdapterInvoiceTypeEnum(Integer invoiceType) {
        if (invoiceType == null) {
            return null;
        }
        return invoiceType == 1 ? InvoiceTypeEnum.SPECIAL : InvoiceTypeEnum.ELECTRONIC;
    }

    /**
     * 转化平台发票抬头
     *
     * @param titleType 平台发票类型
     * @return 发票类型
     */
    @Named("toTitleType")
    default InvoiceTitleTypeEnum toTitleType(Integer titleType) {
        if (titleType == null) {
            return null;
        }
        // 1：增值税普通发票 2：增值税专用发票
        return titleType == 2 ? InvoiceTitleTypeEnum.COMPANY : InvoiceTitleTypeEnum.PERSONAL;
    }

    /**
     * 时间转换
     *
     * @param epochMilli 毫秒
     * @return LocalDateTime
     */
    @Named("toLocalDateTime")
    default LocalDateTime toLocalDateTime(long epochMilli) {
        if (epochMilli < 1) {
            return null;
        }
        return DateUtil.toLocalDateTimeFromEpochMilli(epochMilli);
    }

    /**
     * 转换发票类型
     *
     * @param invoiceType 发票类型
     * @return 发票类型枚举
     */
    @Named("toInvoiceType")
    default Integer toInvoiceType(InvoiceTypeEnum invoiceType) {
        // 发票类型，0:发票类型未知;1:增值税专用发票；2:增值税纸质普通发票；3:增值税电子普通发票；4:形式发票；5:电子专票；6:全电票电子普票；7:全电票纸质专票；8:全电票纸质普票
        if (invoiceType == InvoiceTypeEnum.NORMAL) {
            return 6;
        }
        if (invoiceType == InvoiceTypeEnum.SPECIAL) {
            return 5;
        }
        return 6;
    }

}
