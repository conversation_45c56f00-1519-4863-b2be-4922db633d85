package com.xtc.marketing.adapterservice.notify.converter;

import com.google.common.collect.ImmutableList;
import com.kuaishou.merchant.open.api.domain.order.OrderDetail;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.OmsModifyAddressNotifyCmd;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import org.mapstruct.*;

import java.util.List;
import java.util.Optional;

/**
 * 快手通知转换器
 */
@Mapper(
        componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        builder = @Builder(disableBuilder = true),
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface KuaishouNotifyConverter {

    /**
     * 转换统一地址通知参数
     *
     * @param source 请求参数
     * @return 统一地址通知参数
     */
    @Mapping(target = "event", constant = "KUAISHOU")
    @Mapping(target = "tradeId", source = "orderBaseInfo.oid")
    // 快手改地址，姓名和电话保持不变，从原始地址信息中获取
    @Mapping(target = "receiverName", source = "orderAddress.encryptedConsignee")
    @Mapping(target = "receiverMobile", source = "orderAddress.encryptedMobile")
    // 地址信息从审核信息中的“修改后地址”获取
    @Mapping(target = "receiverProvince", source = "addressUpdateAuditInfo.addressAfterUpdate.province")
    @Mapping(target = "receiverCity", source = "addressUpdateAuditInfo.addressAfterUpdate.city")
    @Mapping(target = "receiverDistrict", source = "addressUpdateAuditInfo.addressAfterUpdate.district")
    @Mapping(target = "receiverTown", source = "addressUpdateAuditInfo.addressAfterUpdate.town")
    @Mapping(target = "receiverAddress", source = "addressUpdateAuditInfo.addressAfterUpdate.encryptedAddress")
    // 密文
    @Mapping(target = "receiverOaid", source = "source", qualifiedByName = "convertReceiverOaId")
    OmsModifyAddressNotifyCmd toOmsModifyAddressNotifyCmd(OrderDetail source);

    /**
     * 修改后收货人信息 转换 receiverOaid
     *
     * @param source 平台订单
     * @return receiverOaid
     */
    @Named("convertReceiverOaId")
    default String convertReceiverOaId(OrderDetail source) {
        // 配置默认值避免使用时解析异常
        String empty = "";
        String receiver = Optional.ofNullable(source.getOrderAddress().getEncryptedConsignee()).orElse(empty);
        String postTel = Optional.ofNullable(source.getOrderAddress().getEncryptedMobile()).orElse(empty);
        String postAddress = Optional.ofNullable(source.getAddressUpdateAuditInfo().getAddressAfterUpdate().getEncryptedAddress()).orElse(empty);
        List<String> cipherTexts = ImmutableList.of(receiver, postTel, postAddress);
        return GsonUtil.objectToJson(cipherTexts);
    }

}
