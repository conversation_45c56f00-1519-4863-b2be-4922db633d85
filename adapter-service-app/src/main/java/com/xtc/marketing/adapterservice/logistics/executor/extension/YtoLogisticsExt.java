package com.xtc.marketing.adapterservice.logistics.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.logistics.converter.YtoLogisticsConverter;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsCloudPrintDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsOrderDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCancelOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCloudPrintCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsOrderGetQry;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsRouteListQry;
import com.xtc.marketing.adapterservice.rpc.yto.YtoRpc;
import com.xtc.marketing.adapterservice.rpc.yto.ytodto.YtoOrderDTO;
import com.xtc.marketing.adapterservice.rpc.yto.ytodto.YtoRouteDTO;
import com.xtc.marketing.adapterservice.rpc.yto.ytodto.command.YtoCreateOrderCmd;
import com.xtc.marketing.adapterservice.rpc.yto.ytodto.command.YtoInterceptCmd;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = LogisticsExtConstant.USE_CASE, scenario = LogisticsExtConstant.SCENARIO_YTO)
public class YtoLogisticsExt implements LogisticsExtPt {

    private final YtoRpc ytoRpc;
    private final YtoLogisticsConverter ytoLogisticsConverter;

    @Override
    public List<LogisticsRouteDTO> routes(LogisticsAccountDO account, LogisticsRouteListQry qry) {
        List<YtoRouteDTO> listRouteDTO = ytoRpc.searchRoutes(account, qry.getWaybillNo());
        return ytoLogisticsConverter.toAdapterRouteDTO(listRouteDTO);
    }

    @Override
    public LogisticsOrderDTO createOrder(LogisticsAccountDO account, LogisticsCreateOrderCmd cmd) {
        YtoCreateOrderCmd createOrderCmd = ytoLogisticsConverter.toYtoCreateOrderCmd(cmd);
        YtoOrderDTO ytoOrderDTO = ytoRpc.createOrder(account, createOrderCmd);
        return LogisticsOrderDTO.builder()
                .wayBillNo(ytoOrderDTO.getMailNo())
                .orderDetail(GsonUtil.objectToJson(ytoOrderDTO))
                .build();
    }

    @Override
    public String getOrder(LogisticsAccountDO account, LogisticsOrderGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public String getWaybillNo(LogisticsAccountDO account, LogisticsOrderGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean cancelOrder(LogisticsAccountDO account, LogisticsCancelOrderCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean intercept(LogisticsAccountDO account, LogisticsInterceptCmd cmd) {
        YtoInterceptCmd ytoInterceptCmd = ytoLogisticsConverter.toYtoInterceptCmd(cmd);
        ytoRpc.intercept(account, ytoInterceptCmd);
        return true;
    }

    @Override
    public LogisticsCloudPrintDTO cloudPrint(LogisticsAccountDO account, LogisticsCloudPrintCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

}
