package com.xtc.marketing.adapterservice.logistics;

import com.xtc.marketing.adapterservice.logistics.dto.LogisticsCloudPrintDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsOrderDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCancelOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCloudPrintCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsOrderGetQry;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsRouteListQry;

import java.util.List;

public interface LogisticsService {

    /**
     * 查询快递路由
     *
     * @param qry 参数
     * @return 快递路由
     */
    List<LogisticsRouteDTO> routes(LogisticsRouteListQry qry);

    /**
     * 下物流单并生成运单号
     *
     * @param cmd 参数
     * @return 订单详情
     */
    LogisticsOrderDTO createOrder(LogisticsCreateOrderCmd cmd);

    /**
     * 查询物流订单
     *
     * @param qry 参数
     * @return 物流订单
     */
    String getOrder(LogisticsOrderGetQry qry);

    /**
     * 查询运单号
     *
     * @param qry 参数
     * @return 运单号
     */
    String getWaybillNo(LogisticsOrderGetQry qry);

    /**
     * 取消订单
     *
     * @param cmd 参数
     * @return 执行结果
     */
    boolean cancelOrder(LogisticsCancelOrderCmd cmd);

    /**
     * 物流拦截
     *
     * @param cmd 参数
     */
    void intercept(LogisticsInterceptCmd cmd);

    /**
     * 面单云打印
     *
     * @param cmd 参数
     * @return 云打印数据
     */
    LogisticsCloudPrintDTO cloudPrint(LogisticsCloudPrintCmd cmd);

}
