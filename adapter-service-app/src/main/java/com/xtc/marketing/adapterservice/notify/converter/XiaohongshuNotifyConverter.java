package com.xtc.marketing.adapterservice.notify.converter;

import com.google.common.collect.ImmutableList;
import com.xiaohongshu.fls.opensdk.entity.order.Response.OrderReceiverInfo;
import com.xtc.marketing.adapterservice.constant.SystemConstant;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.OmsModifyAddressNotifyCmd;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import org.mapstruct.*;

import java.util.List;
import java.util.Optional;

/**
 * 小红书通知数据转换器
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface XiaohongshuNotifyConverter {

    /**
     * 转换为OMS修改地址通知命令
     *
     * @param newReceiver 修改后收货人信息
     * @return OMS修改地址通知命令
     */
    @Mapping(target = "event", constant = SystemConstant.SYSTEM_NAME)
    @Mapping(target = "tradeId", source = "orderId")
    @Mapping(target = "receiverOaid", source = "newReceiver", qualifiedByName = "convertReceiverOaId")
    @Mapping(target = "receiverName", source = "receiverName")
    @Mapping(target = "receiverMobile", source = "receiverPhone")
    @Mapping(target = "receiverProvince", source = "receiverProvinceName")
    @Mapping(target = "receiverCity", source = "receiverCityName")
    @Mapping(target = "receiverDistrict", source = "receiverDistrictName")
    @Mapping(target = "receiverTown", source = "receiverTownName")
    @Mapping(target = "receiverAddress", source = "receiverAddress")
    OmsModifyAddressNotifyCmd toOmsModifyAddressNotifyCmd(OrderReceiverInfo newReceiver);

    /**
     * 修改后收货人信息 转换 receiverOaid
     *
     * @param newReceiver 修改后收货人信息
     * @return receiverOaid
     */
    @Named("convertReceiverOaId")
    default String convertReceiverOaId(OrderReceiverInfo newReceiver) {
        // 配置默认值避免使用时解析异常
        String empty = "";
        String receiverName = Optional.ofNullable(newReceiver.getReceiverName()).orElse(empty);
        String receiverPhone = Optional.ofNullable(newReceiver.getReceiverPhone()).orElse(empty);
        String receiverAddress = Optional.ofNullable(newReceiver.getReceiverAddress()).orElse(empty);
        List<String> cipherTexts = ImmutableList.of(receiverName, receiverPhone, receiverAddress);
        return GsonUtil.objectToJson(cipherTexts);
    }

}
