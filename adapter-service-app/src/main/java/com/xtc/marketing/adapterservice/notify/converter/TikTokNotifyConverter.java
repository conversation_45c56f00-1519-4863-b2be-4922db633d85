package com.xtc.marketing.adapterservice.notify.converter;

import com.doudian.open.spi.order_shopAddress_getReviewResult.param.OrderShopAddressGetReviewResultParam;
import com.doudian.open.spi.order_shopAddress_getReviewResult.param.PostAddress;
import com.doudian.open.spi.order_shopAddress_getReviewResult.param.ToReceiverInfo;
import com.google.common.collect.ImmutableList;
import com.xtc.marketing.adapterservice.constant.SystemConstant;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.OmsModifyAddressNotifyCmd;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import org.mapstruct.*;

import java.util.List;
import java.util.Optional;

@Mapper(
        componentModel = "spring",
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface TikTokNotifyConverter {

    /**
     * 转换统一地址通知参数
     *
     * @param source 请求参数
     * @return 统一地址通知参数
     */
    @Mapping(target = "event", constant = SystemConstant.SYSTEM_NAME)
    @Mapping(target = "tradeId", source = "orderId")
    @Mapping(target = "receiverOaid", source = "toReceiverInfo", qualifiedByName = "convertReceiverOaId")
    @Mapping(target = "receiverName", source = "toReceiverInfo.postReceiver")
    @Mapping(target = "receiverMobile", source = "toReceiverInfo.postTel")
    @Mapping(target = "receiverProvince", source = "toReceiverInfo.postAddress.province.name")
    @Mapping(target = "receiverCity", source = "toReceiverInfo.postAddress.city.name")
    @Mapping(target = "receiverDistrict", source = "toReceiverInfo.postAddress.town.name")
    @Mapping(target = "receiverTown", source = "toReceiverInfo.postAddress.street.name")
    @Mapping(target = "receiverAddress", source = "toReceiverInfo.postAddress.detail")
    OmsModifyAddressNotifyCmd toOmsModifyAddressNotifyCmd(OrderShopAddressGetReviewResultParam source);

    /**
     * 修改后收货人信息 转换 receiverOaid
     *
     * @param toReceiver 修改后收货人信息
     * @return receiverOaid
     */
    @Named("convertReceiverOaId")
    default String convertReceiverOaId(ToReceiverInfo toReceiver) {
        // 配置默认值避免使用时解析异常
        String empty = "";
        String receiver = Optional.ofNullable(toReceiver.getPostReceiver()).orElse(empty);
        String postTel = Optional.ofNullable(toReceiver.getPostTel()).orElse(empty);
        String postAddress = Optional.ofNullable(toReceiver.getPostAddress()).map(PostAddress::getEncryptDetail).orElse(empty);
        List<String> cipherTexts = ImmutableList.of(receiver, postTel, postAddress);
        return GsonUtil.objectToJson(cipherTexts);
    }

}
