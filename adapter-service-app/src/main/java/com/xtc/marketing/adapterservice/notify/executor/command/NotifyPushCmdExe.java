package com.xtc.marketing.adapterservice.notify.executor.command;

import com.alibaba.cola.extension.BizScenario;
import com.alibaba.cola.extension.ExtensionExecutor;
import com.google.common.base.Splitter;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.notify.dataobject.PushConfigDO;
import com.xtc.marketing.adapterservice.notify.dataobject.PushLogDO;
import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.executor.extension.NotifyExtPt;
import com.xtc.marketing.adapterservice.notify.repository.PushConfigRepository;
import com.xtc.marketing.adapterservice.notify.repository.PushLogRepository;
import com.xtc.marketing.adapterservice.notify.repository.ReceiveLogRepository;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 通知推送
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class NotifyPushCmdExe {

    private final PushConfigRepository pushConfigRepository;
    private final ReceiveLogRepository receiveLogRepository;
    private final PushLogRepository pushLogRepository;
    private final ExtensionExecutor extensionExecutor;

    /**
     * 每次推送最大数量
     */
    private static final int LIMIT_SIZE = 1000;
    /**
     * 推送时间范围
     */
    private static final Duration PUSH_DURATION = Duration.ofDays(1);
    /**
     * http 请求工具类
     */
    private static final RestTemplate REST_TEMPLATE;

    static {
        REST_TEMPLATE = new RestTemplate();
        // 设置字符串的转换使用UTF-8编码
        REST_TEMPLATE.getMessageConverters().stream()
                .filter(converter -> converter.getClass().equals(StringHttpMessageConverter.class))
                .forEach(converter -> ((StringHttpMessageConverter) converter).setDefaultCharset(StandardCharsets.UTF_8));
        // 设置请求超时时间
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setConnectTimeout(3000);
        requestFactory.setReadTimeout(3000);
        REST_TEMPLATE.setRequestFactory(requestFactory);
    }

    /**
     * 执行推送
     *
     * @param shardIndex 分片索引
     */
    public void execute(int shardIndex) {
        List<PushConfigDO> configs = pushConfigRepository.listEnabled();
        if (CollectionUtils.isEmpty(configs)) {
            log.info("推送任务 - 无需推送，未找到推送配置");
            return;
        }
        configs.forEach(config -> {
            log.info("推送任务 - 开始推送 {}", config);
            try {
                // 初始化数据
                LocalDateTime now = LocalDateTime.now();
                Optional<LocalDateTime> updateLastPushTime = Optional.empty();
                LocalDateTime updateTimeStart = Optional.ofNullable(config.getLastPushTime()).orElse(LocalDateTime.now().minusHours(1));
                LocalDateTime updateTimeEnd = Optional.of(updateTimeStart.plus(PUSH_DURATION)).filter(now::isAfter).orElse(now);
                String limitSql = "limit " + shardIndex * LIMIT_SIZE + "," + LIMIT_SIZE;
                // 根据上一次推送时间获取一批待推送的数据
                List<ReceiveLogDO> receiveLogs = receiveLogRepository.listByPushConfig(config, updateTimeStart, updateTimeEnd, limitSql);
                if (CollectionUtils.isNotEmpty(receiveLogs)) {
                    // 处理待推送数据
                    updateLastPushTime = this.handelWaitPushData(config, receiveLogs);
                } else {
                    log.info("推送任务 - 无需推送，暂无待推送的数据 {}", config);
                }
                // 更新推送配置的上一次推送时间
                PushConfigDO updatePushConfig = PushConfigDO.builder().id(config.getId())
                        .lastPushTime(updateLastPushTime.orElse(updateTimeEnd)).build();
                pushConfigRepository.updateById(updatePushConfig);
                log.info("推送任务 - 更新推送配置的上一次推送时间 [{}]", DateUtil.toString(updatePushConfig.getLastPushTime()));
            } catch (Exception e) {
                log.warn("推送任务 - 推送异常 {}", config, e);
            }
        });
    }

    /**
     * 处理待推送数据
     *
     * @param config      推送配置
     * @param receiveLogs 接收记录列表
     * @return 推送配置的上一次推送时间
     */
    private Optional<LocalDateTime> handelWaitPushData(PushConfigDO config, List<ReceiveLogDO> receiveLogs) {
        LocalDateTime lastPushTime = null;
        BizScenario scenario = BizScenario.valueOf(config.getPlatformCode(), config.getModuleCode(), config.getScenarioCode());
        for (ReceiveLogDO receiveLog : receiveLogs) {
            this.logInfo("推送循环控制 - 推送数据", receiveLog);
            // 查询最后一次推送记录，确认数据未推送成功过
            Optional<PushLogDO> lastPushLog = pushLogRepository.getLastByPushConfigAndReceiveLogId(config, receiveLog.getId());
            boolean lastPushSuccess = lastPushLog.map(PushLogDO::getPushSuccess).orElse(false);
            if (lastPushSuccess) {
                this.logInfo("推送循环控制 - 无需推送，该接收记录已经推送成功", receiveLog);
                continue;
            }
            // 推送次数超过 5 次，需要最后一次推送时间超过 1 小时后才能推送
            boolean needPushData = true;
            int count = pushLogRepository.countByPushConfigAndReceiveLogId(config, receiveLog.getId());
            if (count >= 5) {
                Optional<LocalDateTime> lastTime = lastPushLog.map(PushLogDO::getCreateTime);
                needPushData = lastTime.map(time -> LocalDateTime.now().isAfter(time.plusHours(1))).orElse(true);
                if (BooleanUtils.isNotTrue(needPushData)) {
                    log.info("推送循环控制 - 推送次数 {} 超过 5 次，需要最后一次推送时间 {} 超过 1 小时后才能推送 {} {}",
                            count, DateUtil.toString(lastTime.orElse(null)), receiveLog.getId(), receiveLog.getDataId());
                }
            }
            // 默认一直循环推送，待推送成功才退出推送循环
            boolean needLoopPush = true;
            if (needPushData) {
                boolean pushSuccess = this.pushData(config, receiveLog, scenario);
                // 推送成功则退出推送循环，推送失败等待下次推送
                needLoopPush = BooleanUtils.isNotTrue(pushSuccess);
            }
            // 更新接收记录的修改时间等待下次推送
            if (needLoopPush) {
                ReceiveLogDO updateReceiveLog = ReceiveLogDO.builder().id(receiveLog.getId()).updateTime(LocalDateTime.now()).build();
                receiveLogRepository.updateById(updateReceiveLog);
                this.logInfo("推送循环控制 - 更新接收记录的修改时间等待下次推送", receiveLog);
            }
            // 记录上一次推送时间为接收记录的更新时间
            lastPushTime = receiveLog.getUpdateTime();
        }
        return Optional.ofNullable(lastPushTime);
    }

    /**
     * 推送数据
     *
     * @param config     推送配置
     * @param receiveLog 接收记录
     * @param scenario   扩展点
     * @return 推送成功标识
     */
    private boolean pushData(PushConfigDO config, ReceiveLogDO receiveLog, BizScenario scenario) {
        try {
            // 转换通知数据字符串转换为 bean 对象
            Object notifyBean = extensionExecutor.execute(NotifyExtPt.class, scenario,
                    exe -> exe.convertToNotifyBean(receiveLog.getRawData()));
            // 推送数据
            ResponseEntity<String> response = this.pushDataHttpExecute(config.getPushUrl(), notifyBean);
            // 判断推送成功
            boolean pushSuccess = this.isPushSuccess(config, response);
            // 保存推送记录
            this.savePushLog(config, receiveLog, pushSuccess, response);
            return pushSuccess;
        } catch (Exception e) {
            this.logWarn("推送数据失败", receiveLog, e);
            return false;
        }
    }

    /**
     * 推送数据 http 执行逻辑
     *
     * @param url  推送地址
     * @param data 推送数据
     * @return 推送结果
     */
    private ResponseEntity<String> pushDataHttpExecute(String url, Object data) {
        if (data == null) {
            return null;
        }
        // http请求携带的信息
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> entity = new HttpEntity<>(data, httpHeaders);
        // 向url发送method请求，请求携带信息entity，返回clazz类型的数据
        log.info("NotifyPushCmdExe url: {}, header: {}, body: {}", url, httpHeaders, entity);
        ResponseEntity<String> response = REST_TEMPLATE.exchange(url, HttpMethod.POST, entity, String.class);
        log.info("NotifyPushCmdExe url: {}, header: {}, body: {}, response: {}", url, httpHeaders, entity, response.getBody());
        return response;
    }

    /**
     * 判断推送成功
     *
     * @param config   推送配置
     * @param response 推送响应
     * @return 执行结果
     */
    private boolean isPushSuccess(PushConfigDO config, ResponseEntity<String> response) {
        if (response == null) {
            return false;
        }
        boolean pushSuccess = response.getStatusCode().is2xxSuccessful();
        if (StringUtils.isAnyBlank(response.getBody(), config.getSuccessCode())) {
            return pushSuccess;
        }
        // 判断成功的响应码，例：code:0000001、success:true
        List<String> successCode = Splitter.on(":").trimResults().omitEmptyStrings().splitToList(config.getSuccessCode());
        if (successCode.size() != 2) {
            log.warn("推送配置的成功响应码格式错误 [{}]", config.getSuccessCode());
            return pushSuccess;
        }
        JsonObject responseBodyObj = GsonUtil.jsonToObject(response.getBody());
        return Optional.ofNullable(responseBodyObj)
                .filter(JsonObject::isJsonObject)
                .map(obj -> obj.get(successCode.get(0)))
                .filter(JsonElement::isJsonPrimitive)
                .map(code -> {
                    // 判断字符串类型响应码
                    if (code.getAsJsonPrimitive().isString()) {
                        return successCode.get(1).equals(code.getAsString());
                    }
                    // 判断布尔类型响应码
                    if (code.getAsJsonPrimitive().isBoolean()) {
                        return BooleanUtils.isTrue(code.getAsBoolean());
                    }
                    return false;
                })
                .orElse(false);
    }

    /**
     * 保存推送记录
     *
     * @param config      推送配置
     * @param receiveLog  接收记录
     * @param pushSuccess 推送成功标识
     * @param response    推送结果
     */
    private void savePushLog(PushConfigDO config, ReceiveLogDO receiveLog, boolean pushSuccess, ResponseEntity<String> response) {
        PushLogDO pushLog = PushLogDO.builder()
                .bizName(config.getBizName())
                .moduleCode(config.getModuleCode())
                .platformCode(config.getPlatformCode())
                .scenarioCode(config.getScenarioCode())
                .pushUrl(config.getPushUrl())
                .dataId(receiveLog.getDataId())
                .receiveLogId(receiveLog.getId())
                .pushSuccess(pushSuccess)
                .build();
        if (response != null) {
            JsonObject responseObj = new JsonObject();
            responseObj.addProperty("statusCode", response.getStatusCodeValue());
            responseObj.addProperty("body", response.getBody());
            pushLog.setResponse(responseObj.toString());
        }
        pushLogRepository.save(pushLog);
    }

    /**
     * 打印 info 日志
     *
     * @param msg        日志信息
     * @param receiveLog 接收记录
     */
    private void logInfo(String msg, ReceiveLogDO receiveLog) {
        log.info("{} receiveLogId: {}, dataId: {}", msg, receiveLog.getId(), receiveLog.getDataId());
    }

    /**
     * 打印 warn 日志
     *
     * @param msg        日志信息
     * @param receiveLog 接收记录
     * @param e          异常
     */
    private void logWarn(String msg, ReceiveLogDO receiveLog, Exception e) {
        log.info("{} receiveLogId: {}, dataId: {}", msg, receiveLog.getId(), receiveLog.getDataId(), e);
    }

}
