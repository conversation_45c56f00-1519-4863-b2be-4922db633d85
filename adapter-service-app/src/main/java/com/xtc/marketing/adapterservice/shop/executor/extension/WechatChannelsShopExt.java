package com.xtc.marketing.adapterservice.shop.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.adapterservice.exception.BizErrorCode;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.WechatChannelsShopRpc;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.*;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.command.WechatChannelsShopLogisticsCreateCmd;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.command.WechatChannelsShopShippingCmd;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.query.WechatChannelsShopOrderPageQry;
import com.xtc.marketing.adapterservice.rpc.wechatchannelsshop.wechatchannelsshopdto.query.WechatChannelsShopRefundPageQry;
import com.xtc.marketing.adapterservice.shop.converter.WechatChannelsShopConverter;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.dto.*;
import com.xtc.marketing.adapterservice.shop.dto.command.*;
import com.xtc.marketing.adapterservice.shop.dto.query.*;
import com.xtc.marketing.adapterservice.shop.util.SkuUtil;
import com.xtc.marketing.adapterservice.util.BeanCopier;
import com.xtc.marketing.adapterservice.util.DateUtil;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = ShopExtConstant.USE_CASE, scenario = ShopExtConstant.SCENARIO_WECHAT_CHANNELS_SHOP)
public class WechatChannelsShopExt implements ShopExtPt {

    private final WechatChannelsShopRpc wechatChannelsShopRpc;
    private final WechatChannelsShopConverter wechatChannelsShopConverter;

    @Override
    public ShopDO getShopToken(ShopDO shop) {
        ShopDO newToken = BeanCopier.copy(shop, ShopDO::new);

        // 生成 AccessToken
        WechatChannelsShopRefreshTokenDTO accessTokenData = wechatChannelsShopRpc.refreshToken(shop);
        if (accessTokenData == null) {
            return newToken;
        }

        // 设置新的 token 数据
        newToken.setAppAccessToken(accessTokenData.getAccessToken());
        newToken.setAppExpireTime(LocalDateTime.now().plusSeconds(accessTokenData.getExpiresIn()));
        return newToken;
    }

    @Override
    public PageResponse<OrderDTO> pageOrders(ShopDO shop, OrderPageQry qry) {
        WechatChannelsShopOrderPageQry orderPageQry = wechatChannelsShopConverter.toWechatChannelsShopOrderPageQry(qry);
        WechatChannelsShopOrderDTO platformOrders = wechatChannelsShopRpc.pageOrders(shop, orderPageQry);
        // 平台只返回订单号数据
        List<OrderDTO> orders = platformOrders.getOrders().stream()
                .map(orderNo -> OrderDTO.builder().orderNo(orderNo).build())
                .collect(Collectors.toList());
        // 返回分页数据，设置 hasNext nextKey 字段
        PageResponse<OrderDTO> response = PageResponse.of(orders, -1, qry.getPageSize(), qry.getPageIndex());
        response.setHasNext(platformOrders.getHasMore());
        response.setNextKey(platformOrders.getNextKey());
        return response;
    }

    @Override
    public OrderDTO getOrder(ShopDO shop, OrderGetQry qry) {
        WechatChannelsShopOrderItemDTO order = wechatChannelsShopRpc.getOrder(shop, qry.getOrderNo());
        return Optional.ofNullable(order)
                .map(originOrder -> {
                    // 拆分平台维护的 skuId
                    List<WechatChannelsShopProductDTO> splitSkuItems = SkuUtil.splitSkuIdsAndCloneItem(
                            originOrder.getOrderDetail().getProducts(),
                            WechatChannelsShopProductDTO::getSkuErpCode,
                            WechatChannelsShopProductDTO::setSkuErpCode
                    );
                    originOrder.getOrderDetail().setProducts(splitSkuItems);
                    OrderDTO orderDTO = wechatChannelsShopConverter.toOrderDTO(order);
                    // 设置达人id
                    setAuthorId(shop, originOrder, orderDTO);
                    orderDTO.setOriginOrderData(order.getOriginData());
                    return orderDTO;
                })
                .orElse(null);
    }

    @Override
    public boolean orderShipping(ShopDO shop, OrderShippingCmd cmd) {
        boolean hasShipping = this.checkOrderHasShipping(shop, cmd.getOrderNo());
        if (hasShipping) {
            return true;
        }
        WechatChannelsShopOrderItemDTO order = wechatChannelsShopRpc.getOrder(shop, cmd.getOrderNo());
        WechatChannelsShopShippingCmd shipping = wechatChannelsShopConverter.toWechatChannelsShopShipping(cmd, order);
        return wechatChannelsShopRpc.orderShipping(shop, shipping);
    }

    @Override
    public boolean orderShippingCancel(ShopDO shop, OrderShippingCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderDummyShipping(ShopDO shop, OrderDummyShippingCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean orderRemark(ShopDO shop, OrderRemarkCmd cmd) {
        return wechatChannelsShopRpc.orderRemark(shop, cmd.getOrderNo(), cmd.getRemark());
    }

    @Override
    public OrderDecryptDTO orderDecrypt(ShopDO shop, OrderDecryptCmd cmd) {
        WechatChannelsShopDecryptDTO orderDecrypt = wechatChannelsShopRpc.orderDecrypt(shop, cmd.getOrderNo());
        return wechatChannelsShopConverter.toOrderDecryptDTO(orderDecrypt, cmd);
    }

    @Override
    public PageResponse<InvoiceApplyDTO> pageInvoiceApply(ShopDO shop, InvoiceApplyPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public InvoiceApplyDTO getInvoiceApply(ShopDO shop, InvoiceApplyGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceFile(ShopDO shop, OrderUploadInvoiceCmd cmd, MultipartFile invoiceFile) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean uploadInvoiceBase64(ShopDO shop, InvoiceUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public InvoiceAmountDTO getInvoiceAmount(ShopDO shop, String orderNo) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<CommentDTO> pageComments(ShopDO shop, CommentPageQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public PageResponse<RefundDTO> pageRefunds(ShopDO shop, RefundPageQry qry) {
        WechatChannelsShopRefundPageQry refundPageQry = WechatChannelsShopRefundPageQry.builder()
                .beginCreateTime(DateUtil.toEpochSecond(qry.getUpdateTimeStart()))
                .endCreateTime(DateUtil.toEpochSecond(qry.getUpdateTimeEnd()))
                .nextKey(qry.getNextKey())
                .build();
        WechatChannelsShopRefundPageDTO platformRefunds = wechatChannelsShopRpc.pageRefunds(shop, refundPageQry);
        // 平台只返回退款单号数据
        List<RefundDTO> refunds = platformRefunds.getAfterSaleOrderIdList().stream()
                .map(refundId -> RefundDTO.builder().serviceNo(refundId).build())
                .collect(Collectors.toList());
        // 返回分页数据，设置 hasNext nextKey 字段
        PageResponse<RefundDTO> response = PageResponse.of(refunds, -1, qry.getPageSize(), qry.getPageIndex());
        response.setHasNext(platformRefunds.getHasMore());
        response.setNextKey(platformRefunds.getNextKey());
        return response;
    }

    @Override
    public RefundDTO getRefund(ShopDO shop, RefundGetQry qry) {
        WechatChannelsShopRefundDTO platformRefund = wechatChannelsShopRpc.getRefund(shop, qry.getRefundId());
        return Optional.ofNullable(platformRefund)
                .map(refund -> {
                    RefundDTO refundDTO = wechatChannelsShopConverter.toRefundDTO(refund);
                    refundDTO.setOriginData(platformRefund.getOriginData());
                    return refundDTO;
                }).orElse(null);
    }

    @Override
    public ShopLogisticsOrderDTO createLogisticsOrder(ShopDO shop, ShopLogisticsOrderCreateCmd cmd) {
        WechatChannelsShopLogisticsCreateCmd createCmd = wechatChannelsShopConverter.toWechatChannelsShopLogisticsCreateCmd(cmd, shop);
        String predOrderNo = wechatChannelsShopRpc.preCreateLogisticsOrder(shop, createCmd);
        createCmd.setOrderNo(predOrderNo);
        WechatChannelsShopLogisticsDTO logisticsOrder = wechatChannelsShopRpc.createLogisticsOrder(shop, createCmd);
        return ShopLogisticsOrderDTO.builder()
                .wayBillNo(logisticsOrder.getWaybillNo())
                .orderDetail(logisticsOrder.getOrderDetail())
                .build();
    }

    @Override
    public boolean cancelLogisticsOrder(ShopDO shop, ShopLogisticsOrderCancelCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void refundGoodsToWarehouse(ShopDO shop, RefundGoodsToWarehouseCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public void barcodeUpload(ShopDO shop, BarcodeUploadCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    /**
     * 检查订单已发货
     *
     * @param shop    店铺
     * @param orderNo 订单号
     * @return 执行结果
     */
    private boolean checkOrderHasShipping(ShopDO shop, String orderNo) {
        WechatChannelsShopOrderItemDTO order = wechatChannelsShopRpc.getOrder(shop, orderNo);
        // 判断订单状态已发货，无需重复发货
        if (order.getOrderState() == 30 || order.getOrderState() == 100) {
            log.warn("订单已发货，无需重复发货 {} {}", orderNo, order.getOrderState());
            return true;
        }
        // 判断订单状态不是待发货，抛异常
        if (order.getOrderState() != 20) {
            // 例：平台的订单状态不符合推送发货状态的条件 3720157767442239744 orderStatus: 10
            String msg = String.format("%s %s orderStatus: %s",
                    BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrDesc(), orderNo, order.getOrderState());
            throw new BizException(BizErrorCode.B_ORDER_OrderStatusNoAllowPushShippingStatus.getErrCode(), msg);
        }
        return false;
    }

    /**
     * 设置达人id
     *
     * @param shop        店铺
     * @param originOrder 平台订单
     * @param orderDTO    订单
     */
    private void setAuthorId(ShopDO shop, WechatChannelsShopOrderItemDTO originOrder, OrderDTO orderDTO) {
        boolean xtcShop = "WECHAT_CHANNELS_SHOP_XTC".equals(shop.getShopCode());
        String authorId = xtcShop ? "sphjLFgwBr8GhZ8" : "sphvX515TwhYZjP";
        String authorName = xtcShop ? "小天才品牌旗舰店" : "步步高官方旗舰店";
        orderDTO.getItems()
                .stream()
                .filter(item -> StringUtils.isBlank(item.getAuthorId()))
                .forEach(item -> {
                    if (CollectionUtils.isEmpty(originOrder.getOrderDetail().getSourceInfoDTO())) {
                        // 设置达人id
                        item.setAuthorId(authorId);
                        item.setAuthorName(authorName);
                        return;
                    }
                    WechatChannelsShopOrderItemDetailDTO.SourceInfoDTO sourceInfoDTO = originOrder.getOrderDetail().getSourceInfoDTO().get(0);
                    item.setAuthorId(StringUtils.isBlank(sourceInfoDTO.getAccountId()) ? authorId : sourceInfoDTO.getAccountId());
                    item.setAuthorName(StringUtils.isBlank(sourceInfoDTO.getAccountId()) ? authorName : sourceInfoDTO.getAccountNickname());
                });
    }

}
