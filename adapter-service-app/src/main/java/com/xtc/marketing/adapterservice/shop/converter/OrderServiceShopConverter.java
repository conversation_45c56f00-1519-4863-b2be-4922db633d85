package com.xtc.marketing.adapterservice.shop.converter;

import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.rpc.orderservice.xtcshopdto.query.XtcShopEncryptQry;
import com.xtc.marketing.adapterservice.shop.dto.InvoiceApplyDTO;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.OrderItemDTO;
import com.xtc.marketing.adapterservice.shop.dto.RefundDTO;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTitleTypeEnum;
import com.xtc.marketing.adapterservice.shop.enums.InvoiceTypeEnum;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import com.xtc.marketing.orderservice.serviceorder.dto.ServiceOrderDTO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.util.List;

@Mapper(
        componentModel = "spring",
        uses = {BaseShopConverter.class},
        // 忽略未设置映射的字段
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        // mapstruct 和 lombok 一起使用时，需要关闭 @Builder 注解，否则 @AfterMapping 方法不会生效
        builder = @Builder(disableBuilder = true),
        // source 每个属性都进行 null 值校验
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        // target 映射遇到 null 值时忽略映射
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface OrderServiceShopConverter {

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    List<OrderDTO> toAdapterOrderDTO(List<com.xtc.marketing.orderservice.order.dto.OrderDTO> source);

    /**
     * 转换统一订单数据
     *
     * @param source 平台订单数据
     * @return 统一订单数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderNo")
    @Mapping(target = "orderType", source = "orderType")
    @Mapping(target = "orderState", source = "orderState")
    @Mapping(target = "sellerId", source = "sellerId")
    @Mapping(target = "sellerName", source = "sellerName")
    @Mapping(target = "sellerMemo", source = "sellerMemo")
    @Mapping(target = "buyerId", source = "buyerId")
    @Mapping(target = "buyerName", source = "buyerName")
    @Mapping(target = "buyerMemo", source = "buyerMemo")
    @Mapping(target = "receiverName", source = "receiverName")
    @Mapping(target = "receiverMobile", source = "receiverMobile")
    @Mapping(target = "receiverProvince", source = "receiverProvince")
    @Mapping(target = "receiverCity", source = "receiverCity")
    @Mapping(target = "receiverDistrict", source = "receiverDistrict")
    @Mapping(target = "receiverAddress", source = "receiverAddress")
    // 金额和时间
    @Mapping(target = "priceTotal", source = "priceTotal")
    @Mapping(target = "payment", source = "payment")
    @Mapping(target = "shippingPayment", source = "shippingPayment")
    // 这里的优惠是平台优惠，而订单服务（自营商城）的优惠是商家优惠，不是平台给的，所以这里设置为0
    @Mapping(target = "discount", constant = "0")
    @Mapping(target = "updateTime", source = "updateTime")
    @Mapping(target = "orderTime", source = "orderTime")
    @Mapping(target = "payTime", source = "payTime")
    @Mapping(target = "shippingTime", source = "shippingTime")
    @Mapping(target = "completedTime", source = "completedTime")
    // 子对象转换
    @Mapping(target = "items", source = "items")
    @Mapping(target = "cipherTexts", source = "source", qualifiedByName = "toCipherString")
    OrderDTO toAdapterOrderDTO(com.xtc.marketing.orderservice.order.dto.OrderDTO source);

    /**
     * 转换统一订单明细
     *
     * @param source 平台订单明细
     * @return 统一订单明细
     */
    @Mapping(target = "orderNo", source = "orderNo")
    @Mapping(target = "itemNo", source = "itemNo")
    @Mapping(target = "itemType", source = "itemType")
    @Mapping(target = "itemState", source = "itemState")
    @Mapping(target = "productId", source = "productId")
    @Mapping(target = "productName", source = "productName")
    @Mapping(target = "productErpCode", source = "productErpCode")
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "skuName", expression = "java(toSkuName(source.getProductName(), source.getSkuName()))")
    @Mapping(target = "skuErpCode", source = "skuErpCode")
    @Mapping(target = "num", source = "num")
    @Mapping(target = "unitPrice", source = "unitPrice")
    @Mapping(target = "priceTotal", source = "priceTotal")
    @Mapping(target = "discount", source = "discount")
    @Mapping(target = "payment", source = "payment")
    OrderItemDTO toAdapterOrderItemDTO(com.xtc.marketing.orderservice.order.dto.OrderItemDTO source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    List<RefundDTO> toAdapterRefundDTO(List<ServiceOrderDTO> source);

    /**
     * 转换统一退款数据
     *
     * @param source 平台退款数据
     * @return 统一退款数据
     */
    // 基础属性
    @Mapping(target = "orderNo", source = "orderNo")
    @Mapping(target = "orderItemNo", source = "orderItemNo")
    @Mapping(target = "serviceNo", source = "serviceNo")
    @Mapping(target = "serviceType", source = "serviceType")
    @Mapping(target = "serviceState", source = "serviceState")
    @Mapping(target = "refundState", source = "refundState")
    @Mapping(target = "refundStateDesc", source = "refundStateDesc")
    @Mapping(target = "applyReason", source = "applyReason")
    @Mapping(target = "sellerId", source = "sellerId")
    @Mapping(target = "sellerName", source = "sellerName")
    @Mapping(target = "sellerMemo", source = "sellerMemo")
    @Mapping(target = "buyerId", source = "buyerId")
    @Mapping(target = "buyerName", source = "buyerName")
    @Mapping(target = "auditorId", source = "auditorId")
    @Mapping(target = "auditorName", source = "auditorName")
    @Mapping(target = "returnWaybillNo", source = "returnWaybillNo")
    @Mapping(target = "returnExpressCompany", source = "returnExpressCompany")
    @Mapping(target = "productId", source = "productId")
    @Mapping(target = "productName", source = "productName")
    @Mapping(target = "skuId", source = "skuId")
    @Mapping(target = "skuName", source = "skuName")
    @Mapping(target = "skuErpCode", source = "skuErpCode")
    @Mapping(target = "num", source = "num")
    // 金额和时间
    @Mapping(target = "refundApplyAmount", source = "refundApplyAmount")
    @Mapping(target = "refundAmount", source = "refundAmount")
    @Mapping(target = "unitPrice", source = "unitPrice")
    @Mapping(target = "payment", source = "payment")
    @Mapping(target = "createTime", source = "createTime")
    @Mapping(target = "auditTime", source = "auditTime")
    @Mapping(target = "refundTime", source = "refundTime")
    @Mapping(target = "returnTime", source = "returnTime")
    RefundDTO toAdapterRefundDTO(ServiceOrderDTO source);

    /**
     * 转换统一订单数据时，设置发票申请数据
     *
     * @param order         统一订单数据
     * @param platformOrder 平台订单数据
     */
    @AfterMapping
    default void setInvoiceApply(@MappingTarget OrderDTO order, com.xtc.marketing.orderservice.order.dto.OrderDTO platformOrder) {
        if (order == null) {
            return;
        }

        // 设置发票申请数据
        if (StringUtils.isNotBlank(platformOrder.getInvoiceInfo())) {
            JsonObject invoice = GsonUtil.jsonToObject(platformOrder.getInvoiceInfo());
            String invoiceTitleType = GsonUtil.getAsString(invoice, "invoiceTitleType");

            InvoiceApplyDTO invoiceApplyDTO = InvoiceApplyDTO.builder()
                    .orderNo(order.getOrderNo())
                    .invoiceType(InvoiceTypeEnum.ELECTRONIC)
                    .invoiceTitleType(InvoiceTitleTypeEnum.valueOf(invoiceTitleType))
                    .invoiceTitle(GsonUtil.getAsString(invoice, "invoiceTitle"))
                    .taxNo(GsonUtil.getAsString(invoice, "taxNumber"))
                    .invoiceUrl(GsonUtil.getAsString(invoice, "invoiceUrl"))
                    .build();
            order.setInvoiceApply(invoiceApplyDTO);
        }
    }

    /**
     * 转换完整的sku名称
     *
     * @param productName 商品名称
     * @param skuName     sku名称
     * @return 完整的sku名称
     */
    @Named("toSkuName")
    default String toSkuName(String productName, String skuName) {
        return productName != null ? productName + skuName : skuName;
    }

    /**
     * 转换加密数据
     *
     * @param platformOrder 平台订单
     * @return 加密字符串
     */
    @Named("toCipherString")
    default List<String> toCipherString(com.xtc.marketing.orderservice.order.dto.OrderDTO platformOrder) {
        XtcShopEncryptQry xtcShopEncryptQry = XtcShopEncryptQry.builder()
                .receiverName(platformOrder.getReceiverName())
                .receiverMobile(platformOrder.getReceiverMobile())
                .receiverAddress(platformOrder.getReceiverAddress())
                .build();
        return Lists.newArrayList(GsonUtil.objectToJson(xtcShopEncryptQry));
    }

}
