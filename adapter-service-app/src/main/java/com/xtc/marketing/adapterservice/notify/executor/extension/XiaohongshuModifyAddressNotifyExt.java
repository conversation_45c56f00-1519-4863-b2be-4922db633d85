package com.xtc.marketing.adapterservice.notify.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.xiaohongshu.fls.opensdk.entity.order.Response.GetOrderReceiverInfoResponse;
import com.xiaohongshu.fls.opensdk.entity.order.Response.OrderReceiverInfo;
import com.xtc.marketing.adapterservice.exception.BizException;
import com.xtc.marketing.adapterservice.notify.converter.XiaohongshuNotifyConverter;
import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.dto.command.XiaohongshuNotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import com.xtc.marketing.adapterservice.rpc.oms.OmsRpc;
import com.xtc.marketing.adapterservice.rpc.oms.omsdto.command.OmsModifyAddressNotifyCmd;
import com.xtc.marketing.adapterservice.rpc.xiaohongshu.XiaohongshuRpc;
import com.xtc.marketing.adapterservice.rpc.xiaohongshu.xiaohongshudto.XiaohongshuModifyAddressNotifyDTO;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.executor.query.ShopGetQryExe;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Optional;

/**
 * 通知扩展点 - 小红书修改地址
 */
@Slf4j
@RequiredArgsConstructor
@Extension(bizId = NotifyExtConstant.BIZ_ID_XIAOHONGSHU,
        useCase = NotifyExtConstant.USE_CASE_SHOP, scenario = NotifyExtConstant.SCENARIO_MODIFY_ADDRESS)
public class XiaohongshuModifyAddressNotifyExt implements NotifyExtPt {

    private final ShopGetQryExe shopGetQryExe;
    private final XiaohongshuRpc xiaohongshuRpc;
    private final XiaohongshuNotifyConverter xiaohongshuNotifyConverter;
    private final OmsRpc omsRpc;

    /**
     * 订单地址变更消息
     */
    private static final String MSG_TAG_ADDRESS_CHANGE = "msg_fulfillment_receiver_change";

    @Override
    public ReceiveLogDO createReceiveLog(NotifyEnum notify, NotifyReceiveCmd cmd) {
        // 参数校验
        Optional<Boolean> checkParamsResult = this.checkParams(cmd);
        if (checkParamsResult.isPresent()) {
            // 返回参数校验失败响应，如果是测试则返回成功，否则返回验签失败
            String responseStr = BooleanUtils.isTrue(checkParamsResult.get()) ? this.responseSuccess() : this.responseSignCheckFailure();
            return ReceiveLogDO.builder().responseStr(responseStr).build();
        }
        // 解析消息体
        XiaohongshuNotifyReceiveCmd notifyCmd = this.convertNotifyReceiveCmd(cmd);
        // 查询店铺，使用消息体里的店铺id
        ShopDO shop = shopGetQryExe.getByShopId(notifyCmd.getSellerId());
        // 签名校验
        try {
            this.checkSign(cmd, shop);
        } catch (Exception e) {
            log.warn("小红书通知签名校验失败 message: {}", e.getMessage(), e);
            return ReceiveLogDO.builder().responseStr(this.responseSignCheckFailure()).build();
        }
        // 仅处理地址变更通知
        if (!MSG_TAG_ADDRESS_CHANGE.equals(notifyCmd.getMsgTag())) {
            log.warn("不处理非地址变更通知 {}", notifyCmd);
            return ReceiveLogDO.builder().responseStr(this.responseSuccess()).build();
        }
        // 修改地址通知数据转换
        Optional<XiaohongshuModifyAddressNotifyDTO> convertNotifyData = this.convertNotifyData(notifyCmd);
        if (!convertNotifyData.isPresent()) {
            log.warn("小红书修改地址通知数据转换失败 {}", notifyCmd);
            return ReceiveLogDO.builder().responseStr(this.responseSuccess()).build();
        }
        // 调用 OMS 系统接口修改地址
        XiaohongshuModifyAddressNotifyDTO modifyAddressDTO = convertNotifyData.get();
        this.omsNotifyModifyAddress(modifyAddressDTO, shop);
        return ReceiveLogDO.builder()
                .dataId(modifyAddressDTO.getOrderId())
                .rawData(cmd.getData())
                .responseStr(this.responseSuccess())
                .build();
    }

    @Override
    public ResponseEntity<String> notifyResponse(String responseStr) {
        return ResponseEntity.ok().body(responseStr);
    }

    @Override
    public Object convertToNotifyBean(String data) {
        return GsonUtil.jsonToObject(data);
    }

    /**
     * 调用 OMS 系统接口修改地址
     *
     * @param modifyAddressDTO 修改地址通知数据
     * @param shop             店铺
     */
    private void omsNotifyModifyAddress(XiaohongshuModifyAddressNotifyDTO modifyAddressDTO, ShopDO shop) {
        GetOrderReceiverInfoResponse getOrderReceiverInfoResponse = xiaohongshuRpc.getOrderReceiver(shop,
                modifyAddressDTO.getOrderId(), modifyAddressDTO.getOpenAddressId());
        if (CollectionUtils.isEmpty(getOrderReceiverInfoResponse.getReceiverInfos())) {
            log.warn("小红书修改地址通知获取收货人信息失败 orderNo: {}, openAddressId: {}",
                    modifyAddressDTO.getOrderId(), modifyAddressDTO.getOpenAddressId());
            throw BizException.of("获取收货人信息失败");
        }
        OrderReceiverInfo newReceiver = getOrderReceiverInfoResponse.getReceiverInfos().get(0);
        OmsModifyAddressNotifyCmd cmd = xiaohongshuNotifyConverter.toOmsModifyAddressNotifyCmd(newReceiver);
        omsRpc.notifyModifyAddress(cmd);
    }

    /**
     * 转换修改地址通知数据
     *
     * @param notifyCmd 小红书消息通知
     * @return 修改地址通知数据
     */
    private Optional<XiaohongshuModifyAddressNotifyDTO> convertNotifyData(XiaohongshuNotifyReceiveCmd notifyCmd) {
        if (notifyCmd == null || StringUtils.isBlank(notifyCmd.getData())) {
            log.warn("小红书改地址通知数据为空");
            return Optional.empty();
        }
        // 解析通知数据
        XiaohongshuModifyAddressNotifyDTO modifyAddressDTO = GsonUtil.jsonToBean(notifyCmd.getData(), XiaohongshuModifyAddressNotifyDTO.class);
        if (modifyAddressDTO == null || StringUtils.isAnyBlank(modifyAddressDTO.getOrderId(), modifyAddressDTO.getOpenAddressId())) {
            return Optional.empty();
        }
        return Optional.of(modifyAddressDTO);
    }

    /**
     * 小红书通知签名校验
     * <p>[{"data":"{\"orderId\":\"P768959627183399481\",\"openAddressId\":\"6e25dda05e0fff6e324da77737a5c01e\",\"packageId\":\"P768959627183399481\",\"updateTime\":1753408057288}","msgTag":"msg_fulfillment_receiver_change","sellerId":"66b48f1ec2125e0015fd5d3f"}]</p>
     *
     * @param cmd  通知接收参数
     * @param shop 店铺
     */
    private void checkSign(NotifyReceiveCmd cmd, ShopDO shop) {
        // 获取请求中的签名相关请求头
        String appKey = cmd.getRequest().getHeader("App-Key");
        String timestamp = cmd.getRequest().getHeader("Timestamp");
        String sign = cmd.getRequest().getHeader("Sign");
        if (StringUtils.isAnyBlank(timestamp, appKey, sign)) {
            log.warn("小红书通知缺少必要的请求头: app-key={}, timestamp={}, sign={}", appKey, timestamp, sign);
            throw BizException.of("缺少必要的请求头");
        }
        // 构建签名字符串并验证
        String expectedSign = xiaohongshuRpc.buildNotifySign(cmd.getRequest(), timestamp, appKey, shop.getAppSecret());
        if (!sign.equals(expectedSign)) {
            log.warn("小红书通知签名验证失败: expected={}, actual={}", expectedSign, sign);
            throw BizException.of("签名验证失败");
        }
    }

    /**
     * 转换小红书消息通知
     *
     * @param cmd 通知接收参数
     * @return 小红书消息通知
     */
    private XiaohongshuNotifyReceiveCmd convertNotifyReceiveCmd(NotifyReceiveCmd cmd) {
        List<XiaohongshuNotifyReceiveCmd> messages = GsonUtil.jsonToList(cmd.getData(), XiaohongshuNotifyReceiveCmd.class);
        if (CollectionUtils.isEmpty(messages)) {
            throw BizException.of("消息体解析失败");
        }
        if (messages.size() > 1) {
            log.warn("小红书通知消息体包含多个消息，当前仅支持单条消息处理 {}", cmd.getData());
        }
        return messages.get(0);
    }

    /**
     * 小红书修改地址通知参数校验
     * <p>测试标识的 body {"test":true}</p>
     *
     * @param cmd 通知接收命令
     * @return 响应实体
     */
    private Optional<Boolean> checkParams(NotifyReceiveCmd cmd) {
        if (StringUtils.isBlank(cmd.getData())) {
            log.warn("小红书通知数据为空");
            return Optional.of(Boolean.FALSE);
        }
        // body 如果是 {"test":true} 返回 true
        Boolean test = GsonUtil.getAsBoolean(cmd.getData(), "test");
        if (BooleanUtils.isTrue(test)) {
            log.info("小红书通知测试数据，直接返回成功");
            return Optional.of(Boolean.TRUE);
        }
        return Optional.empty();
    }

    /**
     * 返回小红书通知的成功响应字符串。
     *
     * @return 成功响应JSON字符串
     */
    private String responseSuccess() {
        return buildResponse(true, 0, "");
    }

    /**
     * 返回小红书通知的验签失败响应字符串。
     *
     * @return 验签失败响应JSON字符串
     */
    private String responseSignCheckFailure() {
        return buildResponse(false, 1001, "验签失败");
    }

    /**
     * 构建小红书响应JSON字符串。
     *
     * @param success   是否成功
     * @param errorCode 错误代码
     * @param errorMsg  错误消息
     * @return JSON响应字符串
     */
    private String buildResponse(boolean success, int errorCode, String errorMsg) {
        String format = "{\"success\": %s, \"error_code\": %d, \"error_msg\": \"%s\"}";
        return String.format(format, success, errorCode, errorMsg);
    }

}
