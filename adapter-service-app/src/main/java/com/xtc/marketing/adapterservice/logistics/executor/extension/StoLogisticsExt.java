package com.xtc.marketing.adapterservice.logistics.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.adapterservice.exception.SysErrorCode;
import com.xtc.marketing.adapterservice.exception.SysException;
import com.xtc.marketing.adapterservice.logistics.converter.StoLogisticsConverter;
import com.xtc.marketing.adapterservice.logistics.dataobject.LogisticsAccountDO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsCloudPrintDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsOrderDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCancelOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCloudPrintCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsOrderGetQry;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsRouteListQry;
import com.xtc.marketing.adapterservice.rpc.sto.StoRpc;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.StoCreateOrderDTO;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.StoRouteDTO;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.command.StoCancelOrderCmd;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.command.StoCreateOrderCmd;
import com.xtc.marketing.adapterservice.rpc.sto.stodto.command.StoInterceptCmd;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Extension(useCase = LogisticsExtConstant.USE_CASE, scenario = LogisticsExtConstant.SCENARIO_STO)
public class StoLogisticsExt implements LogisticsExtPt {

    private final StoRpc stoRpc;
    private final StoLogisticsConverter stoLogisticsConverter;

    @Override
    public List<LogisticsRouteDTO> routes(LogisticsAccountDO account, LogisticsRouteListQry qry) {
        List<StoRouteDTO> routeList = stoRpc.searchRoutes(account, qry.getWaybillNo());
        return stoLogisticsConverter.toLogisticsRoute(routeList);
    }

    @Override
    public LogisticsOrderDTO createOrder(LogisticsAccountDO account, LogisticsCreateOrderCmd cmd) {
        StoCreateOrderCmd order = stoLogisticsConverter.toStoCreateOrder(cmd);
        StoCreateOrderDTO stoCreateOrder = stoRpc.createOrder(account, order);
        return LogisticsOrderDTO.builder()
                .wayBillNo(stoCreateOrder.getWaybillNo())
                .orderDetail(GsonUtil.objectToJson(stoCreateOrder))
                .build();
    }

    @Override
    public String getOrder(LogisticsAccountDO account, LogisticsOrderGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public String getWaybillNo(LogisticsAccountDO account, LogisticsOrderGetQry qry) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

    @Override
    public boolean cancelOrder(LogisticsAccountDO account, LogisticsCancelOrderCmd cmd) {
        StoCancelOrderCmd cancelOrderCmd = stoLogisticsConverter.toStoCancelOrderCmd(cmd);
        return stoRpc.cancelOrder(account, cancelOrderCmd);
    }

    @Override
    public boolean intercept(LogisticsAccountDO account, LogisticsInterceptCmd cmd) {
        StoInterceptCmd interceptCmd = stoLogisticsConverter.toStoInterceptCmd(cmd);
        stoRpc.intercept(account, interceptCmd);
        return true;
    }

    @Override
    public LogisticsCloudPrintDTO cloudPrint(LogisticsAccountDO account, LogisticsCloudPrintCmd cmd) {
        throw SysException.of(SysErrorCode.S_RPC_NotSupport);
    }

}
