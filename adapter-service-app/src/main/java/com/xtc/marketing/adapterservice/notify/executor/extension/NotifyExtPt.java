package com.xtc.marketing.adapterservice.notify.executor.extension;

import com.alibaba.cola.extension.ExtensionPointI;
import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletRequest;

/**
 * 通知扩展点
 */
public interface NotifyExtPt extends ExtensionPointI {

    /**
     * 生成接收记录
     *
     * @param notify 通知
     * @param cmd   数据
     * @return 接收记录
     */
    ReceiveLogDO createReceiveLog(NotifyEnum notify, NotifyReceiveCmd cmd);

    /**
     * 获取通知响应结果
     *
     * @param responseStr 响应结果（传递 createReceiveLog 方法生成的 responseStr）
     * @return 通知响应结果
     */
    ResponseEntity<String> notifyResponse(String responseStr);

    /**
     * 转换通知数据字符串转换为 bean 对象
     *
     * @param data 数据
     * @return 转换后的 bean 对象
     */
    Object convertToNotifyBean(String data);

}
