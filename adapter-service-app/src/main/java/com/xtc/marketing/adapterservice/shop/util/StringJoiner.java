package com.xtc.marketing.adapterservice.shop.util;

import com.beust.jcommander.internal.Sets;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

/**
 * 字符串拼接工具
 */
public class StringJoiner {

    private StringJoiner() {
    }

    /**
     * 拼接两个字符串集合，默认以英文逗号分隔
     *
     * @param currentStr 当前字符串集合
     * @param newStr     新字符串集合
     * @return 拼接后字符串
     */
    public static String concatWithSplit(String currentStr, String newStr) {
        if (StringUtils.isBlank(newStr)) {
            return currentStr;
        }
        Set<String> result = Sets.newHashSet();
        // 当前标签添加到标签集合中
        if (StringUtils.isNotBlank(currentStr)) {
            result.addAll(StringSplitter.split(currentStr));
        }
        // 新标签添加到标签集合中
        result.addAll(StringSplitter.split(newStr));
        // 标签集合拼接成字符串
        return String.join(",", result);
    }

}
