package com.xtc.marketing.invoiceservice.api;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.dto.Response;
import com.xtc.marketing.dto.SingleResponse;
import com.xtc.marketing.invoiceservice.invoice.BuyerAppService;
import com.xtc.marketing.invoiceservice.invoice.dto.BuyerDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.BuyerCreateCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.BuyerEditCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.BuyerPageQry;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 购买方接口
 */
@Validated
@RequiredArgsConstructor
@RequestMapping("/api")
@RestController
public class BuyerApiController {

    private final BuyerAppService buyerAppService;

    /**
     * 购买方分页列表
     *
     * @param qry 参数
     * @return 购买方分页列表
     */
    @GetMapping("/buyers")
    public PageResponse<BuyerDTO> pageBuyers(@Valid BuyerPageQry qry) {
        return buyerAppService.pageBuyers(qry);
    }

    /**
     * 购买方详情
     *
     * @param id 购买方id
     * @return 购买方详情
     */
    @GetMapping("/buyers/{id}")
    public SingleResponse<BuyerDTO> buyerDetail(@NotNull @Positive @PathVariable("id") Long id) {
        BuyerDTO detail = buyerAppService.buyerDetail(id);
        return SingleResponse.of(detail);
    }

    /**
     * 新增购买方
     *
     * @param cmd 参数
     * @return 购买方id
     */
    @PostMapping("/buyers")
    public SingleResponse<Long> createBuyer(@Valid @RequestBody BuyerCreateCmd cmd) {
        Long id = buyerAppService.createBuyer(cmd);
        return SingleResponse.of(id);
    }

    /**
     * 修改购买方
     *
     * @param id  购买方id
     * @param cmd 参数
     * @return 执行结果
     */
    @PutMapping("/buyers/{id}")
    public Response editBuyer(@NotNull @Positive @PathVariable("id") Long id,
                              @Valid @RequestBody BuyerEditCmd cmd) {
        buyerAppService.editBuyer(id, cmd);
        return Response.buildSuccess();
    }

    /**
     * 删除购买方
     *
     * @param id 购买方id
     * @return 执行结果
     */
    @DeleteMapping("/buyers/{id}")
    public Response removeBuyer(@NotNull @Positive @PathVariable("id") Long id) {
        buyerAppService.removeBuyer(id);
        return Response.buildSuccess();
    }

    /**
     * 购买方导出任务
     *
     * @return 执行结果
     */
    @PostMapping("/buyer/export")
    public Response exportBuyer() {
        buyerAppService.exportBuyer();
        return Response.buildSuccess();
    }

    /**
     * 购买方导入任务
     *
     * @param file 文件
     * @return 执行结果
     */
    @PostMapping("/buyer/import")
    public Response importBuyer(@NotNull @RequestParam("file") MultipartFile file) {
        buyerAppService.importBuyer(file);
        return Response.buildSuccess();
    }

}
