package com.xtc.marketing.invoiceservice.invoice;

import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.invoiceservice.invoice.dto.BuyerDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.BuyerCreateCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.BuyerEditCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.BuyerPageQry;
import org.springframework.web.multipart.MultipartFile;

/**
 * 购买方应用服务接口
 */
public interface BuyerAppService {

    /**
     * 购买方分页列表
     *
     * @param qry 参数
     * @return 购买方分页列表
     */
    PageResponse<BuyerDTO> pageBuyers(BuyerPageQry qry);

    /**
     * 购买方详情
     *
     * @param id 购买方id
     * @return 购买方详情
     */
    BuyerDTO buyerDetail(long id);

    /**
     * 新增购买方
     *
     * @param cmd 参数
     * @return 购买方id
     */
    Long createBuyer(BuyerCreateCmd cmd);

    /**
     * 修改购买方
     *
     * @param id  购买方id
     * @param cmd 参数
     */
    void editBuyer(long id, BuyerEditCmd cmd);

    /**
     * 删除购买方
     *
     * @param id 购买方id
     */
    void removeBuyer(long id);

    /**
     * 购买方导出任务
     */
    void exportBuyer();

    /**
     * 导入购买方
     *
     * @param file 文件
     */
    void importBuyer(MultipartFile file);

}
