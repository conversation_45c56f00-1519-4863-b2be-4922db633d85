package com.xtc.marketing.invoiceservice.invoice;

import com.mybatisflex.core.paginate.Page;
import com.xtc.marketing.dto.PageResponse;
import com.xtc.marketing.invoiceservice.context.UserContext;
import com.xtc.marketing.invoiceservice.exception.BizErrorCode;
import com.xtc.marketing.invoiceservice.exception.BizException;
import com.xtc.marketing.invoiceservice.invoice.converter.BuyerConverter;
import com.xtc.marketing.invoiceservice.invoice.dao.BuyerDao;
import com.xtc.marketing.invoiceservice.invoice.dataobject.BuyerDO;
import com.xtc.marketing.invoiceservice.invoice.dto.BuyerDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.BuyerCreateCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.command.BuyerEditCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.BuyerPageQry;
import com.xtc.marketing.invoiceservice.invoice.exceldto.BuyerExportDTO;
import com.xtc.marketing.invoiceservice.invoice.exceldto.BuyerImportDTO;
import com.xtc.marketing.invoiceservice.support.domainservice.TaskService;
import com.xtc.marketing.invoiceservice.support.dto.BaseDTO;
import com.xtc.marketing.invoiceservice.util.BeanCopier;
import com.xtc.marketing.invoiceservice.util.CollectionCopier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 购买方应用服务实现类
 * <p>简单逻辑 10 行以内的代码可以直接在 service 方法内实现</p>
 * <p>复杂逻辑（或可复用逻辑）封装在 executor 里实现，调用链路：service > executor</p>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BuyerAppServiceImpl implements BuyerAppService {

    // 基础设施注入
    private final BuyerDao buyerDao;
    private final BuyerConverter buyerConverter;
    // domainservice
    private final TaskService taskService;

    @Override
    public PageResponse<BuyerDTO> pageBuyers(BuyerPageQry qry) {
        Page<BuyerDO> page = buyerDao.pageBy(qry);
        List<BuyerDTO> records = buyerConverter.doToDto(page.getRecords());
        return PageResponse.of(records, page.getTotalRow(), page.getPageSize(), page.getPageNumber());
    }

    @Override
    public BuyerDTO buyerDetail(long id) {
        BuyerDO buyerDO = buyerDao.getByIdOpt(id)
                .orElseThrow(() -> BizException.of(BizErrorCode.B_BUYER_BuyerNotExists));
        return buyerConverter.doToDto(buyerDO);
    }

    @Override
    public Long createBuyer(BuyerCreateCmd cmd) {
        // 参数校验
        boolean existsByBuyerCode = buyerDao.existsByBuyerCode(cmd.getBuyerCode());
        if (existsByBuyerCode) {
            throw BizException.of("购买方代码已存在");
        }
        // 新增购买方
        BuyerDO buyerDO = buyerConverter.cmdToDo(cmd);
        buyerDao.save(buyerDO);
        return buyerDO.getId();
    }

    @Override
    public void editBuyer(long id, BuyerEditCmd cmd) {
        // 参数校验
        boolean notExists = buyerDao.getByIdOpt(id).isEmpty();
        if (notExists) {
            throw BizException.of(BizErrorCode.B_BUYER_BuyerNotExists);
        }
        if (StringUtils.isNotBlank(cmd.getBuyerCode())) {
            boolean existsByBuyerCode = buyerDao.existsByCodeExcludeId(cmd.getBuyerCode(), id);
            if (existsByBuyerCode) {
                throw BizException.of("购买方代码已存在");
            }
        }
        // 编辑购买方
        BuyerDO buyerDO = BeanCopier.copy(cmd, BuyerDO::new);
        buyerDao.updateById(id, buyerDO);
    }

    @Override
    public void removeBuyer(long id) {
        // 参数校验
        boolean notExists = buyerDao.getByIdOpt(id).isEmpty();
        if (notExists) {
            throw BizException.of(BizErrorCode.B_BUYER_BuyerNotExists);
        }
        // 删除购买方
        buyerDao.removeById(id);
    }

    @Override
    public void exportBuyer() {
        long total = buyerDao.countForExport();
        taskService.submitExportTask(
                "购买方导出",
                null,
                total,
                BuyerExportDTO.class,
                lastData -> {
                    Long lastId = lastData != null ? lastData.getId() : null;
                    List<BuyerDO> buyers = buyerDao.listForExport(lastId);
                    return CollectionCopier.copy(buyers, BuyerExportDTO::new);
                }
        );
    }

    @Override
    public void importBuyer(MultipartFile file) {
        UserContext.User user = UserContext.getUser();
        BaseDTO.OperatorDTO operatorDTO = BaseDTO.OperatorDTO.of(user.getEmployeeId(), user.getUserName());
        taskService.submitImportTask(
                "购买方导入",
                file,
                BuyerImportDTO.class,
                dto -> {
                    BuyerCreateCmd cmd = BeanCopier.copy(dto, BuyerCreateCmd::new);
                    // 默认启用
                    cmd.setEnabled(true);
                    cmd.setUpdateBy(operatorDTO);
                    cmd.setCreateBy(operatorDTO);
                    this.createBuyer(cmd);
                });
    }

}
