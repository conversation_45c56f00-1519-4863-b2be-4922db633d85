<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xtc.onlineretailers.mapper.erp.ERPMapper">
    <update id="updateSdbg">
        begin BBKEDU_OM_EC_PKG.so_ship_p
        (
        #{platformTradeId}
        );
        end;
    </update>
    <select id="getErpDetail" resultType="com.xtc.onlineretailers.pojo.vo.ErpDataDetailVO">
        <!--SELECT *
        FROM
        (
          SELECT
            A.*,
            ROWNUM RN
          FROM
          (-->
        select *
        from
        (
        select
        a.EC_ORDERNUM as platformTradeId,
        a.ERP_ORDERNUMBER as erpId,
        d.customer_number as customerNumber,
        d.customer_name as customerName,
        c.SEGMENT1 as productId,
        c.DESCRIPTION as shortName,
        b.price as price,
        b.transaction_quantity as num,
        a.TRAN_DATE as addDate,
        a.subinv_code as subinvCode,
        a.locator_id as locatorId
        from BBKEDU_OM_EC_TEMP_HEADER a
        inner join BBKEDU_OM_EC_TEMP_line b on a.ec_ordernum=b.ec_ordernum
        inner join MTL_SYSTEM_ITEMS_B c on b.segment1=c.segment1 and c.ORGANIZATION_ID =
        BBKEDU_FIXVALUE_PKG.get_edu_org_id
        inner join BBKEDU_CUSTOMERS_V d on a.cust_number = d.customer_number
        )a
        where 1=1
        <if test="query.keyword != null and query.keyword  != ''">
            and (a.platformTradeId = #{query.keyword} or a.erpId=#{query.keyword})
        </if>
        <if test="query.subinvCode != null and query.subinvCode  != ''">
            and a.subinvCode = #{query.subinvCode}
        </if>
        <if test="query.productId != null and query.productId  != ''">
            and a.productId = #{query.productId}
        </if>
        <if test="query.batchNo != null and query.batchNo  != ''">
            and a.platformTradeId like '%'||#{query.batchNo}||'%'
        </if>
        <if test="query.beginDate != null and query.beginDate != ''">
            and a.addDate>=to_date(#{query.beginDate},'yyyy-MM-dd')
        </if>
        <if test="query.endDate != null and query.endDate != ''">
            <![CDATA[ and a.addDate<=to_date(#{query.endDate}||' 23:59:59','yyyy-MM-dd HH24:mi:ss') ]]>
        </if>
        order by a.addDate desc
        <!--) A
      )-->
    </select>
    <select id="getErpStatistical" resultType="com.xtc.onlineretailers.pojo.vo.ErpStatisticalVO">
        <!--SELECT *
            FROM
            (
              SELECT
                A.*,
                ROWNUM RN
              FROM
              (-->
        select
        subinv_code as subinvCode,
        料号 as productId,
        描述 as shortName,
        数量 as num
        from
        (
        select
        subinv_code,
        料号,
        描述,
        sum(数量) as 数量
        from
        (
        select
        a.subinv_code,
        <!--a.EC_ORDERNUM 电商订单号,-->
        <!--a.ERP_ORDERNUMBER ERP订单号,-->
        <!--d.customer_number 客户编号,-->
        <!--d.customer_name 客户名称,-->
        c.SEGMENT1 料号,
        c.DESCRIPTION 描述,
        <!--b.price 价格,-->
        b.transaction_quantity 数量,
        a.TRAN_DATE 日期
        from BBKEDU_OM_EC_TEMP_HEADER a
        inner join BBKEDU_OM_EC_TEMP_line b on a.ec_ordernum=b.ec_ordernum
        inner join MTL_SYSTEM_ITEMS_B c on b.segment1=c.segment1 and c.ORGANIZATION_ID =
        BBKEDU_FIXVALUE_PKG.get_edu_org_id
        <!--inner join BBKEDU_CUSTOMERS_V d on a.cust_number = d.customer_number-->
        ) a
        where 1=1
        <if test="query.beginDate != null and query.beginDate != ''">
            and 日期>=to_date(to_char(#{query.beginDate}),'yyyy-MM-dd')
        </if>
        <if test="query.endDate != null and query.endDate != ''">
            <![CDATA[ and 日期<=to_date(to_char(#{query.endDate})||' 23:59:59','yyyy-MM-dd HH24:mi:ss') ]]>
        </if>
        and 数量 > 0
        group by 料号,描述,subinv_code
        <!--union all-->
        <!--select-->
        <!--v.仓库 as subinv_code,-->
        <!--v.料号 as 料号,-->
        <!--v.描述 as 描述,-->
        <!--sum(v.数量) as 数量-->
        <!--from apps.BBKEDU_EC_FIN_V v-->
        <!--where-->
        <!--<![CDATA[ v.数量 < 0 ]]>-->
        <!--<if test="query.beginDate != null and query.beginDate != ''">-->
        <!--and 日期>=to_date(to_char(#{query.beginDate}),'yyyy-MM-dd')-->
        <!--</if>-->
        <!--<if test="query.endDate != null and query.endDate != ''">-->
        <!--<![CDATA[ and 日期<=to_date(to_char(#{query.endDate})||' 23:59:59','yyyy-MM-dd HH24:mi:ss') ]]>-->
        <!--</if>-->
        <!--group by v.仓库,v.料号,v.描述-->
        ) s
        where 1=1
        <if test="query.subinvCode != null and query.subinvCode != ''">
            and subinv_code =#{query.subinvCode}
        </if>
        order by subinvCode,productId
        <!--    ) A
          )-->
    </select>
    <select id="getErpSelfCheck" resultType="com.xtc.onlineretailers.pojo.vo.ErpSelfCheckVO">
        select distinct
        组织 as organization,
        电商订单号 as platformTradeId,
        ERP订单号 as erpId,
        客户 as customerNumber,
        客户名称 as customerName,
        状态 as status,
        物料 as productId,
        描述 as shortName,
        订单数量 as num
        from
        apps.BBKEDU_EC_UNDONORDER_V
        <!--(-->
        <!--select distinct *-->
        <!--from apps.BBKEDU_EC_UNDONORDER_V-->
        <!--)-->
        where 1=1
        <if test="query.keyword != null and query.keyword != ''">
            and (电商订单号=#{query.keyword} or ERP订单号=#{query.keyword})
        </if>
        order by 物料
    </select>
    <select id="getErpTakeGoods" resultType="com.xtc.onlineretailers.pojo.vo.ErpTakeGoodsVO">
        <!--<if test="page != null">-->
        <!--SELECT *-->
        <!--FROM (-->
        <!--SELECT A.*,-->
        <!--ROWNUM RN-->
        <!--FROM (-->
        <!--</if>-->
        SELECT
        来源组织 as fromOrganization,
        调入组织 as toOrganization,
        物料代码 as productId,
        描述 as shortName,
        调出仓库 as outStore,
        调入仓库 as inStore,
        数量 as num,
        交易日期 as addDate
        FROM BBK_TCX_TRANSAF_V
        where 1=1
        <if test="query.beginDate != null and query.beginDate != ''">
            and 交易日期>=to_date(#{query.beginDate},'yyyy-MM-dd')
        </if>
        <!--交易日期>=to_date(#{query.beginDate},'yyyy-MM-dd')-->
        <if test="query.fromOrganization != null and query.fromOrganization != ''">
            and 来源组织=#{query.fromOrganization}
        </if>
        <if test="query.toOrganization != null and query.toOrganization != ''">
            and 调入组织=#{query.toOrganization}
        </if>
        <if test="query.productId != null and query.productId != ''">
            and 物料代码=#{query.productId}
        </if>
        <if test="query.endDate != null and query.endDate != ''">
            <!--<![CDATA[ and 交易日期<=to_date(#{query.endDate},'yyyy-MM-dd') ]]>-->
            <![CDATA[ and 交易日期<=to_date(#{query.endDate},'yyyy-MM-dd') ]]>
        </if>
        order by 交易日期 desc
        <!--<if test="page != null">-->
        <!--) A-->
        <!--<![CDATA[ WHERE ROWNUM <= #{page.endNo} ]]>-->
        <!--)-->
        <!--WHERE RN >= #{page.beginNo}-->
        <!--</if>-->
    </select>
    <select id="getErpSendSave" resultType="com.xtc.onlineretailers.pojo.vo.ErpSendSaveVO">
        <!--<if test="page != null">-->
        <!--SELECT *-->
        <!--FROM (-->
        <!--SELECT A.*,-->
        <!--ROWNUM RN-->
        <!--FROM (-->
        <!--</if>-->
        select
        ERP订单 erpId,
        客户编码 customerCode,
        客户名称 customerName,
        料号 productId,
        描述 shortName,
        仓库 store,
        数量 num,
        过账日期 erpDate
        from BBKEDU_EC_TCX_RMA_V v
        where 1=1
        <if test="query.erpId  != null and query.erpId  != ''">
            and ERP订单 = #{query.erpId}
        </if>
        <if test="query.keyword != null and query.keyword  != ''">
            and (料号 like '%'||#{query.keyword}||'%' or 描述 like '%'||#{query.keyword}||'%')
        </if>
        <if test="query.beginDate != null and query.beginDate != ''">
            and 过账日期 >=to_date(#{query.beginDate},'yyyy-MM-dd')
        </if>
        <if test="query.endDate != null and query.endDate != ''">
            <![CDATA[ and 过账日期<=to_date(#{query.endDate}||' 23:59:59','yyyy-MM-dd HH24:mi:ss') ]]>
        </if>
        order by 过账日期 desc,料号
        <!--<if test="page != null">-->
        <!--) A-->
        <!--<![CDATA[-->
        <!--WHERE ROWNUM <= #{page.endNo}-->
        <!--]]>-->
        <!--) WHERE RN >= #{page.beginNo}-->
        <!--</if>-->
    </select>
    <select id="getErpAllot" resultType="com.xtc.onlineretailers.pojo.vo.ErpAllotVO">
        <!--<if test="page != null">-->
        <!--SELECT *-->
        <!--FROM (-->
        <!--SELECT-->
        <!--A.*,-->
        <!--ROWNUM RN-->
        <!--FROM (-->
        <!--</if>-->
        select
        ERP订单 erpId,
        业务类型 type,
        料号 productId,
        描述 shortName,
        源仓库 oldStore,
        源储位 oldStorePlace,
        到仓库 newStore,
        数量 num,
        日期 erpDate
        from BBKEDU_EC_TCX_MOVE_V v
        where 1=1
        <if test="query.erpId  != null and query.erpId  != ''">
            and ERP订单 = #{query.erpId}
        </if>
        <if test="query.keyword != null and query.keyword  != ''">
            and (料号 like '%'||#{query.keyword}||'%' or 描述 like '%'||#{query.keyword}||'%')
        </if>
        <if test="query.beginDate != null and query.beginDate != ''">
            and 日期 >=to_date(#{query.beginDate},'yyyy-MM-dd')
        </if>
        <if test="query.endDate != null and query.endDate != ''">
            <![CDATA[ and 日期<=to_date(#{query.endDate}||' 23:59:59','yyyy-MM-dd HH24:mi:ss') ]]>
        </if>
        order by 日期 desc,料号
        <!--<if test="page != null">-->
        <!--) A-->
        <!--<![CDATA[-->
        <!--WHERE ROWNUM <= #{page.endNo}-->
        <!--]]>-->
        <!--) WHERE RN >= #{page.beginNo}-->
        <!--</if>-->

    </select>
    <select id="getErpCount" resultType="int" parameterType="String">
        select count(*)
        from bbkedu_om_ec_temp_header h
        where h.ec_ordernum = #{tid}
    </select>
    <select id="getOldForNewCount" resultType="int" parameterType="String">
        select count(*)
        from bbkedu_mes_txn_headers h
        where asnno is not null
          and h.asnno = #{assno jdbcType=VARCHAR}
    </select>
    <select id="getOldForNewHeaderId" resultType="int">
        SELECT bbkedu_mes_txn_headers_s.nextval
        FROM DUAL
    </select>
    <insert id="insertOldForNewHeader" parameterType="com.xtc.onlineretailers.pojo.vo.ErpOldForNewHeaderVO">
        insert into bbkedu_mes_txn_headers(header_id, organization_id, transaction_type_id, transaction_type_name,
                                           asnno, created_by, attribute1)
        values (#{header_id}, #{organization_id}, #{transaction_type_id}, #{transaction_type_name}, #{asnno},
                #{created_by}, #{attribute1})
    </insert>
    <insert id="insertOldForNewLine" parameterType="com.xtc.onlineretailers.pojo.vo.ErpOldForNewLineVO">
        insert into bbkedu_mes_txn_lines(header_id, line_id, itemcode, quantity, subinventory_code, created_by)
        values (#{header_id}, bbkedu_mes_txn_lines_s.nextval, #{itemcode}, #{quantity}, #{subinventory_code}, 1)
    </insert>
    <insert id="insertHeader" parameterType="com.xtc.onlineretailers.pojo.vo.ErpHeaderVO">
        insert into apps.BBKEDU_OM_EC_TEMP_HEADER(ec_ordernum, organization_id, ou_id, cust_id, cust_number, qplist_id,
                                                  transtype_id, subinv_code, ordertype, locator_id, tran_date, status,
                                                  partial_return_flag)
        values (#{ec_ordernum,jdbcType=VARCHAR}, #{organization_id,jdbcType=VARCHAR}, #{ou_id,jdbcType=VARCHAR},
                #{cust_id,jdbcType=VARCHAR},
                #{cust_number,jdbcType=VARCHAR}, #{qplist_id,jdbcType=VARCHAR}, #{transtype_id,jdbcType=VARCHAR},
                #{subinv_code,jdbcType=VARCHAR},
                #{orderType,jdbcType=VARCHAR}, #{locator_id,jdbcType=VARCHAR}, sysdate, 0,
                #{partial_return_flag,jdbcType=VARCHAR})
    </insert>
    <insert id="insertLine" parameterType="com.xtc.onlineretailers.pojo.vo.ErpLineVO">
        insert into apps.BBKEDU_OM_EC_TEMP_LINE(ec_ordernum, qplist_id, segment1, transaction_quantity, status, price,
                                                isgive, discount)
        values (#{ec_ordernum,jdbcType=VARCHAR}, #{qplist_id,jdbcType=VARCHAR}, #{segment1,jdbcType=VARCHAR},
                #{transaction_quantity,jdbcType=VARCHAR}, 0, #{price,jdbcType=VARCHAR}, #{isgive,jdbcType=VARCHAR},
                #{discount,jdbcType=VARCHAR})
    </insert>

    <select id="getErpStock" resultType="int" parameterType="map">
        select nvl(count,0) from ( select sum(数量) as count from apps.BBKEDU_OM_TAOBAO_ONHAND_V where 1=1
        <if test="location != null and location != ''">
            and 储位 = #{location jdbcType=VARCHAR}
        </if>
        <if test="subinv_code != null and subinv_code != ''">
            and 子库存 = #{subinv_code jdbcType=VARCHAR}
        </if>
        <if test="proId != null and proId != ''">
            and 物料 = #{proId jdbcType=VARCHAR}
        </if>
        <if test="organization != null and organization != ''">
            and 组织= #{organization jdbcType=VARCHAR}
        </if>
        )
    </select>

    <select id="updateManualSubmit" parameterType="String">
        begin BBKEDU_OM_EC_PKG.so_ship_p
        (
        #{platformTradeId}
        );
        end;
    </select>
    <select id="getOldForNewErpIdByTradeId" parameterType="String" resultType="String">
        select erp_txn_no
        from bbkedu_mes_txn_headers h
        where h.asnno = #{asnno}
    </select>

    <select id="getErpIdByTradeId" parameterType="String" resultType="String">
        select ERP_ORDERNUMBER
        from BBKEDU_OM_EC_TEMP_HEADER
        where ec_ordernum = #{platformTradeId}
    </select>

    <select id="getErpTempReportsForm" resultType="com.xtc.onlineretailers.pojo.vo.ErpTempReportFormVO">
        select
        h.ec_ordernum platformTradeId,h.cust_number customerNumber,to_char(h.tran_date,'yyyy-MM-dd HH24:mi:ss')
        erpPostTime,decode(h.ordertype,1,'发货',2,'退货') tradeType,h.erp_ordernumber erpOrderNumber,
        l.segment1 erpCode,l.transaction_quantity num,l.price price,
        decode(l.isgive,0,'正常',1,'折扣赠送',2,'单纯赠品') orderType,l.discount discount
        from
        Bbkedu_Om_Ec_Temp_Header h,Bbkedu_Om_Ec_Temp_line l
        where
        h.ec_ordernum = l.ec_ordernum
        <if test="query.beginDate != null and query.beginDate  != ''">
            and h.tran_date >= to_date(#{query.beginDate},'yyyy-MM-dd')
        </if>
        <if test="query.endDate != null and query.endDate  != ''">
            and to_date(#{query.endDate},'yyyy-MM-dd HH24:mi:ss') >= h.tran_date
        </if>
        order by h.tran_date
    </select>

    <select id="getErpPostAbnormalOrder" resultType="String">
        select ec_ordernum
        from bbkedu_om_ec_temp_header h
        where h.status = 0
          and sysdate - 1 / 12 >= h.tran_date
    </select>

    <select id="listDueReMainAmountByCustomerCode" resultType="com.xtc.onlineretailers.pojo.vo.ErpMoneyVO">
        select DUE_REMAIN_AMOUNT as amount, CUSTOMER_NUMBER as customerCode
        from cux_ar_ec_cus_v

        <if test="customerCode!=null">
            <where>
                CUSTOMER_NUMBER in
                <foreach collection="customerCode" item="code" open="(" separator="," close=")">
                    #{code}
                </foreach>
            </where>
        </if>

    </select>

    <select id="listTradIdByTranDateAndErpIdOrderNumber" resultType="java.lang.String">
        select EC_ORDERNUM
        from BBKEDU_OM_EC_TEMP_HEADER
        where TRAN_DATE &lt;#{createTime}
        and ERP_ORDERNUMBER=#{erpId}
        order by TRAN_DATE desc

    </select>

</mapper>