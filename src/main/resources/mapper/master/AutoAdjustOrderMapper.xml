<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xtc.onlineretailers.mapper.master.AutoAdjustOrderMapper">

    <select id="syncAutoAdjustOrders" resultType="com.xtc.onlineretailers.pojo.entity.AutoAdjustOrderDO">
        select r.platform_id,r.platform_name,r.platform_trade_id, t.payment totalFee,t.point_alipay_jf,t.point_platform,
        r.refund_fee, refund_create_time refundTime
        from t_platform_refund r
        left JOIN t_trade t on t.platform_trade_id = r.platform_trade_id
        where r.platform_name in ('天猫步步高','天猫小天才')
        and r.refund_create_time >= #{startTime}
        and #{endTime} > r.refund_create_time
        and r.refund_status = 'SUCCESS'
        and r.total_fee != r.refund_fee
    </select>

    <select id="selectAdjustOrders" resultType="com.xtc.onlineretailers.pojo.entity.AutoAdjustOrderDO">
        SELECT platform_trade_id, Max(total_fee) totalFee, SUM(refund_fee) refundFee, MAX(refund_time) refundTime
        FROM `t_auto_adjust_order`
        where status = 0
        <if test="ids != null">
            <foreach collection="ids" item="item" open="and id in (" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by platform_trade_id
        order by MAX(refund_time) desc
    </select>

</mapper>