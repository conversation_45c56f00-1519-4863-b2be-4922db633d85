<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--suppress ALL -->
<mapper namespace="com.xtc.onlineretailers.mapper.master.MallStoreOrderMapper">

    <select id="getExportList" parameterType="com.xtc.onlineretailers.pojo.query.MallStoreOrderQuery"
            resultType="com.xtc.onlineretailers.pojo.vo.MallStoreOrderExportVO">
        select t1.*, t2.material_code, t2.material_name, t2.material_type, t2.price, t2.unit, t2.apply_num, t2.actual_num
        from t_mall_store_order t1
        left join t_mall_store_order_item t2 on t1.order_no = t2.order_no
        where 1=1
        <if test="query.orderNo != null and query.orderNo != ''">
            and t1.order_no = #{query.orderNo}
        </if>
        <if test="query.type != null">
            and t1.type = #{query.type}
        </if>
        <if test="query.status != null">
            and t1.status = #{query.status}
        </if>
        <if test="query.confirmTimeStart != null and query.confirmTimeStart != ''">
            and t1.confirm_time >= concat(#{query.confirmTimeStart}, ' 00:00:00')
        </if>
        <if test="query.confirmTimeEnd != null and query.confirmTimeEnd != ''">
            and concat(#{query.confirmTimeEnd}, ' 23:59:59') >= t1.confirm_time
        </if>
        <if test="query.createTimeStart != null and query.createTimeStart != ''">
            and t1.create_time >= concat(#{query.createTimeStart}, ' 00:00:00')
        </if>
        <if test="query.createTimeEnd != null and query.createTimeEnd != ''">
            and concat(#{query.createTimeEnd}, ' 23:59:59') >= t1.create_time
        </if>
        order by t1.create_time desc
    </select>

</mapper>