<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--suppress ALL -->
<mapper namespace="com.xtc.onlineretailers.mapper.master.RolePlatformMapper">

    <select id="getPlatformsByRoles" resultType="com.xtc.onlineretailers.pojo.dto.RolePlatformDTO">
        select t1.id, t1.role_id, t2.display_name roleName, t2.code roleCode, t1.platform_id, t3.platform_name,
        t3.agent_category
        from t_admin_role_platform t1
        left join t_admin_roles t2 on t1.role_id = t2.id
        left join t_platform t3 on t1.platform_id = t3.platform_id
        where t1.deleted = 0 and t2.deleted = 0 and t3.is_use = 1
        and t2.code in
        <foreach collection="query.roleCodes" item="role" open="(" separator="," close=")">
            #{role}
        </foreach>
    </select>
    <select id="getRoles" resultType="com.xtc.onlineretailers.pojo.dto.RolePlatformDTO">
        select DISTINCT t1.id as role_id, t1.display_name roleName, t1.`code` roleCode
        from t_admin_role_platform t,
             t_admin_roles t1
        where t.role_id = t1.id
    </select>
    <select id="getHideRoles" resultType="java.lang.String">
        SELECT `code`
        from t_admin_roles
        where is_amount_hide = 1
    </select>

    <select id="listByShowHide" resultType="java.lang.Integer">
        SELECT `is_amount_hide`
        from t_admin_roles
        where code in
        <foreach collection="roles" item="role" open="(" separator="," close=")">
            #{role}
        </foreach>
    </select>
</mapper>