<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<mapper namespace="com.xtc.onlineretailers.mapper.master.EfficiencyStatisticsMapper">

    <select id="getStatisticsData" resultType="com.xtc.onlineretailers.pojo.vo.EfficiencyStatisticsVO">
        select
        operator,
        count(case when operation_type = '发货' then 1 else null end) as shippingCount,
        count(case when operation_type = '退货' then 1 else null end) as returnCount
        from (select t2.platform_trade_id, t2.sorter operator, t3.shipping_time, null return_time, '发货' operation_type
        from (select t.platform_trade_id, t1.sorter from t_trade t
        left join t_order_barcode t1 on t1.platform_trade_id = t.platform_trade_id
        where t.shipping_time >= #{query.shippingDateStart} and #{query.shippingDateEnd} >= t.shipping_time and
        t1.sorter is not null
        group by t.platform_trade_id,t1.sorter) t2
        inner join t_trade t3 on t3.platform_trade_id = t2.platform_trade_id
        UNION ALL
        select t4.platform_trade_id, t4.scanner, null, t4.create_time, '退货' from t_return_master t4
        where t4.create_time >= #{query.returnDateStart} and #{query.returnDateEnd} >= t4.create_time and t4.scanner is
        not null) t5
        <trim prefix="where" prefixOverrides="and |or ">
            <if test="query.operationType != null and query.operationType != ''">
                and t5.operation_type = #{query.operationType}
            </if>
            <if test="query.operator != null and query.operator != ''">
                and t5.operator = #{query.operator}
            </if>
            <trim prefix="and (" suffix=")" prefixOverrides="and |or ">
                <trim prefix="(" suffix=")" prefixOverrides="and |or ">
                    <if test="query.shippingDateStart != null and query.shippingDateStart != ''">
                        and t5.shipping_time &gt;= #{query.shippingDateStart}
                    </if>
                    <if test="query.shippingDateEnd != null and query.shippingDateEnd != ''">
                        and t5.shipping_time &lt;= #{query.shippingDateEnd}
                    </if>
                </trim>
                <trim prefix="or (" suffix=")" prefixOverrides="and |or ">
                    <if test="query.returnDateStart != null and query.returnDateStart != ''">
                        and t5.return_time &gt;= #{query.returnDateStart}
                    </if>
                    <if test="query.returnDateEnd != null and query.returnDateEnd != ''">
                        and t5.return_time &lt;= #{query.returnDateEnd}
                    </if>
                </trim>
            </trim>
        </trim>
        group by operator
    </select>

</mapper>