<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >

<!--suppress ALL -->
<mapper namespace="com.xtc.onlineretailers.mapper.master.OrderBarcodeMapper">
    <select id="getOrderBarcode" resultType="java.lang.String">

        select c.barcode as barcode
        from (
                 select b.barcode
                 from t_order_barcode b,
                      (
                          select UPPER(b.barcode) as product_sn
                          from t_order_barcode b,
                               t_product p
                          where b.create_time >= #{startTime}
                            and b.erp_code = p.product_id
                            and p.material_type = 1
                            and b.barcode not like '69%%'
                            and LENGTH(b.barcode) = 13
                          union all
                          select UPPER(s.product_sn) as product_sn
                          from t_order_ship_item s,
                               t_product_barcode b,
                               t_product p
                          where UPPER(s.product_sn) = b.barcode
                            and b.erp_code = p.product_id
                            and p.material_type = 1
                            and s.create_time >= #{startTime}
                            and s.product_sn not like '69%%'
                            and LENGTH(s.product_sn) = 13) a
                 where b.barcode = a.product_sn

                 union all
                 select UPPER(i.product_sn) as barcode
                 from t_order_ship_item i,
                      (
                          select UPPER(b.barcode) as product_sn
                          from t_order_barcode b,
                               t_product p
                          where b.create_time >= #{startTime}
                            and b.erp_code = p.product_id
                            and p.material_type = 1
                            and b.barcode not like '69%%'
                            and LENGTH(b.barcode) = 13
                          union all
                          select UPPER(s.product_sn) as product_sn
                          from t_order_ship_item s,
                               t_product_barcode b,
                               t_product p
                          where UPPER(s.product_sn) = b.barcode
                            and b.erp_code = p.product_id
                            and p.material_type = 1
                            and s.create_time >= #{startTime}
                            and s.product_sn not like '69%%'
                            and LENGTH(s.product_sn) = 13) a
                 where UPPER(i.product_sn) = a.product_sn) c
        group by c.barcode
        HAVING (count(*)) > 1


    </select>
    <select id="getBarcodeList" resultType="java.lang.String">
        select barcode
        from t_order_barcode
    </select>

</mapper>