<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xtc.onlineretailers.mapper.barcode.BarcodeMapper">

    <select id="listWmsBarcode" parameterType="String"
            resultType="com.xtc.onlineretailers.pojo.entity.ProductBarcodeTransitDO">
        select rownum         as id,
               goods.SN       as barcode,
               'admin'        as operator,
               sysdate        as operateTime,
               goods.ITEMCODE as erp_code,
               3              as status,
               3              as saveStatus,
               goods.PACKNO   as cartonNo,
               goods.customer_code as customerCode,
               goods.IMEI          as imei
        from wmsprod.v_out_goods goods
        where customer_code in ('SF01', 'SF02', 'SF03', 'SF04', 'Y05000', '005', '011', 'JDPOP02')
          and shipdate > to_date(#{date}, 'yyyy-MM-dd HH24:mi:ss')
    </select>

</mapper>