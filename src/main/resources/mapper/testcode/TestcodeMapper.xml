<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.xtc.onlineretailers.mapper.testcode.TestcodeMapper">

    <select id="getList" parameterType="String"
            resultType="com.xtc.onlineretailers.pojo.entity.ProductBarcodeTransitDO">
    SELECT rownum as id,IBS.SNCODE as barcode,'admin' as operator,createdate as operateTime,IBS.ITEMCODE as erp_code,3 as status,3 as  saveStatus,'' as  cartonNo  FROM QMSADMIN.IP_BarCode_Scan IBS where createdate >=  to_date(#{date},'yyyy-MM-dd HH24:mi:ss') and status = 'New'
   </select>

</mapper>