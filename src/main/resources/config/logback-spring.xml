<configuration>
    <springProperty scope="context" name="APP_NAME" source="spring.application.name" defaultValue="spring-boot-app"/>

    <!-- 日志文件路径 -->
    <property name="LOG_PATH" value="./logs"/>

    <!-- 日志打印配置 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="ex"
                    converterClass="com.xtc.onlineretailers.configuration.LogbackStackTraceConverter"/>
    <property name="LOG_PATTERN_CONSOLE"
              value="%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %X{trace.id} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan}%clr(:){faint} %m%n%ex"/>
    <property name="LOG_PATTERN"
              value="%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } %X{trace.id} --- [%15.15t] %-40.40logger{39} : %m%n%ex"/>

    <appender name="fileInfo" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/info/${APP_NAME}-info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_PATH}/info/${APP_NAME}-info.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${LOG_PATTERN}</pattern>
        </layout>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="fileWarn" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/warn/${APP_NAME}-warn.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <FileNamePattern>${LOG_PATH}/warn/${APP_NAME}-warn.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${LOG_PATTERN}</pattern>
        </layout>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 输出模式file，滚动记录文件，先将日志文件指定到文件，当符合某个条件时，将日志记录到其他文件 -->
    <appender name="fileError" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 被写入的文件名，可以是相对目录，也可以是绝对目录，如果上级目录不存在会自动创建，没有默认值。 -->
        <file>${LOG_PATH}/error/${APP_NAME}-error.log</file>
        <!-- 滚动策略，基于时间的分包策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- yyyy-MM-dd 时间策略则为一天一个文件 -->
            <FileNamePattern>${LOG_PATH}/error/${APP_NAME}-error.%d{yyyy-MM-dd}.%i.log</FileNamePattern>
            <!-- 日志清除策略，单个文件最大1GB，总日志文件最大10GB，最多保存60天 -->
            <maxFileSize>1GB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <!-- layout 负责把事件转换成字符串，格式化的日志信息的输出 -->
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>${LOG_PATTERN}</pattern>
        </layout>
        <!-- 级别过滤器，根据日志级别进行过滤。如果日志级别等于配置级别，过滤器会根据onMath和onMismatch接收或拒绝日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <!--
	            onMatch="ACCEPT" 表示匹配该级别的打印
				onMatch="DENY" 表示匹配该级别的不打印
				onMatch="NEUTRAL" 表示匹配该级别的，由下一个filter处理，如果当前是最后一个，则表示匹配该级别的打印
				onMismatch="ACCEPT" 表示不匹配该级别的打印
				onMismatch="DENY" 表示不匹配该级别的不打印
				onMismatch="NEUTRAL" 表示不匹配该级别的，由下一个filter处理，如果当前是最后一个，则不匹配该级别的打印
			 -->
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!--控制台日志，控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="fileError"/>
        <appender-ref ref="fileWarn"/>
        <appender-ref ref="fileInfo"/>
    </root>

</configuration>