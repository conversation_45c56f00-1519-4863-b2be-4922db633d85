spring:
  profiles:
    include: login
  redis:
    host: ***********
    port: 6379
    password: LMF+KdwJw1jdllwAh18nkc9h1Kk=
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          username: online_retailers_user
          password: GsNecGHGgGNbXFjXOO8Y
          url: *********************************************************************************************************************************************************
        erp:
          driver-class-name: oracle.jdbc.OracleDriver
          username: apps
          password: apps
          url: ***************************************
        barcode:
          driver-class-name: oracle.jdbc.OracleDriver
          username: wmsquery
          password: xHxfeGQyVnuabS1cTwpc
          url: ******************************************
        testcode:
          driver-class-name: oracle.jdbc.OracleDriver
          username: sel_barc
          password: LGITIOlyk1WwpqcvkCeU
          url: *******************************************

# 调度中心部署地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"
xxl:
  job:
    executor:
      # 执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
      appname: retailers-data
    admin:
      addresses: http://xxl-job:8080/xxl-job-admin/

# 发票文件保存路径
invoice:
  save-path: /apps/data/

# Excel保存路径
excel:
  save-path: /apps/data/download/

# 确认订单时是否允许改备注
order:
  confirm-remark: true

xtc:
  feign:
    client:
      reported-service:
        url: https://ics2.eebbk.com
      erp-sync-service:
        url: https://marketing-erp-sync.okii.com
  sales:
    clientId: OMS
    secret: 545240bb-fb50-45c7-9aa9-1a71ec9c3aca
    reported-token:
      url: https://qdlogin.okii.com/auth/realms/genie/protocol/openid-connect/token
    reported-gateway:
      url: https://jl-gateway.okii.com

# 使用 prod 分支
nacos:
  config:
    namespace: marketing-service-env-prod
ems:
  api:
    url: http://esb.bbkedu.com:8000
    appId: ITMarketing
    secret: 6d74932538844e719aef23a5b0ec6176

minio:
  endpoint: https://eds-prod-2.okii.com:12000
  access-key: VDYWREXJQ2NKP813SRKM
  secret-key: YCPD85tMkFWZarck5nGVrhtkDkDiEjYRtaAwDhEJ
