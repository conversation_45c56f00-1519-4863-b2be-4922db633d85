package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.pojo.entity.LetteringOrderDO;
import com.xtc.onlineretailers.pojo.query.LetteringAddQuery;
import com.xtc.onlineretailers.pojo.query.LetteringPageQuery;
import com.xtc.onlineretailers.pojo.query.LetteringUpdateQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.pojo.vo.PrintVO;
import com.xtc.onlineretailers.service.LetteringService;
import com.xtc.springboot.annotation.ResponseResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 刻字模块
 */
@Validated
@ResponseResult
@RestController
@RequestMapping("/api/lettering")
public class LetteringController {

    @Resource
    private LetteringService letteringService;

    /**
     * 分页查询刻字订单
     */
    @GetMapping("/page")
    public PaginationVO<LetteringOrderDO> page(LetteringPageQuery query) {
        return this.letteringService.list(query);
    }

    /**
     * 新增刻字订单
     */
    @PostMapping("/add")
    public boolean add(@RequestBody LetteringAddQuery query) {
        return this.letteringService.add(query);
    }

    /**
     * 编辑刻字订单
     */
    @PostMapping("/update")
    public boolean update(@RequestBody LetteringUpdateQuery query) {
        return this.letteringService.edit(query);
    }

    /**
     * 审核
     */
    @PutMapping("/review/{id}")
    public boolean review(@PathVariable("id") Long id) {
        return this.letteringService.review(id);
    }

    /**
     * 打印数据
     */
    @PostMapping("/print")
    public PrintVO review(@RequestBody List<String> platformTradeId) {
        return this.letteringService.print(platformTradeId);
    }

    /**
     * 导出刻字订单
     *
     * @param response response
     * @param query    query
     */
    @GetMapping("/export")
    public void export(HttpServletResponse response, LetteringPageQuery query) {
        this.letteringService.export(response, query);
    }

    /**
     * 删除
     *
     * @param id 刻字id
     */
    @DeleteMapping("/{id}")
    public void delete(@PathVariable("id") Long id) {
        this.letteringService.deleteLettering(id);
    }

}
