package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.pojo.entity.OldForNewDO;
import com.xtc.onlineretailers.pojo.entity.OldForNewProductDO;
import com.xtc.onlineretailers.pojo.query.*;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.OldForNewProductService;
import com.xtc.onlineretailers.service.OldForNewService;
import com.xtc.onlineretailers.util.GsonUtil;
import com.xtc.springboot.annotation.ResponseResult;
import io.swagger.annotations.ApiImplicitParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 以旧换新流程
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequestMapping("/api")
public class OldForNewController {

    @Resource
    private OldForNewService oldForNewService;
    @Resource
    private OldForNewProductService oldForNewProductService;

/**
 * 客服新增以旧换新
 */
    @PostMapping("/oldForNew")
public OldForNewDO add(@Valid @RequestBody OldForNewAddQuery query) {
        log.info("query: {}", GsonUtil.objectToJson(query));
        return this.oldForNewService.add(query);
    }

/**
 * 客服修改以旧换新
 */
    @PutMapping("/oldForNew")
public OldForNewDO update(@Valid @RequestBody OldForNewUpdateQuery query) {
        log.info("query: {}", GsonUtil.objectToJson(query));
        return oldForNewService.update(query);
    }

/**
 * 删除以旧换新
 */
    @DeleteMapping("/oldForNew/delete/{id}")
    @ApiImplicitParam(paramType = "path", name = "id", value = "以旧换新删除", dataType = "Integer", required = true)
public void delete(@PathVariable int id) {
        this.oldForNewService.delete(id);
    }

/**
 * 客服退款
 */
    @PostMapping("/oldForNew/serveReturn")
public void serveReturn(@Valid @RequestBody OldForNewReturnQuery query) {
        log.info("query: {}", GsonUtil.objectToJson(query));
        this.oldForNewService.serveReturn(query);
    }

/**
 * 仓库扫描
 */
    @PostMapping("/oldForNew/scan")
public void scan(@Valid @RequestBody OldForNewScanQuery query) {
        log.info("query: {}", GsonUtil.objectToJson(query));
        this.oldForNewService.scan(query);
    }

/**
 * 确认调单
 */
    @PostMapping("/oldForNew/adjust")
public void adjust(@Valid @RequestBody OldForNewAdjustQuery query) {
        log.info("query: {}", GsonUtil.objectToJson(query));
        this.oldForNewService.adjust(query);
    }

/**
 * 抛ERP
 */
    @PostMapping("/oldForNew/erpPostManual")
public void erpPostManual(@RequestBody(required = false) @NotNull List<Integer> ids) {
        this.oldForNewService.erpPostManual(ids);
    }

/**
 * 获取列表
 */
    @GetMapping("/oldForNew/list")
public PaginationVO<OldForNewDO> getOldForNewList(@Valid OldForNewQuery query) {
        return this.oldForNewService.list(query);
    }

/**
 * 导出
 */
    @GetMapping("/oldForNew/export")
public void export(@Valid OldForNewQuery query, HttpServletResponse response) {
        this.oldForNewService.export(query, response);
    }

/**
 * 获取以旧换新物料
 */
    @GetMapping("/oldForNew/products")
public List<OldForNewProductDO> products(OldForNewProductQuery query) {
        return this.oldForNewProductService.getOldForNewProduct(query);
    }
}
