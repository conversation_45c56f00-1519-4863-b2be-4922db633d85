package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.pojo.entity.InvoiceItemDO;
import com.xtc.onlineretailers.pojo.entity.InvoiceMainDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.query.*;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.InvoiceItemService;
import com.xtc.onlineretailers.service.InvoiceService;
import com.xtc.onlineretailers.service.TradeService;
import com.xtc.springboot.annotation.ResponseResult;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 发票信息管理
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequestMapping("/api/invoice")
public class InvoiceController {

    @Resource
    private TradeService tradeService;

    @Resource
    private InvoiceService invoiceService;

    @Resource
    private InvoiceItemService invoiceItemService;

    /**
     * 分页查询交易信息
     */
    @GetMapping("/getTradeList")
    public PaginationVO<TradeDO> getTradeList(@Valid TradeInvoiceQuery query) {
        return this.tradeService.getTradeList(query);
    }

    /**
     * 手动开票
     */
    @PostMapping("/invoiceApply")
    public String create(@Valid @RequestBody CreateInvoiceQuery query) {
        return invoiceService.createInvoice(query.getTradeIds(), query.getInvoiceType());
    }

    /**
     * 手动回传
     */
    @PostMapping("/getResult")
    public String getResult(@NotNull @Size(min = 1) @RequestBody List<String> tradeIds) {
        return invoiceService.getResult(tradeIds);
    }

    /**
     * 批量修改发票
     */
    @PostMapping("/updateInvoiceInfo")
    public void batchUpdateInvoiceInfo(@Valid @RequestBody InvoiceBatchUpdateQuery query) {
        this.tradeService.batchUpdateInvoiceInfo(query);
    }

    /**
     * 查询发票
     */
    @GetMapping("/getInvoiceMain")
    public PaginationVO<InvoiceMainDO> getInvoiceMain(InvoiceMainQuery query) {
        return this.invoiceItemService.getInvoiceMain(query);
    }

    /**
     * 导出发票
     */
    @GetMapping("/exportInvoiceMain")
    public void exportInvoiceMain(InvoiceMainQuery query, HttpServletResponse response) {
        this.invoiceItemService.exportInvoiceMain(query, response);
    }

    /**
     * 查询发票明细
     */
    @GetMapping("/getInvoiceDetail")
    public PaginationVO<InvoiceItemDO> getInvoiceDetail(@Valid InvoiceDetailQuery query) {
        return this.invoiceItemService.getInvoiceDetail(query);
    }

    /**
     * 导出发票明细
     */
    @GetMapping("/exportInvoiceDetail")
    public void exportInvoiceDetail(@Valid InvoiceDetailQuery query, HttpServletResponse response) {
        this.invoiceItemService.exportInvoiceDetail(query, response);
    }

    /**
     * 新增纸质发票/纸质发票冲红
     */
    @PostMapping("/addPaperInvoice")
    public void addPaperInvoice(@Valid @RequestBody InvoicePaperAddQuery query) {
        this.invoiceItemService.addPaperInvoice(query);
    }

    /**
     * 修改纸质发票
     */
    @PostMapping("/updatePaperInvoice")
    public boolean updatePaperInvoice(@Valid @RequestBody InvoicePaperUpdateQuery query) {
        return this.invoiceItemService.updatePaperInvoice(query);
    }

    @GetMapping("/deletePaperInvoice")
    @ApiOperation("删除纸票/纸票作废")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "invoiceId", value = "主键id", required = true, dataType = "long", paramType = "query"),
            @ApiImplicitParam(name = "type", value = "操作类型 删除：del  作废：invalid", required = true, dataType = "String", paramType = "query")
    })
    public boolean deletePaperInvoice(Long id, String type) {
        return this.invoiceItemService.deletePaperInvoice(id, type);
    }

    /**
     * 下载电子发票
     */
    @ResponseResult(false)
    @GetMapping("/download")
    public ResponseEntity<org.springframework.core.io.Resource> download(String url) {
        return this.invoiceService.downloadInvoiceFile(url);
    }

}
