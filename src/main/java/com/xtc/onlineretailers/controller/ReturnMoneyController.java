package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.pojo.entity.ReturnMoneyDO;
import com.xtc.onlineretailers.pojo.query.*;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.ReturnMoneyService;
import com.xtc.onlineretailers.util.GsonUtil;
import com.xtc.springboot.annotation.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

/**
 * 返现模块
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequestMapping("/api")
public class ReturnMoneyController {

    @Resource
    private ReturnMoneyService returnMoneyService;

    /**
     * 获取分页返现列表
     */
    @GetMapping("/returnMoneys")
    public PaginationVO<ReturnMoneyDO> getList(ReturnMoneyQuery query) {
        log.info("query: {}", GsonUtil.objectToJson(query));
        return returnMoneyService.getList(query);
    }

    /**
     * Excel导出
     */
    @GetMapping("/returnMoney/export")
    public void export(ReturnMoneyQuery query, HttpServletResponse response) {
        log.info("query: {}", GsonUtil.objectToJson(query));
        returnMoneyService.export(query, response);
    }

    /**
     * 新增返现
     */
    @PostMapping("/returnMoneys")
    public ReturnMoneyDO add(@Valid @RequestBody ReturnMoneyAddQuery query) {
        log.info("query: {}", GsonUtil.objectToJson(query));
        return returnMoneyService.add(query);
    }

    /**
     * 编辑返现
     */
    @PutMapping("/returnMoneys")
    public ReturnMoneyDO update(@Valid @RequestBody ReturnMoneyUpdateQuery query) {
        log.info("query: {}", GsonUtil.objectToJson(query));
        return returnMoneyService.update(query);
    }

    /**
     * 删除返现
     */
    @PostMapping("/returnMoneys/{returnMoneyId}")
    public boolean delete(@PathVariable(name = "returnMoneyId") String returnMoneyId) {
        return returnMoneyService.delete(returnMoneyId);
    }

    /**
     * 财务备注
     */
    @PutMapping("/returnMoney/updateRemark")
    public boolean updateRemark(@Valid ReturnMoneyUpdateRemarkQuery query) {
        log.info("query: {}", GsonUtil.objectToJson(query));
        return returnMoneyService.updateRemark(query);
    }

    /**
     * 批量返现
     */
    @PutMapping("/returnMoney/batchReturnMoney")
    public boolean batchReturnMoney(@Valid @RequestBody ReturnMoneyBatchQuery query) {//@RequestParam(paramValue = "returnMoneyIds") @NotNull List<String> returnMoneyIds;
        log.info("query: {}", GsonUtil.objectToJson(query));
        return returnMoneyService.batchReturnMoney(query);
    }

    /**
     * 取消返现
     */
    @PutMapping("/returnMoney/cancel")
    public boolean cancel(@Valid @RequestBody ReturnMoneyBatchQuery query) {
        return returnMoneyService.cancel(query);
    }

}
