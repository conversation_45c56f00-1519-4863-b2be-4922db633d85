package com.xtc.onlineretailers.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.WarehouseProductDO;
import com.xtc.onlineretailers.pojo.query.WarehouseImportQuery;
import com.xtc.onlineretailers.pojo.query.WarehouseProductQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.impl.WarehouseProductServiceImpl;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.springboot.annotation.ResponseResult;
import com.xtc.springboot.pojo.vo.ResponseVO;
import io.swagger.annotations.ApiImplicitParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统维护
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequestMapping("/api")
public class WarehouseProductController {

    @Resource
    private WarehouseProductServiceImpl warehouseProductService;

/**
 * 分页查询物料列表
 */
    @GetMapping("/WarehouseProduct")
public PaginationVO<WarehouseProductDO> list(WarehouseProductQuery query) {
        return this.warehouseProductService.list(query);
    }

/**
 * 根据物料code获取物料信息
 */
    @GetMapping("/warehouseProduct/productBarcode")
    @ApiImplicitParam(paramType = "path", name = "productBarcode", value = "物料code码", dataType = "String", required = true)
public List<WarehouseProductDO> getWarehouseProducts(String code) {
        QueryWrapper<WarehouseProductDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("product_barcode", code);
        List<WarehouseProductDO> list = this.warehouseProductService.list(queryWrapper);
        return list;
    }


/**
 * 保存物料信息
 */
    @PostMapping("/warehouseProduct/save")
public ResponseVO getWarehouseProducts(@RequestBody WarehouseProductDO warehouseProductDO) {
        try {
            warehouseProductDO.setCreateTime(DateUtil.localDateTimeToStr(LocalDateTime.now()));
            warehouseProductDO.setUpdateTime(DateUtil.localDateTimeToStr(LocalDateTime.now()));
            if (StringUtils.isNotBlank(warehouseProductDO.getProductBarcode())) {
                LambdaQueryWrapper<WarehouseProductDO> queryWrapper = Wrappers.<WarehouseProductDO>lambdaQuery().eq(WarehouseProductDO::getProductBarcode, warehouseProductDO.getProductBarcode());
                WarehouseProductDO one = this.warehouseProductService.getOne(queryWrapper);
                if (ObjectUtils.isNotEmpty(one)) {
                    Long id = one.getId();
                    BeanUtils.copyProperties(warehouseProductDO, one);
                    one.setId(id);
                    this.warehouseProductService.updateById(one);
                } else {
                    this.warehouseProductService.save(warehouseProductDO);
                }
            } else {
                return new ResponseVO(ResponseEnum.ERROR.getCode(), "物料编号不能为空", null);

            }

            return new ResponseVO(ResponseEnum.SUCCESS.getCode(), "保存物料信息成功", null);
        } catch (Exception e) {
            return new ResponseVO(ResponseEnum.ERROR.getCode(), "保存物料信息失败", null);
        }

    }

/**
 * 修改物料信息
 */
    @PutMapping("/warehouseProduct/update")
public ResponseVO updateWarehouse(@RequestBody WarehouseProductDO warehouseProductDO) {
        try {
            WarehouseProductDO warehouseDO1 = this.warehouseProductService.getById(warehouseProductDO.getId());
            BeanUtils.copyProperties(warehouseProductDO, warehouseDO1);
            this.warehouseProductService.updateById(warehouseDO1);
            return new ResponseVO(ResponseEnum.SUCCESS.getCode(), "修改物料成功", warehouseDO1);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResponseVO(ResponseEnum.ERROR.getCode(), "修改物料失败", null);
        }

    }

/**
 * 物料信息删除
 */
    @DeleteMapping("/warehouseProduct/{id}")
public boolean delete(@PathVariable String id) {

        return this.warehouseProductService.deleteById(id);
    }


/**
 * 物料信息Excel导出
 */
    @GetMapping("/WarehouseProduct/warehouseProductExport")
public void warehouseProductExport( WarehouseImportQuery query, HttpServletResponse response) {
        try {
            this.warehouseProductService.excelExport(query, response);

        } catch (Exception e) {
            throw new GlobalDefaultException("物料信息导出异常");

        }

    }


}
