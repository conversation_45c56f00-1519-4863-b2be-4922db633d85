package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.pojo.entity.LogisticsInterceptDO;
import com.xtc.onlineretailers.pojo.query.LogisticsInterceptPageQry;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.LogisticsInterceptService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotBlank;

/**
 * 物流拦截列表
 */
@Validated
@RestController
@RequestMapping("/api")
public class LogisticsInterceptController {

    @Resource
    private LogisticsInterceptService logisticsInterceptService;

    /**
     * 查询物流拦截列表
     */
    @GetMapping("/logisticsIntercept")
    public PaginationVO<LogisticsInterceptDO> pageLogisticsIntercept(LogisticsInterceptPageQry qry) {
        return logisticsInterceptService.pageLogisticsIntercept(qry);
    }

    /**
     * 新增物流拦截
     */
    @PostMapping("/logisticsIntercept")
    public void addLogisticsIntercept(@RequestBody @NotBlank String tradeId) {
        logisticsInterceptService.addLogisticsIntercept(tradeId);
    }

}

