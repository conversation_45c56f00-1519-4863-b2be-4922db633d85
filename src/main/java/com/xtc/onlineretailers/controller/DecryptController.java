package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.executor.command.DecryptGetCmdExe;
import com.xtc.springboot.annotation.ResponseResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;

/**
 * 数据解密
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/cipher")
public class DecryptController {

    private final DecryptGetCmdExe decryptGetCmdExe;

    /**
     * 数据解密
     *
     * @param decryptText 密文
     * @return 结果
     */
    @GetMapping("/decrypt")
    public String decrypt(@NotBlank String decryptText) {
        return decryptGetCmdExe.execute(decryptText);
    }

}
