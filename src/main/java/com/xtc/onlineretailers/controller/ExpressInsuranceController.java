package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.pojo.entity.ExpressInsuranceDO;
import com.xtc.onlineretailers.pojo.query.ExpressInsuranceAddQuery;
import com.xtc.onlineretailers.pojo.query.ExpressInsuranceDelQuery;
import com.xtc.onlineretailers.pojo.query.ExpressInsurancePageQuery;
import com.xtc.onlineretailers.pojo.query.ExpressInsuranceUpdateQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.ExpressInsuranceService;
import com.xtc.springboot.annotation.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 快递保价模块
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequestMapping("/api")
public class ExpressInsuranceController {

    @Resource
    private ExpressInsuranceService expressInsuranceService;

/**
 * 快递保价新增
 */
    @PostMapping("/expressInsurance")
public void add(@Valid @RequestBody ExpressInsuranceAddQuery query) {
        this.expressInsuranceService.add(query);
    }

/**
 * 快递保价编辑
 */
    @PutMapping("/expressInsurance")
public void update(@RequestBody ExpressInsuranceUpdateQuery query) {
        this.expressInsuranceService.update(query);
    }

/**
 * 快递保价分页查询
 */
    @GetMapping("/expressInsurance")
public PaginationVO<ExpressInsuranceDO> page(ExpressInsurancePageQuery query) {
        return this.expressInsuranceService.page(query);
    }

/**
 * 快递保价删除
 */
    @DeleteMapping("/expressInsurance/{id}")
public void remove(@PathVariable long id) {
        ExpressInsuranceDelQuery query = new ExpressInsuranceDelQuery();
        query.setId(id);
        this.expressInsuranceService.remove(query);
    }

}
