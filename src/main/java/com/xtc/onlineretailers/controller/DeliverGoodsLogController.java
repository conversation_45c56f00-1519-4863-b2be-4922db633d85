package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.pojo.entity.DeliverGoodsLogDO;
import com.xtc.onlineretailers.pojo.query.DeliverGoodsLogQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.DeliverGoodsLogService;
import com.xtc.springboot.annotation.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 发货操作人日志模块
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequestMapping("/api")
public class DeliverGoodsLogController {

    @Resource
    private DeliverGoodsLogService deliverGoodsLogService;

    @GetMapping("/deliverGoodsLog")
    public PaginationVO<DeliverGoodsLogDO> list(DeliverGoodsLogQuery deliverGoodsLogQuery){
        return this.deliverGoodsLogService.list(deliverGoodsLogQuery);
    }
}
