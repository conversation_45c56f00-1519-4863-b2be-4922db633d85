package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.pojo.entity.GroupBarcodeDO;
import com.xtc.onlineretailers.pojo.query.GroupBarcodeQuery;
import com.xtc.onlineretailers.pojo.query.GroupBarcodeSaveQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.GroupBarcodeService;
import com.xtc.onlineretailers.util.GsonUtil;
import com.xtc.springboot.annotation.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 通用物料管理
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequestMapping("/api")
public class GroupBarcodeController {

    @Resource
    private GroupBarcodeService groupBarcodeService;

/**
 * 获取通用物料列表
 */
    @GetMapping("/groups")
public PaginationVO<GroupBarcodeDO> list(GroupBarcodeQuery query) {
        log.info("query: {}", GsonUtil.objectToJson(query));
        return this.groupBarcodeService.getGroupBarcodeList(query);
    }

/**
 * 新增通用物料
 */
    @PostMapping("/groups")
public void add(@Valid @RequestBody GroupBarcodeSaveQuery query) {
        log.info("query: {}", GsonUtil.objectToJson(query));
        this.groupBarcodeService.addGroupBarcodeDo(query);
    }


/**
 * 删除通用物料
 */
    @DeleteMapping("/groups/{id}")
public void delete(@PathVariable long id) {
        this.groupBarcodeService.delete(id);
    }

}
