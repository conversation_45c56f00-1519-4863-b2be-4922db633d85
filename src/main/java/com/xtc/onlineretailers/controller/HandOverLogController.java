package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.pojo.entity.HandOverLogDO;
import com.xtc.onlineretailers.pojo.query.HandOverLogQuery;
import com.xtc.onlineretailers.pojo.query.HandOverLogSignQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.HandOverLogService;
import com.xtc.springboot.annotation.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 手机交接记录
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequestMapping("/api/handOverLog")
public class HandOverLogController {

    @Resource
    private HandOverLogService handOverLogService;

/**
 * 获取打回状态列表
 */
    @GetMapping("/list")
public PaginationVO<HandOverLogDO> getBackList(HandOverLogQuery query) {
        return this.handOverLogService.list(query);
    }

/**
 * 快递签名交接
 */
    @PostMapping("/sign")
public void sign(@Valid @RequestBody HandOverLogSignQuery query) {
        this.handOverLogService.sign(query);
    }

}
