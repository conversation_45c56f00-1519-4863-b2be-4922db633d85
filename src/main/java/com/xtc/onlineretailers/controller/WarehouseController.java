package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.pojo.entity.WarehouseDO;
import com.xtc.onlineretailers.pojo.entity.WarehouseProductDO;
import com.xtc.onlineretailers.pojo.query.StockQuery;
import com.xtc.onlineretailers.pojo.query.WarehouseProductQuery;
import com.xtc.onlineretailers.pojo.query.WarehouseQuery;
import com.xtc.onlineretailers.pojo.query.WarehouseSaveQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.pojo.vo.SFStockVO;
import com.xtc.onlineretailers.refactor.pojo.dto.WarehouseStockDTO;
import com.xtc.onlineretailers.refactor.pojo.query.WarehouseSearchStocksQry;
import com.xtc.onlineretailers.refactor.service.WarehouseApiService;
import com.xtc.onlineretailers.service.WarehouseProductService;
import com.xtc.onlineretailers.service.WarehouseService;
import com.xtc.springboot.annotation.ResponseResult;
import io.swagger.annotations.ApiImplicitParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 仓库管理
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequestMapping("/api")
public class WarehouseController {

    @Resource
    private WarehouseService warehouseService;
    @Resource
    private WarehouseProductService warehouseProductService;
    @Resource
    private WarehouseApiService warehouseApiService;

    /**
     * 获取仓库列表
     */
    @GetMapping("/warehouses")
    public PaginationVO<WarehouseDO> list(WarehouseQuery query) {
        return this.warehouseService.getWarehousePage(query);
    }

    /**
     * 新增仓库信息
     */
    @PostMapping("/warehouses")
    public WarehouseDO add(@Valid @RequestBody WarehouseSaveQuery query) {
        return this.warehouseService.addWarehouse(query);
    }

    /**
     * 编辑仓库信息
     *
     * @param id    仓库id
     * @param query 参数
     * @return 仓库信息
     */
    @PutMapping("/warehouses/{id}")
    @ApiImplicitParam(paramType = "path", name = "id", value = "仓库id", dataType = "long", required = true)
    public WarehouseDO update(@PathVariable long id, @Valid @RequestBody WarehouseSaveQuery query) {
        return this.warehouseService.updateWarehouse(id, query);
    }

    /**
     * 删除仓库信息
     *
     * @param id 仓库id
     * @return 仓库信息
     */
    @DeleteMapping("/warehouses/{id}")
    @ApiImplicitParam(paramType = "path", name = "id", value = "仓库id", dataType = "long", required = true)
    public WarehouseDO delete(@PathVariable long id) {
        WarehouseSaveQuery query = new WarehouseSaveQuery();
        query.setIsUse("-1");
        return this.warehouseService.updateWarehouse(id, query);
    }

    /**
     * 获取顺丰仓库物料
     */
    @GetMapping("/warehouse/products")
    public List<WarehouseProductDO> products(WarehouseProductQuery query) {
        return this.warehouseProductService.getWarehouseProduct(query);
    }

    /**
     * 新增顺丰仓库物料
     */
    @PostMapping("/warehouse/products")
    public WarehouseProductDO addProducts(WarehouseProductQuery query) {
        return this.warehouseProductService.addWarehouseProduct(query);
    }

    /**
     * 删除顺丰仓库物料
     */
    @DeleteMapping("/warehouse/products/{id}")
    public WarehouseProductDO delProducts(@PathVariable long id) {
        WarehouseSaveQuery query = new WarehouseSaveQuery();
        query.setIsUse("-1");
        return this.warehouseProductService.updateWarehouse(id, query);
    }

    /**
     * 获取库存信息
     */
    @GetMapping("/warehouse/stock")
    public List<SFStockVO> stock(StockQuery query) {
        WarehouseSearchStocksQry qry = new WarehouseSearchStocksQry();
        qry.setWarehouseCode(query.getWarehouseStorage());
        qry.setBarcode(query.getProductBarcode());
        List<WarehouseStockDTO> stocks = warehouseApiService.searchStocks(qry);
        return stocks.stream()
                .map(stock -> {
                    SFStockVO vo = new SFStockVO();
                    vo.setWarehouseCode(stock.getWarehouseCode());
                    vo.setWarehouseName(stock.getWarehouseName());
                    vo.setBarcode(stock.getBarcode());
                    vo.setProductName(stock.getProductName());
                    vo.setNum(stock.getTotalQuantity());
                    vo.setAvailableNum(stock.getAvailableQuantity());
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 导出库存信息
     */
    @GetMapping("/warehouse/export")
    public void export(StockQuery query, HttpServletResponse response) {
        this.warehouseService.export(query, response);
    }

}
