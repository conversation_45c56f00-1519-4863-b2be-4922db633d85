package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.pojo.entity.ProductInfoDO;
import com.xtc.onlineretailers.pojo.query.ProductInfoAddQuery;
import com.xtc.onlineretailers.pojo.query.ProductInfoQuery;
import com.xtc.onlineretailers.pojo.query.ProductInfoUpdateQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.ProductInfoService;
import com.xtc.springboot.annotation.ResponseResult;
import com.xtc.springboot.pojo.vo.ResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 物料信息管理
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequestMapping("/api/productInfo")
public class ProductInfoController {

    @Resource
    private ProductInfoService productInfoService;

    /**
     * 获取物料信息列表
     */
    @GetMapping("/getProductInfoList")
    public PaginationVO<ProductInfoDO> getProductInfoList(ProductInfoQuery query) {
        return productInfoService.getProductInfoList(query);
    }

    /**
     * 获取物料分类列表
     */
    @GetMapping("/getProductCategoryList")
    public List<String> getProductCategoryList() {
        return productInfoService.getProductCategoryList();
    }

    /**
     * 获取物料信息列表
     */
    @GetMapping("/getAllProductInfoList")
    public List<ProductInfoDO> getAllProductInfoList(ProductInfoQuery query) {
        return productInfoService.getAllProductInfoList(query);
    }

    /**
     * 新增物料信息
     */
    @PostMapping("/addProductInfo")
    public ProductInfoDO addProductInfo(@Valid @RequestBody ProductInfoAddQuery query) {
        return productInfoService.addProductInfo(query);
    }

    /**
     * 编辑物料信息
     */
    @PostMapping("/updateProductInfo")
    public ProductInfoDO updateProductInfo(@Valid @RequestBody ProductInfoUpdateQuery query) {
        return productInfoService.updateProductInfo(query);
    }

    /**
     * 删除物料信息
     */
    @DeleteMapping("/{id}")
    public void delProductInfo(@PathVariable int id) {
        productInfoService.deleteById(id);
    }

    /**
     * 导出
     */
    @GetMapping("/exportProductInfo")
    public void exportProductInfo(ProductInfoQuery query, HttpServletResponse response) {
        productInfoService.exportProductInfo(query, response);
    }

    /**
     * 获取机型维护归类
     */
    @GetMapping("/getProductMachineTypeList")
    public ResponseVO<List<Map<String, Object>>> exportProductInfo() {
        return ResponseVO.success(productInfoService.getProductMachineTypeList());
    }

    /**
     * 根据物料代码获取物料信息
     */
    @GetMapping("/getProductInfo")
    public ResponseVO getProductInfo(@RequestParam String productId) {
        ProductInfoDO productInfoDO = productInfoService.getProductInfo(productId);
        return ObjectUtils.isEmpty(productInfoService.getProductInfo(productId)) ? new ResponseVO(ResponseEnum.ERROR_ERP_CODE) : new ResponseVO("0000001", "succeed", productInfoDO);
    }

}
