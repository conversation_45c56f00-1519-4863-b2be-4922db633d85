package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.service.SystemParamService;
import com.xtc.springboot.annotation.ResponseResult;
import com.xtc.springboot.security.annotation.AllowAnonymous;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 系统模块
 */
@AllowAnonymous
@ResponseResult
@RestController
@RequestMapping("/api/system")
public class SystemController {

    @Resource
    private SystemParamService systemParamService;

    /**
     * 心跳检测
     */
    @GetMapping("/healthz")
    public String heartbeat() {
        return "ok";
    }

    /**
     * 快递公司列表
     */
    @GetMapping("/listExpressCompany")
    public List<String> getExpressCompanyList() {
        String expressCompany = systemParamService.getSystemParamByName("express_company");
        return Arrays.asList(expressCompany.split(","));
    }

}
