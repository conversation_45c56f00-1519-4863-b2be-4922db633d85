package com.xtc.onlineretailers.controller;

import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.onlineretailers.pojo.dto.AgencyTradeImportDTO;
import com.xtc.onlineretailers.pojo.dto.AgencyTradeUpdateDTO;
import com.xtc.onlineretailers.pojo.query.AgencyTradeImportPageQry;
import com.xtc.onlineretailers.service.TradeUploadLogService;
import com.xtc.springboot.annotation.ResponseResult;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 订单上传记录模块
 */
@Validated
@RequiredArgsConstructor
@ResponseResult
@RequestMapping("/api")
@RestController
public class TradeUploadLogController {

    private final TradeUploadLogService tradeUploadLogService;

    /**
     * 分页订单上传日志列表
     *
     * @param qry 参数
     * @return 订单上传日志列表
     */
    @GetMapping("/distribution/page")
    public PageResponse<AgencyTradeImportDTO> pageTradeUploadLog(AgencyTradeImportPageQry qry) {
        return tradeUploadLogService.pageTradeUploadLog(qry);
    }

    /**
     * 订单锁定
     *
     * @param tradeId 订单号
     */
    @PutMapping("/distribution/lock")
    public void lock(@RequestBody List<String> tradeId) {
        tradeUploadLogService.lockTrade(tradeId);
    }

    /**
     * 上传快递单号
     *
     * @param excel    文件参数
     * @param response 响应对象
     */
    @PostMapping("/distribution/upload/express-number")
    public Boolean uploadExpressTrackingNo(MultipartFile excel, HttpServletResponse response) {
        return tradeUploadLogService.uploadExpressTrackingNo(excel, response);
    }

    /**
     * 上传条码
     *
     * @param excel    文件参数
     * @param response 响应对象
     */
    @PostMapping("/distribution/upload/barcode")
    public Boolean uploadBarcode(MultipartFile excel, HttpServletResponse response) {
        return tradeUploadLogService.uploadBarcode(excel, response);
    }

    /**
     * 修改备注
     *
     * @param agencyTradeUpdateDTO 参数
     */
    @PutMapping("/distribution")
    public void uploadBarcode(@RequestBody AgencyTradeUpdateDTO agencyTradeUpdateDTO) {
        tradeUploadLogService.updateById(agencyTradeUpdateDTO);
    }

    /**
     * 代发记录导出
     *
     * @param qry      查询条件
     * @param response 响应
     */
    @GetMapping("/distribution/export")
    public void export(AgencyTradeImportPageQry qry, HttpServletResponse response) {
        tradeUploadLogService.export(qry, response);
    }

}
