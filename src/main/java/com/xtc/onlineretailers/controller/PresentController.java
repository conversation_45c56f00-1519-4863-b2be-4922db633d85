package com.xtc.onlineretailers.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xtc.marketing.marketingcomponentdto.dto.MultiResponse;
import com.xtc.marketing.marketingcomponentdto.dto.Response;
import com.xtc.onlineretailers.pojo.entity.PresentDO;
import com.xtc.onlineretailers.pojo.entity.PresentDetailDO;
import com.xtc.onlineretailers.pojo.query.PresentDetailQuery;
import com.xtc.onlineretailers.pojo.query.PresentQuery;
import com.xtc.onlineretailers.pojo.query.PresentSaveQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.PresentDetailService;
import com.xtc.onlineretailers.service.PresentService;
import com.xtc.springboot.annotation.ResponseResult;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * 赠品管理
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequestMapping("/api")
public class PresentController {

    @Resource
    private PresentService presentService;

    @Resource
    private PresentDetailService presentDetailService;

/**
 * 获取赠品活动列表
 */
    @GetMapping("/presents")
public PaginationVO<PresentDO> list(PresentQuery query) {
        return presentService.getPresentPage(query);
    }

/**
 * 新增赠品活动信息
 */
    @PostMapping("/presents")
public PresentDO add(@Valid @RequestBody PresentSaveQuery query) {
        return presentService.addPresent(query);
    }

    @PutMapping("/presents/{id}")
    @ApiOperation("编辑赠品活动信息")
    @ApiImplicitParam(paramType = "path", name = "id", value = "赠品活动id", dataType = "long", required = true)
    public PresentDO update(@PathVariable long id, @Valid @RequestBody PresentSaveQuery query) {
        return presentService.updatePresent(id, query);
    }

    @DeleteMapping("/presents/{id}")
    @ApiOperation("删除赠品活动信息")
    @ApiImplicitParam(paramType = "path", name = "id", value = "赠品活动id", dataType = "long", required = true)
    public Response delete(@PathVariable long id) {
        PresentDO presentDO = this.presentService.getById(id);
        presentDO.setIsUse("-1");
        this.presentService.updateById(presentDO);
        boolean remove = this.presentDetailService.remove(Wrappers.<PresentDetailDO>lambdaQuery().eq(PresentDetailDO::getPresentId, id));
        return remove ? Response.buildSuccess() : Response.buildFailure("000001", "删除失败");
    }

/**
 * 赠品活动信息导出
 */
    @GetMapping("/presents/export")
public void export(PresentQuery query, HttpServletResponse response) {
        presentService.export(query, response);
    }

/**
 * 赠品明细列表
 */
    @GetMapping("/presents/presents-detail")
public MultiResponse listByPresentsDetails(@Valid PresentDetailQuery presentDetailQuery) {
        if (StringUtils.isEmpty(presentDetailQuery.getId())) {
            throw new RuntimeException("赠品id不能为空");
        }
        List<PresentDetailDO> list = this.presentDetailService.list(Wrappers.<PresentDetailDO>lambdaQuery().eq(PresentDetailDO::getPresentId, presentDetailQuery.getId()));
        return MultiResponse.of(list);
    }

/**
 * 订单信息Excel表导入
 */
    @PostMapping("/presents/import")
public String PresentsImportOrders(MultipartFile excel) {
        return this.presentService.importOrders(excel);
    }

}
