package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.service.RefundService;
import com.xtc.springboot.annotation.ResponseResult;
import com.xtc.springboot.security.annotation.AllowAnonymous;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 退款模块
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequestMapping("/api")
public class RefundController {

    @Resource
    private RefundService refundService;

    @AllowAnonymous
    @GetMapping("/refunds/{tradeId}/status")
    @ApiOperation("查询退款状态 NO_PROCESSING: 无需处理, AGREE: 同意退款, DISAGREE: 不同意退款")
    @ApiImplicitParam(paramType = "path", name = "tradeId", value = "交易id", dataType = "String", required = true)
    public String status(@PathVariable String tradeId) {
        return this.refundService.getRefundStatus(tradeId);
    }

}
