package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.service.TradeCopyService;
import com.xtc.springboot.annotation.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 历史数据
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequestMapping("/api/history")
public class HistoryController {

    @Resource
    private TradeCopyService tradeCopyService;

    @PutMapping("/{platformTradeId}")
    public void addHistoryTrade(@PathVariable String platformTradeId){
        tradeCopyService.addHistoryTrade(platformTradeId);
    }
}
