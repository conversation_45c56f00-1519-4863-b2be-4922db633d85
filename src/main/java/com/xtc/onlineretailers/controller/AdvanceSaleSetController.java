package com.xtc.onlineretailers.controller;

import com.xtc.onlineretailers.pojo.entity.AdvanceSaleSetDO;
import com.xtc.onlineretailers.pojo.query.AdvanceSaleSetAddQuery;
import com.xtc.onlineretailers.pojo.query.AdvanceSaleSetQuery;
import com.xtc.onlineretailers.pojo.query.AdvanceSaleSetUpdateQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.AdvanceSaleSetService;
import com.xtc.springboot.annotation.ResponseResult;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 预定活动管理
 */
@Slf4j
@Validated
@ResponseResult
@RestController
@RequestMapping("/api/advanceSaleSet")
public class AdvanceSaleSetController {

    @Resource
    private AdvanceSaleSetService advanceSaleSetService;

/**
 * 获取预定活动信息列表
 */
    @GetMapping("/getAdvanceSaleSetList")
public PaginationVO<AdvanceSaleSetDO> getAdvanceSaleSetList(AdvanceSaleSetQuery query) {
        return advanceSaleSetService.getAdvanceSaleSetList(query);
    }

/**
 * 新增预定活动信息
 */
    @PostMapping("/addAdvanceSaleSet")
public AdvanceSaleSetDO addAdvanceSaleSet(@Valid @RequestBody AdvanceSaleSetAddQuery query) {
        return advanceSaleSetService.addAdvanceSaleSet(query);
    }

/**
 * 编辑预定活动信息
 */
    @PostMapping("/updateAdvanceSaleSet")
public AdvanceSaleSetDO updateAdvanceSaleSet(@Valid @RequestBody AdvanceSaleSetUpdateQuery query) {
        return advanceSaleSetService.updateAdvanceSaleSet(query);
    }

/**
 * 删除预定活动信息
 */
    @GetMapping("/delAdvanceSaleSet")
public AdvanceSaleSetDO delAdvanceSaleSet(
            @ApiParam(value = "预定活动id") @RequestParam(value = "advanceId") @NotNull(message = "参数不能为空") Integer advanceId) {
        AdvanceSaleSetUpdateQuery query = new AdvanceSaleSetUpdateQuery();
        query.setAdvanceId(advanceId);
        query.setIsUse("-1");
        return advanceSaleSetService.updateAdvanceSaleSet(query);
    }

/**
 * 导出
 */
    @GetMapping("/exportAdvanceSaleSet")
public void exportAdvanceSaleSet(AdvanceSaleSetQuery query, HttpServletResponse response) {
        advanceSaleSetService.exportAdvanceSaleSet(query, response);
    }

}
