package com.xtc.onlineretailers.refactor.enums;

import java.util.Arrays;

/**
 * 订单预售状态
 */
public enum OrderStatePresales {
    /**
     * 天猫
     */
    TMALL("FRONT_NOPAID_FINAL_NOPAID", "FRONT_PAID_FINAL_NOPAID", "FRONT_PAID_FINAL_PAID"),
    /**
     * 抖音
     */
    TIKTOK("103");

    /**
     * 订单状态
     */
    private final String[] orderState;

    OrderStatePresales(String... orderState) {
        this.orderState = orderState;
    }

    public static boolean isPresales(String platformCode, String orderState) {
        if (platformCode == null || orderState == null) {
            return false;
        }
        try {
            OrderStatePresales orderStatePresales = OrderStatePresales.valueOf(platformCode);
            return Arrays.asList(orderStatePresales.orderState).contains(orderState);
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
