package com.xtc.onlineretailers.refactor.enums;

import lombok.Getter;

/**
 * 异常报表处理处理状态
 */
@Getter
public enum DeliverGoodsReportStatusEnum {

    /**
     * 未处理
     */
    UNTREATED("未处理"),
    /**
     * 处理
     */
    DISPOSE("处理"),
    /**
     * 无需处理
     */
    NO_PROCESSING("无需处理");

    private final String description;

    DeliverGoodsReportStatusEnum(String description) {
        this.description = description;
    }

}
