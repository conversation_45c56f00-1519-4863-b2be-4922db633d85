package com.xtc.onlineretailers.refactor.enums;

import com.google.common.collect.ImmutableList;
import lombok.Getter;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 快递公司
 */
@Getter
public enum LogisticsCompany {

    /**
     * 顺丰
     */
    SF("顺丰速运", ImmutableList.of("顺丰标快", "顺丰特快", "电商标快")),
    /**
     * 圆通
     */
    YTO("圆通速递", Collections.emptyList()),
    /**
     * EMS
     */
    EMS("EMS", Collections.emptyList()),
    /**
     * 京东
     */
    JD("京东物流", Collections.emptyList());

    /**
     * 快递公司
     */
    private final String expressCompany;
    /**
     * 快递类型
     */
    private final List<String> expressType;

    LogisticsCompany(String expressCompany, List<String> expressType) {
        this.expressCompany = expressCompany;
        this.expressType = expressType;
    }

    /**
     * 获取枚举
     *
     * @param expressCompany 快递公司
     * @return 枚举
     */
    public static Optional<LogisticsCompany> of(String expressCompany) {
        for (LogisticsCompany logisticsCompany : values()) {
            if (logisticsCompany.expressCompany.equals(expressCompany)) {
                return Optional.of(logisticsCompany);
            }
            if (logisticsCompany.name().equals(expressCompany)) {
                return Optional.of(logisticsCompany);
            }
        }
        return Optional.empty();
    }
}
