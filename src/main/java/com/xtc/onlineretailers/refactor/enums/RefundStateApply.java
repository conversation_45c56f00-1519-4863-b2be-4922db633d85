package com.xtc.onlineretailers.refactor.enums;

import lombok.Getter;

import java.util.Arrays;

/**
 * 退款申请状态
 */
@Getter
public enum RefundStateApply {
    /**
     * 小天才自营（官方商城、会员商城）
     */
    XTC("APPLY"),
    /**
     * 天猫
     */
    TMALL("WAIT_SELLER_AGREE"),
    /**
     * 京东
     */
    JD("0", "1"),
    /**
     * 抖音
     */
    TIKTOK("6"),
    /**
     * 拼多多
     */
    PDD("2", "3"),
    /**
     * 微信视频号
     */
    WECHAT_CHANNELS_SHOP("MERCHANT_PROCESSING"),
    /**
     * 快手
     */
    KUAISHOU("10", "20", "30", "40", "45", "50"),
    /**
     * 小红书
     */
    XIAOHONGSHU("1", "2", "3"),
    ;

    /**
     * 退款单状态
     */
    private final String[] refundState;

    RefundStateApply(String... refundState) {
        this.refundState = refundState;
    }

    /**
     * 判断退款申请状态
     *
     * @param platformCode 平台代码
     * @param refundState  退款单状态
     * @return 执行结果
     */
    public static boolean isApply(String platformCode, String refundState) {
        if (platformCode == null || refundState == null) {
            return false;
        }
        try {
            String refundStateCleanPrefix = RefundStatePrefix.refundStateCleanPrefix(platformCode, refundState);
            RefundStateApply state = RefundStateApply.valueOf(platformCode);
            return Arrays.asList(state.refundState).contains(refundStateCleanPrefix);
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
