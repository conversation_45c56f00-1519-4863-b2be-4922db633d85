package com.xtc.onlineretailers.refactor.enums;

import lombok.Getter;

/**
 * 核对日志结果类型
 */
@Getter
public enum CheckLogResultEnum {

    /**
     * 成功
     */
    SUCCEED("成功"),
    /**
     * 业务异常
     */
    BUSINESS_EXCEPTION("业务异常"),
    /**
     * 平台异常
     */
    PLATFORM_EXCEPTION("平台异常");

    private final String result;

    CheckLogResultEnum(String result) {
        this.result = result;
    }
}
