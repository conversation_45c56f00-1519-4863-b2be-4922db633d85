package com.xtc.onlineretailers.refactor.rpc.xtcmall;

import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.refactor.rpc.xtcmall.dto.XtcMallBaseResponse;
import com.xtc.onlineretailers.refactor.rpc.xtcmall.dto.XtcMallOpenApiRequest;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.GsonUtil;
import com.xtc.onlineretailers.util.HttpUtil;
import com.xtc.onlineretailers.util.MD5Util;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpServerErrorException;

import java.net.SocketTimeoutException;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.stream.Collectors;

/**
 * 会员商城 RPC 接口
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class XtcMallRpc {

    /**
     * 会员商城测试
     */
    private static final String MEMBER_TEST_URL = "http://xtc-mall-test.okii.com";
    /**
     * 会员商城正式
     */
    private static final String MEMBER_URL = "https://xtc-mall.okii.com";
    /**
     * 秘钥id
     */
    private static final String ACCESS_ID = "47e63e32d20548ba9d";
    /**
     * 秘钥密钥
     */
    private static final String ACCESS_SECRET = "4b52a5e178704d18a5b14545f83e17b3";
    /**
     * 订单同步
     */
    private static final String OMS_SYNC_PATCH = "/instant-retail/open-api/order/oms-sync";
    /**
     * 订单详情
     */
    private static final String ORDER_DETAIL_PATCH = "/instant-retail/open-api/order/oms-get/%5$s";
    /**
     * 参数
     */
    private static final String PARAM = "?accessId=%1$s" + "&ts=%2$s" + "&nonce=%3$s" + "&signature=%4$s";

    @Value("${spring.profiles.active}")
    private String activeProfile;

    /**
     * 订单推送
     *
     * @param order 订单
     */
    public void pushOrder(OrderDTO order) {
        XtcMallOpenApiRequest openApiDTO = buildRequestParam(order);
        String signature = generateSignature(openApiDTO.getParam());
        String requestUrl = constructRequestUrlWithSignature(openApiDTO, signature);
        try {
            HttpUtil.post(requestUrl, openApiDTO.getParam());
        } catch (Exception e) {
            String errorMessage = handleException(e);
            throw rpcSysException(errorMessage, requestUrl, e);
        }
    }

    /**
     * 获取订单详情
     *
     * @param orderId 订单id
     */
    public XtcMallBaseResponse getOrderDetail(String orderId) {
        String url = MEMBER_URL + ORDER_DETAIL_PATCH + PARAM;
        SortedMap<String, Object> signatureParams = generateSignatureParams();
        String signature = generateSignature(signatureParams);
        String requestUrl = String.format(url, ACCESS_ID, signatureParams.get("ts"),
                signatureParams.get("nonce"), signature, orderId);
        try {
            String result = HttpUtil.get(requestUrl);
            return GsonUtil.jsonToBean(result, XtcMallBaseResponse.class);
        } catch (Exception e) {
            String errorMessage = handleException(e);
            throw rpcSysException(errorMessage, requestUrl, e);
        }
    }

    /**
     * 构建会员商城请求参数
     *
     * @param obj 参数
     * @return 会员商城请求参数
     */
    public XtcMallOpenApiRequest buildRequestParam(Object obj) {
        String nonce = RandomStringUtils.randomAlphanumeric(5);
        long second = DateUtil.toEpochSecond(LocalDateTime.now());
        String bodyJson = Base64.getEncoder().encodeToString(GsonUtil.objectToJson(obj).getBytes());
        SortedMap<String, Object> signatureParams = new TreeMap<>();
        signatureParams.put("accessId", ACCESS_ID);
        signatureParams.put("accessSecret", ACCESS_SECRET);
        signatureParams.put("ts", second);
        signatureParams.put("nonce", nonce);
        signatureParams.put("order", bodyJson);
        return XtcMallOpenApiRequest.builder()
                .param(signatureParams)
                .nonce(nonce)
                .ts(second)
                .build();
    }

    /**
     * 生成签名
     *
     * @param params 参数
     * @return 签名
     */
    private String generateSignature(SortedMap<String, Object> params) {
        return MD5Util.encode(params.entrySet().stream()
                .filter(entry -> entry.getValue() != null)
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&")), null);
    }

    /**
     * 生成签名参数
     *
     * @return 签名参数
     */
    private SortedMap<String, Object> generateSignatureParams() {
        SortedMap<String, Object> params = new TreeMap<>();
        params.put("accessId", ACCESS_ID);
        params.put("accessSecret", ACCESS_SECRET);
        params.put("ts", DateUtil.toEpochSecond(LocalDateTime.now()));
        params.put("nonce", RandomStringUtils.randomAlphanumeric(5));
        return params;
    }

    /**
     * 构造请求URL和签名
     *
     * @param openApiDTO 请求参数
     * @param signature  签名
     * @return 请求URL和签名
     */
    private String constructRequestUrlWithSignature(XtcMallOpenApiRequest openApiDTO, String signature) {
        String domain = "dev,test".contains(activeProfile) ? MEMBER_TEST_URL : MEMBER_URL;
        String url = domain + OMS_SYNC_PATCH + PARAM;
        return String.format(url, ACCESS_ID, openApiDTO.getTs(), openApiDTO.getNonce(), signature);
    }

    /**
     * 处理特定异常
     *
     * @param e 异常
     * @return 错误信息
     */
    private String handleException(Exception e) {
        if (e.getCause() instanceof SocketTimeoutException) {
            return "请求超时";
        }
        if (e instanceof HttpServerErrorException) {
            return ((HttpServerErrorException) e).getResponseBodyAsString();
        }
        return "";
    }

    /**
     * 异常处理
     *
     * @param responseStr 异常信息
     * @param url         请求地址
     * @param e           异常
     * @return 异常
     */
    private GlobalDefaultException rpcSysException(String responseStr, String url, Exception e) {
        String msg = String.format("会员商城调用异常 response: %s, url: %s", responseStr, url);
        return GlobalDefaultException.of(e, responseStr, msg, e);
    }

}
