package com.xtc.onlineretailers.refactor.executor.query;

import com.xtc.onlineretailers.refactor.pojo.entity.SupplierOrderDO;
import com.xtc.onlineretailers.refactor.repository.SupplierOrderRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 获取供应商订单
 */
@RequiredArgsConstructor
@Component
public class SupplierOrderGetQryExe {

    private final SupplierOrderRepository supplierOrderRepository;

    public SupplierOrderDO execute(String tradeId) {
        return supplierOrderRepository.getByTradeId(tradeId)
                .orElseThrow(() -> new IllegalArgumentException("供应商订单不存在 " + tradeId));
    }

}
