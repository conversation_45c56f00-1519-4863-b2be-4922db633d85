package com.xtc.onlineretailers.refactor.executor.command;

import com.google.common.collect.Maps;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.refactor.pojo.dto.LoginDTO;
import com.xtc.onlineretailers.refactor.pojo.entity.AdminUsersDO;
import com.xtc.onlineretailers.refactor.repository.AdminUsersRepository;
import com.xtc.onlineretailers.refactor.rpc.scrm.ScrmRpc;
import com.xtc.onlineretailers.refactor.rpc.scrm.dto.EmployeeInfoDTO;
import com.xtc.onlineretailers.refactor.rpc.scrm.qry.EmployeeInfoRequest;
import com.xtc.onlineretailers.refactor.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * oms登录
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class LoginCmdExe {

    private final AdminUsersRepository adminUsersRepository;
    private final ScrmRpc scrmRpc;

    /**
     * sso密钥
     */
    @Value("${xtc.marketing.sso.secretKey}")
    private String ssoSecretKey;
    /**
     * oms密钥
     */
    @Value("${xtc.marketing.oms.secretKey}")
    private String omsSecretKey;

    /**
     * 校验 sso token 合法，换取包含 oms token 的登录数据
     *
     * @return 登录数据
     */
    public LoginDTO execute() {
        String employeeId = null;
        try {
            // 解析 sso token 包含的员工ID
            Map<String, Object> jwtClaims = JwtUtil.parseJwtClaims(ssoSecretKey);
            employeeId = (String) jwtClaims.get("preferred_username");
            log.info("登录员工ID [{}]", employeeId);
            if (StringUtils.isBlank(employeeId)) {
                throw GlobalDefaultException.of("登录失败，请联系IT人员");
            }
            // 调 scrm 接口，获取员工数据
            EmployeeInfoRequest qry = new EmployeeInfoRequest();
            qry.setEmployeeNo(employeeId);
            EmployeeInfoDTO employee = scrmRpc.getCommonEmployeeInfo(qry);
            if (employee == null) {
                throw GlobalDefaultException.of("账号不存在，请联系IT人员");
            }
            log.info("登录员工 [{}] 手机号码 [{}]", employee.getName(), employee.getMobile());
            // 生成 oms token
            AdminUsersDO adminUsersDO = adminUsersRepository.getByEmployeeId(employee.getEmployeeId())
                    .orElseThrow(() -> GlobalDefaultException.of("账号不存在，请联系IT人员"));
            Map<String, Object> claims = Maps.newHashMap();
            claims.put("employeeId", employeeId);
            claims.put("username", adminUsersDO.getUserName());
            claims.put("userId", adminUsersDO.getId());
            String token = JwtUtil.createToken("bbk", claims, omsSecretKey);
            return LoginDTO.builder().accessToken(token).expireTime(JwtUtil.EXPIRATION.getSeconds()).build();
        } catch (Exception e) {
            log.warn("员工ID [{}] 获取token失败：{}", employeeId, e.getMessage(), e);
            throw GlobalDefaultException.of("登录失败，请联系IT人员", e);
        }
    }

}
