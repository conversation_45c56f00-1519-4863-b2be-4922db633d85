package com.xtc.onlineretailers.refactor.executor.command;

import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.pojo.command.DistributorBarcodeUpdateCmd;
import com.xtc.onlineretailers.refactor.pojo.entity.DistributorBarcodeDO;
import com.xtc.onlineretailers.refactor.repository.DistributorBarcodeRepository;
import com.xtc.onlineretailers.repository.TradeDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 修改分销条码
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class DistributorBarcodeUpdateCmdExe {

    private final DistributorBarcodeRepository distributorBarcodeRepository;
    private final TradeDao tradeDao;

    public void execute(DistributorBarcodeUpdateCmd cmd) {
        Optional<DistributorBarcodeDO> distributorBarcodeOpt = distributorBarcodeRepository.getByBarcode(cmd.getBarcode());
        if (distributorBarcodeOpt.isPresent()) {
            throw GlobalDefaultException.of("条码 %s，已存在", cmd.getBarcode());
        }
        TradeDO tradeDO = tradeDao.getByTradeId(cmd.getTradeId())
                .orElseThrow(() -> GlobalDefaultException.of("订单 %s，不存在", cmd.getTradeId()));
        OrderStatus orderStatus = OrderStatus.getEnum(tradeDO.getStatusOrder());
        if (OrderStatus.RETURNED == orderStatus) {
            throw GlobalDefaultException.of("订单 %s，已退货，不可以修改", cmd.getTradeId());
        }
        DistributorBarcodeDO updateDistributorBarcodeDO = new DistributorBarcodeDO();
        updateDistributorBarcodeDO.setId(cmd.getId());
        updateDistributorBarcodeDO.setBarcode(cmd.getBarcode());
        distributorBarcodeRepository.updateById(updateDistributorBarcodeDO);
    }

}
