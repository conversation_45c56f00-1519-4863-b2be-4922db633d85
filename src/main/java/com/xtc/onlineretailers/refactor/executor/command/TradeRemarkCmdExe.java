package com.xtc.onlineretailers.refactor.executor.command;

import com.google.common.base.Joiner;
import com.xtc.marketing.adapterservice.shop.AdapterShopFeignClient;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.command.OrderRemarkCmd;
import com.xtc.marketing.adapterservice.shop.dto.query.OrderGetQry;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.enums.TradeTypeEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.dto.KuaishouParamsDTO;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.executor.query.PlatformGetQryExe;
import com.xtc.onlineretailers.refactor.util.RedissonUtil;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.KuaishouUtil;
import com.xtc.onlineretailers.util.MeituanUtil;
import com.xtc.onlineretailers.util.XiaoHongShuUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 更新订单商家备注
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class TradeRemarkCmdExe {

    private final PlatformGetQryExe platformGetQryExe;
    private final AdapterShopFeignClient adapterShopFeignClient;
    private final RedissonUtil redissonUtil;

    @Value("${order.confirm-remark}")
    private Boolean orderConfirmRemark;

    /**
     * 订单添加商家备注
     *
     * <pre>
     *     remarkJoiner("", "@OK")         = "@OK"
     *     remarkJoiner(null, "@OK")       = "@OK"
     *     remarkJoiner("*OK", "@OK")      = "*OK@OK"
     *     remarkJoiner("*OK", "*OK")      = "*OK*OK"
     *     remarkJoiner("*OK", "*OK，@OK") = "*OK*OK，@OK"
     * </pre>
     *
     * <p>以下情况单独调用平台接口更新备注，或者不更新平台备注</p>
     * <ul>
     *     <li>不支持非对接服务支持的平台（没有店铺代码）</li>
     *     <li>不支持代销平台备注</li>
     * </ul>
     *
     * @param trade  订单
     * @param remark 追加备注
     * @return 新备注内容
     */
    public String execute(TradeDO trade, String remark) {
        return this.execute(trade, remark, "");
    }

    /**
     * 订单添加商家备注
     *
     * <pre>
     *     remarkJoiner("", "@OK", "，")         = "@OK"
     *     remarkJoiner(null, "@OK", "，")       = "@OK"
     *     remarkJoiner("*OK", "@OK", "，")      = "*OK，@OK"
     *     remarkJoiner("*OK", "*OK", "，")      = "*OK，*OK"
     *     remarkJoiner("*OK", "*OK，@OK", "，") = "*OK，*OK，@OK"
     * </pre>
     *
     * <p>以下情况单独调用平台接口更新备注，或者不更新平台备注</p>
     * <ul>
     *     <li>不支持非对接服务支持的平台（没有店铺代码）</li>
     *     <li>不支持代销平台（平台类型：AGENT）</li>
     * </ul>
     *
     * @param trade     订单
     * @param remark    追加备注
     * @param separator 分隔符
     * @return 新备注内容
     */
    public String execute(TradeDO trade, String remark, String separator) {
        PlatformDO platform = platformGetQryExe.getByPlatformId(trade.getPlatformId());
        if (this.noAdapterSupportRemark(platform)) {
            String updateRemark = this.remarkJoiner(trade.getSellerRemark(), remark, separator);
            this.noAdapterOrderRemark(platform, trade.getPlatformTradeId(), updateRemark);
            log.info("{} 非对接服务支持的平台，调用平台接口更新备注", platform.getPlatformName());
            return updateRemark;
        }
        return this.execute(platform.getShopCode(), trade, remark, separator);
    }

    /**
     * 订单添加商家备注
     *
     * <pre>
     *     remarkJoiner("", "@OK", "，")         = "@OK"
     *     remarkJoiner(null, "@OK", "，")       = "@OK"
     *     remarkJoiner("*OK", "@OK", "，")      = "*OK，@OK"
     *     remarkJoiner("*OK", "*OK", "，")      = "*OK，*OK"
     *     remarkJoiner("*OK", "*OK，@OK", "，") = "*OK，*OK，@OK"
     * </pre>
     *
     * @param shopCode  店铺代码
     * @param trade     订单
     * @param remark    追加备注
     * @param separator 分隔符
     * @return 新备注内容
     */
    private String execute(String shopCode, TradeDO trade, String remark, String separator) {
        log.info("订单备注 shopCode: {}, tradeId: {}, remark: {}", shopCode, trade.getPlatformTradeId(), remark);
        // 检查不允许备注，返回订单本地备注内容
        if (this.checkIllegalRemark(shopCode, trade, remark)) {
            return this.remarkJoiner(trade.getSellerRemark(), remark, separator);
        }

        // 订单号加悲观锁，重复处理按顺序添加备注
        return redissonUtil.lock("trade:remark", trade.getPlatformTradeId(), () -> {
            // 查询平台当前的备注内容
            OrderGetQry orderGetQry = OrderGetQry.builder()
                    .shopCode(shopCode).orderNo(trade.getPlatformTradeId()).build();
            OrderDTO platformOrder = adapterShopFeignClient.getOrder(orderGetQry).getData();
            log.info("当前备注内容: {}", platformOrder.getSellerMemo());

            // 拼接新备注内容，当前备注优先使用平台备注，为空则使用本地备注
            String currentRemark = StringUtils.defaultIfBlank(platformOrder.getSellerMemo(), trade.getSellerRemark());
            String updateRemark = this.remarkJoiner(currentRemark, remark, separator);
            log.info("更新备注内容: {}", updateRemark);

            // 更新整个订单商家备注
            OrderRemarkCmd orderSellerMemoCmd = OrderRemarkCmd.builder()
                    .shopCode(shopCode).orderNo(trade.getPlatformTradeId()).remark(updateRemark).build();
            SingleResponse<Boolean> remarkResponse = adapterShopFeignClient.remark(orderSellerMemoCmd);
            if (remarkResponse == null || remarkResponse.isFailure() || BooleanUtils.isFalse(remarkResponse.getData())) {
                log.error("订单备注失败");
            }
            return updateRemark;
        }, new GlobalDefaultException("订单 " + trade.getPlatformTradeId() + " 正在备注，请稍后重试"));
    }

    /**
     * 备注拼接器
     *
     * <pre>
     *     remarkJoiner("", "@OK", "，")         = "@OK"
     *     remarkJoiner(null, "@OK", "，")       = "@OK"
     *     remarkJoiner("*OK", "@OK", "，")      = "*OK，@OK"
     *     remarkJoiner("*OK", "*OK", "，")      = "*OK，*OK"
     *     remarkJoiner("*OK", "*OK，@OK", "，") = "*OK，*OK，@OK"
     * </pre>
     *
     * @param currentRemark 当前备注
     * @param remark        追加备注
     * @param separator     分隔符
     * @return 新备注内容
     */
    private String remarkJoiner(String currentRemark, String remark, String separator) {
        // 生成字符串拼接器，分隔符如果位 null 则使用空字符串
        Joiner joiner = Joiner.on(StringUtils.defaultString(separator)).skipNulls();
        // 确认前缀备注，当前备注为空，则使用 null 值替换，让拼接器可以忽略空值和 null 值
        String prefixRemark = StringUtils.defaultIfBlank(currentRemark, null);
        // 拼接前缀备注和追加备注
        return joiner.join(prefixRemark, remark);
    }

    /**
     * 检查不允许备注
     *
     * @param shopCode 店铺代码
     * @param trade    订单
     * @param remark   追加备注
     * @return 执行结果
     */
    private boolean checkIllegalRemark(String shopCode, TradeDO trade, String remark) {
        if (StringUtils.isBlank(shopCode)) {
            log.info("该平台暂不支持备注，执行本地备注");
            return true;
        }
        if (StringUtils.isBlank(remark)) {
            log.info("追加备注为空，执行本地备注");
            return true;
        }
        if (BooleanUtils.isFalse(this.orderConfirmRemark)) {
            log.info("平台备注功能未开启，执行本地备注");
            return true;
        }
        Duration duration = Duration.between(DateUtil.dateToLocalDateTime(trade.getPaymentTime()), LocalDateTime.now());
        if (duration.toDays() >= 90) {
            log.info("付款时间超过3个月的订单，执行本地备注");
            return true;
        }
        // 只支持普通订单添加备注
        try {
            TradeTypeEnum tradeType = TradeTypeEnum.valueOf(trade.getType());
            if (tradeType != TradeTypeEnum.NORMAL) {
                log.info("订单类型不是普通订单，执行本地备注");
                return true;
            }
        } catch (IllegalArgumentException e) {
            log.error("订单类型不合法，执行本地备注 tradeId: {}, type: {}", trade.getPlatformTradeId(), trade.getType());
            return true;
        }
        return false;
    }

    /**
     * 非对接服务支持的平台，单独调用平台接口更新备注
     *
     * @param platform     平台
     * @param tradeId      订单号
     * @param updateRemark 更新备注
     */
    private void noAdapterOrderRemark(PlatformDO platform, String tradeId, String updateRemark) {
        switch (platform.getPlatformId()) {
            // 快手
            case GlobalConstant.PLATFORM_KUAISHOU_XTC:
            case GlobalConstant.PLATFORM_KUAISHOU_BBK:
                KuaishouParamsDTO kuaishouParamsDTO = new KuaishouParamsDTO(platform);
                KuaishouUtil.addRemark(kuaishouParamsDTO, Long.parseLong(tradeId), updateRemark);
                break;
            // 美团
            case GlobalConstant.PLATFORM_MEITUAN_XTC:
                MeituanUtil.updateRemark("172004", tradeId, updateRemark, DateUtil.nowEpochSecond());
                break;
            // 小红书
            case GlobalConstant.XIAO_HONG_SHU_PLATFORM_XTC:
            case GlobalConstant.XIAO_HONG_SHU_PLATFORM_BBK:
                XiaoHongShuUtil.modifySellerMarkInfo(platform, tradeId, updateRemark);
                break;
            default:
                log.info("该平台暂不支持备注，执行本地备注");
        }
    }

    /**
     * 以下情况不支持对接服务更新备注
     * <ul>
     *     <li>不支持非对接服务支持的平台（没有店铺代码）</li>
     *     <li>不支持代销平台（平台类型：AGENT）</li>
     * </ul>
     *
     * @param platform 平台
     * @return 执行结果
     */
    private boolean noAdapterSupportRemark(PlatformDO platform) {
        return StringUtils.isBlank(platform.getShopCode()) || "AGENT".equals(platform.getPlatformType());
    }

    public static void main(String[] args) {
        TradeRemarkCmdExe exe = new TradeRemarkCmdExe(null, null, null);
        String errorMsg = "remarkJoiner error";
        Assert.isTrue("@OK".equals(exe.remarkJoiner("", "@OK", "，")), errorMsg);
        Assert.isTrue("@OK".equals(exe.remarkJoiner(null, "@OK", "，")), errorMsg);
        Assert.isTrue("*OK，@OK".equals(exe.remarkJoiner("*OK", "@OK", "，")), errorMsg);
        Assert.isTrue("*OK，*OK".equals(exe.remarkJoiner("*OK", "*OK", "，")), errorMsg);
        Assert.isTrue("*OK，*OK，@OK".equals(exe.remarkJoiner("*OK", "*OK，@OK", "，")), errorMsg);
    }

}
