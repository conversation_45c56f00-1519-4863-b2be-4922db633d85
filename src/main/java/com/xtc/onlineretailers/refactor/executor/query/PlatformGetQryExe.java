package com.xtc.onlineretailers.refactor.executor.query;

import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.refactor.repository.PlatformRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 查询平台
 */
@RequiredArgsConstructor
@Component
public class PlatformGetQryExe {

    private final PlatformRepository platformRepository;

    /**
     * 查询平台
     *
     * @param shopCode 店铺代码
     */
    public PlatformDO getByShopCode(String shopCode) {
        return platformRepository.getByShopCode(shopCode)
                .orElseThrow(() -> new IllegalArgumentException("平台不存在 shopCode: " + shopCode));
    }

    /**
     * 查询平台
     *
     * @param shopCode     店铺代码
     * @param platformType 平台类型
     */
    public PlatformDO getByShopCode(String shopCode, String platformType) {
        return platformRepository.getByShopCode(shopCode, platformType)
                .orElseThrow(() -> new IllegalArgumentException("平台不存在 shopCode: " + shopCode + ", platformType: " + platformType));
    }

    /**
     * 查询平台
     *
     * @param platformId 平台id
     */
    public PlatformDO getByPlatformId(int platformId) {
        return platformRepository.getByPlatformId(platformId)
                .orElseThrow(() -> new IllegalArgumentException("平台不存在 platformId: " + platformId));
    }

}
