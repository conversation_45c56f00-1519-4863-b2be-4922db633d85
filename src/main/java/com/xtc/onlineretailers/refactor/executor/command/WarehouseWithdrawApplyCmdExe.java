package com.xtc.onlineretailers.refactor.executor.command;

import com.xtc.marketing.adapterservice.warehouse.AdapterWarehouseFeignClient;
import com.xtc.marketing.adapterservice.warehouse.dto.command.OutboundApplyCmd;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.WarehouseDO;
import com.xtc.onlineretailers.pojo.entity.WarehouseProductDO;
import com.xtc.onlineretailers.pojo.entity.WithdrawStoreOrderDO;
import com.xtc.onlineretailers.pojo.entity.WithdrawStoreOrderItemDO;
import com.xtc.onlineretailers.refactor.constant.WarehouseConstant;
import com.xtc.onlineretailers.refactor.converter.WithdrawStoreOrderConverter;
import com.xtc.onlineretailers.refactor.executor.query.WarehouseGetQryExe;
import com.xtc.onlineretailers.refactor.executor.query.WarehouseProductGetQryExe;
import com.xtc.onlineretailers.refactor.pojo.command.WarehouseItemCmd;
import com.xtc.onlineretailers.refactor.pojo.command.WarehouseWithdrawApplyCmd;
import com.xtc.onlineretailers.refactor.repository.WithdrawStoreOrderItemRepository;
import com.xtc.onlineretailers.refactor.repository.WithdrawStoreOrderRepository;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.springboot.pojo.entity.GlobalContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 仓库退库申请
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class WarehouseWithdrawApplyCmdExe {

    private final WarehouseGetQryExe warehouseGetQryExe;
    private final WarehouseProductGetQryExe warehouseProductGetQryExe;
    private final WithdrawStoreOrderRepository withdrawStoreOrderRepository;
    private final WithdrawStoreOrderItemRepository withdrawStoreOrderItemRepository;
    private final AdapterWarehouseFeignClient adapterWarehouseFeignClient;
    private final WithdrawStoreOrderConverter withdrawStoreOrderConverter;

    public void execute(WarehouseWithdrawApplyCmd cmd) {
        WarehouseDO warehouse = warehouseGetQryExe.execute(cmd.getWarehouseCode());

        // 创建退库单
        WithdrawStoreOrderDO order = this.createOrder(cmd, warehouse);
        List<WithdrawStoreOrderItemDO> orderItems = this.createOrderItems(order, cmd.getItems());

        // 保存退库单和货物数据
        WarehouseWithdrawApplyCmdExe exe = (WarehouseWithdrawApplyCmdExe) AopContext.currentProxy();
        exe.saveData(order, orderItems);

        // 调用对接服务申请退库，成功和失败都需要保存相关数据
        try {
            OutboundApplyCmd adapterCmd = withdrawStoreOrderConverter.toOutboundApplyCmd(order, orderItems);
            String platformOrderId = adapterWarehouseFeignClient.applyOutbound(adapterCmd).getData();
            order.setShipmentId(platformOrderId);
        } catch (Exception e) {
            order.setOrderStatus("2");
            order.setRequestMessage(e.getMessage());
            throw e;
        } finally {
            withdrawStoreOrderRepository.updateByOrderId(order);
        }
    }

    /**
     * 保存退库单和货物数据
     *
     * @param order 退库单
     * @param items 货物集合
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveData(WithdrawStoreOrderDO order, List<WithdrawStoreOrderItemDO> items) {
        withdrawStoreOrderRepository.save(order);
        withdrawStoreOrderItemRepository.saveBatch(items);
    }

    /**
     * 创建退库单货物集合
     *
     * @param order 退库单
     * @param items 退库申请货物参数
     * @return 退库单货物集合
     */
    private List<WithdrawStoreOrderItemDO> createOrderItems(WithdrawStoreOrderDO order, List<WarehouseItemCmd> items) {
        return items.stream()
                .map(item -> {
                    // 确认仓库物料已维护
                    WarehouseProductDO warehouseProduct = warehouseProductGetQryExe.execute(item.getBarcode());
                    WithdrawStoreOrderItemDO withdrawStoreOrderItem = new WithdrawStoreOrderItemDO();
                    withdrawStoreOrderItem.setOrderId(order.getOrderId());
                    withdrawStoreOrderItem.setInventoryStatus(item.getInventoryStatus());
                    withdrawStoreOrderItem.setBarcode(warehouseProduct.getProductBarcode());
                    withdrawStoreOrderItem.setProductName(warehouseProduct.getProductName());
                    withdrawStoreOrderItem.setNum(item.getQuantity());
                    withdrawStoreOrderItem.setAddTime(order.getAddTime());
                    return withdrawStoreOrderItem;
                })
                .collect(Collectors.toList());
    }

    /**
     * 创建退库单
     *
     * @param cmd       退库申请参数
     * @param warehouse 仓库
     * @return 退库单
     */
    private WithdrawStoreOrderDO createOrder(WarehouseWithdrawApplyCmd cmd, WarehouseDO warehouse) {
        Date nowDate = new Date();

        // 生成退库单号
        String orderId = this.createOrderId();

        WithdrawStoreOrderDO withdrawStoreOrder = new WithdrawStoreOrderDO();
        withdrawStoreOrder.setStoreCode(warehouse.getWarehouseStorage());
        withdrawStoreOrder.setStoreName(warehouse.getWarehouseName());
        withdrawStoreOrder.setOrderId(orderId);
        withdrawStoreOrder.setApplyTime(nowDate);
        withdrawStoreOrder.setRemake(cmd.getMemo());
        withdrawStoreOrder.setCreatorName(GlobalContext.getUser().getName());
        withdrawStoreOrder.setAddTime(nowDate);
        withdrawStoreOrder.setOrderStatus("1");
        withdrawStoreOrder.setIsResolve("-1");

        // 设置收件人信息
        withdrawStoreOrder.setReceiverName(cmd.getReceiverName());
        withdrawStoreOrder.setReceiverMobile(cmd.getReceiverMobile());
        withdrawStoreOrder.setReceiverPhone(cmd.getReceiverPhone());
        withdrawStoreOrder.setReceiverProvince(cmd.getReceiverProvince());
        withdrawStoreOrder.setReceiverCity(cmd.getReceiverCity());
        withdrawStoreOrder.setReceiverDistrict(cmd.getReceiverDistrict());
        withdrawStoreOrder.setReceiverAddress(cmd.getReceiverAddress());

        // 合计货物总数
        cmd.getItems().stream()
                .map(WarehouseItemCmd::getQuantity)
                .filter(quantity -> quantity != null && quantity > 0)
                .reduce(Integer::sum)
                .ifPresent(withdrawStoreOrder::setTotalNum);
        return withdrawStoreOrder;
    }

    /**
     * 生成退库单号：固定前缀 + 时间字符串（毫秒）
     *
     * @return 退库单号
     */
    private String createOrderId() {
        String orderId = WarehouseConstant.WITHDRAW_ORDER_ID_PREFIX + DateUtil.nowDateTimeStr("yyyyMMdd-HHmmssSSS");
        // 检查退库单号已存在
        withdrawStoreOrderRepository.getByOrderId(orderId).ifPresent(order -> {
            throw new GlobalDefaultException("退库操作太频繁，请稍后再试");
        });
        return orderId;
    }

}
