package com.xtc.onlineretailers.refactor.executor.command;

import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.SmsDO;
import com.xtc.onlineretailers.pojo.entity.SystemParamDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.repository.SmsRepository;
import com.xtc.onlineretailers.refactor.repository.SystemParamRepository;
import com.xtc.onlineretailers.refactor.util.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 保存订单拦截记录
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SaveTradeInterceptCmdExe {

    private final SmsRepository smsRepository;
    private final SystemParamRepository systemParamRepository;
    private final RedissonUtil redissonUtil;

    /**
     * 保存订单拦截记录
     *
     * @param trade   订单
     * @param title   标题
     * @param content 内容
     */
    public void execute(TradeDO trade, String title, String content) {
        if (trade == null || StringUtils.isAnyBlank(title, content)) {
            throw new IllegalArgumentException("订单拦截记录保存失败 - 方法入参不合法");
        }
        String systemParamName = GlobalConstant.SYSTEM_NOTICE_PHONES;
        String phone = systemParamRepository.getByName(systemParamName)
                .map(SystemParamDO::getParamValue)
                .orElseThrow(() -> new GlobalDefaultException("订单拦截记录保存失败 - 系统参数 " + systemParamName + " 未配置"));

        // 查询拦截记录，不存在则生成新的拦截记录，加锁避免产生重复数据
        redissonUtil.lock(
                "saveTradeIntercept",
                trade.getPlatformTradeId(),
                () -> this.saveTradeIntercept(trade, title, content, phone),
                GlobalDefaultException.of("订单拦截记录保存失败 - 加锁失败，需要重试处理")
        );
    }

    /**
     * 保存订单拦截记录
     *
     * @param trade   订单
     * @param title   标题
     * @param content 内容
     * @param phone   手机号
     */
    private void saveTradeIntercept(TradeDO trade, String title, String content, String phone) {
        SmsDO intercept = smsRepository.getByTradeId(trade.getPlatformTradeId())
                .orElseGet(() -> {
                    SmsDO newIntercept = new SmsDO();
                    newIntercept.setTelephone(phone);
                    newIntercept.setPlatformId(trade.getPlatformId());
                    newIntercept.setPlatformTradeId(trade.getPlatformTradeId());
                    newIntercept.setTitle(title);
                    newIntercept.setContent(content);
                    newIntercept.setReceiverCount(1);
                    newIntercept.setMessageNumber(1);
                    // 重点字段需要留意
                    newIntercept.setIsIntercept(0);
                    newIntercept.setTradeStatus(0);
                    return newIntercept;
                });

        // 检查重复的拦截记录
        if (intercept.getId() != null) {
            log.warn("订单拦截记录保存失败 - 已存在拦截记录 {}", intercept.getId());
            return;
        }
        // 保存拦截记录
        smsRepository.save(intercept);
    }

}
