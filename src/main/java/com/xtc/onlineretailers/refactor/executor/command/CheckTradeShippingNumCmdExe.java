package com.xtc.onlineretailers.refactor.executor.command;

import com.xtc.marketing.adapterservice.shop.AdapterShopFeignClient;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.OrderItemDTO;
import com.xtc.marketing.adapterservice.shop.dto.query.OrderGetQry;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import com.xtc.onlineretailers.enums.QuestionTypeEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.*;
import com.xtc.onlineretailers.refactor.enums.DeliverGoodsReportStatusEnum;
import com.xtc.onlineretailers.refactor.enums.OrderItemNeedShippingState;
import com.xtc.onlineretailers.refactor.executor.query.PlatformGetQryExe;
import com.xtc.onlineretailers.refactor.executor.query.ProductGetQryExe;
import com.xtc.onlineretailers.refactor.repository.DeliverGoodsReportNewRepository;
import com.xtc.onlineretailers.refactor.repository.OrderRepository;
import com.xtc.onlineretailers.refactor.repository.SmsRepository;
import com.xtc.onlineretailers.refactor.repository.TradeRepository;
import com.xtc.onlineretailers.refactor.util.RedissonUtil;
import com.xtc.onlineretailers.util.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * 订单发货数量核对
 */
@RequiredArgsConstructor
@Component
@Slf4j
public class CheckTradeShippingNumCmdExe {

    private final RedissonUtil redissonUtil;
    private final PlatformGetQryExe platformGetQryExe;
    private final AdapterShopFeignClient adapterShopFeignClient;
    private final ProductGetQryExe productGetQryExe;
    private final SmsRepository smsRepository;
    private final OrderRepository orderRepository;
    private final TradeRepository tradeRepository;
    private final DeliverGoodsReportNewRepository deliverGoodsReportNewRepository;

    public void execute(TradeDO trade) {
        redissonUtil.tryLock(
                "trade:checkShippingNum",
                trade.getPlatformTradeId(),
                () -> {
                    log.info("开始核对订单发货数量 {}", trade.getPlatformTradeId());
                    PlatformDO platform = platformGetQryExe.getByPlatformId(trade.getPlatformId());
                    if (StringUtils.isBlank(platform.getShopCode())) {
                        log.info("非对接订单，无需核对 {}", trade.getPlatformTradeId());
                        return;
                    }
                    Optional<OrderDTO> platformOrderOpt = this.getPlatformOrder(platform, trade);
                    if (!platformOrderOpt.isPresent()) {
                        log.info("平台订单不存在 {}", trade.getPlatformTradeId());
                        return;
                    }
                    OrderDTO platformOrder = platformOrderOpt.get();
                    // 计算平台明细数量
                    int platformNum = this.sumPlatformNum(platform, platformOrder);
                    // 计算系统明细数量
                    int tradeNum = this.sumTradeNum(trade);
                    // 第一次核对
                    if (platformNum == tradeNum) {
                        log.info("第一次核对无误 {}", trade.getPlatformTradeId());
                        return;
                    }
                    log.info("第一次核对不一致 {} [平台 {} 系统 {}]", trade.getPlatformTradeId(), platformNum, tradeNum);
                    // 当平台订单数量大于订单明细数量时，查询额外订单明细数量，进行第二次核对
                    if (platformNum > tradeNum) {
                        boolean secondCheckSuccess = this.secondCheckAddExtraNum(trade, platform, platformOrder, platformNum, tradeNum);
                        if (secondCheckSuccess) {
                            log.info("第二次核对无误 {}", trade.getPlatformTradeId());
                            return;
                        }
                    }
                    // 构建发货异常报表
                    DeliverGoodsReportNewDO reportDO = this.buildDeliverGoodsReportNew(trade, platformNum, tradeNum);
                    deliverGoodsReportNewRepository.saveOrUpdateByTradeId(reportDO);
                },
                new GlobalDefaultException("订单正在核对中，放弃本次核对 " + trade.getPlatformTradeId())
        );
    }

    /**
     * 获取平台订单
     *
     * @param platform 平台
     * @param trade    订单
     * @return 平台订单
     */
    private Optional<OrderDTO> getPlatformOrder(PlatformDO platform, TradeDO trade) {
        OrderGetQry qry = OrderGetQry.builder().shopCode(platform.getShopCode()).orderNo(trade.getPlatformTradeId()).build();
        SingleResponse<OrderDTO> response = adapterShopFeignClient.getOrder(qry);
        return Optional.ofNullable(response).map(SingleResponse::getData);
    }

    /**
     * 计算平台明细数量
     *
     * @param platformOrder 平台订单
     * @return 平台明细数量
     */
    private int sumPlatformNum(PlatformDO platform, OrderDTO platformOrder) {
        Predicate<OrderItemDTO> needShippingItem = item -> {
            ProductInfoDO product;
            try {
                product = productGetQryExe.execute(platform, item.getSkuErpCode());
            } catch (Exception e) {
                return true;
            }
            return product.getMaterialType() != null
                    && product.getMaterialType() != 3
                    && OrderItemNeedShippingState.isNeedShipping(platform.getPlatformCode(), item.getItemState());
        };
        // 筛选需要发货的明细
        Map<String, Integer> needShippingItems = platformOrder.getItems().stream()
                .filter(needShippingItem)
                .collect(Collectors.groupingBy(OrderItemDTO::getSkuErpCode,
                        Collectors.reducing(0, OrderItemDTO::getNum, Integer::sum)));
        log.info("计算平台明细数量，需要发货的物料 {}", needShippingItems);
        return needShippingItems.values().stream().mapToInt(Integer::intValue).sum();
    }

    /**
     * 计算订单明细数量
     *
     * @param trade 订单
     * @return 订单明细数量
     */
    private int sumTradeNum(TradeDO trade) {
        // 筛选非虚拟物料
        Predicate<OrderDO> notVirtualMaterial = order -> order.getMaterialType() != null && order.getMaterialType() != 3;
        // 筛选正常明细
        Predicate<OrderDO> normalOrder = order -> order.getOrderType() != null && order.getOrderType() == 1;
        // 筛选自管的明细（非第三方公司，第三方公司：物料名称前两位是新品）
        Predicate<OrderDO> selfOrder = order -> !"新品".equals(StringUtils.left(order.getErpName(), 2));
        // 筛选自管的已发货的明细
        Map<String, Integer> selfShippedOrders = orderRepository.listByTradeId(trade.getPlatformTradeId()).stream()
                .filter(notVirtualMaterial)
                .filter(normalOrder)
                .filter(selfOrder)
                .collect(Collectors.groupingBy(OrderDO::getErpCode, Collectors.reducing(0, OrderDO::getNum, Integer::sum)));
        log.info("计算系统明细数量，已发货的物料 {}", selfShippedOrders);
        return selfShippedOrders.values().stream().mapToInt(Integer::intValue).sum();
    }

    /**
     * 第二次核对，查询额外订单明细数量，进行第二次核对
     *
     * @param trade         订单
     * @param platform      平台
     * @param platformOrder 平台订单
     * @param platformNum   平台数量
     * @param tradeNum      系统数量
     * @return 执行结果
     */
    private boolean secondCheckAddExtraNum(TradeDO trade, PlatformDO platform, OrderDTO platformOrder,
                                           int platformNum, int tradeNum) {
        // 计算第二次核对需要补充的系统数量
        int secondExtraNum = this.sumSecondExtraNum(trade, platform, platformOrder);
        if (secondExtraNum <= 0) {
            return false;
        }
        int secondTradeNum = tradeNum + secondExtraNum;
        if (platformNum == secondTradeNum) {
            return true;
        }
        log.info("第二次核对不一致 {} [平台 {} 系统 {}]", trade.getPlatformTradeId(), platformNum, secondTradeNum);
        return false;
    }

    /**
     * 计算第二次核对补充系统数量
     *
     * @param trade         订单
     * @param platform      平台
     * @param platformOrder 平台订单
     * @return 第二次核对补充系统数量
     */
    private int sumSecondExtraNum(TradeDO trade, PlatformDO platform, OrderDTO platformOrder) {
        // 补充的订单明细
        List<OrderDO> extraOrders = Collections.emptyList();
        // 天猫平台，订单类型为 step 的订单
        if ("TMALL".equals(platform.getPlatformCode()) && "step".equals(platformOrder.getOrderType())) {
            // 查询所有关联的订单，合计所有订单明细的数量
            List<String> tradeIds = tradeRepository.listTradeIdByOldPlatformTradeId(trade.getPlatformTradeId());
            extraOrders = orderRepository.listByTradeIds(tradeIds);
        }
        // 计算第二次核对补充系统数量
        if (CollectionUtils.isEmpty(extraOrders)) {
            return 0;
        }
        Map<String, Integer> extraErpCode = extraOrders.stream()
                .collect(Collectors.groupingBy(OrderDO::getErpCode, Collectors.reducing(0, OrderDO::getNum, Integer::sum)));
        log.info("计算第二次核对补充系统数量，额外物料 {}", extraErpCode);
        return extraErpCode.values().stream().mapToInt(Integer::intValue).sum();
    }

    /**
     * 构建发货异常报表
     *
     * @param trade       订单
     * @param platformNum 平台数量
     * @param tradeNum    系统数量
     * @return 发货异常报表
     */
    private DeliverGoodsReportNewDO buildDeliverGoodsReportNew(TradeDO trade, int platformNum, int tradeNum) {
        SmsDO smsDO = smsRepository.getByTradeId(trade.getPlatformTradeId()).orElse(null);
        // 设置拦截状态 1,已拦截，0,未拦截
        String interceptStatus = smsDO == null ? "0" : "1";
        // 设置发货异常报表状态
        String deliverGoodsReportStatus = DeliverGoodsReportStatusEnum.UNTREATED.name();
        // 卖家设置备注包含拦截，设置发货报表状态无需处理
        if (StringUtils.isNotBlank(trade.getSellerRemark()) && trade.getSellerRemark().contains("拦截")) {
            deliverGoodsReportStatus = DeliverGoodsReportStatusEnum.NO_PROCESSING.name();
        }
        // 拦截状态为 1 时设置发货报表状态无需处理
        if ("1".equals(interceptStatus)) {
            deliverGoodsReportStatus = DeliverGoodsReportStatusEnum.NO_PROCESSING.name();
        }
        DeliverGoodsReportNewDO reportDO = new DeliverGoodsReportNewDO();
        reportDO.setQuestionType(QuestionTypeEnum.DETAILED_QUANTITY_ANOMALY.name());
        reportDO.setQuestionDescribe("订单明细不一致，平台 " + platformNum + " 系统 " + tradeNum);
        reportDO.setInterceptStatus(interceptStatus);
        reportDO.setStatus(deliverGoodsReportStatus);
        reportDO.setPlatformNum(platformNum);
        reportDO.setBusinessNum(tradeNum);
        reportDO.setPlatformId(trade.getPlatformId());
        reportDO.setPlatformName(trade.getPlatformName());
        reportDO.setPlatformTradeId(trade.getPlatformTradeId());
        reportDO.setPaymentTime(DateUtil.dateToLocalDateTime(trade.getPaymentTime()));
        reportDO.setShippingTime(DateUtil.dateToLocalDateTime(trade.getShippingTime()));
        reportDO.setExpressTrackingNo(trade.getExpressTrackingNo());
        reportDO.setExpressCompany(trade.getExpressCompany());
        reportDO.setProductDetail(trade.getProductDetail());
        reportDO.setRemake(trade.getSellerRemark());
        reportDO.setCreateTime(LocalDateTime.now());
        return reportDO;
    }

}
