package com.xtc.onlineretailers.refactor.executor.command;

import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.OrderDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.constant.PlatformConstant;
import com.xtc.onlineretailers.refactor.executor.query.TradeGetQryExe;
import com.xtc.onlineretailers.refactor.pojo.entity.SupplierOrderDO;
import com.xtc.onlineretailers.refactor.pojo.entity.SupplierOrderItemDO;
import com.xtc.onlineretailers.refactor.repository.OrderRepository;
import com.xtc.onlineretailers.refactor.repository.SupplierOrderItemRepository;
import com.xtc.onlineretailers.refactor.repository.SupplierOrderRepository;
import com.xtc.onlineretailers.util.BeanCopier;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.springboot.pojo.entity.GlobalContext;
import lombok.RequiredArgsConstructor;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 供应商抛单
 */
@RequiredArgsConstructor
@Component
public class SupplierOrderConfirmCmdExe {

    private final TradeGetQryExe tradeGetQryExe;
    private final SupplierOrderRepository supplierOrderRepository;
    private final OrderRepository ordersRepository;
    private final SupplierOrderItemRepository supplierOrderItemRepository;

    public void execute(SupplierOrderConfirmCmd cmd) {
        for (String tradeId : cmd.getTradeIds()) {
            TradeDO tradeDO = tradeGetQryExe.execute(tradeId);
            checkData(tradeDO);
            SupplierOrderDO supplierOrderDO = this.createSupplierOrder(tradeDO, cmd.getSupplierCode());
            List<SupplierOrderItemDO> supplierOrderItems = this.createSupplierOrderItems(tradeDO.getPlatformTradeId());
            SupplierOrderConfirmCmdExe supplierOrderConfirmCmdExe = (SupplierOrderConfirmCmdExe) AopContext.currentProxy();
            supplierOrderConfirmCmdExe.saveData(supplierOrderDO, supplierOrderItems);
        }
    }

    /**
     * 创建供应商订单明细列表
     *
     * @param tradeId 订单号
     * @return 供应商订单明细列表
     */
    private List<SupplierOrderItemDO> createSupplierOrderItems(String tradeId) {
        List<OrderDO> orderDOS = ordersRepository.listByTradeId(tradeId);
        return orderDOS.stream()
                .map(order -> {
                    SupplierOrderItemDO itemDO = BeanCopier.copy(order, SupplierOrderItemDO::new, "id");
                    itemDO.setTradeId(tradeId);
                    return itemDO;
                })
                .collect(Collectors.toList());
    }

    /**
     * 创建供应商订单
     *
     * @param tradeDO      订单
     * @param supplierCode 供应商编码
     * @return 供应商订单
     */
    private SupplierOrderDO createSupplierOrder(TradeDO tradeDO, String supplierCode) {
        SupplierOrderDO supplierOrderDO = BeanCopier.copy(tradeDO, SupplierOrderDO::new, "id");
        supplierOrderDO.setTradeId(tradeDO.getPlatformTradeId());
        supplierOrderDO.setCreator(GlobalContext.getUser().getName());
        supplierOrderDO.setCreateTime(LocalDateTime.now());
        supplierOrderDO.setSupplierCode(supplierCode);
        supplierOrderDO.setPaymentTime(DateUtil.dateToLocalDateTime(tradeDO.getPaymentTime()));
        return supplierOrderDO;
    }

    /**
     * 校验数据
     *
     * @param tradeDO 订单
     */
    private void checkData(TradeDO tradeDO) {
        OrderStatus orderStatus = OrderStatus.getEnum(tradeDO.getStatusOrder());
        boolean isOrderStatus = orderStatus == OrderStatus.WAIT_SHIP || orderStatus == OrderStatus.ORDER_CONFIRMED;
        if (!isOrderStatus) {
            throw GlobalDefaultException.of("不允许供应商抛单，不是待发货或已确认状态 %s", tradeDO.getPlatformTradeId());
        }
        Optional<SupplierOrderDO> supplierOrderOpt = supplierOrderRepository.getByTradeId(tradeDO.getPlatformTradeId());
        supplierOrderOpt.ifPresent(supplierOrderDO -> {
            throw GlobalDefaultException.of("不允许供应商抛单，订单 %s 已存在", tradeDO.getPlatformTradeId());
        });
        if (!Objects.equals(tradeDO.getPlatformId(), PlatformConstant.XTC_SHOP)) {
            throw GlobalDefaultException.of("不允许供应商抛单，订单 %s", tradeDO.getPlatformTradeId());
        }
        if (!tradeDO.getProductDetail().contains("新品")) {
            throw GlobalDefaultException.of("不允许供应商抛单，订单的主要商品不包含文案 [新品] %s", tradeDO.getPlatformTradeId());
        }
    }

    /**
     * 保存数据
     *
     * @param supplierOrderDO    供应商订单
     * @param supplierOrderItems 供应商订单明细
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveData(SupplierOrderDO supplierOrderDO, List<SupplierOrderItemDO> supplierOrderItems) {
        supplierOrderRepository.save(supplierOrderDO);
        supplierOrderItemRepository.saveBatch(supplierOrderItems);
    }

}
