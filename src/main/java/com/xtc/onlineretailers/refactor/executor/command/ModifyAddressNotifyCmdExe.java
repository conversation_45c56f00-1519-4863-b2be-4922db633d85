package com.xtc.onlineretailers.refactor.executor.command;

import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.enums.SFStatus;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.ability.checker.TradeShippingChecker;
import com.xtc.onlineretailers.refactor.pojo.command.ModifyAddressNotifyCmd;
import com.xtc.onlineretailers.refactor.repository.TradeRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 修改地址通知处理
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ModifyAddressNotifyCmdExe {

    private final TradeRepository tradeRepository;
    private final TradeShippingChecker tradeShippingChecker;
    private final WarehouseOutboundCancelCmdExe warehouseOutboundCancelCmdExe;

    /**
     * 修改地址通知处理
     *
     * @param cmd 参数
     */
    public void execute(ModifyAddressNotifyCmd cmd) {
        // 判断订单未同步
        Optional<TradeDO> tradeOpt = tradeRepository.getByTradeId(cmd.getTradeId());
        if (Boolean.FALSE.equals(tradeOpt.isPresent())) {
            log.info("更新收件人地址，订单未同步 {}", cmd.getTradeId());
            return;
        }
        TradeDO trade = tradeOpt.get();
        // 判断订单是待处理状态
        OrderStatus orderStatus = OrderStatus.getEnum(trade.getStatusOrder());
        if (orderStatus == OrderStatus.WAIT_DEAL) {
            this.updateReceiverAddress(cmd);
            log.info("更新收件人地址，待处理的订单 {}", cmd.getTradeId());
            return;
        }
        // 判断订单未发货
        boolean notShipped = tradeShippingChecker.isNotShipped(trade);
        if (notShipped) {
            this.updateReceiverAddress(cmd);
            log.info("更新收件人地址，订单未发货 {}", cmd.getTradeId());
            return;
        }
        // 顺丰仓如果已抛单但是未发货，则可以尝试取消出库，取消出库成功后更新收件人地址
        SFStatus sfStatus = SFStatus.getEnum(trade.getStatusSf());
        if (sfStatus == SFStatus.HAS_TURN_LEFT) {
            try {
                warehouseOutboundCancelCmdExe.execute(trade.getPlatformTradeId());
                this.updateReceiverAddress(cmd);
                log.info("更新收件人地址，顺丰仓取消出库成功 {}", cmd.getTradeId());
                return;
            } catch (Exception e) {
                throw GlobalDefaultException.of("顺丰仓取消出库失败：%s", e.getMessage());
            }
        }
        throw GlobalDefaultException.of("订单已发货或已取消，不支持更新地址 %s", cmd.getTradeId());
    }

    /**
     * 更新收件人地址
     *
     * @param cmd 参数
     */
    private void updateReceiverAddress(ModifyAddressNotifyCmd cmd) {
        TradeDO updateTrade = new TradeDO();
        updateTrade.setPlatformTradeId(cmd.getTradeId());
        updateTrade.setReceiverOaid(cmd.getReceiverOaid());
        updateTrade.setReceiverName(cmd.getReceiverName());
        updateTrade.setReceiverMobile(cmd.getReceiverMobile());
        updateTrade.setReceiverProvince(cmd.getReceiverProvince());
        updateTrade.setReceiverCity(cmd.getReceiverCity());
        updateTrade.setReceiverDistrict(cmd.getReceiverDistrict());
        updateTrade.setReceiverAddress(StringUtils.join(cmd.getReceiverTown(), cmd.getReceiverAddress()));
        tradeRepository.updateByTradeId(updateTrade);
    }

}
