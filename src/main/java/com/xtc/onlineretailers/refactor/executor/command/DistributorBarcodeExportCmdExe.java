package com.xtc.onlineretailers.refactor.executor.command;

import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.onlineretailers.enums.AppointmentDownloadStatusEnum;
import com.xtc.onlineretailers.enums.AppointmentDownloadType;
import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.AppointmentDownloadDO;
import com.xtc.onlineretailers.refactor.executor.query.DistributorBarcodePageQryExe;
import com.xtc.onlineretailers.refactor.pojo.dto.excel.DistributorBarcodeExportDTO;
import com.xtc.onlineretailers.refactor.pojo.entity.DistributorBarcodeDO;
import com.xtc.onlineretailers.refactor.pojo.query.DistributorBarcodePageQry;
import com.xtc.onlineretailers.refactor.repository.AppointmentDownloadRepository;
import com.xtc.onlineretailers.util.CollectionCopier;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.ExcelUtil;
import com.xtc.onlineretailers.util.MinioManager;
import com.xtc.springboot.pojo.entity.UserDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.util.Date;
import java.util.List;

/**
 * 分销导出
 */
@RequiredArgsConstructor
@Component
@Slf4j
public class DistributorBarcodeExportCmdExe {

    private final MinioManager minioManager;
    private final AppointmentDownloadRepository appointmentDownloadRepository;
    private final DistributorBarcodePageQryExe distributorBarcodePageQryExe;

    @Async("asyncExecutor")
    public void execute(DistributorBarcodePageQry qry, UserDetail user) {
        PageResponse<DistributorBarcodeDO> pageResponse = distributorBarcodePageQryExe.execute(qry);
        List<DistributorBarcodeDO> data = pageResponse.getData();
        String fileName = DateUtil.dateToStr(new Date(), "yyyy-MM-dd") + "分销数据导出";
        // 保存预约信息
        AppointmentDownloadDO appointmentDownloadDO = new AppointmentDownloadDO();
        appointmentDownloadDO.setBookingPerson(user.getName());
        appointmentDownloadDO.setType(AppointmentDownloadType.DISTRIBUTOR.name());
        appointmentDownloadDO.setFileName(fileName);
        appointmentDownloadDO.setStatus(AppointmentDownloadStatusEnum.PROCESSING.name());
        appointmentDownloadRepository.save(appointmentDownloadDO);
        // 导出Excel到minio
        List<DistributorBarcodeExportDTO> distributorBarcodes = CollectionCopier.copy(data, DistributorBarcodeExportDTO::new);
        String downloadUrl = exportExcelToMinio(distributorBarcodes, fileName);
        // 更新预约信息
        AppointmentDownloadDO update = new AppointmentDownloadDO();
        update.setId(appointmentDownloadDO.getId());
        update.setDownloadUrl(StringUtils.isNotBlank(downloadUrl) ? downloadUrl : null);
        update.setStatus(StringUtils.isNotBlank(downloadUrl) ? AppointmentDownloadStatusEnum.COMPLETED.name() : AppointmentDownloadStatusEnum.FAIL.name());
        this.appointmentDownloadRepository.updateById(update);
    }

    /**
     * 导出Excel到minio
     *
     * @param data     数据
     * @param fileName 文件名称
     * @return 文件路径
     */
    private String exportExcelToMinio(List<DistributorBarcodeExportDTO> data, String fileName) {
        // 文件名无需带上根路径 "/"
        String objectName = "download/" + DateUtil.nowDateStr("yyyyMMdd") + "/" + fileName + ".xlsx";
        ByteArrayInputStream byteArrayInputStream = ExcelUtil.writeDataToStream(data, DistributorBarcodeExportDTO.class);
        try {
            this.minioManager.putObject(objectName, byteArrayInputStream);
        } catch (Exception e) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_APPOINTMENT_FILE_UPLOAD, e);
        }
        return objectName;
    }

}
