package com.xtc.onlineretailers.refactor.executor.command;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 供应商抛单
 */
@Getter
@Setter
@ToString
public class SupplierOrderConfirmCmd {

    /**
     * 订单号列表
     */
    @NotEmpty
    @Size(max = 20)
    private List<String> tradeIds;
    /**
     * 供应商编码
     */
    @NotBlank
    @Length(max = 20)
    private String supplierCode;

}
