package com.xtc.onlineretailers.refactor.executor.command;

import com.xtc.marketing.invoiceservice.invoice.InvoiceFeignClient;
import com.xtc.marketing.invoiceservice.invoice.NotSystemInvoiceFeignClient;
import com.xtc.marketing.invoiceservice.invoice.dto.ApplyHistoryDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDetailDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceApplyDetailGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.NotSystemRedDetailQry;
import com.xtc.marketing.invoiceservice.invoice.enums.ApplyStateEnum;
import com.xtc.marketing.invoiceservice.invoice.enums.CreateTypeEnum;
import com.xtc.onlineretailers.enums.InvoiceStatus;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.InvoiceItemDO;
import com.xtc.onlineretailers.pojo.entity.InvoiceMainDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.constant.InvoiceApplyConstant;
import com.xtc.onlineretailers.refactor.enums.InvoiceState;
import com.xtc.onlineretailers.refactor.pojo.bo.InvoiceHistoryBO;
import com.xtc.onlineretailers.refactor.pojo.entity.InvoiceApplyDO;
import com.xtc.onlineretailers.refactor.repository.InvoiceApplyRepository;
import com.xtc.onlineretailers.refactor.repository.InvoiceItemRepository;
import com.xtc.onlineretailers.refactor.repository.InvoiceMainRepository;
import com.xtc.onlineretailers.refactor.repository.TradeRepository;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.MoneyUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 发票 - 开票结果同步
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class InvoiceCreateResultCmdExe {

    private final InvoiceApplyRepository invoiceApplyRepository;
    private final InvoiceFeignClient invoiceFeignClient;
    private final NotSystemInvoiceFeignClient notSystemInvoiceFeignClient;
    private final InvoiceMainRepository invoiceMainRepository;
    private final InvoiceItemRepository invoiceItemRepository;
    private final TradeRepository tradeRepository;

    public void execute(Long applyId) {
        // 检查发票申请数据合法性
        InvoiceApplyDO apply = invoiceApplyRepository.getById(applyId);
        if (apply == null) {
            throw GlobalDefaultException.of("发票申请不存在");
        }
        if (apply.getInvoiceState() != InvoiceState.ALREADY_APPLIED) {
            throw GlobalDefaultException.of("发票申请状态不允许操作 [%s]", apply.getInvoiceState());
        }
        String serialNo = StringUtils.defaultIfBlank(apply.getRedSerialNo(), apply.getBlueSerialNo());
        // 查询发票申请详情，判断数电票还是税控票
        InvoiceApplyDetailDTO applyDetailDTO = StringUtils.isNotBlank(apply.getApplyId()) ?
                this.queryInvoiceApply(apply) : this.queryNormalIssueRed(apply);
        // 通过发票申请状态判断开票结果
        boolean applySuccess = ApplyStateEnum.of(applyDetailDTO.getApplyState()).map(ApplyStateEnum::success).orElse(false);
        if (applySuccess && applyDetailDTO.getInvoice() != null) {
            log.info("开票成功 {} {}", apply.getTradeId(), serialNo);
            this.saveInvoiceSuccess(apply, applyDetailDTO.getInvoice());
        } else if (ApplyStateEnum.FAILED.equalsName(applyDetailDTO.getApplyState())) {
            String failMsg = applyDetailDTO.getApplyHistory().get(applyDetailDTO.getApplyHistory().size() - 1).getHandleRemark();
            log.info("开票失败 {} {} {}", apply.getTradeId(), serialNo, failMsg);
            this.saveInvoiceFail(apply, applyDetailDTO.getInvoice(), failMsg);
        } else if (ApplyStateEnum.PENDING.equalsName(applyDetailDTO.getApplyState())
                || ApplyStateEnum.ACCEPTED.equalsName(applyDetailDTO.getApplyState())) {
            log.info("开票中，不处理 {} {}", apply.getTradeId(), serialNo);
        } else {
            String errorMsg = String.format("开票状态不合法，需要人工确认 [%s] %s %s", applyDetailDTO.getApplyState(), apply.getTradeId(), serialNo);
            throw GlobalDefaultException.of(errorMsg);
        }
    }

    /**
     * 查询发票申请详情
     *
     * @param apply 发票申请
     * @return 发票申请详情
     */
    private InvoiceApplyDetailDTO queryInvoiceApply(InvoiceApplyDO apply) {
        InvoiceApplyDetailGetQry qry = InvoiceApplyDetailGetQry.builder().applyId(apply.getApplyId()).build();
        return invoiceFeignClient.applyDetail(qry).getData();
    }

    /**
     * 查询税控发票冲红结果
     *
     * @param apply 发票申请
     * @return 发票申请详情
     */
    private InvoiceApplyDetailDTO queryNormalIssueRed(InvoiceApplyDO apply) {
        InvoiceApplyDetailDTO applyDetailDTO = new InvoiceApplyDetailDTO();
        try {
            // 查询税控发票冲红结果
            NotSystemRedDetailQry qry = NotSystemRedDetailQry.builder()
                    .sellerTaxNo(InvoiceApplyConstant.PAYEE_REGISTER_NO)
                    .serialNo(apply.getRedSerialNo())
                    .invoiceDate(apply.getRedTime().toLocalDate())
                    .build();
            InvoiceDTO invoiceDTO = notSystemInvoiceFeignClient.notSystemRedDetail(qry).getData();
            applyDetailDTO.setInvoice(invoiceDTO);
            // 接口有数据判断冲红成功，没有数据则开票中
            applyDetailDTO.setApplyState(invoiceDTO != null ? ApplyStateEnum.ISSUED_RED.name() : ApplyStateEnum.PENDING.name());
        } catch (Exception e) {
            // 接口异常则开票失败
            applyDetailDTO.setApplyState(ApplyStateEnum.FAILED.name());
            ApplyHistoryDTO historyDTO = new ApplyHistoryDTO();
            historyDTO.setHandleRemark("发票系统响应 " + e.getMessage());
            applyDetailDTO.setApplyHistory(Collections.singletonList(historyDTO));
        }
        return applyDetailDTO;
    }

    /**
     * 开票成功数据变更
     *
     * @param apply         发票申请
     * @param invoiceResult 发票结果
     */
    private void saveInvoiceSuccess(InvoiceApplyDO apply, InvoiceDTO invoiceResult) {
        boolean isRed = CreateTypeEnum.RED.equalsName(invoiceResult.getCreateType());
        // 更新发票申请数据
        InvoiceApplyDO updateApply = new InvoiceApplyDO();
        updateApply.setId(apply.getId());
        updateApply.setInvoiceId(invoiceResult.getInvoiceId());
        updateApply.setInvoiceHistory(apply.getInvoiceHistory());
        // 开票和冲红更新不同数据
        String remark = StringUtils.isNotBlank(invoiceResult.getInvoiceId()) ?
                String.format("发票id %s", invoiceResult.getInvoiceId()) : null;
        if (isRed) {
            updateApply.setInvoiceState(InvoiceState.CREATE_RED);
            updateApply.setRedTime(invoiceResult.getInvoiceTime());
            updateApply.setRedInvoiceNo(invoiceResult.getRedInvoiceNo());
            updateApply.getInvoiceHistory().add(InvoiceHistoryBO.of("冲红成功", remark));
        } else {
            updateApply.setInvoiceState(InvoiceState.CREATE_BLUE);
            updateApply.setBlueInvoiceId(invoiceResult.getInvoiceId());
            updateApply.setBlueTime(invoiceResult.getInvoiceTime());
            updateApply.setBlueInvoiceCode(invoiceResult.getBlueInvoiceCode());
            updateApply.setBlueInvoiceNo(invoiceResult.getBlueInvoiceNo());
            updateApply.getInvoiceHistory().add(InvoiceHistoryBO.of("开票成功", remark));
        }
        // 更新发票数据
        InvoiceMainDO updateMain = null;
        List<InvoiceItemDO> saveItems = null;
        String serialNo = isRed ? apply.getRedSerialNo() : apply.getBlueSerialNo();
        Optional<InvoiceMainDO> invoiceMainOpt = invoiceMainRepository.getBySerialNo(serialNo);
        if (invoiceMainOpt.isPresent()) {
            InvoiceMainDO invoiceMain = invoiceMainOpt.get();
            // 更新发票主体
            updateMain = new InvoiceMainDO();
            updateMain.setId(invoiceMain.getId());
            String mainStatus = isRed ? "已冲红" : "已开票";
            updateMain.setInvoiceStatus(mainStatus);
            this.updateInvoiceMain(updateMain, invoiceResult);
            // 生成发票明细
            saveItems = this.createInvoiceItemDO(invoiceMain, mainStatus, invoiceResult);
        }
        // 更新订单数据
        TradeDO updateTrade = new TradeDO();
        updateTrade.setPlatformTradeId(apply.getTradeId());
        InvoiceStatus invoiceStatus = CreateTypeEnum.BLUE.equalsName(invoiceResult.getCreateType()) ?
                InvoiceStatus.HAS_RETURNED : InvoiceStatus.WASHED_RED_SUCCESS;
        updateTrade.setInvoiceStatus(invoiceStatus.name());
        // 事务保存发票数据
        InvoiceCreateResultCmdExe exe = (InvoiceCreateResultCmdExe) AopContext.currentProxy();
        exe.saveData(updateApply, updateMain, saveItems, updateTrade);
    }

    /**
     * 开票失败数据变更
     *
     * @param apply         发票申请
     * @param invoiceResult 开票结果
     * @param failMsg       失败信息
     */
    private void saveInvoiceFail(InvoiceApplyDO apply, InvoiceDTO invoiceResult, String failMsg) {
        // 更新发票申请数据
        InvoiceApplyDO updateApply = new InvoiceApplyDO();
        updateApply.setId(apply.getId());
        updateApply.setInvoiceState(InvoiceState.CREATE_FAILED);
        updateApply.setInvoiceHistory(apply.getInvoiceHistory());
        updateApply.getInvoiceHistory().add(InvoiceHistoryBO.of("开票失败", failMsg));
        // 更新发票主体
        InvoiceMainDO updateMain = null;
        String serialNo = StringUtils.defaultIfBlank(apply.getRedSerialNo(), apply.getBlueSerialNo());
        Optional<InvoiceMainDO> invoiceMain = invoiceMainRepository.getBySerialNo(serialNo);
        if (invoiceMain.isPresent()) {
            updateMain = new InvoiceMainDO();
            updateMain.setId(invoiceMain.get().getId());
            updateMain.setInvoiceStatus("已作废");
            this.updateInvoiceMain(updateMain, invoiceResult);
        }
        // 事务保存发票数据
        InvoiceCreateResultCmdExe exe = (InvoiceCreateResultCmdExe) AopContext.currentProxy();
        exe.saveData(updateApply, updateMain, null, null);
    }

    /**
     * 更新发票主体
     *
     * @param updateMain    发票主体
     * @param invoiceResult 发票结果
     */
    private void updateInvoiceMain(InvoiceMainDO updateMain, InvoiceDTO invoiceResult) {
        if (invoiceResult == null) {
            return;
        }
        boolean isRed = CreateTypeEnum.RED.equalsName(invoiceResult.getCreateType());
        updateMain.setNormalInvoiceCode(isRed ? null : invoiceResult.getBlueInvoiceCode());
        updateMain.setNormalInvoiceNo(isRed ? invoiceResult.getRedInvoiceNo() : invoiceResult.getBlueInvoiceNo());
        updateMain.setRedInvoiceNo(isRed ? invoiceResult.getBlueInvoiceNo() : null);
        updateMain.setInvoiceTime(DateUtil.localDateTimeToDate(invoiceResult.getInvoiceTime()));
        updateMain.setInvoiceAmount(MoneyUtil.centToYuan(invoiceResult.getInvoiceAmount()));
        updateMain.setSumPrice(MoneyUtil.centToYuan(invoiceResult.getPriceAmount()));
        updateMain.setSumTax(MoneyUtil.centToYuan(invoiceResult.getTaxAmount()));
    }

    /**
     * 生成发票明细
     *
     * @param invoiceMain   发票主体
     * @param mainStatus    发票状态
     * @param invoiceResult 发票结果
     * @return 发票明细列表
     */
    private List<InvoiceItemDO> createInvoiceItemDO(InvoiceMainDO invoiceMain, String mainStatus, InvoiceDTO invoiceResult) {
        if (invoiceResult == null || CollectionUtils.isEmpty(invoiceResult.getItems())) {
            return null;
        }
        return invoiceResult.getItems().stream()
                .map(item -> {
                    InvoiceItemDO invoiceItem = new InvoiceItemDO();
                    invoiceItem.setInvoiceId(invoiceMain.getId());
                    invoiceItem.setPlatformId(invoiceMain.getPlatformId());
                    invoiceItem.setPlatformName(invoiceMain.getPlatformName());
                    invoiceItem.setTradeId(invoiceMain.getPlatformTid());
                    invoiceItem.setInvoiceStatus(mainStatus);
                    invoiceItem.setPayerName(invoiceMain.getPayerName());
                    invoiceItem.setNormalInvoiceCode(StringUtils.isNotBlank(invoiceResult.getRedInvoiceNo()) ? null : invoiceResult.getBlueInvoiceCode());
                    invoiceItem.setNormalInvoiceNo(StringUtils.defaultIfBlank(invoiceResult.getRedInvoiceNo(), invoiceResult.getBlueInvoiceNo()));
                    invoiceItem.setInvoiceDate(DateUtil.localDateTimeToDate(invoiceResult.getInvoiceTime()));
                    invoiceItem.setRowType(item.getItemType());
                    invoiceItem.setItemNo(item.getTaxClassificationCode());
                    invoiceItem.setSpecification(item.getSpecification());
                    invoiceItem.setShortName(item.getSpecification());
                    invoiceItem.setItemName(item.getSpecification());
                    invoiceItem.setUnit(item.getUnit());
                    invoiceItem.setQuantity(item.getNum().toString());
                    invoiceItem.setPrice(MoneyUtil.centToYuan(item.getUnitPrice()));
                    invoiceItem.setSumPrice(MoneyUtil.centToYuan(item.getTotalPrice()));
                    invoiceItem.setTaxRate(item.getTaxRate().toPlainString());
                    invoiceItem.setTax(MoneyUtil.centToYuan(item.getTaxAmount()));
                    invoiceItem.setAmount(MoneyUtil.centToYuan(item.getTotalPrice() + item.getTaxAmount()));
                    invoiceItem.setAddTime(new Date());
                    return invoiceItem;
                })
                .collect(Collectors.toList());
    }

    /**
     * 事务保存发票数据
     *
     * @param updateApply 发票申请
     * @param updateMain  发票主体
     * @param saveItems   发票明细列表
     * @param updateTrade 订单
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveData(InvoiceApplyDO updateApply, InvoiceMainDO updateMain, List<InvoiceItemDO> saveItems, TradeDO updateTrade) {
        invoiceApplyRepository.updateById(updateApply);
        if (updateMain != null) {
            invoiceMainRepository.updateById(updateMain);
        }
        if (saveItems != null) {
            invoiceItemRepository.saveBatch(saveItems);
        }
        if (updateTrade != null) {
            tradeRepository.updateByTradeId(updateTrade);
        }
    }

}
