package com.xtc.onlineretailers.refactor.executor.query;

import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.RolesDO;
import com.xtc.onlineretailers.repository.RolesDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 获取角色
 */
@RequiredArgsConstructor
@Component
public class RoleGetQryExe {

    private final RolesDao roleDao;

    /**
     * 获取角色
     *
     * @param roleId 角色id
     * @return 角色
     */
    public RolesDO execute(String roleId) {
        RolesDO rolesDO = roleDao.getById(roleId);
        return Optional.ofNullable(rolesDO)
                .orElseThrow(() -> GlobalDefaultException.of("角色不存在"));
    }

}
