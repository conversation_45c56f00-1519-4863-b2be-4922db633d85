package com.xtc.onlineretailers.refactor.executor.command;

import com.xtc.marketing.adapterservice.logistics.AdapterLogisticsFeignClient;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.warehouse.enums.WarehouseEnum;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.entity.WarehouseDO;
import com.xtc.onlineretailers.refactor.constant.AdapterAccountConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 物流拦截
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class LogisticsInterceptCmdExe {

    private final AdapterLogisticsFeignClient adapterLogisticsFeignClient;

    /**
     * 物流拦截
     *
     * @param trade     订单
     * @param warehouse 仓库
     * @return 拦截结果
     */
    public String execute(TradeDO trade, WarehouseDO warehouse) {
        // 初始化拦截参数
        LogisticsInterceptCmd.LogisticsInterceptCmdBuilder interceptCmdBuilder = LogisticsInterceptCmd.builder()
                .orderId(trade.getExpressTrackingNo())
                .receiverName("张小天")
                .receiverMobile("0769-********-3096")
                .receiverProvince("广东省")
                .receiverCity("东莞市")
                .receiverDistrict("长安镇")
                .receiverAddress("沙头靖海西路113号小天才生产中心一楼网销仓")
                .interceptMemo("客户要求退回");

        // 根据快递公司名称，设置对应的账户
        if (trade.getExpressCompany().contains("顺丰")) {
            interceptCmdBuilder.warehouse(WarehouseEnum.SF)
                    .orderId(trade.getExpressTrackingNo())
                    .bizAccount(StringUtils.defaultIfBlank(warehouse.getMonthCard(), GlobalConstant.SF_MONTH_CARD));
        } else if (trade.getExpressCompany().contains("圆通")) {
            interceptCmdBuilder.bizAccount(AdapterAccountConstant.getAccountByExpressCompany(trade.getExpressCompany()));
        } else if (trade.getExpressCompany().contains("申通")) {
            interceptCmdBuilder.bizAccount(AdapterAccountConstant.getAccountByExpressCompany(trade.getExpressCompany()));
        } else {
            throw new GlobalDefaultException("物流拦截失败 - 不支持的物流公司: " + trade.getExpressCompany());
        }

        // 执行物流拦截
        adapterLogisticsFeignClient.intercept(interceptCmdBuilder.build());
        return "";
    }

}
