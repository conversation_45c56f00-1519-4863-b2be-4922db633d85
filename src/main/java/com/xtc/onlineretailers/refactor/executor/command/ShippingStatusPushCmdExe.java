package com.xtc.onlineretailers.refactor.executor.command;

import com.xtc.marketing.adapterservice.shop.AdapterShopFeignClient;
import com.xtc.marketing.adapterservice.shop.dto.command.OrderDummyShippingCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.OrderShippingBarcodeCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.OrderShippingCmd;
import com.xtc.marketing.adapterservice.shop.enums.LogisticsCompanyEnum;
import com.xtc.onlineretailers.enums.InvoiceStatus;
import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.enums.TradeTypeEnum;
import com.xtc.onlineretailers.pojo.entity.*;
import com.xtc.onlineretailers.refactor.ability.domainservice.TradeTagDomainService;
import com.xtc.onlineretailers.refactor.constant.PlatformConstant;
import com.xtc.onlineretailers.refactor.constant.TradeConstant;
import com.xtc.onlineretailers.refactor.enums.OrderTag;
import com.xtc.onlineretailers.refactor.executor.query.PlatformGetQryExe;
import com.xtc.onlineretailers.refactor.executor.query.TradeGetQryExe;
import com.xtc.onlineretailers.refactor.repository.OrderBarcodeRepository;
import com.xtc.onlineretailers.refactor.repository.OrderShipItemInfoRepository;
import com.xtc.onlineretailers.refactor.repository.ProductInfoRepository;
import com.xtc.onlineretailers.refactor.repository.TradeRepository;
import com.xtc.onlineretailers.repository.ProductBarcodeRepository;
import com.xtc.onlineretailers.util.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 向平台推送订单发货状态，并更新订单状态为已发货
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ShippingStatusPushCmdExe {

    private final TradeGetQryExe tradeGetQryExe;
    private final PlatformGetQryExe platformGetQryExe;
    private final AdapterShopFeignClient adapterShopFeignClient;
    private final TradeRepository tradeRepository;
    private final OrderBarcodeRepository orderBarcodeRepository;
    private final ShippingStatusPushNoAdapterCmdExe shippingStatusPushNoAdapterCmdExe;
    private final ProductInfoRepository productInfoRepository;
    private final ProductBarcodeRepository productBarcodeRepository;
    private final OrderShipItemInfoRepository orderShipItemInfoRepository;
    private final TradeTagDomainService tradeTagDomainService;

    /**
     * 物流发货
     *
     * @param tradeId 订单号
     */
    public boolean shipping(String tradeId) {
        return this.execute(tradeId, false);
    }

    /**
     * 虚拟发货
     *
     * @param tradeId 订单号
     */
    public boolean dummyShipping(String tradeId) {
        return this.execute(tradeId, true);
    }

    /**
     * 订单发货
     *
     * @param tradeId       订单号
     * @param dummyShipping 虚拟发货
     */
    private boolean execute(String tradeId, boolean dummyShipping) {
        TradeDO trade = tradeGetQryExe.execute(tradeId);
        PlatformDO platform = platformGetQryExe.getByPlatformId(trade.getPlatformId());
        boolean pushSuccess;
        if (this.checkNoNeedToPush(trade)) {
            // 不需要推送的订单，标记推送成功
            pushSuccess = true;
        } else if (dummyShipping) {
            pushSuccess = this.dummyShipping(platform.getShopCode(), trade);
        } else {
            pushSuccess = this.shipping(platform, trade);
        }
        log.info("推送发货状态到平台 {}: {}", trade.getPlatformTradeId(), pushSuccess);
        // 推送成功，更新订单状态为已发货，针对定金发货有特殊字段处理
        if (pushSuccess) {
            this.updateTradeShippingStatus(trade);
        }
        return pushSuccess;
    }

    /**
     * 物流发货
     *
     * @param platform 平台
     * @param trade    订单
     * @return 执行结果
     */
    private boolean shipping(PlatformDO platform, TradeDO trade) {
        if (StringUtils.isBlank(platform.getShopCode())) {
            // 没有店铺代码说明非对接服务支持的平台，调用平台接口
            return shippingStatusPushNoAdapterCmdExe.execute(platform, trade);
        }
        // 发货条码
        List<OrderShippingBarcodeCmd> barcodes = this.buildOrderShippingBarcodes(platform, trade);

        // 对接服务支持的平台
        OrderShippingCmd orderShippingCmd = OrderShippingCmd.builder()
                .shopCode(platform.getShopCode())
                .orderNo(trade.getPlatformTradeId())
                .logisticsCompany(this.getLogisticsCompanyEnum(trade.getExpressCompany()))
                .waybillNo(trade.getExpressTrackingNo())
                .shippingTime(DateUtil.dateToLocalDateTime(trade.getShippingTime()))
                .barcodes(barcodes)
                .build();
        Boolean result = adapterShopFeignClient.shipping(orderShippingCmd).getData();
        return BooleanUtils.isTrue(result);
    }

    /**
     * 构建发货条码列表
     *
     * @param platform 平台
     * @param trade    订单
     * @return 发货条码列表
     */
    public List<OrderShippingBarcodeCmd> buildOrderShippingBarcodes(PlatformDO platform, TradeDO trade) {
        // 代销平台，推送发货条码
        if ("AGENT".equals(platform.getPlatformType())) {
            List<OrderBarcodeDO> orderBarcodes = orderBarcodeRepository.listByPlatformTradeId(trade.getPlatformTradeId());
            return orderBarcodes.stream()
                    .map(orderBarcode -> OrderShippingBarcodeCmd.builder()
                            .itemNo(orderBarcode.getPlatformOrderId()).barcode(orderBarcode.getBarcode()).build()
                    )
                    .collect(Collectors.toList());
        }
        if (tradeTagDomainService.isTagExist(trade, OrderTag.NATIONAL_SUBSIDY)
                || PlatformConstant.KUAISHOU.contains(trade.getPlatformId())) {
            // 国补订单，本地仓库，推送发货条码和IMEI
            if ("005".equals(trade.getWarehouseCode())) {
                List<OrderBarcodeDO> orderBarcodes = orderBarcodeRepository.listByPlatformTradeId(trade.getPlatformTradeId());
                return orderBarcodes.stream()
                        .map(ship -> this.buildOrderShippingImeiCmd(trade, ship.getBarcode(), ship.getErpCode()))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
            // 国补订单，顺丰仓库，推送发货条码和IMEI
            if ("006".equals(trade.getWarehouseCode())) {
                List<OrderShipItemInfoDO> orderShipItems = orderShipItemInfoRepository.listByTradeId(trade.getPlatformTradeId());
                return orderShipItems.stream()
                        .map(ship -> this.buildOrderShippingImeiCmd(trade, ship.getProductSn(), ship.getBarcode()))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
            }
        }
        return Collections.emptyList();
    }

    /**
     * 构建发货条码和IMEI
     *
     * @param trade       订单
     * @param barcode     条码
     * @param productCode 产品代码
     * @return 发货条码和IMEI
     */
    private OrderShippingBarcodeCmd buildOrderShippingImeiCmd(TradeDO trade, String barcode, String productCode) {
        Optional<ProductInfoDO> productOpt;
        if ("005".equals(trade.getWarehouseCode())) {
            // 本地仓使用物料代码查询产品
            productOpt = productInfoRepository.getByErpCode(productCode);
        } else if ("006".equals(trade.getWarehouseCode())) {
            // 顺丰仓使用顺丰69码查询产品
            productOpt = productInfoRepository.getByBarcode(productCode);
        } else {
            return null;
        }
        // 机器才推送条码和IMEI
        boolean isMachine = productOpt.map(product -> product.getMaterialType() == 1).orElse(false);
        if (BooleanUtils.isFalse(isMachine)) {
            return null;
        }
        // 查询IMEI，构建发货条码和IMEI
        String imei = productBarcodeRepository.getByBarcode(barcode).map(ProductDO::getImeiBarcode).orElse(null);
        return OrderShippingBarcodeCmd.builder().itemNo(trade.getPlatformTradeId()).barcode(barcode).imei(imei).build();
    }

    /**
     * 虚拟发货
     *
     * @param shopCode 店铺代码
     * @param trade    订单
     * @return 执行结果
     */
    private boolean dummyShipping(String shopCode, TradeDO trade) {
        OrderDummyShippingCmd orderDummyShippingCmd = OrderDummyShippingCmd.builder()
                .shopCode(shopCode)
                .orderNo(trade.getPlatformTradeId())
                .build();
        Boolean result = adapterShopFeignClient.dummyShipping(orderDummyShippingCmd).getData();
        return BooleanUtils.isTrue(result);
    }

    /**
     * 更新订单状态为已发货，针对定金发货有特殊字段处理
     *
     * @param trade 订单
     */
    private void updateTradeShippingStatus(TradeDO trade) {
        TradeDO updateTrade = new TradeDO();
        updateTrade.setId(trade.getId());
        updateTrade.setStatusOrder(OrderStatus.HAS_SHIP.name());
        // 定金发货特殊字段处理
        if (trade.getIsAdvanceShip() == 1) {
            // ERP过账：当ERP过账状态为未过账时，修改为不过账
            if (trade.getErpPostStatus() == 0) {
                updateTrade.setErpPostStatus(2);
            }
            // 发票状态：当发票状态为未开票时，修改为无需开票
            if (InvoiceStatus.NOT_MAKE.name().equals(trade.getInvoiceStatus())) {
                updateTrade.setInvoiceStatus(InvoiceStatus.NOT_NEED.name());
            }
        }
        tradeRepository.updateById(updateTrade);
        log.info("更新订单状态为已发货 {}", trade.getPlatformTradeId());
    }

    /**
     * 获取物流公司枚举
     *
     * @param logisticsCompany 物流公司
     * @return 物流公司枚举
     */
    private LogisticsCompanyEnum getLogisticsCompanyEnum(String logisticsCompany) {
        if (logisticsCompany == null) {
            return null;
        }
        if (logisticsCompany.contains("顺丰")) {
            return LogisticsCompanyEnum.SF;
        }
        if (logisticsCompany.contains("圆通")) {
            return LogisticsCompanyEnum.YTO;
        }
        if (logisticsCompany.contains("京东")) {
            return LogisticsCompanyEnum.JD;
        }
        if (logisticsCompany.contains("申通")) {
            return LogisticsCompanyEnum.STO;
        }
        if (logisticsCompany.equalsIgnoreCase("EMS")) {
            return LogisticsCompanyEnum.EMS;
        }
        throw new IllegalArgumentException("不支持的物流公司：" + logisticsCompany);
    }

    /**
     * 判断不需要推送的订单
     *
     * @param trade 订单
     * @return 执行结果
     */
    private boolean checkNoNeedToPush(TradeDO trade) {
        // 非正常订单类型不需要推送发货状态
        TradeTypeEnum orderType = TradeTypeEnum.getEnum(trade.getType());
        if (orderType != TradeTypeEnum.NORMAL) {
            return true;
        }
        // 人工导入的订单，不需要推送发货状态
        return TradeConstant.isImportTrade(trade.getStatusTaobao());
    }

}
