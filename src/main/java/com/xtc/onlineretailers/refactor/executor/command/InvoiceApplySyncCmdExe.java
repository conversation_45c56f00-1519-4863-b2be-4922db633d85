package com.xtc.onlineretailers.refactor.executor.command;

import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import com.xtc.marketing.adapterservice.shop.AdapterShopFeignClient;
import com.xtc.marketing.adapterservice.shop.dto.InvoiceApplyDTO;
import com.xtc.marketing.adapterservice.shop.dto.query.InvoiceApplyGetQry;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.enums.TradeTypeEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.annotation.FieldMeta;
import com.xtc.onlineretailers.refactor.constant.InvoiceApplyConstant;
import com.xtc.onlineretailers.refactor.enums.InvoiceState;
import com.xtc.onlineretailers.refactor.enums.InvoiceTitleType;
import com.xtc.onlineretailers.refactor.enums.InvoiceType;
import com.xtc.onlineretailers.refactor.executor.query.PlatformGetQryExe;
import com.xtc.onlineretailers.refactor.executor.query.TradeGetQryExe;
import com.xtc.onlineretailers.refactor.pojo.bo.InvoiceApplyIdentityBO;
import com.xtc.onlineretailers.refactor.pojo.bo.InvoiceHistoryBO;
import com.xtc.onlineretailers.refactor.pojo.command.InvoiceApplyAddCmd;
import com.xtc.onlineretailers.refactor.pojo.entity.InvoiceApplyDO;
import com.xtc.onlineretailers.refactor.repository.InvoiceApplyRepository;
import com.xtc.onlineretailers.refactor.repository.TradeRepository;
import com.xtc.onlineretailers.refactor.repository.TradeSyncRepository;
import com.xtc.onlineretailers.refactor.util.BeanMapUtil;
import com.xtc.onlineretailers.refactor.util.RedissonUtil;
import com.xtc.onlineretailers.util.BeanCopier;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.GsonUtil;
import com.xtc.springboot.pojo.entity.GlobalContext;
import com.xtc.springboot.pojo.entity.UserDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Comparator;
import java.util.Map;
import java.util.Optional;

/**
 * 同步发票申请数据
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class InvoiceApplySyncCmdExe {

    private final InvoiceApplyRepository invoiceApplyRepository;
    private final AdapterShopFeignClient adapterShopFeignClient;
    private final TradeRepository tradeRepository;
    private final TradeSyncRepository tradeSyncRepository;
    private final PlatformGetQryExe platformGetQryExe;
    private final RedissonUtil redissonUtil;
    private final TradeGetQryExe tradeGetQryExe;

    /**
     * 不允许发票申请的订单类型
     */
    private static final ImmutableList<TradeTypeEnum> NOT_ALLOW_INVOICE_APPLY_STATUS =
            ImmutableList.of(TradeTypeEnum.EXCHANGE_GOODS, TradeTypeEnum.GIFT);

    /**
     * 新增发票申请
     *
     * @param cmd 参数
     */
    public void execute(InvoiceApplyAddCmd cmd) {
        TradeDO tradeDO = tradeGetQryExe.execute(cmd.getTradeId());
        TradeTypeEnum typeEnum = TradeTypeEnum.getEnum(tradeDO.getType());
        if (NOT_ALLOW_INVOICE_APPLY_STATUS.contains(typeEnum)) {
            throw GlobalDefaultException.of("订单类型不允许开票 [%s]", typeEnum.getDescription());
        }
        OrderStatus orderStatus = OrderStatus.getEnum(tradeDO.getStatusOrder());
        if (OrderStatus.RETURNED == orderStatus) {
            throw GlobalDefaultException.of("订单状态不允许开票 [%s]", orderStatus.getDescription());
        }
        // 统计待开票和申请开票数量
        long count = invoiceApplyRepository.countWaitCreateAndAlreadyApplied(cmd.getTradeId());
        if (count > 0) {
            throw GlobalDefaultException.of("订单已存在发票申请记录，无需重复申请");
        }
        InvoiceApplyDO invoiceApplyDO = BeanCopier.copy(cmd, InvoiceApplyDO::new);
        String creator = Optional.ofNullable(GlobalContext.getUser()).map(UserDetail::getName)
                .orElseThrow(() -> GlobalDefaultException.of("请先登录"));
        invoiceApplyDO.setCreator(creator);
        invoiceApplyDO.setInvoiceState(InvoiceState.WAIT_CREATE);
        this.saveInvoiceApply(invoiceApplyDO);
    }

    /**
     * 发票申请数据同步
     *
     * @param platformId 平台id
     * @param tradeId    订单号
     */
    public void execute(Integer platformId, String tradeId) {
        PlatformDO platform = platformGetQryExe.getByPlatformId(platformId);
        if (StringUtils.isBlank(platform.getShopCode())) {
            log.info("平台未维护 shopCode 不做处理");
            return;
        }
        InvoiceApplyGetQry qry = InvoiceApplyGetQry.builder().orderNo(tradeId).shopCode(platform.getShopCode()).build();
        SingleResponse<InvoiceApplyDTO> shopInvoiceApply = adapterShopFeignClient.getInvoiceApply(qry);
        if (shopInvoiceApply == null || shopInvoiceApply.getData() == null) {
            throw GlobalDefaultException.of("发票申请数据不存在");
        }
        // 同步天才星的发票申请数据
        if (StringUtils.isNotBlank(shopInvoiceApply.getData().getSellerIdentifyNo()) && !shopInvoiceApply.getData().getSellerIdentifyNo().equals("91441900324794417T")) {
            log.info("订单 {} 不是天才星发票申请 无需同步", shopInvoiceApply.getData().getOrderNo());
            return;
        }
        if (StringUtils.isNotBlank(shopInvoiceApply.getData().getSellerName()) && !shopInvoiceApply.getData().getSellerName().equals("广东天才星网络科技有限公司")) {
            log.info("订单 {} 不是天才星的发票税号 无需同步", shopInvoiceApply.getData().getOrderNo());
            return;
        }
        this.execute(platform, shopInvoiceApply.getData());
    }

    /**
     * 发票申请数据同步
     *
     * @param platform     平台
     * @param adapterApply 平台发票申请
     */
    public void execute(PlatformDO platform, InvoiceApplyDTO adapterApply) {
        // 同步天才星的发票申请数据
        if (StringUtils.isNotBlank(adapterApply.getSellerIdentifyNo()) && !adapterApply.getSellerIdentifyNo().equals("91441900324794417T")) {
            log.info("订单 {} 不是天才星发票申请 无需同步", adapterApply.getOrderNo());
            return;
        }
        if (StringUtils.isNotBlank(adapterApply.getSellerName()) && !adapterApply.getSellerName().equals("广东天才星网络科技有限公司")) {
            log.info("订单 {} 不是天才星的发票税号 无需同步", adapterApply.getOrderNo());
            return;
        }
        InvoiceApplyDO invoiceApplyDO = InvoiceApplyDO.builder()
                .platformId(platform.getPlatformId())
                .platformName(platform.getPlatformName())
                .tradeId(adapterApply.getOrderNo())
                .creator(InvoiceApplyConstant.CREATOR_SYSTEM)
                .invoiceType(InvoiceType.valueOf(adapterApply.getInvoiceType().name()))
                .invoiceTitleType(InvoiceTitleType.valueOf(adapterApply.getInvoiceTitleType().name()))
                .invoiceTitle(adapterApply.getInvoiceTitle())
                .taxNo(adapterApply.getTaxNo())
                .bankName(adapterApply.getBankName())
                .bankAccount(adapterApply.getBankAccount())
                .mobile(adapterApply.getCompanyMobile())
                .address(adapterApply.getCompanyAddress())
                .buyerMemo(adapterApply.getBuyerMemo())
                .invoiceMemo(adapterApply.getInvoiceMemo())
                .invoiceAmount(adapterApply.getInvoiceAmount())
                .invoiceState(InvoiceState.WAIT_CREATE)
                .build();
        this.saveInvoiceApply(invoiceApplyDO);
    }

    /**
     * 新增发票申请，使用克隆方式
     *
     * @param cloneTradeId 克隆订单号
     * @param newTradeId   新订单号
     * @param remark       备注
     */
    public void executeUsingClone(String cloneTradeId, String newTradeId, String remark) {
        InvoiceApplyDO currentApply = invoiceApplyRepository.listByTradeId(cloneTradeId).stream()
                .filter(apply -> apply.getInvoiceState() == InvoiceState.WAIT_CREATE
                        || apply.getInvoiceState() == InvoiceState.ALREADY_APPLIED
                        || apply.getInvoiceState() == InvoiceState.CREATE_BLUE
                        || apply.getInvoiceState() == InvoiceState.CREATE_RED)
                .max(Comparator.comparing(InvoiceApplyDO::getUpdateTime))
                .orElse(null);
        if (currentApply == null) {
            log.info("订单 {} 没有开过发票，无需生成新发票申请", cloneTradeId);
            return;
        }

        // 获取发票申请标识属性
        InvoiceApplyIdentityBO identity = BeanCopier.copy(currentApply, InvoiceApplyIdentityBO::new);
        // 生成新的发票申请，与当前数据一致
        InvoiceApplyDO newApply = BeanCopier.copy(identity, InvoiceApplyDO::new);
        // 填充发票申请基础数据
        newApply.setPlatformId(currentApply.getPlatformId());
        newApply.setPlatformName(currentApply.getPlatformName());
        newApply.setTradeId(newTradeId);
        newApply.setInvoiceState(InvoiceState.WAIT_CREATE);
        newApply.setCreator(InvoiceApplyConstant.CREATOR_SYSTEM);
        newApply.setPaymentTime(currentApply.getPaymentTime());
        newApply.setPostTime(currentApply.getPostTime());
        newApply.setBuyerMemo(currentApply.getBuyerMemo());
        // 添加发票历史
        InvoiceHistoryBO createHistory = InvoiceHistoryBO.builder()
                .handler(InvoiceApplyConstant.CREATOR_SYSTEM)
                .handleTime(DateUtil.nowDateTimeStr())
                .handleResult("新建发票申请")
                .handleRemark(remark + "【复制新建 " + cloneTradeId + "】")
                .build();
        newApply.setInvoiceHistory(Collections.singletonList(createHistory));
        invoiceApplyRepository.save(newApply);
    }

    /**
     * 保存发票申请
     *
     * @param invoiceApplyDO 发票申请
     */
    private void saveInvoiceApply(InvoiceApplyDO invoiceApplyDO) {
        log.info("同步发票申请 {}", invoiceApplyDO.getTradeId());
        // 检查重复申请
        this.checkDuplicateApply(invoiceApplyDO);
        // 填充发票申请基础数据
        this.fillInvoiceApplyBaseData(invoiceApplyDO);
        // 查询已开票记录，校验当前申请数据合法
        invoiceApplyRepository.getByTradeIdAndState(invoiceApplyDO.getTradeId(), InvoiceState.CREATE_BLUE)
                .ifPresent(lastApply -> {
                    InvoiceApplyIdentityBO currentIdentity = BeanCopier.copy(invoiceApplyDO, InvoiceApplyIdentityBO::new);
                    InvoiceApplyIdentityBO lastIdentity = BeanCopier.copy(lastApply, InvoiceApplyIdentityBO::new);
                    // 企业发票校验
                    if (currentIdentity.getInvoiceTitleType() == InvoiceTitleType.COMPANY && currentIdentity.equals(lastIdentity)) {
                        throw GlobalDefaultException.of("发票申请已开票，无需重复申请 %s", invoiceApplyDO.getTradeId());
                    }
                    // 个人发票校验：发票抬头、税号相同
                    if (currentIdentity.getInvoiceTitleType() == InvoiceTitleType.PERSONAL
                            && currentIdentity.getInvoiceTitle().equals(lastIdentity.getInvoiceTitle())
                            && currentIdentity.getTaxNo().equals(lastIdentity.getTaxNo())) {
                        throw GlobalDefaultException.of("发票申请已开票，无需重复申请 %s", invoiceApplyDO.getTradeId());
                    }
                    // 优先人工录入，后续平台录入设置无需开票状态：当前是平台录入，上一次已开票状态的申请是人工录入
                    if (InvoiceApplyConstant.CREATOR_SYSTEM.equals(invoiceApplyDO.getCreator())
                            && !InvoiceApplyConstant.CREATOR_SYSTEM.equals(lastApply.getCreator())) {
                        invoiceApplyDO.setInvoiceState(InvoiceState.WAIT_CREATE);
                        log.info("平台录入新发票申请 {}", invoiceApplyDO.getTradeId());
                    }
                    // 设置差异内容
                    String diffContent = this.buildDiffContent(currentIdentity, lastIdentity);
                    invoiceApplyDO.setDiffContent(diffContent);
                });
        // 保存发票申请，重复申请直接抛弃
        redissonUtil.tryLock("invoice-apply:sync", invoiceApplyDO.getTradeId(),
                () -> {
                    invoiceApplyRepository.save(invoiceApplyDO);
                },
                GlobalDefaultException.of("保存发票申请，抛弃重复执行的操作 %s", invoiceApplyDO.getTradeId()));
    }

    /**
     * 填充发票申请
     *
     * @param invoiceApplyDO 发票申请
     */
    private void fillInvoiceApplyBaseData(InvoiceApplyDO invoiceApplyDO) {
        // 添加订单数据
        Optional<TradeDO> tradeOpt = tradeRepository.getByTradeId(invoiceApplyDO.getTradeId());
        if (tradeOpt.isPresent()) {
            TradeDO trade = tradeOpt.get();
            invoiceApplyDO.setPlatformId(trade.getPlatformId());
            invoiceApplyDO.setPlatformName(trade.getPlatformName());
            invoiceApplyDO.setPaymentTime(DateUtil.dateToLocalDateTime(trade.getPaymentTime()));
            invoiceApplyDO.setPostTime(DateUtil.dateToLocalDateTime(trade.getErpPostTime()));
        } else {
            tradeSyncRepository.getByTradeId(invoiceApplyDO.getTradeId())
                    .ifPresent(trade -> {
                        invoiceApplyDO.setPlatformId(trade.getPlatformId());
                        invoiceApplyDO.setPlatformName(trade.getPlatformName());
                        invoiceApplyDO.setPaymentTime(trade.getPaymentTime());
                    });
        }
        // 当前操作人
        String creator = StringUtils.defaultIfBlank(invoiceApplyDO.getCreator(), InvoiceApplyConstant.CREATOR_SYSTEM);
        invoiceApplyDO.setCreator(creator);
        // 添加发票历史
        InvoiceHistoryBO createHistory = InvoiceHistoryBO.of(creator, "新建发票申请", null);
        invoiceApplyDO.setInvoiceHistory(Collections.singletonList(createHistory));
    }

    /**
     * 检查重复申请
     *
     * @param invoiceApplyDO 发票申请
     */
    private void checkDuplicateApply(InvoiceApplyDO invoiceApplyDO) {
        // 手动录入发票，不检查重复申请
        boolean isManual = GlobalContext.getUser() != null;
        invoiceApplyRepository.listSystem(invoiceApplyDO.getTradeId()).stream()
                .filter(lastApply -> lastApply.getInvoiceState() != InvoiceState.NOT_CREATE)
                .filter(lastApply -> isManual && lastApply.getInvoiceState() != InvoiceState.CREATE_RED)
                .forEach(lastApply -> {
                    InvoiceApplyIdentityBO currentIdentity = BeanCopier.copy(invoiceApplyDO, InvoiceApplyIdentityBO::new);
                    InvoiceApplyIdentityBO lastIdentity = BeanCopier.copy(lastApply, InvoiceApplyIdentityBO::new);
                    if (currentIdentity.equals(lastIdentity)) {
                        throw GlobalDefaultException.of("发票申请已记录，无需重复申请 %s", invoiceApplyDO.getTradeId());
                    }
                });
    }

    /**
     * 构建差异内容
     *
     * @param currentIdentity 当前发票申请标识
     * @param lastIdentity    上一次发票申请标识
     * @return 差异内容
     */
    private String buildDiffContent(InvoiceApplyIdentityBO currentIdentity, InvoiceApplyIdentityBO lastIdentity) {
        Map<String, Object> diffMap = Maps.newHashMapWithExpectedSize(2);

        Map<String, Object> currentMap = BeanMapUtil.getMap(currentIdentity, FieldMeta.class);
        diffMap.put("current", currentMap);

        Map<String, Object> lastMap = BeanMapUtil.getMap(lastIdentity, FieldMeta.class);
        diffMap.put("last", lastMap);

        return GsonUtil.objectToJson(diffMap);
    }

}
