package com.xtc.onlineretailers.refactor.executor.command;

import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.shop.AdapterShopFeignClient;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.query.OrderGetQry;
import com.xtc.marketing.adapterservice.warehouse.AdapterWarehouseFeignClient;
import com.xtc.marketing.adapterservice.warehouse.dto.WarehouseStockDTO;
import com.xtc.marketing.adapterservice.warehouse.dto.command.OutboundApplyCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.command.WarehouseItemCmd;
import com.xtc.marketing.adapterservice.warehouse.dto.query.WarehouseStockQry;
import com.xtc.marketing.adapterservice.warehouse.enums.WarehouseEnum;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.enums.SFStatus;
import com.xtc.onlineretailers.enums.TradeTypeEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.*;
import com.xtc.onlineretailers.refactor.ability.checker.StockEnoughChecker;
import com.xtc.onlineretailers.refactor.ability.checker.WarehouseOutboundApplyChecker;
import com.xtc.onlineretailers.refactor.ability.domainservice.LogisticsAddressDomainService;
import com.xtc.onlineretailers.refactor.ability.domainservice.ReceiverDefaultStrDomainService;
import com.xtc.onlineretailers.refactor.constant.PlatformConstant;
import com.xtc.onlineretailers.refactor.constant.TradeConstant;
import com.xtc.onlineretailers.refactor.enums.OrderTag;
import com.xtc.onlineretailers.refactor.executor.query.PlatformGetQryExe;
import com.xtc.onlineretailers.refactor.executor.query.ProductGetQryExe;
import com.xtc.onlineretailers.refactor.executor.query.TradeGetQryExe;
import com.xtc.onlineretailers.refactor.executor.query.WarehouseGetQryExe;
import com.xtc.onlineretailers.refactor.function.ReleaseStockFunction;
import com.xtc.onlineretailers.refactor.pojo.bo.LogisticsAddressBO;
import com.xtc.onlineretailers.refactor.pojo.bo.WarehouseOutboundApplyBO;
import com.xtc.onlineretailers.refactor.pojo.command.WarehouseOutboundApplyCmd;
import com.xtc.onlineretailers.refactor.repository.OrderRepository;
import com.xtc.onlineretailers.refactor.repository.OrderShipInfoRepository;
import com.xtc.onlineretailers.refactor.repository.TradeRepository;
import com.xtc.onlineretailers.util.BeanCopier;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

/**
 * 仓库出库申请
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WarehouseOutboundApplyCmdExe {

    private final TradeGetQryExe tradeGetQryExe;
    private final OrderRepository orderRepository;
    private final PlatformGetQryExe platformGetQryExe;
    private final WarehouseGetQryExe warehouseGetQryExe;
    private final WarehouseOutboundApplyChecker warehouseOutboundApplyChecker;
    private final ProductGetQryExe productGetQryExe;
    private final StockEnoughChecker stockEnoughChecker;
    private final AdapterWarehouseFeignClient adapterWarehouseFeignClient;
    private final LogisticsAddressDomainService logisticsAddressDomainService;
    private final TradeRemarkCmdExe tradeRemarkCmdExe;
    private final OrderShipInfoRepository orderShipInfoRepository;
    private final TradeRepository tradeRepository;
    private final ReceiverDefaultStrDomainService receiverDefaultStrDomainService;
    private final AdapterShopFeignClient adapterShopFeignClient;

    /**
     * 仓库出库申请
     *
     * @param cmd 参数
     */
    public void execute(WarehouseOutboundApplyCmd cmd) {
        this.execute(cmd, null, null);
    }

    /**
     * 仓库出库申请
     *
     * @param cmd            参数
     * @param outboundRemark 出库备注
     * @param productChecker 仓库出库物料检查
     */
    public void execute(WarehouseOutboundApplyCmd cmd, String outboundRemark, Consumer<ProductInfoDO> productChecker) {
        // 初始化数据，检查数据完整性
        TradeDO trade = tradeGetQryExe.execute(cmd.getTradeId());
        List<OrderDO> orders = orderRepository.listByTradeId(trade.getPlatformTradeId());
        PlatformDO platform = platformGetQryExe.getByPlatformId(trade.getPlatformId());
        WarehouseDO warehouse = warehouseGetQryExe.execute(trade.getWarehouseStorage());
        Optional<OrderShipInfoDO> orderShip = orderShipInfoRepository.getByTradeId(trade.getPlatformTradeId());

        // 生成物流地址，收件人信息
        LogisticsAddressBO logisticsAddress = logisticsAddressDomainService.createLogisticsAddress(trade);
        log.info("订单 {} 生成物流地址 {}", trade.getPlatformTradeId(), logisticsAddress);

        // 组装出库参数
        WarehouseOutboundApplyBO outboundApplyBO = WarehouseOutboundApplyBO.builder()
                .warehouse(warehouse).platform(platform).trade(trade).orders(orders).logisticsAddress(logisticsAddress)
                .checkRefund(cmd.getCheckRefund()).orderShip(orderShip.orElse(null)).build();

        // 检查数据合法性
        warehouseOutboundApplyChecker.check(outboundApplyBO);
        log.info("订单 {} 出库检查通过", trade.getPlatformTradeId());

        // 筛选需要出库的订单明细列表
        List<OrderDO> outboundOrders = this.listOutboundOrders(trade.getPlatformTradeId(), orders, productChecker);
        outboundApplyBO.setOutboundOrders(outboundOrders);

        // 判断全部库存充足，返回释放临时占用的库存的回调方法集合
        List<ReleaseStockFunction> listReleaseStockFunction = this.checkStockEnough(trade, outboundOrders, warehouse.getWarehouseStorage());
        try {
            // 生成出库申请单，调用对接服务，仓库申请出库接口
            OutboundApplyCmd outboundApplyCmd = this.createApplyOutboundCmd(outboundApplyBO, platform, trade);
            log.info("出库申请单 {}", GsonUtil.objectToJson(outboundApplyCmd));
            String platformOrderId = this.adapterWarehouseOutbound(outboundApplyCmd);

            // 修改平台订单备注，系统抛单备注【*OK】，其他操作备注【@OK】
            String updateRemark = this.updateTradeRemark(trade, outboundRemark);

            // 数据保存，设置数据库事务
            WarehouseOutboundApplyCmdExe exe = (WarehouseOutboundApplyCmdExe) AopContext.currentProxy();
            exe.saveOutboundData(trade, warehouse, platformOrderId, orderShip.isPresent(), updateRemark);
        } finally {
            // 释放临时占用的库存，如果释放失败则继续占用库存，等待过期自动释放所有库存
            listReleaseStockFunction.forEach(ReleaseStockFunction::release);
        }
    }

    /**
     * 数据保存，设置数据库事务
     *
     * @param trade           订单
     * @param warehouse       仓库
     * @param platformOrderId 平台出库单号
     * @param updateOrderShip 更新出库单
     * @param updateRemark    更新订单备注
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void saveOutboundData(TradeDO trade, WarehouseDO warehouse, String platformOrderId,
                                 boolean updateOrderShip, String updateRemark) {
        // 有平台出库单号，设置状态为出库成功，否则状态为出库失败
        SFStatus shipStatus = StringUtils.isNotBlank(platformOrderId) ? SFStatus.HAS_TURN_LEFT : SFStatus.OUTBOUND_FAILED;
        log.info("{} 出库状态设置 {}", trade.getPlatformTradeId(), shipStatus);

        // 保存出库记录
        OrderShipInfoDO orderShip = new OrderShipInfoDO();
        orderShip.setStoreCode(warehouse.getWarehouseStorage());
        orderShip.setStoreName(warehouse.getWarehouseName());
        orderShip.setTradeId(trade.getPlatformTradeId());
        orderShip.setShipmentId(platformOrderId);
        orderShip.setShipStatus(shipStatus.name());
        orderShip.setPlatformName(trade.getPlatformName());
        orderShip.setPlatformId(trade.getPlatformId());
        orderShip.setIsSystem("1");
        orderShip.setIsResolve("-1");
        orderShip.setApplyTime(new Date());
        if (updateOrderShip) {
            // 支持重复出库，把所有数据初始化
            orderShipInfoRepository.updateByTradeId(orderShip);
        } else {
            // 只有新增的时候设置创建时间
            orderShip.setAddTime(new Date());
            orderShipInfoRepository.save(orderShip);
        }

        // 更新订单数据
        String nowDateTime = DateUtil.nowDateTimeStr();
        TradeDO updateTrade = new TradeDO();
        updateTrade.setPlatformTradeId(trade.getPlatformTradeId());
        updateTrade.setUpdateTime(nowDateTime);
        updateTrade.setOrderConfirmTime(nowDateTime);
        updateTrade.setSellerRemark(updateRemark);
        updateTrade.setStatusSf(shipStatus.name());
        // 如果订单状态是已打回，就修改成待发货
        OrderStatus orderStatus = OrderStatus.getEnum(trade.getStatusOrder());
        if (orderStatus == OrderStatus.REJECT) {
            updateTrade.setStatusOrder(OrderStatus.WAIT_SHIP.name());
        }
        tradeRepository.updateByTradeId(updateTrade);
    }

    /**
     * 修改平台订单备注，默认系统抛单备注【*OK】，其他操作备注【@OK】
     *
     * @param trade          订单
     * @param outboundRemark 出库备注
     * @return 新备注内容
     */
    private String updateTradeRemark(TradeDO trade, String outboundRemark) {
        String separator = "，";
        String remark = StringUtils.defaultIfBlank(outboundRemark, "@OK");
        try {
            return tradeRemarkCmdExe.execute(trade, remark, separator);
        } catch (Exception e) {
            // 异常时使用原订单备注内容加上新备注
            log.warn("订单 {} 修改平台备注异常，请检查平台配置", trade.getPlatformTradeId(), e);
            return trade.getSellerRemark() + separator + remark;
        }
    }

    /**
     * 调用对接服务，仓库申请出库接口
     * <p>仓库出库申请超时，当作出库成功处理，但是状态设置为顺丰出库失败</p>
     *
     * @param outboundApplyCmd 出库申请单
     * @return 平台出库单号
     */
    private String adapterWarehouseOutbound(OutboundApplyCmd outboundApplyCmd) {
        try {
            return adapterWarehouseFeignClient.applyOutbound(outboundApplyCmd).getData();
        } catch (Exception e) {
            if (e.getMessage().contains("请求超时")) {
                log.warn("仓库出库申请超时，当作出库成功处理，但是状态设置为出库失败 {}", outboundApplyCmd.getOrderId());
                return "";
            }
            throw e;
        }
    }

    /**
     * 生成出库申请单（确认收件人信息）
     *
     * @param outboundApplyBO 出库申请参数
     * @param platform        平台
     * @param trade           订单
     * @return 出库申请单
     */
    private OutboundApplyCmd createApplyOutboundCmd(WarehouseOutboundApplyBO outboundApplyBO, PlatformDO platform, TradeDO trade) {
        // 生成出库申请单
        OutboundApplyCmd.OutboundApplyCmdBuilder outboundApplyCmdBuilder = OutboundApplyCmd.builder()
                .shopCode(platform.getShopCode())
                .warehouse(WarehouseEnum.SF)
                .warehouseCode(outboundApplyBO.getWarehouse().getWarehouseStorage())
                .orderId(outboundApplyBO.getTrade().getPlatformTradeId())
                .shopOrderNo(outboundApplyBO.getTrade().getPlatformTradeId())
                .useSender(true)
                .senderName("张小天")
                .senderPhone("0769-88619398")
                .senderProvince("广东省")
                .senderCity("东莞市")
                .senderDistrict("长安镇")
                .senderAddress("沙头靖海西路113号小天才生产中心一楼网销仓");
        // 设置需要“传输密文”的逻辑
        boolean encryptShipping = this.isEncryptShipping(outboundApplyBO, trade);
        if (encryptShipping) {
            outboundApplyCmdBuilder.platformCode(outboundApplyBO.getPlatform().getPlatformCode());
        }
        // 设置平台参数
        Optional<String> platformJson = this.buildPlatformJson(platform, trade);
        platformJson.ifPresent(outboundApplyCmdBuilder::platformJson);
        // 设置收件人信息
        LogisticsAddressBO logisticsAddress = outboundApplyBO.getLogisticsAddress();
        outboundApplyCmdBuilder
                .receiverOaid(logisticsAddress.getReceiverOaid())
                .receiverCompany(StringUtils.defaultIfBlank(outboundApplyBO.getTrade().getInvoiceTitle(), "个人"))
                .receiverName(logisticsAddress.getReceiverName())
                .receiverMobile(logisticsAddress.getReceiverMobile())
                .receiverPhone(logisticsAddress.getReceiverPhone())
                .receiverProvince(logisticsAddress.getReceiverProvince())
                .receiverCity(logisticsAddress.getReceiverCity())
                .receiverDistrict(logisticsAddress.getReceiverDistrict())
                .receiverAddress(logisticsAddress.getReceiverAddress());
        // 收件人数据全是符号【*】，则设置默认值
        outboundApplyCmdBuilder.receiverName(receiverDefaultStrDomainService.defaultStringIfStar(logisticsAddress.getReceiverName()));
        outboundApplyCmdBuilder.receiverMobile(receiverDefaultStrDomainService.defaultStringIfStar(logisticsAddress.getReceiverMobile()));
        outboundApplyCmdBuilder.receiverAddress(receiverDefaultStrDomainService.defaultStringIfStar(logisticsAddress.getReceiverAddress()));
        // 设置物流产品
        String logisticsProduct = this.getLogisticsProduct(outboundApplyBO);
        outboundApplyCmdBuilder.logisticsProduct(logisticsProduct);
        // 设置出库货物
        List<WarehouseItemCmd> warehouseItems = outboundApplyBO.getOutboundOrders().stream()
                .map(outboundOrder -> WarehouseItemCmd.builder()
                        .skuId(outboundOrder.getBarcode()).quantity(outboundOrder.getNum()).build())
                .collect(Collectors.toList());
        outboundApplyCmdBuilder.items(warehouseItems);
        return outboundApplyCmdBuilder.build();
    }

    /**
     * 密文发货
     * <p>常订单，没有更新过地址，不是导入订单</p>
     *
     * @param outboundApplyBO 出库单
     * @param trade           订单
     * @return 执行结果
     */
    private boolean isEncryptShipping(WarehouseOutboundApplyBO outboundApplyBO, TradeDO trade) {
        TradeTypeEnum tradeType = TradeTypeEnum.getEnum(outboundApplyBO.getTrade().getType());
        boolean notUpdateAddress = outboundApplyBO.getTrade().getIsUpdateAddress() == null
                || outboundApplyBO.getTrade().getIsUpdateAddress() == 0;
        return tradeType == TradeTypeEnum.NORMAL
                && notUpdateAddress
                && TradeConstant.notImportTrade(trade.getStatusTaobao());
    }

    /**
     * 获取物流产品
     *
     * @param outboundApplyBO 出库参数
     * @return 物流产品
     */
    private String getLogisticsProduct(WarehouseOutboundApplyBO outboundApplyBO) {
        TradeDO trade = outboundApplyBO.getTrade();
        if (StringUtils.isNotBlank(trade.getExpressType()) && "电商标快".equals(trade.getExpressType())) {
            // 电商标快编码
            return "SE0136";
        }
        // 顺丰标快编码
        return "S2";
    }

    /**
     * 构建平台参数
     *
     * @param platform 平台
     * @param trade    订单
     * @return 平台参数
     */
    private Optional<String> buildPlatformJson(PlatformDO platform, TradeDO trade) {
        JsonObject platformJson = new JsonObject();
        // 设置微派编码
        Optional<String> wpServiceCode = this.setWpServiceCode(platform, trade);
        wpServiceCode.filter(StringUtils::isNotBlank).ifPresent(code -> platformJson.addProperty("wpServiceCode", code));
        // 构建平台参数
        return platformJson.size() != 0 ? Optional.of(platformJson.toString()) : Optional.empty();
    }

    /**
     * 设置微派编码
     *
     * @param platform 平台
     * @param trade    订单
     * @return 微派编码
     */
    private Optional<String> setWpServiceCode(PlatformDO platform, TradeDO trade) {
        if (!PlatformConstant.XIAOHONGSHU.contains(trade.getPlatformId())) {
            return Optional.empty();
        }
        if (OrderTag.NATIONAL_SUBSIDY.notEquals(trade.getOrderTag())) {
            return Optional.empty();
        }
        OrderGetQry qry = OrderGetQry.builder().orderNo(trade.getPlatformTradeId()).shopCode(platform.getShopCode()).build();
        SingleResponse<OrderDTO> order = adapterShopFeignClient.getOrder(qry);
        if (order.getData() == null) {
            return Optional.empty();
        }
        // 解析小红书订单数据，获取微派编码
        String wpServiceCode = GsonUtil.getAsString(order.getData().getOriginOrderData(), "subsidyWpServiceCode");
        return Optional.ofNullable(wpServiceCode);
    }

    /**
     * 判断库存充足
     *
     * @param trade          订单
     * @param outboundOrders 出库订单明细
     * @param warehouseCode  仓库编码
     * @return 释放临时占用的库存的回调方法集合
     */
    private List<ReleaseStockFunction> checkStockEnough(TradeDO trade, List<OrderDO> outboundOrders, String warehouseCode) {
        // 合计 69 码的出库数量
        Map<String, OrderDO> outboundStocks = outboundOrders.stream()
                .collect(Collectors.toMap(OrderDO::getBarcode, Function.identity(),
                        (obj1, obj2) -> BeanCopier.copy(obj1, OrderDO::new, (s, t) -> t.setNum(obj1.getNum() + obj2.getNum()))
                ));

        // 查询仓库总库存
        Map<String, WarehouseStockDTO> warehouseStocks = this.queryStocks(trade.getPlatformTradeId(),
                warehouseCode, outboundStocks.keySet());

        // 存储回调方法，用于释放临时占用的库存
        List<ReleaseStockFunction> listReleaseStockFunction = Lists.newArrayListWithCapacity(outboundOrders.size());
        // 检查每个物料的库存充足
        for (Map.Entry<String, OrderDO> entry : outboundStocks.entrySet()) {
            String barcode = entry.getKey();
            OrderDO outboundOrder = entry.getValue();
            // 需要出库的库存
            int outboundStock = outboundOrder.getNum();
            try {
                // 检查仓库可用库存充足
                WarehouseStockDTO warehouseStock = Optional.ofNullable(warehouseStocks.get(barcode))
                        .filter(stock -> stock.getAvailableQuantity() > 0)
                        .filter(stock -> stock.getTotalQuantity() >= outboundStock)
                        .orElseThrow(() -> GlobalDefaultException.of("物料 %s 仓库总库存不足", barcode));
                // 本地占用的库存
                int localUseStock = orderRepository.getBaseMapper().sumLocalUseStock(warehouseCode, barcode);
                // 检查库存充足，使用物料 69 码加锁，返回回调方法用于释放临时占用的库存
                String bizName = "warehouse:outbound_barcode:" + warehouseCode;
                ReleaseStockFunction releaseCacheStock = stockEnoughChecker.check(bizName, barcode,
                        warehouseStock.getTotalQuantity(), outboundStock, localUseStock);
                // 记录临时占用的库存
                listReleaseStockFunction.add(releaseCacheStock);
            } catch (Exception e) {
                // 释放临时占用的库存，如果释放失败则继续占用库存，等待过期自动释放所有库存
                listReleaseStockFunction.forEach(ReleaseStockFunction::release);
                if (e instanceof GlobalDefaultException) {
                    throw GlobalDefaultException.of("订单 %s %s [%s]",
                            trade.getPlatformTradeId(), e.getMessage(), outboundOrder.getErpName());
                }
                throw GlobalDefaultException.of(e, "订单 %s 物料 %s 检查库存异常 [%s]",
                        trade.getPlatformTradeId(), barcode, outboundOrder.getErpName());
            }
        }
        return listReleaseStockFunction;
    }

    /**
     * 查询仓库库存
     *
     * @param tradeId       订单号
     * @param warehouseCode 仓库编码
     * @param barcodes      物料编码集合
     * @return 仓库库存集合
     */
    private Map<String, WarehouseStockDTO> queryStocks(String tradeId, String warehouseCode, Set<String> barcodes) {
        try {
            WarehouseStockQry queryStocksQry = WarehouseStockQry.builder().warehouse(WarehouseEnum.SF)
                    .warehouseCode(warehouseCode).skuIds(Lists.newArrayList(barcodes)).build();
            List<WarehouseStockDTO> listWarehouseStocks = adapterWarehouseFeignClient.queryStocks(queryStocksQry).getData();
            return listWarehouseStocks.stream().collect(Collectors.toMap(WarehouseStockDTO::getSkuId, Function.identity()));
        } catch (Exception e) {
            throw GlobalDefaultException.of(e, "订单 %s 查询库存失败: %s", tradeId, e.getMessage());
        }
    }

    /**
     * 筛选需要出库的订单明细列表
     *
     * @param tradeId        订单号
     * @param orders         订单明细
     * @param productChecker 仓库出库物料检查
     * @return 订单明细列表
     */
    private List<OrderDO> listOutboundOrders(String tradeId, List<OrderDO> orders, Consumer<ProductInfoDO> productChecker) {
        // 订单明细出库检查
        UnaryOperator<OrderDO> mapOutboundOrder = order -> {
            ProductInfoDO product = productGetQryExe.execute(order.getErpCode());
            // 虚拟物料不支持出库，但是不终止出库操作
            if (product.getMaterialType() == 3) {
                return null;
            }
            if (product.getIsUse() != 1) {
                throw warehouseOutboundApplyChecker.notAllowException(tradeId, "物料 " + product.getProductId() + " 配置未启用");
            }
            if (StringUtils.isBlank(product.getBarcode())) {
                throw warehouseOutboundApplyChecker.notAllowException(tradeId, "物料 " + product.getProductId() + " 未维护69码");
            }
            // 系统抛单出库时，判断非系统抛单的物料，不允许出库
            if (productChecker != null) {
                productChecker.accept(product);
            }
            // 生成需要出库的订单明细，使用物料维护的 69 码
            OrderDO outboundOrder = BeanCopier.copy(order, OrderDO::new);
            outboundOrder.setBarcode(product.getBarcode());
            return outboundOrder;
        };

        // 筛选需要出库的订单明细列表
        List<OrderDO> outboundOrders = orders.stream()
                .filter(order -> order.getNum() != null && order.getNum() > 0)
                .map(mapOutboundOrder)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (outboundOrders.isEmpty()) {
            throw warehouseOutboundApplyChecker.notAllowException(tradeId, "不存在可以出库物料，请检查订单明细数据是否正确");
        }
        Map<String, Integer> logOutboundStocks = outboundOrders.stream()
                .collect(Collectors.toMap(OrderDO::getBarcode, OrderDO::getNum, Integer::sum));
        log.info("需要出库的物料 {}", GsonUtil.objectToJson(logOutboundStocks));
        return outboundOrders;
    }

}
