package com.xtc.onlineretailers.refactor.executor.query;

import com.xtc.marketing.invoiceservice.invoice.InvoiceFeignClient;
import com.xtc.marketing.invoiceservice.invoice.NotSystemInvoiceFeignClient;
import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceDetailGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoiceFileGetQry;
import com.xtc.marketing.invoiceservice.invoice.dto.query.NotSystemFileGetQry;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.refactor.enums.InvoiceCreateType;
import com.xtc.onlineretailers.refactor.pojo.entity.InvoiceApplyDO;
import com.xtc.onlineretailers.refactor.pojo.query.InvoiceApplyFileGetQry;
import com.xtc.onlineretailers.refactor.repository.InvoiceApplyRepository;
import com.xtc.onlineretailers.refactor.util.FileDownloader;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 获取发票申请的文件命令执行器
 */
@RequiredArgsConstructor
@Component
public class InvoiceFileGetQryExe {

    private final InvoiceApplyRepository invoiceApplyRepository;
    private final InvoiceFeignClient invoiceFeignClient;
    private final NotSystemInvoiceFeignClient notSystemInvoiceFeignClient;

    /**
     * 获取发票文件
     *
     * @param qry 参数
     * @return 发票文件
     */
    public ResponseEntity<Resource> execute(InvoiceApplyFileGetQry qry) {
        InvoiceApplyDO apply = invoiceApplyRepository.getById(qry.getApplyId());
        if (apply == null) {
            throw new GlobalDefaultException("发票申请不存在");
        }
        // 判断非发票系统文件，发票代码不为空则为非发票系统文件
        boolean isNotSystem = qry.getCreateType() == InvoiceCreateType.BLUE
                ? StringUtils.isNotBlank(apply.getBlueInvoiceCode()) : StringUtils.isNotBlank(apply.getRedInvoiceCode());
        Resource invoiceFileResource;
        if (isNotSystem) {
            // 非发票系统文件获取
            String objectName = qry.getCreateType() == InvoiceCreateType.BLUE ? apply.getBlueInvoiceUrl() : apply.getRedInvoiceUrl();
            NotSystemFileGetQry notSystemFileGetQry = NotSystemFileGetQry.builder().objectName(objectName).build();
            ResponseEntity<Resource> invoiceFileResponse = notSystemInvoiceFeignClient.invoiceFile(notSystemFileGetQry);
            invoiceFileResource = invoiceFileResponse.getBody();
        } else {
            // 红票取 invoiceId 蓝票取 blueInvoiceId 或者 invoiceId
            String invoiceId = qry.getCreateType() == InvoiceCreateType.RED
                    ? apply.getInvoiceId()
                    : StringUtils.defaultIfBlank(apply.getBlueInvoiceId(), apply.getInvoiceId());
            InvoiceDetailGetQry invoiceDetailGetQry = InvoiceDetailGetQry.builder().invoiceId(invoiceId).build();
            InvoiceDTO invoiceDTO = invoiceFeignClient.invoiceDetail(invoiceDetailGetQry).getData();
            InvoiceFileGetQry invoiceFileGetQry = InvoiceFileGetQry.builder().fileToken(invoiceDTO.getFileToken()).build();
            ResponseEntity<Resource> invoiceFileResponse = invoiceFeignClient.invoiceFile(invoiceFileGetQry);
            invoiceFileResource = invoiceFileResponse.getBody();
        }
        return this.buildResourceResponseEntity(invoiceFileResource);
    }

    /**
     * 构建文件下载的响应实体
     *
     * @param invoiceFileResource 发票文件响应
     * @return 响应实体
     */
    private ResponseEntity<Resource> buildResourceResponseEntity(Resource invoiceFileResource) {
        if (invoiceFileResource == null) {
            throw GlobalDefaultException.of("发票文件不存在");
        }
        try {
            return FileDownloader.buildResourceResponseEntity(
                    invoiceFileResource.getFilename(),
                    MediaType.APPLICATION_PDF,
                    FileDownloader.ContentDisposition.INLINE,
                    invoiceFileResource.getInputStream()
            );
        } catch (IOException e) {
            throw GlobalDefaultException.of("发票文件获取失败", e);
        }
    }

}
