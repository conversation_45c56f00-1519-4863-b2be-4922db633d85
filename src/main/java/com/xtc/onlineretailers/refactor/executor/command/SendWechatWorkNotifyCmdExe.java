package com.xtc.onlineretailers.refactor.executor.command;

import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.pojo.entity.SystemParamDO;
import com.xtc.onlineretailers.refactor.repository.SystemParamRepository;
import com.xtc.onlineretailers.refactor.util.RedissonUtil;
import com.xtc.onlineretailers.util.MD5Util;
import com.xtc.onlineretailers.util.XtcMessageSender;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.StringJoiner;

/**
 * 发送企业微信通知
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SendWechatWorkNotifyCmdExe {

    private final SystemParamRepository systemParamRepository;
    private final RedissonUtil redissonUtil;

    /**
     * 发送企业微信通知（默认重复发送限制时间 1 小时）
     *
     * @param content 内容，支持 String.format(content, args)
     * @param args    内容模板参数
     */
    public void execute(String content, Object... args) {
        this.execute(Duration.ofHours(1), this.getDefaultUsers(), content, args);
    }

    /**
     * 发送企业微信通知（默认重复发送限制时间 1 小时）
     *
     * @param users   用户列表（工号1|工号2|工号3|...）
     * @param content 内容，支持 String.format(content, args)
     * @param args    内容模板参数
     */
    public void execute(List<String> users, String content, Object... args) {
        this.execute(Duration.ofHours(1), users, content, args);
    }

    /**
     * 发送企业微信通知
     *
     * @param expire  重复发送限制时间，默认 1 小时
     * @param users   用户列表（工号1|工号2|工号3|...）
     * @param content 内容，支持 String.format(content, args)
     * @param args    内容模板参数
     */
    public void execute(Duration expire, List<String> users, String content, Object... args) {
        if (StringUtils.isBlank(content)) {
            return;
        }
        try {
            // 用户列表格式化
            StringJoiner userJoiner = new StringJoiner("|");
            users.forEach(userJoiner::add);
            // 内容格式化
            String contentFormat = args != null && args.length > 0 ? String.format(content, args) : content;
            // 重复发送限制
            expire = expire != null ? expire : Duration.ofHours(1);
            String contentKey = MD5Util.encode(contentFormat);
            Long incr = redissonUtil.incr(contentKey, expire);
            if (incr != null && incr > 1) {
                log.warn("发送企业微信通知失败 - 已发送过通知 {} 秒内只能发送一次", expire.getSeconds());
                return;
            }
            // 发送企业微信
//            XtcMessageSender.cardTextMessage(userJoiner.toString(), contentFormat);
        } catch (Exception e) {
            log.warn("发送企业微信通知失败 - {}", e.getMessage(), e);
        }
    }

    /**
     * 获取默认用户列表
     *
     * @return 用户列表
     */
    private List<String> getDefaultUsers() {
        String users = systemParamRepository.getByName(GlobalConstant.ORDER_FILL_ERP_CODE_ERROR)
                .map(SystemParamDO::getParamValue)
                .orElseThrow(() -> new RuntimeException("系统参数未配置，发送企业微信通知用户无法获取"));
        return Collections.singletonList(users);
    }

}
