package com.xtc.onlineretailers.refactor.executor.query;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.onlineretailers.pojo.entity.WithdrawStoreOrderDO;
import com.xtc.onlineretailers.refactor.converter.WithdrawStoreOrderConverter;
import com.xtc.onlineretailers.refactor.pojo.dto.WithdrawStoreOrderDTO;
import com.xtc.onlineretailers.refactor.pojo.query.WithdrawStorePageQry;
import com.xtc.onlineretailers.refactor.repository.WithdrawStoreOrderRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 退库单分页查询
 */
@RequiredArgsConstructor
@Component
public class WithdrawStorePageQryExe {

    private final WithdrawStoreOrderRepository withdrawStoreOrderRepository;
    private final WithdrawStoreOrderConverter withdrawStoreOrderConverter;

    public PageResponse<WithdrawStoreOrderDTO> execute(WithdrawStorePageQry qry) {
        IPage<WithdrawStoreOrderDO> page = withdrawStoreOrderRepository.pageBy(qry);
        List<WithdrawStoreOrderDTO> pageWithdrawStoreOrderDO =
                withdrawStoreOrderConverter.toPageWithdrawStoreOrderDO(page.getRecords());
        return PageResponse.of(pageWithdrawStoreOrderDO, (int) page.getTotal(),
                (int) page.getSize(), (int) page.getCurrent());
    }

}
