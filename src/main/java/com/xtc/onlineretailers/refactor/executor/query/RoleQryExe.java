package com.xtc.onlineretailers.refactor.executor.query;

import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.RolesDO;
import com.xtc.onlineretailers.pojo.entity.SystemParamDO;
import com.xtc.onlineretailers.refactor.pojo.dto.RoleDTO;
import com.xtc.onlineretailers.refactor.repository.SystemParamRepository;
import com.xtc.onlineretailers.repository.RolesDao;
import com.xtc.onlineretailers.util.CollectionCopier;
import com.xtc.onlineretailers.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 获取角色列表
 */
@RequiredArgsConstructor
@Component
public class RoleQryExe {

    private final RolesDao roleDao;
    private final SystemParamRepository systemParamRepository;

    /**
     * 系统配置：过滤的角色列表
     */
    private static final String FILTERED_ROLES = "filtered_roles";

    public List<RoleDTO> execute() {
        // 获取过滤的系统资源
        Map<String, String> roleMap = this.getFilteredSystemResource();
        // 获取角色列表
        List<RolesDO> roles = roleDao.list();
        // 过滤角色列表
        List<RolesDO> rolesDOS = roles.stream()
                .filter(rolesDO -> !roleMap.containsKey(rolesDO.getCode()))
                .collect(Collectors.toList());
        return CollectionCopier.copy(rolesDOS, RoleDTO::new);
    }

    /**
     * 获取过滤的系统资源
     *
     * @return 角色列表
     */
    private Map<String, String> getFilteredSystemResource() {
        try {
            SystemParamDO systemParamDO = systemParamRepository.getByName(FILTERED_ROLES)
                    .orElseThrow(() -> GlobalDefaultException.of("系统配置不存在：[%s]", FILTERED_ROLES));
            String roleJson = systemParamDO.getParamValue();
            List<RoleDTO> filteredRoles = GsonUtil.jsonToList(roleJson, RoleDTO.class);
            return filteredRoles.stream()
                    .collect(Collectors.toMap(RoleDTO::getCode, RoleDTO::getName, (o, n) -> o));
        } catch (Exception e) {
            throw GlobalDefaultException.of("系统资源配置错误：[%s]", FILTERED_ROLES);
        }
    }

}
