package com.xtc.onlineretailers.refactor.executor.command;

import com.google.common.collect.Lists;
import com.xtc.onlineretailers.enums.InvoiceStatus;
import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.OrdinaryInvoiceRegisterDO;
import com.xtc.onlineretailers.pojo.entity.SpecialInvoiceRegisterDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.converter.OrdinaryInvoiceRegisterConverter;
import com.xtc.onlineretailers.refactor.pojo.command.OrdinaryInvoiceAddCmd;
import com.xtc.onlineretailers.refactor.repository.OrdinaryInvoiceRegisterRepository;
import com.xtc.onlineretailers.refactor.repository.SpecialInvoiceRegisterRepository;
import com.xtc.onlineretailers.refactor.repository.TradeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 添加普票记录
 */
@RequiredArgsConstructor
@Component
public class OrdinaryInvoiceAddCmdExe {

    private final OrdinaryInvoiceRegisterRepository ordinaryInvoiceRegisterRepository;
    private final OrdinaryInvoiceRegisterConverter ordinaryInvoiceRegisterConverter;
    private final SpecialInvoiceRegisterRepository specialInvoiceRegisterRepository;
    private final TradeRepository tradeRepository;
    private static final List<Integer> INVOICE_STATUS = Lists.newArrayList(0, 1, 2);

    public void execute(OrdinaryInvoiceAddCmd ordinaryInvoiceAddCmd) {
        // 校验普票的处理状态
        this.checkOrdinaryInvoice(ordinaryInvoiceAddCmd);

        // 校验订单状态和发票状态
        this.checkTradeStatusAndInvoiceStatus(ordinaryInvoiceAddCmd);

        // 校验专票信息
        this.checkSpecialInvoiceRegister(ordinaryInvoiceAddCmd);

        // 转化普票实体类
        OrdinaryInvoiceRegisterDO ordinaryInvoiceRegisterDO = ordinaryInvoiceRegisterConverter.toOrdinaryInvoiceRegisterRepositoryDO(ordinaryInvoiceAddCmd);

        // 保存数据
        this.ordinaryInvoiceRegisterRepository.save(ordinaryInvoiceRegisterDO);
    }

    /**
     * 校验普票处理状态
     *
     * @param ordinaryInvoiceAddCmd 参数
     */
    private void checkOrdinaryInvoice(OrdinaryInvoiceAddCmd ordinaryInvoiceAddCmd) {
        Optional<OrdinaryInvoiceRegisterDO> optional =
                ordinaryInvoiceRegisterRepository.getByPlatformTradeId(ordinaryInvoiceAddCmd.getPlatformTradeId());
        if (optional.isPresent() && INVOICE_STATUS.contains(optional.get().getStatus())) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_ORDINARY_INVOICE_REGISTER_RECORD_EXIST);
        }
    }

    /**
     * 校验是否已开专票
     *
     * @param ordinaryInvoiceAddCmd 参数
     */
    private void checkSpecialInvoiceRegister(OrdinaryInvoiceAddCmd ordinaryInvoiceAddCmd) {
        Optional<SpecialInvoiceRegisterDO> specialInvoiceRegisterOptional =
                specialInvoiceRegisterRepository.getByPlatformTradeId(ordinaryInvoiceAddCmd.getPlatformTradeId());
        if (specialInvoiceRegisterOptional.isPresent()) {
            throw new GlobalDefaultException("订单:" + ordinaryInvoiceAddCmd.getPlatformTradeId() + "已登记了专票,不可以再登记");
        }
    }

    /**
     * 校验发票状态(已开纸票)和订单状态（已退货）
     *
     * @param ordinaryInvoiceAddCmd 参数
     */
    private void checkTradeStatusAndInvoiceStatus(OrdinaryInvoiceAddCmd ordinaryInvoiceAddCmd) {
        Optional<TradeDO> tradeDO = tradeRepository.getByTradeId(ordinaryInvoiceAddCmd.getPlatformTradeId());
        TradeDO trade = tradeDO.orElseThrow(() -> new GlobalDefaultException("订单不存在"));
        if (OrderStatus.RETURNED.name().equals(trade.getStatusOrder())
                || OrderStatus.HAS_CANCEL.name().equals(trade.getStatusOrder())) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_INVOICE_TRADE_STATUS);
        }
        if (InvoiceStatus.OPENED_PAPER.name().equals(trade.getInvoiceStatus())) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_INVOICE_TRADE_CHEQUE_PAPER);
        }
    }

}
