package com.xtc.onlineretailers.refactor.executor.query;

import com.xtc.marketing.adapterservice.shop.AdapterShopFeignClient;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.query.OrderGetQry;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import com.xtc.onlineretailers.pojo.entity.OrderDO;
import com.xtc.onlineretailers.pojo.entity.PlaintextDO;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.enums.TradeShippingType;
import com.xtc.onlineretailers.refactor.executor.command.DecryptTextCmdExe;
import com.xtc.onlineretailers.refactor.pojo.dto.ExtendDataDTO;
import com.xtc.onlineretailers.refactor.pojo.dto.OriginOrderDataDTO;
import com.xtc.onlineretailers.refactor.pojo.dto.TradeShippingDTO;
import com.xtc.onlineretailers.refactor.repository.OrderRepository;
import com.xtc.onlineretailers.refactor.repository.TradeRepository;
import com.xtc.onlineretailers.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Component
public class TradeShippingQryExe {

    private final TradeRepository tradeRepository;
    private final OrderRepository orderRepository;
    private final AdapterShopFeignClient adapterShopFeignClient;
    private final PlatformGetQryExe platformGetQryExe;
    private final DecryptTextCmdExe decryptTextCmdExe;

    /**
     * 发货订单明细生成
     *
     * @param tradeIds 订单号列表
     * @return 发货明细列表
     */
    public List<TradeShippingDTO> execute(List<String> tradeIds) {
        List<TradeDO> trades = tradeRepository.listShipping(tradeIds);
        List<TradeShippingDTO> shippingList = new ArrayList<>();
        trades.forEach(tradeDO -> {
            // 订单解密
            PlaintextDO plaintextDO = decryptTextCmdExe.execute(tradeDO);
            String oldAddress = plaintextDO != null ? plaintextDO.getReceiverAddress() : tradeDO.getReceiverAddress();
            // 查询会员商城订单明细
            PlatformDO platform = platformGetQryExe.getByPlatformId(tradeDO.getPlatformId());
            List<OriginOrderDataDTO.Item> itemList = this.listOrder(tradeDO.getPlatformTradeId(), platform.getShopCode());
            // 查询订单明细
            List<OrderDO> orderList = orderRepository.listByTradeId(tradeDO.getPlatformTradeId())
                    .stream()
                    // 过滤无发货数量明细
                    .filter(orderDO -> StringUtils.isNotBlank(orderDO.getErpCode()))
                    // 过滤无物料代码明细
                    .filter(orderDO -> orderDO.getNum() != null)
                    .collect(Collectors.toList());
            List<OrderDO> allRefultOrderList = orderList.stream()
                    .filter(orderDO -> orderDO.getNum().equals(orderDO.getRefundNum())).collect(Collectors.toList());
            // 部分退款
            if (orderList.size() != allRefultOrderList.size()) {
                // 填充用户订单明细
                List<TradeShippingDTO> userShippingList = this.fillUserShippingList(orderList, tradeDO, plaintextDO, itemList);
                shippingList.addAll(userShippingList);
                // 填充代理订单明细
                List<TradeShippingDTO> agentShippingList = this.fillAgentShippingList(orderList, oldAddress, itemList, TradeShippingType.AGENT_TRADE);
                shippingList.addAll(agentShippingList);
                return;
            }
            // 全退退款
            List<TradeShippingDTO> agentShippingList = this.fillAgentShippingList(allRefultOrderList, oldAddress, itemList, TradeShippingType.ALL_REFUND_TRADE);
            shippingList.addAll(agentShippingList);
        });
        return shippingList.stream().sorted(Comparator.comparing(TradeShippingDTO::getShippingType)).collect(Collectors.toList());
    }

    /**
     * 填充代理发货明细
     *
     * @param orderList  订单明细列表
     * @param oldAddress 原地址
     * @param itemList   会员商城明细列表
     * @return 代理发货明细列表
     */
    private List<TradeShippingDTO> fillAgentShippingList(List<OrderDO> orderList, String oldAddress,
                                                         List<OriginOrderDataDTO.Item> itemList, TradeShippingType type) {
        List<TradeShippingDTO> shippingList = new ArrayList<>();
        orderList.stream().filter(orderDO -> orderDO.getRefundNum() != null && orderDO.getRefundNum() > 0)
                .forEach(orderDO -> {
                    TradeShippingDTO shippingDTO = new TradeShippingDTO();
                    itemList.stream().filter(i -> i.getSkuErpCode().equals(orderDO.getErpCode()))
                            .findFirst()
                            .ifPresent(value -> this.fillAgentOrderDate(value, shippingDTO));
                    String newTradeId = orderDO.getPlatformTradeId() + 0;
                    shippingDTO.setNewTradeId(newTradeId);
                    // 原单信息
                    shippingDTO.setOrderId(orderDO.getPlatformOrderId());
                    shippingDTO.setTradeId(orderDO.getPlatformTradeId());
                    shippingDTO.setErpCode(orderDO.getErpCode());
                    shippingDTO.setErpName(orderDO.getErpName());
                    shippingDTO.setAddress(oldAddress);
                    // 特有信息
                    shippingDTO.setNum(0);
                    shippingDTO.setChangeNum(orderDO.getRefundNum());
                    shippingDTO.setShippingType(type);
                    shippingList.add(shippingDTO);
                });
        return shippingList;
    }

    /**
     * 填充代理订单明细
     *
     * @param item        会员商城订单明细
     * @param shippingDTO 发货明细
     */
    private void fillAgentOrderDate(OriginOrderDataDTO.Item item, TradeShippingDTO shippingDTO) {
        // 设置明细订单号
        shippingDTO.setNewOrderId(item.getItemNo());
        // 填充收件人信息
        ExtendDataDTO extendDataDTO = GsonUtil.jsonToBean(item.getExtendData(), ExtendDataDTO.class);
        Optional.ofNullable(extendDataDTO).map(ExtendDataDTO::getAfterSaleAddress)
                .ifPresent(saleAddress -> {
                    shippingDTO.setReceiverName(saleAddress.getReceiverName());
                    shippingDTO.setReceiverAddress(saleAddress.getReceiverAddress());
                    shippingDTO.setReceiverProvince(saleAddress.getReceiverProvince());
                    shippingDTO.setReceiverCity(saleAddress.getReceiverCity());
                    shippingDTO.setReceiverDistrict(saleAddress.getReceiverDistrict());
                    shippingDTO.setReceiverMobile(saleAddress.getReceiverMobile());
                });
    }

    /**
     * 填充用户发货明细
     *
     * @param orders      订单明细列表
     * @param plaintextDO 收件人信息
     */
    private List<TradeShippingDTO> fillUserShippingList(List<OrderDO> orders, TradeDO tradeDO,
                                                        PlaintextDO plaintextDO, List<OriginOrderDataDTO.Item> itemList) {
        List<TradeShippingDTO> shippingList = new ArrayList<>();
        orders.stream().filter(orderDO -> !orderDO.getNum().equals(orderDO.getRefundNum()))
                .forEach(orderDO -> {
                    TradeShippingDTO shippingDTO = new TradeShippingDTO();
                    // 设置明细订单号
                    itemList.stream().filter(i -> i.getSkuErpCode().equals(orderDO.getErpCode()))
                            .findFirst()
                            .ifPresent(value -> shippingDTO.setNewOrderId(value.getItemNo()));
                    // 调整后数量：原数量-原单退货数量
                    int changeNum = orderDO.getRefundNum() == null ? orderDO.getNum() : orderDO.getNum() - orderDO.getRefundNum();
                    shippingDTO.setChangeNum(changeNum);
                    // 原明细信息
                    shippingDTO.setErpCode(orderDO.getErpCode());
                    shippingDTO.setOrderId(orderDO.getPlatformOrderId());
                    shippingDTO.setTradeId(orderDO.getPlatformTradeId());
                    shippingDTO.setNum(orderDO.getNum());
                    shippingDTO.setErpName(orderDO.getErpName());
                    // 特有信息
                    shippingDTO.setReceiverAddress(tradeDO.getReceiverAddress());
                    shippingDTO.setShippingType(TradeShippingType.USER_TRADE);
                    // 收件人信息
                    if (plaintextDO != null) {
                        shippingDTO.setAddress(plaintextDO.getReceiverAddress());
                        shippingDTO.setReceiverProvince(plaintextDO.getReceiverProvince());
                        shippingDTO.setReceiverDistrict(plaintextDO.getReceiverDistrict());
                        shippingDTO.setReceiverCity(plaintextDO.getReceiverCity());
                        shippingDTO.setReceiverName(plaintextDO.getReceiverName());
                        shippingDTO.setReceiverMobile(plaintextDO.getReceiverMobile());
                        shippingDTO.setReceiverAddress(plaintextDO.getReceiverAddress());
                        shippingList.add(shippingDTO);
                        return;
                    }
                    shippingDTO.setAddress(tradeDO.getReceiverAddress());
                    shippingDTO.setReceiverProvince(tradeDO.getReceiverProvince());
                    shippingDTO.setReceiverDistrict(tradeDO.getReceiverDistrict());
                    shippingDTO.setReceiverCity(tradeDO.getReceiverCity());
                    shippingDTO.setReceiverName(tradeDO.getReceiverName());
                    shippingDTO.setReceiverMobile(tradeDO.getReceiverMobile());
                    shippingList.add(shippingDTO);
                });
        return shippingList;
    }

    /**
     * 查询会员商城订单明细
     *
     * @param tradeId  订单号
     * @param shopCode 店铺代码
     */
    private List<OriginOrderDataDTO.Item> listOrder(String tradeId, String shopCode) {
        OrderGetQry qry = OrderGetQry.builder()
                .shopCode(shopCode).orderNo(tradeId).build();
        SingleResponse<OrderDTO> order = adapterShopFeignClient.getOrder(qry);
        if (order == null || order.getData() == null) {
            return Collections.emptyList();
        }
        String orderData = order.getData().getOriginOrderData();
        OriginOrderDataDTO originOrderData = GsonUtil.jsonToBean(orderData, OriginOrderDataDTO.class);
        return originOrderData.getItems().stream()
                .filter(item -> Boolean.TRUE.equals(item.getOmsShipment())).collect(Collectors.toList());
    }

}
