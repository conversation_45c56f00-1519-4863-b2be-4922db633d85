package com.xtc.onlineretailers.refactor.executor.checker;

import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.refactor.pojo.entity.CheckLogDO;
import com.xtc.onlineretailers.refactor.repository.CheckLogRepository;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Optional;

@Component
public class CheckLogChecker {

    @Resource
    private CheckLogRepository checkLogRepository;

    /**
     * 校验核对日志
     *
     * @param tradeId 订单号
     */
    public void check(String tradeId) {
        Optional<CheckLogDO> checkLogDO = checkLogRepository.getByTradeId(tradeId);
        if (checkLogDO.isPresent()) {
            throw new GlobalDefaultException("此订单已核对");
        }
    }

}
