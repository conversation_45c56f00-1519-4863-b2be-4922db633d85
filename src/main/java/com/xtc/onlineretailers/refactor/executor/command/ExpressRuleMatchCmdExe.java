package com.xtc.onlineretailers.refactor.executor.command;

import com.google.common.collect.ImmutableList;
import com.xtc.onlineretailers.pojo.entity.ProductInfoDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.enums.LogisticsCompany;
import com.xtc.onlineretailers.refactor.pojo.dto.ExpressRuleDTO;
import com.xtc.onlineretailers.util.DateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 快递规则
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ExpressRuleMatchCmdExe {

    private final ExpressRuleGetExe expressRuleGetExe;

    /**
     * 特殊处理的收件地址：含有【乡、镇、村】
     */
    public static final List<String> SPECIAL_ADDRESS = ImmutableList.of("乡", "镇", "村");

    public ExpressRuleDTO execute(TradeDO trade, ProductInfoDO productInfoDO) {
        List<ExpressRuleDTO> expressRules = expressRuleGetExe.execute();
        // 所有快递规则配置，按照固定条件匹配一条规则
        ExpressRuleDTO expressRule = expressRules.stream()
                .filter(rule -> checkExpressRuleValid(trade, rule))
                .findAny()
                .orElse(null);
        if (expressRule != null) {
            // 匹配规则明细
            boolean matchRule = expressRule.getRules().stream()
                    .map(rule -> this.matchExpressRule(trade, rule, productInfoDO))
                    .findAny()
                    .orElse(false);
            if (matchRule) {
                log.info("订单 {} 匹配快递规则 {}", trade.getPlatformTradeId(), expressRule);
                return expressRule;
            }
            // 默认快递规则
            if (expressRule.getDefaultExpressCompany() != null && StringUtils.isNotBlank(expressRule.getDefaultExpressType())) {
                ExpressRuleDTO defaultExpressRule = new ExpressRuleDTO();
                defaultExpressRule.setExpressCompany(expressRule.getDefaultExpressCompany());
                defaultExpressRule.setExpressType(expressRule.getDefaultExpressType());
                log.info("订单 {} 匹配快递规则 {} 使用默认快递公司 {}", trade.getPlatformTradeId(), expressRule, defaultExpressRule);
                return defaultExpressRule;
            }
        }
        ExpressRuleDTO defaultExpressRule = this.getDefaultExpressRule(trade);
        log.info("订单 {} 使用系统默认快递规则 {}", trade.getPlatformTradeId(), defaultExpressRule);
        return defaultExpressRule;
    }

    /**
     * 校验快递规则有效
     *
     * @param trade          订单
     * @param expressRuleDTO 快递规则
     * @return 执行结果
     */
    public boolean checkExpressRuleValid(TradeDO trade, ExpressRuleDTO expressRuleDTO) {
        LocalDateTime now = LocalDateTime.now();
        int isProduct = expressRuleDTO.getIsProduct() != null && expressRuleDTO.getIsProduct() ? 1 : 0;
        return DateUtil.isBetween(now, expressRuleDTO.getStartTime(), expressRuleDTO.getEndTime())
                && isProduct == trade.getIsProduct()
                && expressRuleDTO.getExpressCompany() != null
                && expressRuleDTO.getPlatformIds().contains(trade.getPlatformId().toString())
                && ObjectUtils.isNotEmpty(expressRuleDTO.getDefaultExpressCompany());
    }
    /**
     * 获取默认的快递规则
     *
     * @param trade 订单
     * @return 默认的快递规则
     */
    private ExpressRuleDTO getDefaultExpressRule(TradeDO trade) {
        ExpressRuleDTO expressRule = new ExpressRuleDTO();
        // 机器选择顺丰，默认标快
        boolean isProduct = trade.getIsProduct() == 1;
        if (isProduct) {
            expressRule.setExpressCompany(LogisticsCompany.SF);
            expressRule.setExpressType("顺丰标快");
            return expressRule;
        }
        // 配件并且收件地址含有【乡、镇、村】，则选择：EMS
        if (StringUtils.isNotBlank(trade.getReceiverAddress())) {
            boolean addressMatch = SPECIAL_ADDRESS.stream().anyMatch(trade.getReceiverAddress()::contains);
            if (addressMatch) {
                expressRule.setExpressType("");
                expressRule.setExpressCompany(LogisticsCompany.EMS);
                return expressRule;
            }
        }
        // 配件默认：圆通速递
        expressRule.setExpressType("");
        expressRule.setExpressCompany(LogisticsCompany.YTO);
        return expressRule;
    }

    /**
     * 匹配快递规则
     *
     * @param trade         订单
     * @param rule          规则
     * @param productInfoDO 产品
     * @return 执行结果
     */
    private boolean matchExpressRule(TradeDO trade, ExpressRuleDTO.Rule rule, ProductInfoDO productInfoDO) {
        // 物料代码匹配
        Boolean productRule = Optional.ofNullable(rule.getErpCodes())
                .map(productIds -> productIds.contains(productInfoDO.getProductId()))
                .orElse(false);
        // 机型匹配
        Boolean modelRule = Optional.ofNullable(rule.getModels())
                .map(models -> models.contains(productInfoDO.getModelSort()))
                .orElse(false);
        // 省份匹配
        Boolean provinceRule = Optional.ofNullable(rule.getProvince())
                .map(province -> province.equals(trade.getReceiverProvince()))
                .orElse(false);
        // 订单城市匹配：订单城市包含规则城市
        Boolean cityRule = Optional.ofNullable(rule.getCity())
                .map(city -> city.stream().anyMatch(town -> trade.getReceiverCity().contains(town)))
                .orElse(false);
        return productRule || modelRule || provinceRule || cityRule;
    }

}
