package com.xtc.onlineretailers.refactor.service;

import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.refactor.ability.checker.ExpressRuleChecker;
import com.xtc.onlineretailers.refactor.executor.command.ExpressRuleGetExe;
import com.xtc.onlineretailers.refactor.executor.command.ExpressRuleUpdateCmdExe;
import com.xtc.onlineretailers.refactor.pojo.command.ExpressRuleAddCmd;
import com.xtc.onlineretailers.refactor.pojo.command.ExpressRuleUpdateCmd;
import com.xtc.onlineretailers.refactor.pojo.dto.ExpressRuleDTO;
import com.xtc.onlineretailers.refactor.pojo.query.ExpressRuleQry;
import com.xtc.onlineretailers.util.BeanCopier;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
@RequiredArgsConstructor
public class ExpressRuleServiceImpl implements ExpressRuleService {

    private final ExpressRuleGetExe expressRuleGetExe;
    private final ExpressRuleUpdateCmdExe expressRuleUpdateCmdExe;
    private final ExpressRuleChecker expressRuleChecker;

    @Override
    public List<ExpressRuleDTO> listExpressRules(ExpressRuleQry qry) {
        List<ExpressRuleDTO> expressTypes = expressRuleGetExe.execute();
        return expressTypes.stream()
                .filter(expressTypeDTO -> qry.getExpressCompany() == null
                        || expressTypeDTO.getExpressCompany().equals(qry.getExpressCompany()))
                // qry.contains(11111) DB: [11111,213214,1432543] qry: 11111,213214
                .filter(expressTypeDTO -> qry.getPlatformIds() == null
                        || expressTypeDTO.getPlatformIds().stream().anyMatch(id -> qry.getPlatformIds().contains(id)))
                .filter(expressTypeDTO -> qry.getIsProduct() == null
                        || Objects.equals(qry.getIsProduct(), expressTypeDTO.getIsProduct()))
                .collect(Collectors.toList());
    }

    @Override
    public void addExpressRules(ExpressRuleAddCmd cmd) {
        List<ExpressRuleDTO> expressRules = expressRuleGetExe.execute();
        // 判断平台快递规则存在
        expressRuleChecker.checkExpressRule(expressRules, cmd.getPlatformIds(), cmd.getIsProduct());
        // 生成唯一 id 如果重复则最多生成 3 次
        String ruleId = IntStream.range(0, 3)
                .mapToObj(i -> RandomStringUtils.randomAlphanumeric(6))
                .filter(id -> expressRules.stream().noneMatch(expressRule -> expressRule.getId().equals(id)))
                .findFirst()
                .orElseThrow(() -> GlobalDefaultException.of("生成规则失败，请稍后再试"));
        ExpressRuleDTO newRule = BeanCopier.copy(cmd, ExpressRuleDTO::new);
        newRule.setId(ruleId);
        expressRules.add(newRule);
        expressRuleUpdateCmdExe.execute(expressRules);
    }

    @Override
    public void updateExpressRules(String id, ExpressRuleUpdateCmd cmd) {
        List<ExpressRuleDTO> expressRules = expressRuleGetExe.execute();
        // 判断平台快递规则存在
        expressRuleChecker.checkExpressRule(expressRules, cmd.getPlatformIds(), cmd.getIsProduct());
        ExpressRuleDTO expressRule = expressRuleGetExe.getById(expressRules, id);
        expressRules.remove(expressRule);
        ExpressRuleDTO updateRule = BeanCopier.copy(cmd, ExpressRuleDTO::new);
        expressRules.add(updateRule);
        expressRuleUpdateCmdExe.execute(expressRules);
    }

    @Override
    public void deleteExpressRules(String id) {
        List<ExpressRuleDTO> expressRules = expressRuleGetExe.execute();
        ExpressRuleDTO expressRule = expressRuleGetExe.getById(expressRules, id);
        expressRules.remove(expressRule);
        expressRuleUpdateCmdExe.execute(expressRules);
    }

}
