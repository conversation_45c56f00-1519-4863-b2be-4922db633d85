package com.xtc.onlineretailers.refactor.service;

import com.xtc.marketing.adapterservice.shop.dto.RefundDTO;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.executor.command.RefundSyncCmdExe;
import com.xtc.onlineretailers.refactor.executor.query.PlatformGetQryExe;
import com.xtc.onlineretailers.refactor.executor.query.TradeGetQryExe;
import com.xtc.onlineretailers.refactor.pojo.bo.PullRefundParamBO;
import com.xtc.onlineretailers.refactor.pojo.command.ImportRefundSyncCmd;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class RefundApiServiceImpl implements RefundApiService {

    private final PlatformGetQryExe platformGetQryExe;
    private final TradeGetQryExe tradeGetQryExe;
    private final RefundSyncCmdExe refundSyncCmdExe;

    @Override
    public void importRefundSync(ImportRefundSyncCmd cmd) {
        // 查询平台数据，如果传参没有平台id，则使用订单数据里的平台id字段
        PlatformDO platform = this.getPlatform(cmd);
        if (StringUtils.isBlank(platform.getShopCode())) {
            log.info("平台未维护 shopCode 不做处理");
            return;
        }
        // 同步退款单数据
        PullRefundParamBO jobParam = PullRefundParamBO.builder().shopCode(platform.getShopCode()).pullRefundDetail(true).build();
        RefundDTO syncRefund = RefundDTO.builder().serviceNo(cmd.getRefundId()).orderNo(cmd.getTradeId()).build();
        refundSyncCmdExe.execute(jobParam, platform, syncRefund);
    }

    /**
     * 查询平台数据
     *
     * @param cmd 参数
     * @return 平台
     */
    private PlatformDO getPlatform(ImportRefundSyncCmd cmd) {
        Integer platformId = cmd.getPlatformId();
        if (platformId == null) {
            TradeDO trade = tradeGetQryExe.execute(cmd.getTradeId());
            platformId = trade.getPlatformId();
        }
        return platformGetQryExe.getByPlatformId(platformId);
    }

}
