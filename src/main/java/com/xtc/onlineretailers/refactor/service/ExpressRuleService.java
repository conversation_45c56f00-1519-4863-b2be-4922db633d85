package com.xtc.onlineretailers.refactor.service;

import com.xtc.onlineretailers.refactor.pojo.command.ExpressRuleAddCmd;
import com.xtc.onlineretailers.refactor.pojo.command.ExpressRuleUpdateCmd;
import com.xtc.onlineretailers.refactor.pojo.dto.ExpressRuleDTO;
import com.xtc.onlineretailers.refactor.pojo.query.ExpressRuleQry;

import java.util.List;

public interface ExpressRuleService {

    /**
     * 快递规则列表
     *
     * @param qry 参数
     * @return 快递规则列表
     */
    List<ExpressRuleDTO> listExpressRules(ExpressRuleQry qry);

    /**
     * 保存快递规则
     *
     * @param cmd 参数
     */
    void addExpressRules(ExpressRuleAddCmd cmd);

    /**
     * 更新快递规则
     *
     * @param id  唯一标识
     * @param cmd 参数
     */
    void updateExpressRules(String id, ExpressRuleUpdateCmd cmd);

    /**
     * 删除快递规则
     *
     * @param id 唯一标识
     */
    void deleteExpressRules(String id);

}
