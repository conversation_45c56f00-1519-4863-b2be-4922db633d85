package com.xtc.onlineretailers.refactor.service;

import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.onlineretailers.pojo.bo.DistributorBarcodeUploadBO;
import com.xtc.onlineretailers.refactor.executor.command.DistributorBarcodeDeleteCmdExe;
import com.xtc.onlineretailers.refactor.executor.command.DistributorBarcodeExportCmdExe;
import com.xtc.onlineretailers.refactor.executor.command.DistributorBarcodeUpdateCmdExe;
import com.xtc.onlineretailers.refactor.executor.command.DistributorBarcodeUploadCmdExe;
import com.xtc.onlineretailers.refactor.executor.query.DistributorBarcodePageQryExe;
import com.xtc.onlineretailers.refactor.pojo.command.DistributorBarcodeUpdateCmd;
import com.xtc.onlineretailers.refactor.pojo.entity.DistributorBarcodeDO;
import com.xtc.onlineretailers.refactor.pojo.query.DistributorBarcodePageQry;
import com.xtc.onlineretailers.util.ExcelUtil;
import com.xtc.springboot.pojo.entity.UserDetail;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

@Slf4j
@Component
@RequiredArgsConstructor
public class DistributorBarcodeServiceImpl implements DistributorBarcodeService {

    private final DistributorBarcodeUploadCmdExe distributorBarcodeUploadCmdExe;
    private final DistributorBarcodePageQryExe distributorBarcodeQryExe;
    private final DistributorBarcodeUpdateCmdExe distributionBarcodeUpdateCmdExe;
    private final DistributorBarcodeDeleteCmdExe distributorBarcodeDeleteCmdExe;
    private final DistributorBarcodeExportCmdExe distributorBarcodeExportCmdExe;

    @Override
    public PageResponse<DistributorBarcodeDO> pageByDistributorBarcodeDO(DistributorBarcodePageQry qry) {
        return distributorBarcodeQryExe.execute(qry);
    }

    @Override
    public void updateBarcode(DistributorBarcodeUpdateCmd cmd) {
        distributionBarcodeUpdateCmdExe.execute(cmd);
    }

    @Override
    public void export(DistributorBarcodePageQry qry, UserDetail user) {
        distributorBarcodeExportCmdExe.execute(qry, user);
    }

    @Override
    public void deleteBarcode(Long id) {
        distributorBarcodeDeleteCmdExe.execute(id);
    }

    @Override
    public void uploadBarcode(MultipartFile excel) {
        ExcelUtil.readExcel(excel, DistributorBarcodeUploadBO.class, distributorBarcodeUploadCmdExe::execute);
    }

}
