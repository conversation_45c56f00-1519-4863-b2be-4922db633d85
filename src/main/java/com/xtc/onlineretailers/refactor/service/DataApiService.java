package com.xtc.onlineretailers.refactor.service;

import com.xtc.onlineretailers.refactor.pojo.dto.LogisticsCompanyDTO;
import com.xtc.onlineretailers.refactor.pojo.dto.MenuDTO;
import com.xtc.onlineretailers.refactor.pojo.dto.RoleDTO;

import java.util.List;

public interface DataApiService {

    /**
     * 获取角色列表
     *
     * @return 角色列表
     */
    List<RoleDTO> roles();

    /**
     * 获取菜单列表
     *
     * @param roleCode 角色编码
     * @return 菜单列表
     */
    List<MenuDTO> menus(String roleCode);

    /**
     * 获取快递公司列表
     *
     * @return 快递公司列表
     */
    List<LogisticsCompanyDTO> listExpressCompany();

}
