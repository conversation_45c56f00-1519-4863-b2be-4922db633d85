package com.xtc.onlineretailers.refactor.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.onlineretailers.refactor.executor.command.OrderBarcodeMigrationCmdExe;
import com.xtc.onlineretailers.refactor.pojo.command.OrderBarcodeMigrationCmd;
import com.xtc.onlineretailers.refactor.pojo.dto.OrderBarcodeMigrationDTO;
import com.xtc.onlineretailers.refactor.pojo.entity.OrderBarcodeMigrationDO;
import com.xtc.onlineretailers.refactor.pojo.query.OrderBarcodeMigrationPageQry;
import com.xtc.onlineretailers.refactor.repository.OrderBarcodeMigrationRepository;
import com.xtc.onlineretailers.util.CollectionCopier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Service
public class OrderBarcodeMigrationServiceImpl implements OrderBarcodeMigrationService {

    private final OrderBarcodeMigrationCmdExe orderBarcodeMigrationCmdExe;
    private final OrderBarcodeMigrationRepository orderBarcodeMigrationRepository;

    @Override
    public PageResponse<OrderBarcodeMigrationDTO> pageOrderBarcodeMigration(OrderBarcodeMigrationPageQry qry) {
        IPage<OrderBarcodeMigrationDO> page = orderBarcodeMigrationRepository.pageBy(qry);
        List<OrderBarcodeMigrationDTO> dtoList = CollectionCopier.copy(page.getRecords(), OrderBarcodeMigrationDTO::new);
        return PageResponse.of(dtoList, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    @Override
    public void migration(OrderBarcodeMigrationCmd cmd) {
        orderBarcodeMigrationCmdExe.execute(cmd);
    }

}
