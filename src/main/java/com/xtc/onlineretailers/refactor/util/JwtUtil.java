package com.xtc.onlineretailers.refactor.util;

import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.time.Duration;
import java.util.*;

/**
 * JWT工具类
 */
@Slf4j
public class JwtUtil {

    private JwtUtil() {
    }

    /**
     * JWT存储在请求头headers里的名称
     */
    public static final String JWT_HEADER = "Authorization";
    /**
     * JWT存储在请求头headers里的值的前缀
     */
    public static final String JWT_PREFIX = "Bearer ";
    /**
     * JWT的过期时间，时间戳（毫秒级），1天后过期
     */
    public static final Duration EXPIRATION = Duration.ofDays(1);

    /**
     * 解析 token 获取公共声明
     *
     * @param publicKeyStr 公钥
     * @return 公共声明
     */
    public static Map<String, Object> parseJwtClaims(String publicKeyStr) {
        try {
            String jwt = getJwtFromHeader();
            Key publicKey = generatePublicKey(publicKeyStr);
            return Jwts.parserBuilder()
                    .setSigningKey(publicKey)
                    .build()
                    .parseClaimsJws(jwt)
                    .getBody();
        } catch (Exception e) {
            log.warn("Error occurred while parsing JWT", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 获取请求头headers里的jwt字符串
     *
     * @return jwt字符串
     */
    public static String getJwtFromHeader() {
        // 获取request
        ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (servletRequestAttributes == null) {
            return null;
        }
        HttpServletRequest request = servletRequestAttributes.getRequest();

        // 获取jwt
        Enumeration<String> requestHeader = request.getHeaderNames();
        while (requestHeader.hasMoreElements()) {
            String headerKey = requestHeader.nextElement();
            if (JWT_HEADER.equalsIgnoreCase(headerKey)) {
                String jwt = request.getHeader(headerKey);
                if (jwt != null) {
                    return jwt.replace(JWT_PREFIX, "");
                }
            }
        }
        return null;
    }

    /**
     * 生成 token
     *
     * @param issuer    发行方
     * @param claims    公共声明
     * @param secretKey 密钥
     * @return token
     */
    public static String createToken(String issuer, Map<String, Object> claims, String secretKey) {
        byte[] bytes = secretKey.getBytes();
        Key key = Keys.hmacShaKeyFor(bytes);
        long exp = System.currentTimeMillis() + EXPIRATION.getSeconds() * 1000;
        Date expirationDate = new Date(exp);
        JwtBuilder builder = Jwts.builder()
                .setClaims(claims)
                .setIssuer(issuer)
                .setExpiration(expirationDate)
                .signWith(key);
        return builder.compact();
    }

    /**
     * 生成秘钥key
     *
     * @param publicKey 公用秘钥
     * @return 秘钥key
     * @throws InvalidKeySpecException  InvalidKeySpecException
     * @throws NoSuchAlgorithmException NoSuchAlgorithmException
     */
    private static Key generatePublicKey(String publicKey) throws InvalidKeySpecException, NoSuchAlgorithmException {
        byte[] keyByte = Base64.getDecoder().decode(publicKey.getBytes(StandardCharsets.UTF_8));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyByte);
        return keyFactory.generatePublic(keySpec);
    }

}
