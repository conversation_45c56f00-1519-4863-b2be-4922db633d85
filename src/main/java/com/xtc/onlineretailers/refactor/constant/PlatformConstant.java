package com.xtc.onlineretailers.refactor.constant;

import com.google.common.collect.ImmutableList;

import java.util.List;

/**
 * 平台常量
 */
public class PlatformConstant {

    private PlatformConstant() {
    }

    /**
     * 平台类型：代销、代发
     */
    public static final String TYPE_AGENT = "AGENT";
    /**
     * 平台类型：门店履约平台（全渠道订单）
     */
    public static final String TYPE_OMNICHANNEL = "OMNICHANNEL";

    /**
     * 会员商城
     */
    public static final List<Integer> XTC_MALL = ImmutableList.of(2106, 2120);
    /**
     * 内部平台：内部购机，内销测试机
     */
    public static final List<Integer> INTERNAL = ImmutableList.of(1004, 1007);
    /**
     * 天猫平台
     */
    public static final List<Integer> TMALL = ImmutableList.of(1001, 1002);
    /**
     * 平台id：快手平台
     */
    public static final List<Integer> KUAISHOU = ImmutableList.of(2025, 2026);
    /**
     * 平台id：小红书
     */
    public static final List<Integer> XIAOHONGSHU = ImmutableList.of(2087, 2088);
    /**
     * 平台id：官方商城
     */
    public static final int XTC_SHOP = 1003;

}
