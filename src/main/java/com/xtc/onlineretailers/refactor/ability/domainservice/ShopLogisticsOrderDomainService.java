package com.xtc.onlineretailers.refactor.ability.domainservice;

import com.google.common.collect.Lists;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.shop.AdapterShopFeignClient;
import com.xtc.marketing.adapterservice.shop.dto.ShopLogisticsOrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsBarcodeCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsCargoCmd;
import com.xtc.marketing.adapterservice.shop.dto.command.ShopLogisticsOrderCreateCmd;
import com.xtc.marketing.adapterservice.shop.enums.ShopLogisticsCompanyEnum;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.OrderDO;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.constant.PlatformConstant;
import com.xtc.onlineretailers.refactor.enums.OrderTag;
import com.xtc.onlineretailers.refactor.executor.query.ProductBarcodeGetQryExe;
import com.xtc.onlineretailers.util.GsonUtil;
import com.xtc.onlineretailers.util.MoneyUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 店铺电子面单领域服务
 */
@RequiredArgsConstructor
@Component
public class ShopLogisticsOrderDomainService {

    private final AdapterShopFeignClient adapterShopFeignClient;
    private final ReceiverDefaultStrDomainService receiverDefaultStrDomainService;
    private final ProductBarcodeGetQryExe productBarcodeGetQryExe;
    private final TradeTagDomainService tradeTagDomainService;

    /**
     * 生成电子面单
     *
     * @param platform 平台
     * @param trade    订单
     * @param orders   订单明细
     * @param barcodes 条码列表
     * @return 电子面单
     */
    public ShopLogisticsOrderDTO createOrder(PlatformDO platform, TradeDO trade, List<OrderDO> orders, List<String> barcodes) {
        // 初始化电子面单参数
        ShopLogisticsOrderCreateCmd cmd = ShopLogisticsOrderCreateCmd.builder()
                .shopCode(platform.getShopCode())
                .shopOrderNo(trade.getPlatformTradeId())
                .logisticsOrderId(UUID.randomUUID().toString().replace("-", ""))
                .senderName(this.getSenderName(platform.getPlatformName()))
                .senderPhone("0769-88619398")
                .senderProvince("广东省")
                .senderCity("东莞市")
                .senderDistrict("长安镇")
                .senderAddress("沙头靖海西路113号小天才生产中心一楼网销仓")
                .receiverProvince(trade.getReceiverProvince())
                .receiverCity(trade.getReceiverCity())
                .receiverDistrict(trade.getReceiverDistrict())
                .build();
        // 设置收件人密文数据
        this.setReceiverCipher(cmd, platform, trade);
        // 收件人数据全是符号【*】，则设置默认值
        cmd.setReceiverName(receiverDefaultStrDomainService.defaultStringIfStar(cmd.getReceiverName()));
        cmd.setReceiverMobile(receiverDefaultStrDomainService.defaultStringIfStar(cmd.getReceiverMobile()));
        cmd.setReceiverAddress(receiverDefaultStrDomainService.defaultStringIfStar(cmd.getReceiverAddress()));
        // 设置物流数据
        ShopLogisticsCompanyEnum logisticsCompanyEnum = this.getLogisticsCompanyEnum(trade.getExpressCompany());
        cmd.setLogisticsCompany(logisticsCompanyEnum);
        cmd.setLogisticsType(this.getLogisticsType(logisticsCompanyEnum));
        // 设置货物数据
        this.setCargos(cmd, orders);
        // 设置条码
        if (PlatformConstant.XIAOHONGSHU.contains(platform.getPlatformId())
                && tradeTagDomainService.isTagExist(trade, OrderTag.NATIONAL_SUBSIDY)
                && barcodes != null) {
            this.setBarcodes(cmd, barcodes);
        }
        // 生成电子面单
        SingleResponse<ShopLogisticsOrderDTO> logisticsOrder = adapterShopFeignClient.createLogisticsOrder(cmd);
        return Optional.ofNullable(logisticsOrder.getData())
                .orElseThrow(() -> GlobalDefaultException.of("生成电子面单异常"));
    }

    /**
     * 设置条码
     *
     * @param cmd      参数
     * @param barcodes 条码列表
     */
    private void setBarcodes(ShopLogisticsOrderCreateCmd cmd, List<String> barcodes) {
        List<ShopLogisticsBarcodeCmd> shopLogisticsBarcodes = barcodes.stream()
                .map(productBarcodeGetQryExe::getByBarcode)
                .map(barcode -> {
                    ShopLogisticsBarcodeCmd shopLogisticsBarcodeCmd = new ShopLogisticsBarcodeCmd();
                    shopLogisticsBarcodeCmd.setImei(barcode.getImeiBarcode());
                    shopLogisticsBarcodeCmd.setBarcode(barcode.getBarcode());
                    return shopLogisticsBarcodeCmd;
                })
                .collect(Collectors.toList());
        cmd.setBarcodes(shopLogisticsBarcodes);
    }

    /**
     * 设置货物数据
     *
     * @param cmd    电子面单参数
     * @param orders 订单明细
     */
    private void setCargos(ShopLogisticsOrderCreateCmd cmd, List<OrderDO> orders) {
        // 主要货物名称
        String mainCargoName = orders.stream().max(Comparator.comparing(OrderDO::getPrice)).map(OrderDO::getErpName).orElse(null);
        cmd.setMainCargoName(mainCargoName);
        // 货物总金额
        int cargoPriceTotal = orders.stream().map(OrderDO::getPriceTotal).map(MoneyUtil::yuanToCent).reduce(Integer::sum).orElse(0);
        cmd.setCargoPriceTotal(cargoPriceTotal);
        // 货物明细
        List<ShopLogisticsCargoCmd> cargos = Lists.newArrayList();
        for (int i = 0; i < orders.size(); i++) {
            OrderDO order = orders.get(i);
            int unitPrice = MoneyUtil.yuanToCent(order.getPrice());
            ShopLogisticsCargoCmd cargo = ShopLogisticsCargoCmd.builder()
                    .name(String.format("[货品%s]%s", i, order.getErpName()))
                    .quantity(order.getNum())
                    .unitPrice(unitPrice)
                    .priceTotal(unitPrice * order.getNum())
                    .build();
            cargos.add(cargo);
        }
        cmd.setCargos(cargos);
    }

    /**
     * 设置收件人密文数据
     *
     * @param platform 平台
     * @param trade    订单
     * @param cmd      电子面单参数
     */
    private void setReceiverCipher(ShopLogisticsOrderCreateCmd cmd, PlatformDO platform, TradeDO trade) {
        // 订单修改过地址则使用主数据
        boolean isUpdateAddress = trade.getIsUpdateAddress() != null && trade.getIsUpdateAddress() == 1;
        if (isUpdateAddress || StringUtils.isBlank(trade.getReceiverOaid())) {
            cmd.setReceiverName(trade.getReceiverName());
            cmd.setReceiverMobile(StringUtils.defaultIfBlank(trade.getReceiverMobile(), trade.getReceiverPhone()));
            cmd.setReceiverAddress(trade.getReceiverAddress());
            return;
        }
        // 未开启电子面单打印控件，解析密文数据
        if (StringUtils.isBlank(platform.getLogisticsPrintClient())) {
            JsonObject receiverOaid = GsonUtil.jsonToObject(trade.getReceiverOaid());
            cmd.setReceiverName(GsonUtil.getAsString(receiverOaid, "receiverName"));
            cmd.setReceiverMobile(GsonUtil.getAsString(receiverOaid, "receiverMobile"));
            cmd.setReceiverAddress(GsonUtil.getAsString(receiverOaid, "receiverAddress"));
            return;
        }
        // 默认传递密文数据
        cmd.setReceiverOaid(trade.getReceiverOaid());
        cmd.setReceiverName(trade.getReceiverName());
        cmd.setReceiverMobile(StringUtils.defaultIfBlank(trade.getReceiverMobile(), trade.getReceiverPhone()));
        cmd.setReceiverAddress(trade.getReceiverAddress());
    }

    /**
     * 获取物流类型
     *
     * @param logisticsCompanyEnum 物流公司枚举
     * @return 物流类型
     */
    private String getLogisticsType(ShopLogisticsCompanyEnum logisticsCompanyEnum) {
        if (logisticsCompanyEnum == ShopLogisticsCompanyEnum.SF) {
            return "2";
        }
        if (logisticsCompanyEnum == ShopLogisticsCompanyEnum.JD) {
            return "1";
        }
        return null;
    }

    /**
     * 获取物流公司枚举
     *
     * @param logisticsCompany 物流公司
     * @return 物流公司枚举
     */
    private ShopLogisticsCompanyEnum getLogisticsCompanyEnum(String logisticsCompany) {
        if (logisticsCompany == null) {
            throw GlobalDefaultException.of("订单未设置物流公司");
        }
        if (logisticsCompany.contains("顺丰")) {
            return ShopLogisticsCompanyEnum.SF;
        }
        if (logisticsCompany.contains("圆通")) {
            return ShopLogisticsCompanyEnum.YTO;
        }
        if (logisticsCompany.contains("EMS")) {
            return ShopLogisticsCompanyEnum.EMS;
        }
        if (logisticsCompany.contains("京东")) {
            return ShopLogisticsCompanyEnum.JD;
        }
        throw GlobalDefaultException.of("订单设置的物流公司不支持发货: %s", logisticsCompany);
    }

    /**
     * 获取寄件人姓名
     *
     * @param platform 平台
     * @return 寄件人姓名
     */
    private String getSenderName(String platform) {
        if (platform == null) {
            return "杜小天";
        }
        if (platform.contains("拼多多")) {
            return "拼小天";
        }
        return "杜小天";
    }

}
