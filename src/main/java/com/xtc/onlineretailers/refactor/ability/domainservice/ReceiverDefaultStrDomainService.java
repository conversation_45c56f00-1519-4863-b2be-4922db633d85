package com.xtc.onlineretailers.refactor.ability.domainservice;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 默认收件人数据领域服务
 */
@RequiredArgsConstructor
@Component
public class ReceiverDefaultStrDomainService {

    /**
     * 默认收件人数据
     */
    private static final String RECEIVER_DEFAULT_STR = "XTC-OMS";
    /**
     * 默认收件人数据的匹配规则：全是符号【*】则设置默认值
     */
    private static final String RECEIVER_DEFAULT_MATCH = "^\\*+$";

    /**
     * 字符串全是符号【*】，则设置默认值
     *
     * @param str 字符串
     * @return 默认值
     */
    public String defaultStringIfStar(String str) {
        if (StringUtils.isBlank(str)) {
            return RECEIVER_DEFAULT_STR;
        }
        if (str.matches(RECEIVER_DEFAULT_MATCH)) {
            return RECEIVER_DEFAULT_STR;
        }
        return str;
    }

}
