package com.xtc.onlineretailers.refactor.ability.extension.tradesync;

import com.alibaba.cola.extension.Extension;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.entity.WarehouseBarcodeDO;
import com.xtc.onlineretailers.refactor.enums.WarehouseBarcodeSyncState;
import com.xtc.onlineretailers.refactor.executor.query.PlatformGetQryExe;
import com.xtc.onlineretailers.refactor.repository.TradeRepository;
import com.xtc.onlineretailers.refactor.util.RedissonUtil;
import com.xtc.onlineretailers.repository.WarehouseBarcodeDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 京东仓库订单逻辑 - 订单同步扩展点
 */
@Slf4j
@RequiredArgsConstructor
@Extension(useCase = TradeSyncExtConstant.USE_CASE, scenario = TradeSyncExtConstant.JD_WAREHOUSE)
public class JdPopWarehouseTradeSyncExt implements TradeSyncExtPt {

    private final PlatformGetQryExe platformGetQryExe;
    private final TradeRepository tradeRepository;
    private final WarehouseBarcodeDao warehouseBarcodeDao;
    private final RedissonUtil redissonUtil;

    /**
     * 已发货待用户确认状态
     */
    public static final String WAIT_GOODS_RECEIVE_CONFIRM_STATE = "WAIT_GOODS_RECEIVE_CONFIRM";

    @Override
    public PlatformDO getPlatform(OrderDTO platformOrder) {
        return platformGetQryExe.getByShopCode(platformOrder.getShopCode());
    }

    @Override
    public void saveData(PlatformDO platform, TradeDO dbTrade, OrderDTO platformOrder) {
        // 不是已发货待用户确认状态不处理
        if (this.notWaitGoodsReceiveConfirm(platformOrder.getOrderState())) {
            return;
        }
        if (dbTrade == null) {
            throw GlobalDefaultException.of(ResponseEnum.ERROR_ORDER_WAIT_DEAL);
        }
        OrderStatus orderStatus = OrderStatus.getEnum(dbTrade.getStatusOrder());
        if (orderStatus == OrderStatus.WAIT_DEAL) {
            throw GlobalDefaultException.of(ResponseEnum.ERROR_ORDER_WAIT_DEAL);
        }
        if (orderStatus != OrderStatus.WAIT_SHIP) {
            return;
        }
        // 更新订单状态为已发货，未过账
        this.updateTrade(dbTrade, platformOrder);
        // 保存发货条码记录
        this.saveBarcode(dbTrade);
    }

    /**
     * 更新订单状态为已发货，未过账
     *
     * @param trade         订单
     * @param platformOrder 平台订单
     */
    private void updateTrade(TradeDO trade, OrderDTO platformOrder) {
        TradeDO updateTrade = new TradeDO();
        updateTrade.setPlatformTradeId(trade.getPlatformTradeId());
        updateTrade.setErpPostStatus(0);
        updateTrade.setStatusOrder(OrderStatus.HAS_SHIP.name());
        updateTrade.setShippingTime(new Date());
        updateTrade.setExpressCompany("京东物流");
        updateTrade.setExpressTrackingNo(platformOrder.getWaybillNo());
        tradeRepository.updateByTradeId(updateTrade);
        log.info("京东仓库订单已发货，更新订单状态为已发货 {}", trade.getPlatformTradeId());
    }

    /**
     * 保存发货条码记录
     *
     * @param dbTrade 订单
     */
    private void saveBarcode(TradeDO dbTrade) {
        redissonUtil.lock(
                "barcode-sync:jdWarehouse",
                dbTrade.getPlatformTradeId(),
                () -> {
                    long count = warehouseBarcodeDao.countByTradeId(dbTrade.getPlatformTradeId());
                    if (count > 0) {
                        log.info("订单 {} 京东仓库条码已存在", dbTrade.getPlatformTradeId());
                        return;
                    }
                    // 保存京东仓库条码记录
                    WarehouseBarcodeDO warehouseBarcodeDO = WarehouseBarcodeDO.builder()
                            .syncState(WarehouseBarcodeSyncState.NOT_SYNC)
                            .platformTradeId(dbTrade.getPlatformTradeId())
                            .retryNum(0)
                            .build();
                    warehouseBarcodeDao.save(warehouseBarcodeDO);
                },
                GlobalDefaultException.of("订单 %s 京东仓条码加锁失败", dbTrade.getPlatformTradeId())
        );
    }

    /**
     * 判断不是已发货待用户确认状态
     *
     * @param state 订单状态
     * @return 执行结果
     */
    private boolean notWaitGoodsReceiveConfirm(String state) {
        return !WAIT_GOODS_RECEIVE_CONFIRM_STATE.equals(state);
    }

}
