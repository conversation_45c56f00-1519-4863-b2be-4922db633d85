package com.xtc.onlineretailers.refactor.ability.checker;

import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.refactor.executor.command.SendWechatWorkNotifyCmdExe;
import com.xtc.onlineretailers.refactor.function.ReleaseStockFunction;
import com.xtc.onlineretailers.refactor.util.RedissonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 库存充足检查
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class StockEnoughChecker {

    private final RedissonUtil redissonUtil;
    private final SendWechatWorkNotifyCmdExe sendWechatWorkNotifyCmdExe;

    /**
     * 库存充足检查，使用 skuId 加锁
     * <pre>
     *     查询临时占用的库存 -> 判断库存充足 -> 增加临时占用的库存
     * </pre>
     *
     * @param bizName    业务名称
     * @param skuId      skuId
     * @param totalStock 总库存
     * @param needStock  需要的库存
     * @param inUseStock 使用中的库存
     * @return 返回回调方法释放临时占用的库存
     */
    public ReleaseStockFunction check(String bizName, String skuId, int totalStock, int needStock, int inUseStock) {
        // 使用 skuId 加锁
        String cacheStockKey = bizName + ":" + skuId;

        redissonUtil.lock(bizName, skuId, () -> {
            // 查询临时占用的库存
            RAtomicLong cacheStock = redissonUtil.getRedissonClient().getAtomicLong(cacheStockKey);
            long tempStock = cacheStock.get();

            // 判断库存充足，可用库存 = 总库存 - 使用中的库存 - 临时占用的库存
            long availableStock = totalStock - inUseStock - tempStock;
            log.info("{} 临时占用的库存: {}, 使用中的库存: {}, 总库存: {}, 可用库存: {}",
                    skuId, tempStock, inUseStock, totalStock, availableStock);
            if (availableStock < needStock) {
                throw new GlobalDefaultException("物料 " + skuId + " 仓库可用库存不足");
            }

            // 增加临时占用的库存
            long incr = cacheStock.addAndGet(needStock);
            log.info("{} 临时占用的库存 增加: {}, 当前: {}", skuId, needStock, incr);

            // 设置超时时间，防止死锁
            cacheStock.expire(Duration.ofMinutes(30));
        }, e -> {
            if (e instanceof IllegalMonitorStateException) {
                // 捕获到 IllegalMonitorStateException 异常，依然会把业务逻辑执行完毕，说明库存已经被占用需要释放
                log.warn("{} 库存充足检查超时，需要检查临时占用的库存数量是否正确", cacheStockKey);
                sendWechatWorkNotifyCmdExe.execute("IT 尽快确认，库存充足检查超时，需要检查临时占用的库存数量是否正确 bizName: %s", bizName);
                return;
            }
            throw new GlobalDefaultException("物料 " + skuId + " 库存充足检查失败，请稍后重试", e);
        });

        // 回调方法，用于释放临时占用的库存
        return this.releaseCacheStockFunction(cacheStockKey, needStock);
    }

    /**
     * 释放临时占用的库存
     *
     * @param key          锁的 key
     * @param releaseStock 释放库存
     */
    private ReleaseStockFunction releaseCacheStockFunction(String key, int releaseStock) {
        return () -> {
            // 释放临时占用的库存，如果释放失败则继续占用库存，等待过期自动释放所有库存
            try {
                RAtomicLong cacheStock = redissonUtil.getRedissonClient().getAtomicLong(key);
                long incr = cacheStock.addAndGet(-releaseStock);
                log.info("{} 临时占用的库存 释放：{}, 当前: {}", key, releaseStock, incr);
            } catch (Exception e) {
                log.warn("{} 释放临时占用的库存失败 {}", key, e.getMessage(), e);
            }
        };
    }

}
