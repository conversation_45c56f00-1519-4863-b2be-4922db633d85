package com.xtc.onlineretailers.refactor.ability.checker;

import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.enums.SFStatus;
import com.xtc.onlineretailers.enums.WarehouseStatus;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import org.springframework.stereotype.Component;

/**
 * 订单发货检查
 */
@Component
public class TradeShippingChecker {

    /**
     * 判断订单未发货
     *
     * @param trade 订单
     * @return 执行结果
     */
    public boolean isNotShipped(TradeDO trade) {
        if (trade.getIsUpdateAddress() != null && trade.getIsUpdateAddress() == 1) {
            return false;
        }
        return OrderStatus.isNoShip(trade.getStatusOrder())
                && WarehouseStatus.isNoShip(trade.getWarehouseStatus())
                && SFStatus.isNoShip(trade.getStatusSf());
    }

}
