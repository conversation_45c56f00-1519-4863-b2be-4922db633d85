package com.xtc.onlineretailers.refactor.controller;

import com.xtc.onlineretailers.refactor.pojo.dto.LogisticsCompanyDTO;
import com.xtc.onlineretailers.refactor.pojo.dto.MenuDTO;
import com.xtc.onlineretailers.refactor.pojo.dto.RoleDTO;
import com.xtc.onlineretailers.refactor.service.DataApiService;
import com.xtc.springboot.annotation.ResponseResult;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 数据模块
 */
@Validated
@RequiredArgsConstructor
@ResponseResult
@RequestMapping("/api")
@RestController
public class DataController {

    private final DataApiService dataApiService;

    /**
     * 获取角色列表
     *
     * @return 角色列表
     */

    @GetMapping("/data/roles")
    public List<RoleDTO> roles() {
        return dataApiService.roles();
    }

    /**
     * 获取菜单列表
     *
     * @param roleCode 角色编码
     * @return 菜单列表
     */
    @GetMapping("/data/menus/{roleCode}")
    public List<MenuDTO> menus(@PathVariable("roleCode") String roleCode) {
        return dataApiService.menus(roleCode);
    }

    /**
     * 获取快递公司
     *
     * @return 快递公司
     */
    @GetMapping("/data/express")
    public List<LogisticsCompanyDTO> listExpressCompany() {
        return dataApiService.listExpressCompany();
    }

}
