package com.xtc.onlineretailers.refactor.controller;

import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.marketing.marketingcomponentdto.dto.Response;
import com.xtc.onlineretailers.refactor.pojo.command.WarehouseWithdrawApplyCmd;
import com.xtc.onlineretailers.refactor.pojo.command.WithdrawStoreCancelCmd;
import com.xtc.onlineretailers.refactor.pojo.command.WithdrawStoreUpdateRemakeCmd;
import com.xtc.onlineretailers.refactor.pojo.dto.WithdrawStoreOrderDTO;
import com.xtc.onlineretailers.refactor.pojo.dto.WithdrawStoreOrderItemDTO;
import com.xtc.onlineretailers.refactor.pojo.query.WithdrawStoreItemPageQry;
import com.xtc.onlineretailers.refactor.pojo.query.WithdrawStorePageQry;
import com.xtc.onlineretailers.refactor.service.WarehouseApiService;
import com.xtc.springboot.annotation.ResponseResult;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 顺丰退库模块
 */
@Validated
@ResponseResult
@RequiredArgsConstructor
@RestController
@RequestMapping("/api")
public class WithdrawStoreController {

    private final WarehouseApiService warehouseApiService;

    /**
     * 申请退库
     *
     * @param cmd 参数
     * @return 处理结果
     */
    @PostMapping("/withdraw-store/apply-withdraw")
    public Response applyWithdraw(@RequestBody WarehouseWithdrawApplyCmd cmd) {
        warehouseApiService.applyWithdraw(cmd);
        return Response.buildSuccess();
    }

    /**
     * 取消退库
     *
     * @param cmd 参数
     * @return 结果
     */
    @PostMapping("/withdraw-store/cancel-withdraw")
    public Response cancelWithdraw(@RequestBody WithdrawStoreCancelCmd cmd) {
        warehouseApiService.cancelWithdraw(cmd.getOrderId());
        return Response.buildSuccess();
    }

    /**
     * 退库单分页列表
     *
     * @param qry 查询
     * @return 退库单分页列表
     */
    @GetMapping("/withdraw-store")
    public PageResponse<WithdrawStoreOrderDTO> pageWithdrawStoreOrder(WithdrawStorePageQry qry) {
        return this.warehouseApiService.pageWithdrawStoreOrder(qry);
    }

    /**
     * 退库明细分页列表
     *
     * @param qry 查询
     * @return 退库明细分页列表
     */
    @GetMapping("/withdraw-store/item")
    public PageResponse<WithdrawStoreOrderItemDTO> pageWithdrawStoreOrderItem(WithdrawStoreItemPageQry qry) {
        return this.warehouseApiService.pageWithdrawStoreOrderItem(qry);
    }

    /**
     * 退库修改备注
     *
     * @param orderId 退库单号
     * @param cmd     参数
     * @return 处理结果
     */
    @PutMapping("/withdraw-store/{orderId}")
    public Response updateWithdrawStoreOrderRemark(@PathVariable("orderId") String orderId,
                                                   @RequestBody WithdrawStoreUpdateRemakeCmd cmd) {
        this.warehouseApiService.updateWithdrawStoreOrderRemark(orderId, cmd);
        return Response.buildSuccess();
    }

}
