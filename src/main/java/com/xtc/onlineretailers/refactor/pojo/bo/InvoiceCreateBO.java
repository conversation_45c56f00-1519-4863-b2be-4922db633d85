package com.xtc.onlineretailers.refactor.pojo.bo;

import com.xtc.marketing.invoiceservice.invoice.dto.InvoiceApplyDTO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceApplySubmitCmd;
import com.xtc.onlineretailers.pojo.entity.InvoiceMainDO;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * 开票参数
 */
@Getter
@Setter
@Builder
public class InvoiceCreateBO {

    /**
     * 发票主体
     */
    private InvoiceMainDO invoiceMain;
    /**
     * 发票服务 - 发票申请提交参数
     */
    private InvoiceApplySubmitCmd invoiceApplySubmitCmd;
    /**
     * 发票服务 - 发票申请结果（数电票）
     */
    private InvoiceApplyDTO invoiceApplyDTO;

}
