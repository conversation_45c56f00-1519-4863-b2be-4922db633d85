package com.xtc.onlineretailers.refactor.pojo.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 发货条码迁移DTO
 */
@Getter
@Setter
@ToString
public class OrderBarcodeMigrationDTO {

    /**
     * 唯一标识
     */
    private Long id;
    /**
     * 订单号
     */
    private String tradeId;
    /**
     * 调单单号
     */
    private String adjTradeId;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
