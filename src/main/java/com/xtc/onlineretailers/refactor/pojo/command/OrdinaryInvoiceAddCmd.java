package com.xtc.onlineretailers.refactor.pojo.command;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 添加普票
 */
@Data
public class OrdinaryInvoiceAddCmd {

    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 电商订单号
     */
    @NotBlank
    private String platformTradeId;

    /**
     * 买家名称
     */
    private String buyerNickname;

    /**
     * 原发票内容
     */
    private String originalContent;

    /**
     * 修改发票内容
     */
    private String modifyContent;

    /**
     * 备注
     */
    private String memo;

}
