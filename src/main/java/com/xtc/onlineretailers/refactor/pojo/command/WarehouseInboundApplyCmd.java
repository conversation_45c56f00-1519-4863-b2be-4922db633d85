package com.xtc.onlineretailers.refactor.pojo.command;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 仓库入库申请单
 */
@Getter
@Setter
@ToString
public class WarehouseInboundApplyCmd {

    /**
     * 仓库编码
     */
    @NotBlank
    @Length(max = 50)
    private String warehouseCode;

    /**
     * 申请时间（格式：2023-01-01 00:00:00）
     */
    @NotNull
    private LocalDateTime applyTime;

    /**
     * 预计收货时间（格式：2023-01-01 00:00:00）
     */
    @NotNull
    private LocalDateTime scheduledReceiptTime;

    /**
     * 备注
     */
    @Length(max = 1000)
    private String memo;

    /**
     * 货物集合，最多 200 个
     */
    @Valid
    @NotEmpty
    @Size(min = 1, max = 200)
    private List<WarehouseItemCmd> items;

}
