package com.xtc.onlineretailers.refactor.pojo.command;

import com.xtc.marketing.adapterservice.bill.enums.BillPlatformEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * 同步账单任务
 */
@Getter
@Setter
@AllArgsConstructor
@Builder
public class PayBillSyncCmd {

    /**
     * 账单平台
     */
    private BillPlatformEnum billPlatform;

    /**
     * 账单日期，格式：yyyy-MM-dd
     */
    private LocalDate billDate;

    /**
     * 业务代码（基础服务数据）
     */
    private String bizCode;

    /**
     * 商户id
     */
    private String mchId;

}
