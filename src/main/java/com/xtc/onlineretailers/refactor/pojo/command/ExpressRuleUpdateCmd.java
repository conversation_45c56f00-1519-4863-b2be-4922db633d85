package com.xtc.onlineretailers.refactor.pojo.command;

import com.xtc.onlineretailers.refactor.enums.LogisticsCompany;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 修改快递规则
 */
@Getter
@Setter
@ToString
public class ExpressRuleUpdateCmd {

    /**
     * 唯一标识
     */
    @NotBlank
    @Length(max = 50)
    private String id;
    /**
     * 标题
     */
    @NotBlank
    @Length(max = 50)
    private String title;
    /**
     * 开始时间
     */
    @NotNull
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @NotNull
    private LocalDateTime endTime;
    /**
     * 平台id集合
     */
    @NotNull
    @Size(min = 1, max = 20)
    private List<String> platformIds;
    /**
     * 物料类型：机器、配件
     */
    @NotNull
    private Boolean isProduct;
    /**
     * 快递公司
     */
    @NotNull
    private LogisticsCompany expressCompany;
    /**
     * 快递时效类型
     */
    @Length(max = 50)
    private String expressType;
    /**
     * 快递公司
     */
    @NotNull
    private LogisticsCompany defaultExpressCompany;
    /**
     * 快递时效
     */
    @Length(max = 50)
    private String defaultExpressType;
    /**
     * 规则
     */
    @Valid
    @Size(max = 20)
    private List<Rule> rules;

    /**
     * 规则
     */
    @Getter
    @Setter
    @ToString
    public static class Rule {

        /**
         * 物料代码
         */
        @Size(max = 20)
        private List<String> erpCodes;
        /**
         * 省份
         */
        @Length(max = 20)
        private String province;
        /**
         * 市
         */
        @Size(max = 20)
        private List<String> city;
        /**
         * 机型分类：IOT 电话手表 扫描笔 家教机 电话手表
         */
        @Size(max = 20)
        private List<String> models;

    }

}
