package com.xtc.onlineretailers.refactor.pojo.query;

import com.xtc.onlineretailers.refactor.enums.LogisticsCompany;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 快递类型查询
 */
@Getter
@Setter
@ToString
public class ExpressRuleQry {

    /**
     * 平台id
     */
    @Length(max = 50)
    private String platformIds;
    /**
     * 物料类型：机器、配件
     */
    private Boolean isProduct;
    /**
     * 物流公司
     */
    private LogisticsCompany expressCompany;

}
