package com.xtc.onlineretailers.refactor.pojo.command;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 天猫订单修改地址
 */
@Getter
@Setter
@ToString
public class ModifyAddressNotifyCmd {

    /**
     * 订单号
     */
    @NotBlank
    @Length(max = 50)
    private String tradeId;
    /**
     * 收件人密文数据
     */
    private String receiverOaid;
    /**
     * 收件人姓名
     */
    private String receiverName;
    /**
     * 收件人手机号
     */
    private String receiverMobile;
    /**
     * 收件人省份
     */
    private String receiverProvince;
    /**
     * 收件人城市
     */
    private String receiverCity;
    /**
     * 收件人区县
     */
    private String receiverDistrict;
    /**
     * 收件人乡镇
     */
    private String receiverTown;
    /**
     * 收件人详细地址
     */
    private String receiverAddress;

}
