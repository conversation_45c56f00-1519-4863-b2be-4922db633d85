package com.xtc.onlineretailers.refactor.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 订单发货条码迁移
 */
@Getter
@Setter
@ToString
@TableName("t_order_barcode_migration")
public class OrderBarcodeMigrationDO {

    /**
     * 唯一标识
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 订单号
     */
    private String tradeId;
    /**
     * 调单单号
     */
    private String adjTradeId;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
