package com.xtc.onlineretailers.refactor.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.mapper.master.InvoiceMainMapper;
import com.xtc.onlineretailers.pojo.entity.InvoiceMainDO;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Repository
public class InvoiceMainRepository extends ServiceImpl<InvoiceMainMapper, InvoiceMainDO> {

    /**
     * 查询发票
     *
     * @param serialNo 流水号
     * @return 发票
     */
    public Optional<InvoiceMainDO> getBySerialNo(String serialNo) {
        if (serialNo == null) {
            return Optional.empty();
        }
        Wrapper<InvoiceMainDO> wrapper = Wrappers.<InvoiceMainDO>lambdaQuery()
                .eq(InvoiceMainDO::getSerialNo, serialNo)
                .orderByDesc(InvoiceMainDO::getId)
                .last(BaseRepository.LIMIT_ONE);
        InvoiceMainDO one = this.getOne(wrapper);
        return Optional.ofNullable(one);
    }

    /**
     * 查询发票列表
     *
     * @param tradeId 订单号
     * @return 发票列表
     */
    public List<InvoiceMainDO> listByTradeId(String tradeId) {
        if (tradeId == null) {
            return Collections.emptyList();
        }
        Wrapper<InvoiceMainDO> wrapper = Wrappers.<InvoiceMainDO>lambdaQuery()
                .eq(InvoiceMainDO::getPlatformTid, tradeId)
                .orderByAsc(InvoiceMainDO::getId)
                .last(BaseRepository.DEFAULT_LIST_SIZE_LIMIT);
        return this.list(wrapper);
    }

}
