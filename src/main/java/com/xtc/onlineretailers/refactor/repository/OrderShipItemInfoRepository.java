package com.xtc.onlineretailers.refactor.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.mapper.master.OrderShipItemInfoMapper;
import com.xtc.onlineretailers.pojo.entity.OrderShipItemInfoDO;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class OrderShipItemInfoRepository extends ServiceImpl<OrderShipItemInfoMapper, OrderShipItemInfoDO> {

    /**
     * 保存或者更新出库明细，根据订单号、物料 69 码、条码匹配
     *
     * @param item 出库明细
     */
    public void saveOrUpdateByTradeIdAndBarcodeAndProductSn(OrderShipItemInfoDO item) {
        if (item == null || item.getTradeId() == null || item.getBarcode() == null) {
            return;
        }
        Wrapper<OrderShipItemInfoDO> wrapper = Wrappers.<OrderShipItemInfoDO>lambdaUpdate()
                .eq(OrderShipItemInfoDO::getTradeId, item.getTradeId())
                .eq(OrderShipItemInfoDO::getBarcode, item.getBarcode())
                .eq(item.getProductSn() != null, OrderShipItemInfoDO::getProductSn, item.getProductSn())
                .orderByDesc(OrderShipItemInfoDO::getItemId)
                .last(BaseRepository.LIMIT_ONE);
        this.saveOrUpdate(item, wrapper);
    }

    /**
     * 根据订单号获取顺丰出库明细列表
     *
     * @param platformTradeId 订单号
     * @return 出库明细列表
     */
    public List<OrderShipItemInfoDO> listByTradeId(String platformTradeId) {
        Wrapper<OrderShipItemInfoDO> wrapper = Wrappers.<OrderShipItemInfoDO>lambdaUpdate()
                .eq(OrderShipItemInfoDO::getTradeId, platformTradeId)
                .orderByDesc(OrderShipItemInfoDO::getShipTime)
                .last(BaseRepository.DEFAULT_LIST_SIZE_LIMIT);
        return this.list(wrapper);
    }

}
