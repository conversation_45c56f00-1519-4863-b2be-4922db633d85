package com.xtc.onlineretailers.refactor.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.mapper.master.WithdrawStoreOrderMapper;
import com.xtc.onlineretailers.pojo.entity.WithdrawStoreOrderDO;
import com.xtc.onlineretailers.refactor.pojo.query.WithdrawStorePageQry;
import com.xtc.onlineretailers.util.DateUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Optional;

@Component
public class WithdrawStoreOrderRepository extends ServiceImpl<WithdrawStoreOrderMapper, WithdrawStoreOrderDO> {

    /**
     * 查询退库单
     *
     * @param orderId 退库单号
     * @return 退库单
     */
    public Optional<WithdrawStoreOrderDO> getByOrderId(String orderId) {
        if (orderId == null) {
            return Optional.empty();
        }
        Wrapper<WithdrawStoreOrderDO> wrapper = Wrappers.<WithdrawStoreOrderDO>lambdaQuery()
                .eq(WithdrawStoreOrderDO::getOrderId, orderId)
                .orderByDesc(WithdrawStoreOrderDO::getApplyTime)
                .last(BaseRepository.LIMIT_ONE);
        WithdrawStoreOrderDO one = this.getOne(wrapper);
        return Optional.ofNullable(one);
    }

    /**
     * 更新退库单，根据退库单号匹配
     *
     * @param withdrawStoreOrder 退库单
     */
    public void updateByOrderId(WithdrawStoreOrderDO withdrawStoreOrder) {
        if (withdrawStoreOrder == null || withdrawStoreOrder.getOrderId() == null) {
            return;
        }
        Wrapper<WithdrawStoreOrderDO> wrapper = Wrappers.<WithdrawStoreOrderDO>lambdaUpdate()
                .eq(WithdrawStoreOrderDO::getOrderId, withdrawStoreOrder.getOrderId())
                .orderByDesc(WithdrawStoreOrderDO::getApplyTime)
                .last(BaseRepository.LIMIT_ONE);
        this.update(withdrawStoreOrder, wrapper);
    }

    /**
     * 查询退库单列表
     *
     * @param qry 查询
     * @return 退库单列表
     */
    public IPage<WithdrawStoreOrderDO> pageBy(WithdrawStorePageQry qry) {
        LocalDateTime startTime = DateUtil.minTimeOfLocalDate(qry.getCreateTimeStart());
        LocalDateTime endTime = DateUtil.minTimeOfLocalDate(qry.getCreateTimeEnd());
        Wrapper<WithdrawStoreOrderDO> wrapper = Wrappers.<WithdrawStoreOrderDO>lambdaQuery()
                .eq(qry.getOrderId() != null, WithdrawStoreOrderDO::getOrderId, qry.getOrderId())
                .eq(qry.getStoreCode() != null, WithdrawStoreOrderDO::getStoreCode, qry.getStoreCode())
                .between(ObjectUtils.allNotNull(startTime, endTime), WithdrawStoreOrderDO::getApplyTime, startTime, endTime)
                .orderByDesc(WithdrawStoreOrderDO::getApplyTime);
        return this.page(qry.createPage(), wrapper);
    }

}
