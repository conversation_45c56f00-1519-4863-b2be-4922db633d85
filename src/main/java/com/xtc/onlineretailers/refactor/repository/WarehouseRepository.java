package com.xtc.onlineretailers.refactor.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.mapper.master.WarehouseMapper;
import com.xtc.onlineretailers.pojo.entity.WarehouseDO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
public class WarehouseRepository extends ServiceImpl<WarehouseMapper, WarehouseDO> {

    /**
     * 查询仓库
     *
     * @param warehouseStorage 仓库储位
     * @return 仓库
     */
    public Optional<WarehouseDO> getByWarehouseStorage(String warehouseStorage) {
        if (warehouseStorage == null) {
            return Optional.empty();
        }
        Wrapper<WarehouseDO> wrapper = Wrappers.<WarehouseDO>lambdaQuery()
                .eq(WarehouseDO::getWarehouseStorage, warehouseStorage)
                .orderByDesc(WarehouseDO::getId)
                .last(BaseRepository.LIMIT_ONE);
        WarehouseDO one = this.getOne(wrapper);
        return Optional.ofNullable(one);
    }

    /**
     * 查询非本地仓列表
     *
     * @return 仓库列表
     */
    public List<WarehouseDO> listNotLocalWarehouse() {
        Wrapper<WarehouseDO> wrapper = Wrappers.<WarehouseDO>lambdaQuery()
                .eq(WarehouseDO::getIsUse, "1")
                .ne(WarehouseDO::getWarehouseStorage, GlobalConstant.WAREHOUSE_LOCAL)
                .orderByDesc(WarehouseDO::getId);
        return this.list(wrapper);
    }

}
