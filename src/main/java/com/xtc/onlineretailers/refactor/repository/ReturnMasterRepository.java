package com.xtc.onlineretailers.refactor.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.enums.AutoRefundStatusEnum;
import com.xtc.onlineretailers.mapper.master.ReturnMasterMapper;
import com.xtc.onlineretailers.pojo.entity.ReturnMasterDO;
import com.xtc.onlineretailers.refactor.enums.ReturnMasterType;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Component
public class ReturnMasterRepository extends ServiceImpl<ReturnMasterMapper, ReturnMasterDO> {

    /**
     * 查询退货单
     *
     * @param tradeId 订单号
     * @return 退货单
     */
    public Optional<ReturnMasterDO> getByTradeId(String tradeId) {
        if (tradeId == null) {
            return Optional.empty();
        }
        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .eq(ReturnMasterDO::getPlatformTradeId, tradeId)
                .orderByDesc(ReturnMasterDO::getId)
                .last(BaseRepository.LIMIT_ONE);
        ReturnMasterDO one = this.getOne(wrapper);
        return Optional.ofNullable(one);
    }

    /**
     * 查询需要处理碎屏宝的退货分页列表
     * <p>筛选出符合条件的订单：已收货、已过账、退货类型</p>
     *
     * @param current             分页大小
     * @param pageSize            分页数量
     * @param erpPostStartTime    过账开始时间
     * @param erpPostStartEndTime 过账结束时间
     * @return 退货分页列表
     */
    public IPage<ReturnMasterDO> refundScreenInsuranceHandle(int current, int pageSize,
                                                             LocalDateTime erpPostStartTime, LocalDateTime erpPostStartEndTime) {
        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .between(ReturnMasterDO::getErpPostTime, erpPostStartTime, erpPostStartEndTime)
                .eq(ReturnMasterDO::getIsEndProductStore, 1)
                .eq(ReturnMasterDO::getErpPostStatus, 1)
                .eq(ReturnMasterDO::getType, ReturnMasterType.RETURN)
                .orderByDesc(ReturnMasterDO::getErpPostTime);
        Page<ReturnMasterDO> page = new Page<>(current, pageSize, false);
        return this.page(page, wrapper);
    }

    /**
     * 查询需要处理碎屏宝的退货分页列表
     * <p>筛选出符合条件的订单：已收货、已过账、换货类型</p>
     *
     * @param current             分页大小
     * @param pageSize            分页数量
     * @param erpPostStartTime    过账开始时间
     * @param erpPostStartEndTime 过账结束时间
     * @return 退货分页列表
     */
    public IPage<ReturnMasterDO> exchangeScreenInsuranceHandleJob(int current, int pageSize,
                                                                  LocalDateTime erpPostStartTime, LocalDateTime erpPostStartEndTime) {
        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .between(ReturnMasterDO::getErpPostTime, erpPostStartTime, erpPostStartEndTime)
                .eq(ReturnMasterDO::getIsEndProductStore, 1)
                .eq(ReturnMasterDO::getErpPostStatus, 1)
                .eq(ReturnMasterDO::getType, ReturnMasterType.EXCHANGE)
                .orderByDesc(ReturnMasterDO::getErpPostTime);
        Page<ReturnMasterDO> page = new Page<>(current, pageSize, false);
        return this.page(page, wrapper);
    }

    /**
     * 获取需要退款的订单
     *
     * @param createTimeDuration 创建时间范围
     * @param limitSql           限制数量 sql
     * @return 需要退款的订单
     */
    public List<ReturnMasterDO> listNotAutoRefundStatusByCreateTime(Duration createTimeDuration, String limitSql) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minus(createTimeDuration);

        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .eq(ReturnMasterDO::getAutoRefundStatus, AutoRefundStatusEnum.NOT)
                .between(ReturnMasterDO::getCreateTime, startTime, endTime)
                .orderByDesc(ReturnMasterDO::getCreateTime)
                .last(limitSql);
        return this.list(wrapper);
    }

}
