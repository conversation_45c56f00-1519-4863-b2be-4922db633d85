package com.xtc.onlineretailers.refactor.job;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xtc.onlineretailers.pojo.entity.SmsDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.entity.WarehouseDO;
import com.xtc.onlineretailers.refactor.executor.command.LogisticsInterceptCmdExe;
import com.xtc.onlineretailers.refactor.executor.command.SendWechatWorkNotifyCmdExe;
import com.xtc.onlineretailers.refactor.executor.query.TradeGetQryExe;
import com.xtc.onlineretailers.refactor.executor.query.WarehouseGetQryExe;
import com.xtc.onlineretailers.refactor.repository.SmsRepository;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * 物流拦截任务
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class LogisticsInterceptJob {

    private final SmsRepository smsRepository;
    private final TradeGetQryExe tradeGetQryExe;
    private final WarehouseGetQryExe warehouseGetQryExe;
    private final LogisticsInterceptCmdExe logisticsInterceptCmdExe;
    private final SendWechatWorkNotifyCmdExe sendWechatWorkNotifyCmdExe;

    /**
     * 创建时间范围（结束时间为当前时间）
     */
    private static final Duration CREATE_TIME_DURATION = Duration.ofDays(30);
    /**
     * 任务处理数量
     */
    private static final int LIMIT_SIZE = 1000;

    /**
     * 物流拦截任务
     *
     * @param param 订单号集合（英文逗号分割），集合索引大于分片数量的订单将不会被处理
     */
    @XxlJob("logisticsInterceptJob")
    public ReturnT<String> logisticsInterceptJob(String param) throws Exception {
        return BaseTask.run(param, () -> {
            // 筛选出需要执行拦截的拦截记录列表
            List<SmsDO> listLogisticsIntercept = this.listLogisticsIntercept(param);

            // 初始化推送失败的订单集合，最多保存 5 个订单
            Map<String, String> failedMap = Maps.newHashMapWithExpectedSize(5);
            listLogisticsIntercept.forEach(intercept -> {
                try {
                    TradeDO trade = tradeGetQryExe.execute(intercept.getPlatformTradeId());
                    WarehouseDO warehouse = warehouseGetQryExe.execute(trade.getWarehouseStorage());
                    String interceptResponse = logisticsInterceptCmdExe.execute(trade, warehouse);
                    this.updateInterceptSuccess(intercept.getPlatformTradeId(), interceptResponse);
                } catch (Exception e) {
                    String msg = e.getMessage();
                    log.error("物流拦截失败 {}: {}", intercept.getPlatformTradeId(), msg, e);
                    this.putFailedMessage(failedMap, intercept.getPlatformTradeId(), msg);
                }
            });

            // 发送企业微信通知
            if (!failedMap.isEmpty()) {
                String msg = Joiner.on("\n").withKeyValueSeparator(": ").join(failedMap);
                sendWechatWorkNotifyCmdExe.execute("物流拦截失败（最多显示 5 个订单，具体情况需要二次确认）\n%s", msg);
            }
        });
    }

    /**
     * 筛选出需要执行拦截的拦截记录列表
     *
     * @param tradeIds 订单号集合（英文逗号分割）
     * @return 待发货订单
     */
    private List<SmsDO> listLogisticsIntercept(String tradeIds) {
        List<SmsDO> listLogisticsIntercept;
        if (StringUtils.isNotBlank(tradeIds)) {
            // 分割订单号集合，根据分片索引获取订单号，集合索引大于分片数量的订单将不会被处理
            int index = BaseTask.getShardingIndex();
            String tradeId = Splitter.on(",").omitEmptyStrings().trimResults().splitToList(tradeIds).get(index);
            SmsDO intercept = new SmsDO();
            intercept.setPlatformTradeId(tradeId);
            listLogisticsIntercept = Lists.newArrayListWithCapacity(5);
            listLogisticsIntercept.add(intercept);
        } else {
            // 筛选出需要执行拦截的拦截记录列表
            listLogisticsIntercept = smsRepository.listLogisticsIntercept(CREATE_TIME_DURATION, BaseTask.getShardingLimitSql(LIMIT_SIZE));
        }
        log.info("筛选出需要执行拦截的拦截记录列表，数量：{}", listLogisticsIntercept.size());
        return listLogisticsIntercept;
    }

    /**
     * 更新拦截成功状态
     *
     * @param tradeId           订单号
     * @param interceptResponse 拦截响应
     */
    private void updateInterceptSuccess(String tradeId, String interceptResponse) {
        SmsDO updateIntercept = new SmsDO();
        updateIntercept.setPlatformTradeId(tradeId);
        updateIntercept.setResponseMessage(interceptResponse);
        updateIntercept.setIsIntercept(1);
        updateIntercept.setTradeStatus(1);
        smsRepository.updateByTradeId(updateIntercept);
    }

    /**
     * 记录失败的订单
     *
     * @param failedMap 失败的订单集合
     * @param tradeId   订单号
     * @param failedMsg 失败信息
     */
    private void putFailedMessage(Map<String, String> failedMap, String tradeId, String failedMsg) {
        if (failedMsg.contains("不支持的物流公司")) {
            return;
        }
        if (failedMsg.contains("运单清单信息缺失")) {
            return;
        }
        if (failedMap == null) {
            failedMap = Maps.newHashMapWithExpectedSize(5);
        }
        if (failedMap.size() >= 5) {
            return;
        }
        failedMap.put(tradeId, StringUtils.substring(failedMsg, 0, 200));
    }

}
