package com.xtc.onlineretailers.refactor.job;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xtc.marketing.adapterservice.shop.AdapterShopFeignClient;
import com.xtc.marketing.adapterservice.shop.dto.InvoiceApplyDTO;
import com.xtc.marketing.adapterservice.shop.dto.query.InvoiceApplyGetQry;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.executor.command.InvoiceApplySyncCmdExe;
import com.xtc.onlineretailers.refactor.executor.command.SendWechatWorkNotifyCmdExe;
import com.xtc.onlineretailers.refactor.executor.query.PlatformGetQryExe;
import com.xtc.onlineretailers.refactor.repository.TradeRepository;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 拉取京东发票
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class PullJdInvoiceApplyJob {

    private final TradeRepository tradeRepository;
    private final InvoiceApplySyncCmdExe invoiceApplySyncCmdExe;
    private final SendWechatWorkNotifyCmdExe sendWechatWorkNotifyCmdExe;
    private final PlatformGetQryExe platformGetQryExe;
    private final AdapterShopFeignClient adapterShopFeignClient;

    /**
     * 任务偏移量，单位：天
     */
    private static final long OFFSET_DAYS = 7;
    /**
     * 任务处理数量
     */
    private static final int JOB_LIMIT_SIZE = 1500;
    /**
     * 京东平台id
     */
    private static final List<Integer> JD_PLATFORM_IDS = Lists.newArrayList(2062, 2063, 2151);

    /**
     * 拉取京东发票任务
     */
    @XxlJob("pullJdInvoiceApplyJob")
    public ReturnT<String> pullJdInvoiceApplyJob(String param) {
        return BaseTask.run(() -> {
            // 初始化处理失败的订单集合，最多保存 5 个订单
            Map<String, String> failedMap = Maps.newHashMapWithExpectedSize(5);
            // 筛选京东平台订单：订单类型为已发货，订单状态为正常，按 shipping_time 倒序排列，且 shipping_time 在指定时间范围内
            List<TradeDO> list = this.listPlatformInvoiceApply();
            list.forEach(tradeDO -> {
                try {
                    PlatformDO platform = platformGetQryExe.getByPlatformId(tradeDO.getPlatformId());
                    InvoiceApplyGetQry qry = InvoiceApplyGetQry.builder()
                            .orderNo(tradeDO.getPlatformTradeId())
                            .shopCode(platform.getShopCode())
                            .build();
                    SingleResponse<InvoiceApplyDTO> shopInvoiceApply = adapterShopFeignClient.getInvoiceApply(qry);
                    if (shopInvoiceApply == null || shopInvoiceApply.getData() == null) {
                        return;
                    }
                    invoiceApplySyncCmdExe.execute(platform, shopInvoiceApply.getData());
                } catch (Exception e) {
                    log.warn("拉取京东发票失败 {} {}", tradeDO.getPlatformTradeId(), e.getMessage(), e);
                    this.putFailedMessage(failedMap, tradeDO.getPlatformTradeId(), e.getMessage());
                }
            });
            // 发送企业微信消息
            if (!failedMap.isEmpty()) {
                String msg = Joiner.on("\n").withKeyValueSeparator(": ").join(failedMap);
                sendWechatWorkNotifyCmdExe.execute("拉取京东发票申请失败（最多显示 5 个订单，具体情况需要二次确认）\n%s", msg);
            }
        });
    }

    /**
     * 筛选京东平台订单：订单类型为已发货，订单状态为正常，按 shipping_time 倒序排列，且 shipping_time 在指定时间范围内
     *
     * @return 订单列表
     */
    private List<TradeDO> listPlatformInvoiceApply() {
        String limitSize = BaseTask.getShardingLimitSql(JOB_LIMIT_SIZE);
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(OFFSET_DAYS);
        return tradeRepository.listPlatformInvoiceApply(JD_PLATFORM_IDS, startTime, endTime, limitSize);
    }

    /**
     * 记录异常信息
     *
     * @param failedMap 失败的订单集合
     * @param tradeId   订单号
     * @param failedMsg 失败信息
     */
    private void putFailedMessage(Map<String, String> failedMap, String tradeId, String failedMsg) {
        if (failedMap == null) {
            failedMap = Maps.newHashMapWithExpectedSize(5);
        }
        if (failedMap.size() >= 5) {
            return;
        }
        if (failedMsg == null) {
            failedMsg = "未知错误，需要IT确认";
        }
        if (failedMsg.contains("无需重复申请")) {
            return;
        }
        failedMap.put(tradeId, StringUtils.substring(failedMsg, 0, 200));
    }

}
