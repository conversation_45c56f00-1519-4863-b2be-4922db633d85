package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xtc.onlineretailers.pojo.entity.PresentDO;
import com.xtc.onlineretailers.pojo.query.PresentQuery;
import com.xtc.onlineretailers.pojo.query.PresentSaveQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

public interface PresentService extends IService<PresentDO> {

    /**
     * 分页获取赠品列表
     *
     * @param query 查询条件
     * @return 赠品活动列表
     */
    PaginationVO<PresentDO> getPresentPage(PresentQuery query);

    /**
     * 新增赠品信息
     *
     * @param query 参数
     * @return 赠品信息
     */
    PresentDO addPresent(PresentSaveQuery query);

    /**
     * 编辑赠品信息
     *
     * @param id    赠品活动id
     * @param query 参数
     * @return 赠品信息
     */
    PresentDO updatePresent(long id, PresentSaveQuery query);

    /**
     * 获取符合条件的赠品列表
     * 1：符合当前平台
     * 2：符合活动指定的物料或者买家关键字活动（不限物料）
     * 3：支付时间在活动期间内或者活动不限时间
     *
     * @param platformId         平台id
     * @param productId          物料代码
     * @param paymentTime        付款时间
     * @param paymentTimeAdvance 预售付款时间
     * @return 赠品列表
     */
    List<PresentDO> getPresentList(int platformId, String productId, Date paymentTime, Date paymentTimeAdvance);

    /**
     * 赠品信息导出
     * @param query 查询条件
     * @param response 响应对象
     */
    void export(PresentQuery query, HttpServletResponse response);

    /**
     * 赠品订单导入
     *
     * @param excel 文件
     * @return 结果
     */
    String importOrders(MultipartFile excel);


    /**
     * 赠品订单导入
     *
     * @param platformId 平台id
     * @param productId 物料id
     * @param authorId 达人id
     * @return 结果
     */
    List<PresentDO> listPresentDOByPlatformIdAndProductIdAndAuthorId(Integer platformId,String productId,String authorId);
}
