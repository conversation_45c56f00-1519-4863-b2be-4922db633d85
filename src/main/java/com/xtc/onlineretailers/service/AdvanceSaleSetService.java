package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xtc.onlineretailers.pojo.entity.AdvanceSaleSetDO;
import com.xtc.onlineretailers.pojo.query.AdvanceSaleSetAddQuery;
import com.xtc.onlineretailers.pojo.query.AdvanceSaleSetQuery;
import com.xtc.onlineretailers.pojo.query.AdvanceSaleSetUpdateQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;

import javax.servlet.http.HttpServletResponse;

public interface AdvanceSaleSetService extends IService<AdvanceSaleSetDO> {

    /**
     * @description: 获取预售活动信息列表
     * @param: AdvanceSaleSetQuery
     * @return: PaginationVO<AdvanceSaleSetDO>
     * @throws:
     */
    PaginationVO<AdvanceSaleSetDO> getAdvanceSaleSetList(AdvanceSaleSetQuery query);

    /**
     * @description: 编辑预售活动信息
     * @param: AdvanceSaleSetUpdateQuery
     * @return: AdvanceSaleSetDO
     * @throws:
     */
    AdvanceSaleSetDO updateAdvanceSaleSet(AdvanceSaleSetUpdateQuery query);

    /**
     * @description: 新增预售活动信息
     * @param: AdvanceSaleSetAddQuery
     * @return: AdvanceSaleSetDO
     * @throws:
     */
    AdvanceSaleSetDO addAdvanceSaleSet(AdvanceSaleSetAddQuery query);

    /**
     * @description: 导出预售活动信息列表
     * @param: AdvanceSaleSetQuery
     * @return: 文件
     * @throws:
     */
    void exportAdvanceSaleSet(AdvanceSaleSetQuery query, HttpServletResponse response);

}
