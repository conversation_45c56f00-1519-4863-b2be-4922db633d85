package com.xtc.onlineretailers.service;

import com.xtc.onlineretailers.pojo.entity.LogisticsInterceptDO;
import com.xtc.onlineretailers.pojo.query.LogisticsInterceptPageQry;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;

public interface LogisticsInterceptService {

    /**
     * 分页查询物流拦截列表
     *
     * @param qry 查询
     * @return 物流拦截列表
     */
    PaginationVO<LogisticsInterceptDO> pageLogisticsIntercept(LogisticsInterceptPageQry qry);

    /**
     * 新建物流拦截
     *
     * @param tradeId 参数
     */
    void addLogisticsIntercept(String tradeId);

}
