package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xtc.onlineretailers.pojo.entity.AgentInfoDO;
import com.xtc.onlineretailers.pojo.query.AgentInfoAddOrUpdateQuery;
import com.xtc.onlineretailers.pojo.query.AgentInfoQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface AgentInfoService extends IService<AgentInfoDO> {

    /**
     * @description: 获取代理信息列表
     * @param: AgentInfoQuery
     * @return: PaginationVO<AgentInfoDO>
     * @throws:
     */
    PaginationVO<AgentInfoDO> getAgentInfoList(AgentInfoQuery query);

    /**
     * @description: 编辑代理信息
     * @param: AgentInfoAddOrUpdateQuery
     * @return: AgentInfoDO
     * @throws:
     */
    AgentInfoDO updateAgentInfo(AgentInfoAddOrUpdateQuery query);

    /**
     * @description: 新增代理信息
     * @param: AgentInfoAddOrUpdateQuery
     * @return: AgentInfoDO
     * @throws:
     */
    AgentInfoDO addAgentInfo(AgentInfoAddOrUpdateQuery query);

    /**
     * @description: 导出代理信息列表
     * @param: AgentInfoQuery
     * @return:
     * @throws:
     */
    void exportAgentInfo(AgentInfoQuery query, HttpServletResponse response);

    /**
     * 根据品牌获取代理信息
     *
     * @param brand 品牌
     * @return
     */
    List<AgentInfoDO> getAgentInfoByBrand(String brand);

}
