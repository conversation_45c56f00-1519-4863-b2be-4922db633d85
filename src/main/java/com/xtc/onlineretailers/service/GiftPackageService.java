package com.xtc.onlineretailers.service;

import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.onlineretailers.pojo.command.GiftPackageAddCmd;
import com.xtc.onlineretailers.pojo.command.GiftPackageUpdateCmd;
import com.xtc.onlineretailers.pojo.entity.GiftPackageDO;
import com.xtc.onlineretailers.pojo.entity.GiftPackageDetailDO;
import com.xtc.onlineretailers.pojo.query.GiftPackagePageQry;

import java.util.List;

public interface GiftPackageService {

    /**
     * 分页礼包列表
     *
     * @param qry 查询
     * @return 礼包列表
     */
    PageResponse<GiftPackageDO> pageGiftPackage(GiftPackagePageQry qry);

    /**
     * 新增礼包
     *
     * @param cmd 参数
     */
    void addGiftPackage(GiftPackageAddCmd cmd);

    /**
     * 修改礼包
     *
     * @param cmd 参数
     */
    void updateGiftPackage(GiftPackageUpdateCmd cmd);

    /**
     * 删除礼包
     *
     * @param id 礼包id
     */
    void deleteGiftPackage(Long id);

    /**
     * 查询礼包明细列表
     *
     * @param giftPackageId 礼包id
     * @return 礼包明细列表
     */
    List<GiftPackageDetailDO> listByGiftPackageId(Long giftPackageId);

}
