package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xtc.onlineretailers.pojo.entity.OrderDO;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.ProductInfoDO;
import com.xtc.onlineretailers.pojo.query.MaterialInfoQuery;
import com.xtc.onlineretailers.pojo.query.ProductInfoAddQuery;
import com.xtc.onlineretailers.pojo.query.ProductInfoQuery;
import com.xtc.onlineretailers.pojo.query.ProductInfoUpdateQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface ProductInfoService extends IService<ProductInfoDO> {

    /**
     * @description: 获取物料信息列表
     * @param: ProductInfoQuery
     * @return: PaginationVO<ProductInfoDO>
     * @throws:
     */
    PaginationVO<ProductInfoDO> getProductInfoList(ProductInfoQuery query);

    /**
     * @description: 编辑物料信息
     * @param: ProductInfoUpdateQuery
     * @return: ProductInfoDO
     * @throws:
     */
    ProductInfoDO updateProductInfo(ProductInfoUpdateQuery query);

    /**
     * @description: 新增物料信息
     * @param: ProductInfoAddQuery
     * @return: ProductInfoDO
     * @throws:
     */
    ProductInfoDO addProductInfo(ProductInfoAddQuery query);

    /**
     * @param newErpCode 物料码
     * @return 产品信息
     * @description: 根据物料码获取产品信息
     */
    ProductInfoDO getProductInfoByErpCode(String newErpCode);

    /**
     * @description: 导出物料信息
     * @param: ProductInfoQuery ，HttpServletResponse
     * @return: excel
     * @throws:
     */
    void exportProductInfo(ProductInfoQuery query, HttpServletResponse response);

    /**
     * @description: 获取物料信息列表
     * @param: ProductInfoQuery
     * @return: List<ProductInfoDO>
     * @throws:
     */
    List<ProductInfoDO> getAllProductInfoList(ProductInfoQuery query);

    /**
     * 获取物料信息集合
     *
     * @param query
     * @return
     */
    IPage<ProductInfoDO> getProductInfo(MaterialInfoQuery query);

    /**
     * 根据物料代码获取物料信息
     *
     * @param productId 物料代码
     * @return
     */
    ProductInfoDO getProductInfo(String productId);

    /**
     * 获取物料集合
     *
     * @param erpCodes
     * @return
     */
    List<ProductInfoDO> getByErpCodes(List<String> erpCodes);

    /**
     * 根据物料代码和品牌名称获取物料
     *
     * @param productId
     * @param platformType
     * @return
     */
    ProductInfoDO getProductByErpCodeAndType(String productId, String platformType);

    /**
     * 获取物料信息
     *
     * @param platform 平台信息
     * @param erpCode  物料代码
     * @return 物料信息
     */
    ProductInfoDO getProductInfo(PlatformDO platform, String erpCode);

    /**
     * 同步物料重量、体积
     */
    void syncErpVolumeWeightJob();

    /**
     * 查询物料分类列表
     *
     * @return 物料分类
     */
    List<String> getProductCategoryList();

    List<Map<String, Object>> getProductMachineTypeList();

    /**
     * 取出最大金额的商品信息
     *
     * @param orderDOS 订单明细
     * @return 商品信息
     */
    ProductInfoDO getMaxProductInfoDO(List<OrderDO> orderDOS);

    /**
     * 获取手动抛单并且是可用的物料代码
     *
     * @param manual 抛单类型
     * @return 物料代码
     */
    List<ProductInfoDO> listErpCodesByOrderFlowTypeAndIsUser(String manual);

    /**
     * 查询物料代码列表
     *
     * @param materialType 物料类型
     * @param isUser       是否再用
     * @return 物料代码列表
     */
    List<String> listByIsUserAndMaterialType(String materialType, String isUser);

    /**
     * 查看所有记账物料列表
     *
     * @return 记账物料列表
     */
    List<ProductInfoDO> listByIsKeepAccountsProduct();

    /**
     * 查看第三方代发商品
     *
     * @param newProductName 代发名称
     * @return 物料代码
     */
    List<ProductInfoDO> listByShortName(String newProductName);

    /**
     * 根据物料代码查看仓库自管数量
     *
     * @param productIds 物料代码
     * @return 数量
     */
    long countByProductIdsAndIsKeepAccounts(List<String> productIds);

    /**
     * 根据物料代码获取商品商品信息
     *
     * @param productId 物料代码
     * @return 商品信息
     */
    Optional<ProductInfoDO> getByProductId(String productId);

    /**
     * 删除物料
     *
     * @param id id
     */
    void deleteById(int id);

    /**
     * 同步重量体积物料列表
     *
     * @return 同步重量体积物料列表
     */
    List<ProductInfoDO> listBySyncProductWeightAndVolume();

}
