package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xtc.onlineretailers.pojo.entity.OrdinaryInvoiceRegisterDO;
import com.xtc.onlineretailers.pojo.query.OrdinaryInvoiceRegisterAddQuery;
import com.xtc.onlineretailers.pojo.query.OrdinaryInvoiceRegisterConfirmQuery;
import com.xtc.onlineretailers.pojo.query.OrdinaryInvoiceRegisterQuery;
import com.xtc.onlineretailers.pojo.query.OrdinaryInvoiceRegisterUpdateQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;

import java.util.List;
import java.util.Optional;

public interface OrdinaryInvoiceRegisterService extends IService<OrdinaryInvoiceRegisterDO> {

    void add(OrdinaryInvoiceRegisterAddQuery query);

    PaginationVO<OrdinaryInvoiceRegisterDO> pageBy(OrdinaryInvoiceRegisterQuery query);

    Optional<OrdinaryInvoiceRegisterDO> getOneByTradeIdAndStatus(String platformTradeId, List<Integer> status);

    void update(OrdinaryInvoiceRegisterUpdateQuery query);

    boolean delete(long id);

    void confirm(OrdinaryInvoiceRegisterConfirmQuery query);

}
