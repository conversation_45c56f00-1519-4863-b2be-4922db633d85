package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xtc.onlineretailers.pojo.entity.RealNameAuthenticationDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.query.RealNameAuthenticationQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;

public interface RealNameAuthenticationService extends IService<RealNameAuthenticationDO> {

    /**
     * 分页查询交易列表
     *
     * @param query 查询条件
     * @return 交易列表
     */
    PaginationVO<RealNameAuthenticationDO> list(RealNameAuthenticationQuery query);

    /**
     *
     * 实名拦截
     *
     * @param realNameDTO 订单
     * @return 拦截
     */

    boolean realNameIntercept(TradeDO realNameDTO);

}
