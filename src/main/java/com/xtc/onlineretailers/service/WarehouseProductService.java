package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xtc.onlineretailers.pojo.entity.WarehouseProductDO;
import com.xtc.onlineretailers.pojo.query.WarehouseImportQuery;
import com.xtc.onlineretailers.pojo.query.WarehouseProductQuery;
import com.xtc.onlineretailers.pojo.query.WarehouseSaveQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface WarehouseProductService extends IService<WarehouseProductDO> {

    /**
     * 获取顺丰仓库物料
     *
     * @param query 查询条件
     * @return 顺丰仓库物料集合
     */
    List<WarehouseProductDO> getWarehouseProduct(WarehouseProductQuery query);

    /**
     * 新增顺丰仓库物料
     *
     * @param query 查询条件
     * @return 顺丰仓库物料集合
     */
    WarehouseProductDO addWarehouseProduct(WarehouseProductQuery query);

    /**
     * 删除顺丰仓库物料
     *
     * @param id 查询条件
     * @return 顺丰仓库物料集合
     */
    WarehouseProductDO updateWarehouse(long id , WarehouseSaveQuery query);

    /**
     * 获取仓库物料
     *
     * @param barcode 物料69条码
     * @return 仓库物料
     */
    List<WarehouseProductDO> getWarehouseProducts(String barcode);

    /**
     * 获取物料名称
     *
     * @param barcode 物料69条码
     * @return 物料名称
     */
    String getProductName(String barcode);

    boolean deleteById(String id);


    PaginationVO<WarehouseProductDO> list(WarehouseProductQuery query);

    void excelExport(WarehouseImportQuery query, HttpServletResponse response);
}
