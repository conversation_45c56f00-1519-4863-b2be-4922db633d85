package com.xtc.onlineretailers.service;

import com.xtc.onlineretailers.pojo.dto.ProductSaleDetailStatisticDTO;
import com.xtc.onlineretailers.pojo.dto.ProductSaleStatisticDTO;
import com.xtc.onlineretailers.pojo.query.ProductSaleStatisticQuery;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ProductSaleStatisticService {

    List<ProductSaleStatisticDTO> statistic(ProductSaleStatisticQuery query);

    List<ProductSaleDetailStatisticDTO> singlePlatformStatistic(ProductSaleStatisticQuery query);

    void export(ProductSaleStatisticQuery query, HttpServletResponse response);

    void exportSingle(ProductSaleStatisticQuery query, HttpServletResponse response);

}
