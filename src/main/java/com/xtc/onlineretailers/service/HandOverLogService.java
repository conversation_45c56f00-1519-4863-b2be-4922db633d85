package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xtc.onlineretailers.pojo.entity.HandOverLogDO;
import com.xtc.onlineretailers.pojo.query.HandOverLogQuery;
import com.xtc.onlineretailers.pojo.query.HandOverLogSignQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;

public interface HandOverLogService extends IService<HandOverLogDO> {

    /**
     * 获取打回状态列表
     *
     * @param query
     * @return
     */
    PaginationVO<HandOverLogDO> list(HandOverLogQuery query);

    /**
     * 快递签名交接
     *
     * @param query
     */
    void sign(HandOverLogSignQuery query);

    /**
     * 保存交接记录
     *
     * @param handOverNo 交接单号
     */
    void saveHandOverLog(String handOverNo);

    /**
     * 根据快递公司,获取下一个新的交接单号
     *
     * @param expressCompany
     * @return
     */
    String getHandOverNo(String expressCompany);

    /**
     * 根据交接单号获取交接记录
     *
     * @param handOverNo 交接单号
     * @return
     */
    HandOverLogDO getOneByHandOverNo(String handOverNo);

}
