package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xtc.onlineretailers.pojo.entity.MallStoreDO;
import com.xtc.onlineretailers.pojo.query.MallStoreAddQuery;
import com.xtc.onlineretailers.pojo.query.MallStoreQuery;
import com.xtc.onlineretailers.pojo.query.MallStoreUpdateQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface MallStoreService extends IService<MallStoreDO> {

    PaginationVO<MallStoreDO> page(MallStoreQuery query);

    void add(MallStoreAddQuery query);

    MallStoreDO getByMaterialCode(String materialCode);

    void reduceStoreInventory(String materialCode, int actualCount);

    void addStoreInventory(String materialCode, int actualCount);

    void update(MallStoreUpdateQuery query);

    void removeBatch(List<Integer> ids);

    void export(MallStoreQuery query, HttpServletResponse response);

}
