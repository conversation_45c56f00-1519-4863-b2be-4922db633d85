package com.xtc.onlineretailers.service;

import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.onlineretailers.pojo.dto.AgencyTradeImportDTO;
import com.xtc.onlineretailers.pojo.dto.AgencyTradeUpdateDTO;
import com.xtc.onlineretailers.pojo.query.AgencyTradeImportPageQry;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface TradeUploadLogService {

    /**
     * 分页订单上传日志列表
     *
     * @param qry 参数
     * @return 订单上传日志列表
     */
    PageResponse<AgencyTradeImportDTO> pageTradeUploadLog(AgencyTradeImportPageQry qry);

    /**
     * 锁单
     *
     * @param tradeId 订单号
     */
    void lockTrade(List<String> tradeId);

    /**
     * 订单上传
     *
     * @param excel    文件参数
     * @param response 响应
     */
    Boolean uploadExpressTrackingNo(MultipartFile excel, HttpServletResponse response);

    /**
     * 上传条码
     *
     * @param excel    文件参数
     * @param response 响应
     */
    Boolean uploadBarcode(MultipartFile excel, HttpServletResponse response);

    /**
     * 修改备注
     *
     * @param agencyTradeUpdateDTO 参数
     */
    void updateById(AgencyTradeUpdateDTO agencyTradeUpdateDTO);

    /**
     * 代发记录导出
     *
     * @param qry      查询
     * @param response 响应
     */
    void export(AgencyTradeImportPageQry qry, HttpServletResponse response);

}
