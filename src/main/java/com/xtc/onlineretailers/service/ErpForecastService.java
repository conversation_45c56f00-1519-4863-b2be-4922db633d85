package com.xtc.onlineretailers.service;

import com.xtc.onlineretailers.pojo.query.ErpForecastQuery;
import com.xtc.onlineretailers.rpc.erp.dto.ErpExpectRptDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ErpForecastService {

    /**
     * erp预期报表查询
     *
     * @param query
     * @return
     */
    List<ErpExpectRptDTO> list(ErpForecastQuery query);

    /**
     * erp预期报表查询导出
     *
     * @param query
     * @param response
     */
    void erpForecastExport(ErpForecastQuery query, HttpServletResponse response);

}
