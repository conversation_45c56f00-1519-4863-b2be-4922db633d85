package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.taobao.api.domain.Refund;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.PlatformRefundDO;

import java.time.Duration;
import java.util.List;
import java.util.Optional;

public interface PlatformRefundService extends IService<PlatformRefundDO> {

    /**
     * 同步淘宝退货数据
     *
     * @param platformId  平台id
     * @param systemParam 系统参数
     */
    void syncTaobaoRefunds(int platformId, String systemParam);

    /**
     * 同步抖音退货数据
     *
     * @param platformId  平台id
     * @param systemParam 系统参数
     */
    void syncTicTokRefunds(int platformId, String systemParam);

    /**
     * 同步快手退货数据
     *
     * @param platformId  平台id
     * @param systemParam 系统参数
     */
    void syncKuaishouRefunds(int platformId, String systemParam);

    /**
     * 获取退货列表
     *
     * @param platformTradeId 交易订单号
     * @return 退货列表
     */
    List<PlatformRefundDO> getRefundList(String platformTradeId);

    /**
     * 同步app商城退货数据
     *
     * @param platformId
     * @param systemParam
     */
    void syncAppRefunds(int platformId, String systemParam);

    /**
     * 部分退货数据处理
     */
    void partRefundProcessJob();

    /**
     * 退货数据处理
     */
    void refundsDataProcessJob();

    /**
     * 退货数据更新
     */
    void updateRefundStatus();

    /**
     * 判断部分退货
     *
     * @param platformId 平台id
     * @param tradeId    订单号
     * @return 执行结果
     */
    Optional<Boolean> partRefund(int platformId, String tradeId);

    /**
     * 根据订单号和平台id获取退货数据
     */
    PlatformRefundDO getByTradeIdAndPlatformId(String platformTradeId, Integer platformId);

    /**
     * 更新拦截记录的快递路由
     */
    void updateInterceptRouting();

    /**
     * 顺丰自动拦截快递
     */
    void sfAutoIntercept();

    /**
     * 构建平台退货单
     *
     * @param platform     平台信息
     * @param taobaoRefund 淘宝退货单
     * @return 平台退货单
     */
    PlatformRefundDO buildTaobaoPlatformRefund(PlatformDO platform, Refund taobaoRefund);

    /**
     * /**
     * 同步抖音退货数据
     *
     * @param platformTictokXtc             平台id
     * @param systemTictokRefundSyncTimeXtc 系统参数
     */
    void syncJingDongRefunds(int platformTictokXtc, String systemTictokRefundSyncTimeXtc);

    /**
     * 根据订单号查询退款成功的退款信息
     *
     * @param tradeIds 订单号集合
     * @return 退款信息
     */
    List<PlatformRefundDO> listByPlatformTradeIds(List<String> tradeIds);

    /**
     * 同步小红书退货数据
     *
     * @param platformId  平台id
     * @param systemParma 系统参数
     */
    void syncXiaoHongShuRefundsJob(int platformId, String systemParma);

    /**
     * 根据快递单号查询退款列表
     *
     * @param expressNo 快递单号
     * @return 退款列表
     */
    List<PlatformRefundDO> listByExpressNo(String expressNo);

    /**
     * 获取退款列表
     *
     * @param duration  间隔时间
     * @param limitSize 数量大小
     * @return 退款列表
     */
    List<PlatformRefundDO> listByExpressWayBillNoAndUpdateTime(Duration duration, String limitSize);

}
