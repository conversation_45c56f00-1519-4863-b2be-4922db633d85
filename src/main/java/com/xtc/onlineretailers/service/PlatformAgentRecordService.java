package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xtc.onlineretailers.pojo.entity.PlatformAgentDO;
import com.xtc.onlineretailers.pojo.entity.PlatformAgentRecordDO;
import com.xtc.onlineretailers.pojo.query.PlatformAgentRecordQuery;
import com.xtc.onlineretailers.pojo.query.PlatformAgentRecordUpdateQuery;
import com.xtc.onlineretailers.pojo.query.platformAgentRecordAddQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;

public interface PlatformAgentRecordService extends IService<PlatformAgentRecordDO> {

    /**
     * 新增代销打款
     *
     * @param query
     */
    void add(platformAgentRecordAddQuery query);

    /**
     * 编辑代销打款
     *
     * @param query
     */
    void update(PlatformAgentRecordUpdateQuery query);

    /**
     * 删除代销打款
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 分页获取代销打款
     *
     * @param query
     * @return
     */
    PaginationVO<PlatformAgentRecordDO> getPage(PlatformAgentRecordQuery query);

    /**
     * 余额小于阈值发送短息
     *
     * @param platformAgentDO
     */
    void sendMessage(PlatformAgentDO platformAgentDO);

}
