package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xtc.onlineretailers.pojo.entity.DeliverGoodsDO;
import com.xtc.onlineretailers.pojo.query.DeliverGoodsQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;

public interface DeliverGoodsService  extends IService<DeliverGoodsDO> {
    /**
     * 分页查询优先发货列表
     *
     * @param query 查询条件
     * @return
     */
    PaginationVO<DeliverGoodsDO> list(DeliverGoodsQuery query);

    /**
     * 根据电商id获取发货订单信息
     *
     * @param platformTradeId
     * @return
     */
    DeliverGoodsDO getplatformTradeId(String platformTradeId);

    /**
     * 保存发货订单信息
     *
     */
    void saveDeliverGoods();

    /**
     * 修改物料信息
     *
     * @param platformTradeId 电商交易id
     * @param code 物料编号
     */
    void updateThread(String platformTradeId,String code);
}
