package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xtc.onlineretailers.pojo.entity.MallStoreOrderItemDO;
import com.xtc.onlineretailers.pojo.query.MallStoreOrderItemAddQuery;
import com.xtc.onlineretailers.pojo.query.MallStoreOrderItemListQuery;
import com.xtc.onlineretailers.pojo.query.MallStoreOrderItemUpdateQuery;

import java.util.List;

public interface MallStoreOrderItemService extends IService<MallStoreOrderItemDO> {

    void add(MallStoreOrderItemAddQuery query);

    void updateItem(MallStoreOrderItemUpdateQuery query);

    void deleteById(long id);

    List<MallStoreOrderItemDO> getItemList(MallStoreOrderItemListQuery query);

}
