package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xtc.onlineretailers.pojo.entity.OrderInterceptSetDO;
import com.xtc.onlineretailers.pojo.query.OrderInterceptSetAddQuery;
import com.xtc.onlineretailers.pojo.query.OrderInterceptSetQuery;
import com.xtc.onlineretailers.pojo.query.OrderInterceptSetUpdateQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;

import javax.servlet.http.HttpServletResponse;

public interface OrderInterceptSetService extends IService<OrderInterceptSetDO> {

    /**
     * @description: 获取订单拦截配置信息列表
     * @param: OrderInterceptSetQuery
     * @return: PaginationVO<OrderInterceptSetDO>
     * @throws:
     */
    PaginationVO<OrderInterceptSetDO> getOrderInterceptSetList(OrderInterceptSetQuery query);

    /**
     * @description: 编辑订单拦截配置信息
     * @param: OrderInterceptSetUpdateQuery
     * @return: OrderInterceptSetDO
     * @throws:
     */
    OrderInterceptSetDO updateOrderInterceptSet(OrderInterceptSetUpdateQuery query);

    /**
     * @description: 新增订单拦截配置信息
     * @param: OrderInterceptSetAddQuery
     * @return: OrderInterceptSetDO
     * @throws:
     */
    OrderInterceptSetDO addOrderInterceptSet(OrderInterceptSetAddQuery query);

    /**
     * @description: 导出订单拦截配置信息列表
     * @param: OrderInterceptSetQuery ， HttpServletResponse
     * @return: excel
     * @throws:
     */
    void exportInterceptSet(OrderInterceptSetQuery query, HttpServletResponse response);

    /**
     * @description: 删除订单拦截配置信息
     * @param: id
     * @return: boolean
     * @throws:
     */
    boolean deleteOrderInterceptSet(int id);

}
