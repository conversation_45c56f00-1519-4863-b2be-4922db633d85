package com.xtc.onlineretailers.service;

import com.xtc.onlineretailers.pojo.entity.LetteringOrderDO;
import com.xtc.onlineretailers.pojo.query.LetteringAddQuery;
import com.xtc.onlineretailers.pojo.query.LetteringPageQuery;
import com.xtc.onlineretailers.pojo.query.LetteringUpdateQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.pojo.vo.PrintVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Optional;

public interface LetteringService {

    /**
     * 分页查询刻字订单列表
     *
     * @param query 查询
     * @return 刻字订单列表
     */
    PaginationVO<LetteringOrderDO> list(LetteringPageQuery query);

    /**
     * 新增刻字订单
     *
     * @param query 查询
     * @return 结果
     */
    boolean add(LetteringAddQuery query);

    /**
     * 编辑刻字订单
     *
     * @param query 条件
     * @return 结果
     */
    boolean edit(LetteringUpdateQuery query);

    /**
     * 查询刻字订单
     *
     * @param platformTradeId 订单号
     * @return 刻字订单
     */
    List<LetteringOrderDO> listByPlatformTradeId(String platformTradeId);

    /**
     * 查询刻字订单
     *
     * @param platformTradeId 订单号
     * @param status          刻字状态
     * @return 刻字订单
     */
    List<LetteringOrderDO> listByPlatformTradeIds(List<String> platformTradeId, String status);

    /**
     * 查询刻字订单列表
     *
     * @param platformTradeIds 订单号集合
     * @return 刻字订单列表
     */
    List<LetteringOrderDO> getLetteringList(List<String> platformTradeIds);

    /**
     * 审核
     *
     * @param id id
     * @return 结果
     */
    boolean review(Long id);

    /**
     * 获取刻字订单打印数据
     *
     * @param platformTradeId
     * @return 打印数据
     */
    PrintVO print(List<String> platformTradeId);

    /**
     * 获取刻字订单
     *
     * @param remake          刻字内容
     * @param platformTradeId 订单号
     * @return 刻字订单
     */
    Optional<LetteringOrderDO> getLetteringByRemakeAndTradeId(String remake, String platformTradeId);

    /**
     * 导出
     *
     * @param response 响应对象
     * @param query    查询
     */
    void export(HttpServletResponse response, LetteringPageQuery query);

    /**
     * 刻字删除
     *
     * @param id 刻字id
     */
    void deleteLettering(Long id);

}
