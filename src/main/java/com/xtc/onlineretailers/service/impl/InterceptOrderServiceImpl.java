package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.mapper.master.InterceptOrderMapper;
import com.xtc.onlineretailers.pojo.entity.InterceptOrderDO;
import com.xtc.onlineretailers.pojo.query.InterceptOrderQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.InterceptOrderService;
import org.springframework.stereotype.Service;

@Service
public class InterceptOrderServiceImpl   extends ServiceImpl<InterceptOrderMapper, InterceptOrderDO> implements InterceptOrderService {
    @Override
    public PaginationVO<InterceptOrderDO> list(InterceptOrderQuery query) {
        Wrapper<InterceptOrderDO> wrapper = this.getInterceptOrderDOWrapper(query);
        IPage<InterceptOrderDO> result = this.page(query.createPage(), wrapper);
        return PaginationVO.newVO(result);
    }

    private Wrapper<InterceptOrderDO> getInterceptOrderDOWrapper(InterceptOrderQuery query) {
        return Wrappers.<InterceptOrderDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getPlatformName()),InterceptOrderDO::getPlatformName,query.getPlatformName())
                .gt(StringUtils.isNotBlank(query.getBeginTime()),InterceptOrderDO::getCreateTime,query.getBeginTime())
                .lt(StringUtils.isNotBlank(query.getEndTime()),InterceptOrderDO::getCreateTime,query.getEndTime())
                .eq(ObjectUtils.isNotNull(query.getForbid()),InterceptOrderDO::getForbid,query.getForbid())
                .eq(ObjectUtils.isNotNull(query.getPlatformId()),InterceptOrderDO::getPlatformId,query.getPlatformId())
                .orderByDesc(InterceptOrderDO::getCreateTime);
    }
}
