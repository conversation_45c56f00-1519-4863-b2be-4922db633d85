package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.enums.*;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.mapper.master.TradeLogMapper;
import com.xtc.onlineretailers.pojo.entity.*;
import com.xtc.onlineretailers.pojo.query.DeliverGoodsOrderQuery;
import com.xtc.onlineretailers.pojo.query.TradeConfirmQuery;
import com.xtc.onlineretailers.pojo.query.TradeLogQuery;
import com.xtc.onlineretailers.pojo.vo.OrderConfirmVO;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.pojo.vo.TradeLogDeliverGoodsVO;
import com.xtc.onlineretailers.pojo.vo.TradeLogVO;
import com.xtc.onlineretailers.refactor.executor.command.TradeRemarkCmdExe;
import com.xtc.onlineretailers.service.*;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.springboot.pojo.entity.GlobalContext;
import com.xtc.springboot.pojo.vo.ResponseVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.xtc.onlineretailers.constant.GlobalConstant.WAREHOUSE_LOCAL;

@Slf4j
@RequiredArgsConstructor
@Service
public class TradeLogServiceImpl extends ServiceImpl<TradeLogMapper, TradeLogDO> implements TradeLogService {

    private final TradeServiceImpl tradeService;
    private final OrderService orderService;
    private final DeliverGoodsService deliverGoodsService;
    private final DeliverGoodsLogService deliverGoodsLogService;
    private final DeliverGoodsDetailsService deliverGoodsDetailsService;
    private final SystemParamService systemParamService;
    private final TradeRemarkCmdExe tradeRemarkCmdExe;

    @Override
    public PaginationVO<TradeLogDO> list(TradeLogQuery query) {
        Wrapper<TradeLogDO> wrapper = this.getTradeLogDOWrapper(query);
        IPage<TradeLogDO> result = this.page(query.createPage(), wrapper);
        return PaginationVO.newVO(result);
    }

    @Override
    public void deliverGoods(TradeLogDeliverGoodsVO tradeLogDeliverGoodsVO) {
        // 查询订单表
        List<TradeDO> list = tradeService.list(Wrappers.<TradeDO>lambdaQuery()
                .eq(ObjectUtils.isNotNull(tradeLogDeliverGoodsVO.getPlatformId()), TradeDO::getPlatformId, tradeLogDeliverGoodsVO.getPlatformId())
                .gt(StringUtils.isNotBlank(tradeLogDeliverGoodsVO.getBeginTime()), TradeDO::getPaymentTime, tradeLogDeliverGoodsVO.getBeginTime())
                .lt(StringUtils.isNotBlank(tradeLogDeliverGoodsVO.getEndTime()), TradeDO::getPaymentTime, tradeLogDeliverGoodsVO.getEndTime())
                .eq(TradeDO::getStatusOrder, OrderStatus.WAIT_SHIP.name())
                .ne(TradeDO::getType, TradeTypeEnum.EXCHANGE_GOODS.name())
                .eq(TradeDO::getWarehouseCode, WAREHOUSE_LOCAL));

        if (CollectionUtils.isEmpty(list)) {
            throw new GlobalDefaultException(ResponseEnum.DELIVER_GOODS_ERRPR);
        }
        int success = 0;
        List<TradeDO> succesTradeList = new ArrayList<>();
        for (TradeDO tradeDO : list) {
            // 查询订单明细
            List<OrderDO> orderDOS = orderService.getOrderDOS(tradeDO.getPlatformTradeId());

            if (CollectionUtils.isEmpty(orderDOS)) {
                continue;
            }
            // 过滤
            List<OrderDO> normalOrderList = orderDOS.stream().filter(o -> !"折扣".equals(o.getErpName())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(normalOrderList)) {
                continue;
            }
            for (OrderDO orderDO : normalOrderList) {
                if (orderDO.getErpCode().equals(tradeLogDeliverGoodsVO.getRawMaterialCode())) {
                    // 保存订单明细
                    DeliverGoodsDetailsDO deliverGoodsDetailsDO = new DeliverGoodsDetailsDO();
                    BeanUtils.copyProperties(orderDO, deliverGoodsDetailsDO);

                    orderDO.setErpCode(tradeLogDeliverGoodsVO.getMaterialCode());
                    this.deliverGoodsDetailsService.save(deliverGoodsDetailsDO);
                    this.orderService.update(orderDO);
                    success++;
                    succesTradeList.add(tradeDO);
                }
            }
        }
        if (success == 0) {
            throw new GlobalDefaultException(ResponseEnum.DELIVER_GOODS_ERRPR);
        } else {
            // 保存操作日志表
            DeliverGoodsLogDO deliverGoodsLogDO = new DeliverGoodsLogDO();
            deliverGoodsLogDO.setTradeId(tradeLogDeliverGoodsVO.getId());
            deliverGoodsLogDO.setOperator(GlobalContext.getUser().getName());
            deliverGoodsLogDO.setUpdateTime(DateUtil.localDateTimeToStr(LocalDateTime.now()));
            deliverGoodsLogDO.setType("deliverGood");
            deliverGoodsLogService.save(deliverGoodsLogDO);
            DeliverGoodsLogDO one = this.deliverGoodsLogService.getOne(Wrappers.<DeliverGoodsLogDO>lambdaQuery().eq(DeliverGoodsLogDO::getTradeId, tradeLogDeliverGoodsVO.getId()).orderByDesc(DeliverGoodsLogDO::getUpdateTime).last("limit 1"));
            if (one != null) {
                // 保存操作记录
                saveOrderOrLog(tradeLogDeliverGoodsVO, succesTradeList, deliverGoodsLogDO.getId());
            }
        }
    }

    /**
     * 保存订单数据和操作记录
     *
     * @param tradeLogDeliverGoodsVO 日志数据
     * @param logId                  日志id
     */
    private void saveOrderOrLog(TradeLogDeliverGoodsVO tradeLogDeliverGoodsVO, List<TradeDO> list, Long logId) {
        // 保存订单信息
        for (TradeDO tradeDO : list) {
            DeliverGoodsDO deliverGoodsDO = new DeliverGoodsDO();
            BeanUtils.copyProperties(tradeDO, deliverGoodsDO);
            deliverGoodsDO.setStatus(0);
            deliverGoodsDO.setTradeId(logId);
            deliverGoodsDO.setOperator(GlobalContext.getUser().getName());
            deliverGoodsService.save(deliverGoodsDO);
        }

        // 修改发货任务的操作状态
        TradeLogDO tradeLogDO = this.getById(tradeLogDeliverGoodsVO.getId());
        if (ObjectUtils.isNull(tradeLogDO.getIsDelete()) || tradeLogDO.getIsDelete() == 0) {
            tradeLogDO.setIsDelete(1);
            this.updateById(tradeLogDO);
        }
    }

    @Override
    public void saveTask(TradeLogVO tradeLogVO) {
        TradeLogDO tradeLogDO = new TradeLogDO();
        BeanUtils.copyProperties(tradeLogVO, tradeLogDO);
        tradeLogDO.setOperator(GlobalContext.getUser().getName());
        tradeLogDO.setStatus(0);
        tradeLogDO.setCreateTime(DateUtil.localDateTimeToStr(LocalDateTime.now()));
        this.getBaseMapper().insert(tradeLogDO);
    }

    @Override
    public PaginationVO<DeliverGoodsDO> getTradeList(DeliverGoodsOrderQuery deliverGoodsQuery) {
        Page<DeliverGoodsDO> page = deliverGoodsService.page(deliverGoodsQuery.createPage(), Wrappers.<DeliverGoodsDO>lambdaQuery().eq(DeliverGoodsDO::getTradeId, deliverGoodsQuery.getTreadId()));
        return PaginationVO.newVO(page);
    }

    @Override
    public String confirm(TradeConfirmQuery query) {
        // 获取符合条件的订单列表
        List<TradeDO> oldTrades = getTradeDOS(query);

        // 修改订单信息
        updateTread(oldTrades);

        // 校验库存信息
        StringBuilder result = checkoutRepertory(query);

        return result.toString();
    }

    /**
     * 获取满足条件胡订单信息
     *
     * @param query 订单条件
     * @return
     */
    @NotNull
    private List<TradeDO> getTradeDOS(TradeConfirmQuery query) {
        // 确认条件判断
        List<TradeDO> oldTrades = Lists.newArrayList();
        Integer platformId = null;
        List<String> applyReturnTiDs = Lists.newArrayList();
        for (String platformTradeId : query.getPlatformTradeIds()) {
            TradeDO oldTrade = this.tradeService.getExistTradeDO(platformTradeId);
            platformId = this.tradeService.confirmCheck(platformId, oldTrade, query.getIsCheckReturn(), applyReturnTiDs);
            oldTrades.add(oldTrade);
        }
        if (!applyReturnTiDs.isEmpty()) {
            throw new GlobalDefaultException(ResponseEnum.TRADE_APPLY_REFUND, applyReturnTiDs.toString().substring(1, applyReturnTiDs.toString().length() - 1));
        }
        return oldTrades;
    }

    /**
     * 判断库存是否充足
     *
     * @param query
     * @return
     */
    @NotNull
    private StringBuilder checkoutRepertory(TradeConfirmQuery query) {
        // 检查库存是否充足
        List<OrderConfirmVO> orderConfirmList = Lists.newArrayList();

        // 库存判断开关
        String checkStockValue = this.systemParamService.getSystemParamByName(GlobalConstant.SYSTEM_SWITCH_CHECK_ERP_STOCK_MAIN);
        boolean checkStock = "1".equals(checkStockValue);
        if (checkStock) {
            orderConfirmList = this.tradeService.checkConfirmStockEnough(query.getPlatformTradeIds());
            orderConfirmList.stream()
                    .filter(orderConfirmVO -> orderConfirmVO.getNum() <= 0)
                    .forEach(orderConfirmVO -> {
                        log.error("库存不足，物料代码：{}，仓库储位：本地仓", orderConfirmVO.getErpCode());
                        throw new GlobalDefaultException(ResponseEnum.ERROR_STOCK_NOT_ENOUGH,
                                "物料代码为" + orderConfirmVO.getErpCode() + "的库存数量不足,不允许确认,请去除");
                    });
        }

        StringBuilder result = new StringBuilder();
        orderConfirmList.stream().filter(orderConfirmVO -> orderConfirmVO.getNum() >= 1 && orderConfirmVO.getNum() <= 10).forEach(orderConfirmVO -> {
            log.error("库存不足10个，物料代码：{}，仓库储位：本地仓", orderConfirmVO.getErpCode());
            result.append("物料代码为").append(orderConfirmVO.getErpCode())
                    .append("的库存数量为").append(orderConfirmVO.getNum())
                    .append(",不足10个!\r\n");
        });
        return result;
    }

    /**
     * 修改订单信息
     *
     * @param oldTrades 初始订单信息数据
     */
    private void updateTread(List<TradeDO> oldTrades) {
        // 更新订单信息
        List<DeliverGoodsDO> deliverGoodsDOList = Lists.newArrayList();
        // 原订单信息
        List<TradeDO> tradeDOList = Lists.newArrayList();

        for (TradeDO oldTrade : oldTrades) {
            List<OrderDO> orderDos = this.orderService.getOrderDOS(oldTrade.getPlatformTradeId());
            int count = (int) orderDos.stream().filter(orderDO -> "FREIGHT".equals(orderDO.getErpCode()) || "BPZY000".equals(orderDO.getErpCode())).count();
            if (count > 0) {
                throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "运费和补拍专用不能抛单,请改成价外费用!");
            }
            // 地址校验
            checkAddress(oldTrade);

            DeliverGoodsDO updateTrade = this.deliverGoodsService.getplatformTradeId(oldTrade.getPlatformTradeId());
            // 确认状态的修改：把statusOrder改成已确认，warehouseStatus改成已确认
            updateTrade.setStatusOrder(OrderStatus.ORDER_CONFIRMED.name());
            updateTrade.setWarehouseStatus(WarehouseStatus.HAVE_CONFIRMED.name());
            updateTrade.setStatusSf(SFStatus.NOT_SF.name());
            updateTrade.setOrderConfirmTime(DateUtil.nowDateTimeStr());

            oldTrade.setStatusOrder(OrderStatus.ORDER_CONFIRMED.name());
            oldTrade.setWarehouseStatus(WarehouseStatus.HAVE_CONFIRMED.name());
            oldTrade.setStatusSf(SFStatus.NOT_SF.name());
            oldTrade.setOrderConfirmTime(DateUtil.nowDateTimeStr());

            // 修改卖家备注
            String updateRemark = tradeRemarkCmdExe.execute(oldTrade, "@OK", "，");
            updateTrade.setSellerRemark(updateRemark);
            oldTrade.setSellerRemark(updateRemark);

            deliverGoodsDOList.add(updateTrade);
            tradeDOList.add(oldTrade);
        }
        // 修改订单信息
        this.deliverGoodsService.updateBatchById(deliverGoodsDOList);
        this.tradeService.updateBatchById(tradeDOList);
    }

    @Override
    public ResponseVO deleteDeliverGoods(String id) {
        // 查看操作日志表
        List<DeliverGoodsLogDO> deliverGoodsLogDOS = this.deliverGoodsLogService.list(Wrappers.<DeliverGoodsLogDO>lambdaQuery().eq(DeliverGoodsLogDO::getTradeId, id));
        if (CollectionUtils.isEmpty(deliverGoodsLogDOS)) {
            this.removeById(id);
            return new ResponseVO(ResponseEnum.SUCCESS);
        }
        return new ResponseVO(ResponseEnum.DELIVER_GOODS_DELETE_ERROR);
    }

    /**
     * 校验订单地址信息
     *
     * @param oldTrade 订单信息
     */
    private void checkAddress(TradeDO oldTrade) {
        if (StringUtils.isBlank(oldTrade.getReceiverProvince()) || StringUtils.isBlank(oldTrade.getReceiverCity()) || StringUtils.isBlank(oldTrade.getReceiverAddress())) {
            throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, oldTrade.getPlatformTradeId() + ",抛单地址错误，省，市，地址不能为空!");
        }
    }

    private Wrapper<TradeLogDO> getTradeLogDOWrapper(TradeLogQuery query) {
        return Wrappers.<TradeLogDO>lambdaQuery()
                .eq(ObjectUtils.isNotEmpty(query.getPlatformId()), TradeLogDO::getPlatformId, query.getPlatformId())
                .gt(StringUtils.isNotBlank(query.getBeginTime()), TradeLogDO::getCreateTime, query.getBeginTime())
                .lt(StringUtils.isNotBlank(query.getEndTime()), TradeLogDO::getEndTime, query.getEndTime())
                .orderByDesc(TradeLogDO::getCreateTime);
    }

}
