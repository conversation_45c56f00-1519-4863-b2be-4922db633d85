package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.xtc.onlineretailers.enums.*;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.mapper.master.RepairExchangeMapper;
import com.xtc.onlineretailers.mapper.master.RepairMapper;
import com.xtc.onlineretailers.pojo.entity.*;
import com.xtc.onlineretailers.pojo.query.*;
import com.xtc.onlineretailers.pojo.vo.*;
import com.xtc.onlineretailers.refactor.service.LogisticsService;
import com.xtc.onlineretailers.repository.AppointmentDownloadDao;
import com.xtc.onlineretailers.repository.ReceiveMasterDao;
import com.xtc.onlineretailers.repository.RepairDao;
import com.xtc.onlineretailers.service.*;
import com.xtc.onlineretailers.util.*;
import com.xtc.springboot.pojo.entity.GlobalContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RepairServiceImpl extends ServiceImpl<RepairMapper, RepairDO> implements RepairService {

    @Resource
    private PrintTemplateService printTemplateService;

    @Resource
    private TradeService tradeService;

    @Resource
    private ExpressLogService expressLogService;

    @Resource
    private PlatformService platformService;

    @Resource
    private ProductService productService;

    @Resource
    private ProductInfoService productInfoService;

    @Resource
    private OrderBarcodeService orderBarcodeService;

    @Resource
    private ReturnMasterService returnMasterService;

    @Resource
    private ReturnDetailsService returnDetailsService;

    @Resource
    private RolePlatformService rolePlatformService;

    @Resource
    private RepairExchangeService repairExchangeService;

    @Resource
    private RepairExchangeMapper repairExchangeMapper;

    @Resource
    private OrderService orderService;

    @Resource
    private LogisticsService logisticsService;

    @Resource
    private MinioManager minioManager;

    @Resource
    private AppointmentDownloadDao appointmentDownloadDao;

    @Resource
    private RepairDao repairsDao;

    @Resource
    private ReceiveMasterDao receiveMasterDao;

    @Override
    public PaginationVO<RepairDO> getList(RepairQuery query) {
        Wrapper<RepairDO> wrapper = getRepairDOWrapper(query);
        IPage<RepairDO> result = this.page(query.createPage(), wrapper);
        return PaginationVO.newVO(result);
    }

    private Wrapper<RepairDO> getRepairDOWrapper(RepairQuery query) {
        // 开始时间筛选
        LocalDateTime startTime = DateUtil.minTimeOfLocalDate(query.getCreateStartTime());
        LocalDateTime endTime = DateUtil.maxTimeOfLocalDate(query.getCreateEndTime());
        return Wrappers.<RepairDO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(query.getPlatformIds()), RepairDO::getPlatformId, query.getPlatformIds())
                .eq(StringUtils.isNotBlank(query.getServiceName()), RepairDO::getServiceName, query.getServiceName())
                .eq(StringUtils.isNotBlank(query.getPayType()), RepairDO::getPayType, query.getPayType())
                .eq(StringUtils.isNotBlank(query.getStatus()), RepairDO::getStatus, query.getStatus())
                .eq(StringUtils.isNotBlank(query.getType()), RepairDO::getType, query.getType())
                .eq(StringUtils.isNotBlank(query.getPayee()), RepairDO::getPayee, query.getPayee())
                .eq(StringUtils.isNotBlank(query.getBuyerNick()), RepairDO::getBuyerNick, query.getBuyerNick())
                .eq(StringUtils.isNotBlank(query.getReceiverName()), RepairDO::getReceiverName, query.getReceiverName())
                .eq(StringUtils.isNotBlank(query.getTelephone()), RepairDO::getTelephone, query.getTelephone())
                .eq(StringUtils.isNotBlank(query.getExpressTrackingNo()), RepairDO::getExpressTrackingNo, query.getExpressTrackingNo())
                .eq(StringUtils.isNotBlank(query.getBackExpressTrackingNo()), RepairDO::getBackExpressTrackingNo, query.getBackExpressTrackingNo())
                .eq(StringUtils.isNotBlank(query.getPlatformTradeId()), RepairDO::getPlatformTradeId, query.getPlatformTradeId())
                .eq(StringUtils.isNotBlank(query.getExpressCompany()), RepairDO::getExpressCompany, query.getExpressCompany())
                .and(StringUtils.isNotBlank(query.getKeyword()),
                        i -> i.like(StringUtils.isNotBlank(query.getKeyword()), RepairDO::getBuyerNick, query.getKeyword())
                                .or().like(StringUtils.isNotBlank(query.getKeyword()), RepairDO::getReceiverName, query.getKeyword())
                                .or().like(StringUtils.isNotBlank(query.getKeyword()), RepairDO::getTelephone, query.getKeyword())
                                .or().like(StringUtils.isNotBlank(query.getKeyword()), RepairDO::getExpressTrackingNo, query.getKeyword())
                                .or().like(StringUtils.isNotBlank(query.getKeyword()), RepairDO::getPlatformTradeId, query.getKeyword()))
                .like(StringUtils.isNotBlank(query.getProductName()), RepairDO::getProductName, query.getProductName())
                .ge(StringUtils.isNotBlank(query.getBeginCreateTime()), RepairDO::getCreateTime, query.getBeginCreateTime() + " 00:00:00")
                .le(StringUtils.isNotBlank(query.getEndCreateTime()), RepairDO::getCreateTime, query.getEndCreateTime() + " 23:59:59")
                .ge(StringUtils.isNotBlank(query.getBeginSendTime()), RepairDO::getSendTime, query.getBeginSendTime() + " 00:00:00")
                .le(StringUtils.isNotBlank(query.getEndSendTime()), RepairDO::getSendTime, query.getEndSendTime() + " 23:59:59")
                .eq(StringUtils.isNotBlank(query.getExchangeGoodsOrder()), RepairDO::getExchangeGoodsOrder, query.getExchangeGoodsOrder())
                .eq(StringUtils.isNotBlank(query.getExchangeGoodsType()), RepairDO::getExchangeGoodsType, query.getExchangeGoodsType())
                .eq(ObjectUtils.isNotEmpty(query.getIsGenerate()), RepairDO::getIsGenerate, query.getIsGenerate())
                .eq(ObjectUtils.isNotEmpty(query.getIsConfirm()), RepairDO::getIsConfirm, query.getIsConfirm())
                .between(ObjectUtils.allNotNull(query.getCreateStartTime(), query.getCreateEndTime()), RepairDO::getRepairTime, startTime, endTime)
                .orderByDesc(RepairDO::getCreateTime);
    }

    @Override
    @Async("asyncExecutor")
    public void export(RepairQuery query, HttpServletResponse response,String name) {
        String fileName = DateUtil.dateToStr(new Date(), "yyyy-MM-dd") + "维修换货报表导出";
        AppointmentDownloadDO appointmentDownloadDO = new AppointmentDownloadDO();
        appointmentDownloadDO.setBookingPerson(name);
        appointmentDownloadDO.setType(AppointmentDownloadType.REPAIR.name());
        appointmentDownloadDO.setFileName(fileName);
        appointmentDownloadDO.setStatus(AppointmentDownloadStatusEnum.PROCESSING.name());
        appointmentDownloadDao.save(appointmentDownloadDO);
        Wrapper<RepairDO> wrapper = getRepairDOWrapper(query);
        List<RepairDO> repairDOS = this.repairsDao.list(wrapper);
        List<RepairExportVO> list = new ArrayList<>();
        for (RepairDO repairDO : repairDOS) {
            RepairExportVO repairExportVO = new RepairExportVO();
            BeanUtils.copyProperties(repairDO, repairExportVO);
            repairExportVO.setType(ReceiveTypeEnum.REPAIR.name().equals(repairExportVO.getType()) ? "维修" : "换货");
            if (StringUtils.isNotBlank(repairDO.getStatus())) {
                repairExportVO.setStatus(PostSaleStatus.valueOf(repairDO.getStatus()).getDescription());
            }
            if (StringUtils.isNotBlank(repairDO.getPayType())) {
                repairExportVO.setPayType(PaymentType.valueOf(repairDO.getPayType()).getDescription());
            }
            list.add(repairExportVO);
        }
        // 文件名称
        String url = exportExcelToMinio(list, RepairExportVO.class, fileName);
        // 更新预约信息,发送消息通知
        AppointmentDownloadDO updateDto = new AppointmentDownloadDO();
        updateDto.setId(appointmentDownloadDO.getId());
        updateDto.setDownloadUrl(url);
        updateDto.setStatus(AppointmentDownloadStatusEnum.COMPLETED.name());
        appointmentDownloadDao.updateById(updateDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RepairDO add(RepairAddQuery query) {
        checkout(query);

        RepairDO repairDO1 = this.getOne(Wrappers.<RepairDO>lambdaQuery().eq(RepairDO::getPlatformTradeId, query.getPlatformTradeId()));
        if (repairDO1 != null) {
            throw new GlobalDefaultException(ResponseEnum.REPAIR_EXIST);
        }
        RepairDO repairDO = new RepairDO();
        BeanUtils.copyProperties(query, repairDO);
        repairDO.setServiceName(GlobalContext.getUser().getName());
        // 状态 : 客服下单
        repairDO.setStatus(PostSaleStatus.SERVICE_ORDER.name());
        repairDO.setIsExchange(-1);
        repairDO.setExchangeGoodsType(query.getExchangeGoodsType());
        this.save(repairDO);
        // 是否存在换货物料
        if (CollectionUtils.isNotEmpty(query.getRepairExchangeList())) {
            List<RepairExchangeDO> exchangeList = CollectionCopier.copy(query.getRepairExchangeList(), RepairExchangeDO::new);
            exchangeList.forEach(p -> p.setPlatformTradeId(query.getPlatformTradeId()));
            this.repairExchangeService.saveBatch(exchangeList);
        }

        return repairDO;
    }

    /**
     * 校验金额和数量
     *
     * @param query
     */
    private void checkout(RepairAddQuery query) {
        // 金额判断
        List<RepairExchangeAddQuery> repairExchangeList = query.getRepairExchangeList();
        // 是否拦截标识
        boolean intercept = true;
        // 查看订单明细
        List<OrderDO> orderDOS = this.orderService.getOrderByTid(query.getPlatformTradeId());
        if ("NORMAL_TYPE".equals(query.getExchangeGoodsType())) {
            for (RepairExchangeAddQuery repairExchangeAddQuery : repairExchangeList) {

                for (OrderDO orderDO : orderDOS) {
                    // 过滤折扣明细
                    if (orderDO.getErpName().equals("折扣")) {
                        continue;
                    }

                    // 如果金额或者数量和订单明细不一致的话 拦截
                    if (!(repairExchangeAddQuery.getPrice().compareTo(orderDO.getPayment()) == 0 || repairExchangeAddQuery.getNum() <= orderDO.getNum())) {
                        intercept = false;
                        break;
                    }
                }
                if (!intercept) {
                    throw new GlobalDefaultException(ResponseEnum.EXCHANGE_GOODS_MONEY_NUM_ERRPR);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RepairDO update(RepairUpdateQuery query) {

        // 金额判断
        List<RepairExchangeAddQuery> repairExchangeList = query.getRepairExchangeList();
        // 是否拦截标识
        boolean intercept = true;
        // 查看订单明细
        List<OrderDO> orderDOS = this.orderService.getOrderByTid(query.getPlatformTradeId());

        if (CollectionUtils.isEmpty(orderDOS) && query.getType().equals("EXCHANGE")) {
            throw new GlobalDefaultException("找不到订单明细");
        }

        if ("NORMAL_TYPE".equals(query.getExchangeGoodsType()) && CollectionUtils.isNotEmpty(query.getRepairExchangeList())) {
            List<OrderDO> orderDOList = orderDOS.stream().filter(orderDO -> !orderDO.getErpName().equals("折扣")).collect(Collectors.toList());
            for (RepairExchangeAddQuery repairExchangeAddQuery : repairExchangeList) {

                for (OrderDO orderDO : orderDOList) {
                    // 如果金额或者数量和订单明细不一致的话 拦截
                    if (!(repairExchangeAddQuery.getPrice().compareTo(orderDO.getPrice()) == 0
                            || repairExchangeAddQuery.getNum() <= orderDO.getNum())) {
                        intercept = false;
                        break;
                    }
                }
                if (!intercept) {
                    throw new GlobalDefaultException(ResponseEnum.EXCHANGE_GOODS_MONEY_NUM_ERRPR);
                }
            }
        }

        RepairDO oldRepair = this.getOne(Wrappers.<RepairDO>lambdaQuery().eq(RepairDO::getPlatformTradeId, query.getPlatformTradeId()));
        if (oldRepair == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_NULL);
        }
        long id = query.getId();
        if (id != oldRepair.getId()) {
            throw new GlobalDefaultException(ResponseEnum.REPAIR_EXIST);
        }
        // 判断客服下单、收货送修、送修完成
        if (!(PostSaleStatus.SERVICE_ORDER.name().equals(oldRepair.getStatus())
                || PostSaleStatus.RECEIVE_REPAIR.name().equals(oldRepair.getStatus())
                || PostSaleStatus.REPAIR_COMPLETED.name().equals(oldRepair.getStatus()))) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_UPDATE);
        }
        RepairDO repairDO = new RepairDO();
        BeanUtils.copyProperties(query, repairDO);
        if (ReceiveTypeEnum.REPAIR.name().equals(query.getType())) {
            repairDO.setIsExchange(-1);
        }
        this.updateById(repairDO);
        // 更新换货信息
        if (CollectionUtils.isNotEmpty(query.getRepairExchangeList())) {
            List<RepairExchangeDO> exchangeList = CollectionCopier.copy(query.getRepairExchangeList(), RepairExchangeDO::new);
            exchangeList.forEach(p -> p.setPlatformTradeId(query.getPlatformTradeId()));
            this.repairExchangeMapper.deleteByTradeId(query.getPlatformTradeId());
            this.repairExchangeService.saveBatch(exchangeList);
        }
        return repairDO;
    }

    @Override
    public RepairDO receivedAndToRepair(RepairToSendQuery query) {
        RepairDO oldRepair = this.getById(query.getId());
        if (oldRepair == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_NULL);
        }
        // 收货送修只能在客服下单+收货送修可以点击
        if (!(PostSaleStatus.SERVICE_ORDER.name().equals(oldRepair.getStatus())
                || PostSaleStatus.RECEIVE_REPAIR.name().equals(oldRepair.getStatus()))) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_RECEIVE);
        }
        RepairDO repairDO = new RepairDO();
        BeanUtils.copyProperties(query, repairDO);
        repairDO.setRepairName(GlobalContext.getUser().getName());
        // 状态 :收货送修
        repairDO.setStatus(PostSaleStatus.RECEIVE_REPAIR.name());
        this.updateById(repairDO);

        // 同步快递收件信息
        if (ReceiveTypeEnum.REPAIR.name().equals(oldRepair.getType()) || ReceiveTypeEnum.EXCHANGE.name().equals(oldRepair.getType())) {
            // 判断是否存在快递信息
            ReceiveMasterDO receiveMasterDO = this.receiveMasterDao.getOne(Wrappers.<ReceiveMasterDO>lambdaQuery()
                    .eq(ReceiveMasterDO::getExpressTrackingNo, query.getBackExpressTrackingNo()));
            if (ObjectUtils.isEmpty(receiveMasterDO)) {
                ReceiveMasterDO receiveMaster = new ReceiveMasterDO();
                receiveMaster.setType(oldRepair.getType());
                receiveMaster.setExpressCompany(query.getBackExpressCompany());
                receiveMaster.setCourier("维修换货");
                receiveMaster.setExpressTrackingNo(query.getBackExpressTrackingNo());
                receiveMaster.setRecipient(GlobalContext.getUser().getUserName());
                receiveMaster.setCreateTime(DateUtil.dateToStr(new Date()));
                this.receiveMasterDao.save(receiveMaster);
            }
        }

        return repairDO;
    }

    @Override
    public RepairDO serviceConfirm(RepairServiceConfirmQuery query) {
        // 客服确认状态:1.请选择要确认结账方式的售后信息;2.确保状态正确，并且售后已填写处理结果,才可确认,请查看状态
        RepairDO oldRepair = this.getById(query.getId());
        if (oldRepair == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_NULL);
        }
        // 客服确认只有维修+送修完成或客服确认可以点击
        if (!(ReceiveTypeEnum.REPAIR.name().equals(oldRepair.getType())
                && (PostSaleStatus.REPAIR_COMPLETED.name().equals(oldRepair.getStatus()) && StringUtils.isNotBlank(oldRepair.getResult()))
                || PostSaleStatus.SERVICE_CONFIRM.name().equals(oldRepair.getStatus()))) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_STATUS_RESULT);
        }

        RepairDO repairDO = new RepairDO();

        repairDO.setId(query.getId());
        // 付款方式
        repairDO.setPayType(query.getPayType());
        // 确认客服
        repairDO.setConfirmServiceName(GlobalContext.getUser().getName());
        // 客服售后确认时间
        repairDO.setServiceTime(DateUtil.dateToStr(new Date()));
        // 修改维修单状态 :客服确认
        repairDO.setStatus(PostSaleStatus.SERVICE_CONFIRM.name());
        // 客服对送修处理结果及收费问题进行确认
        boolean b = this.updateById(repairDO);
        return repairDO;
    }

    @Override
    public boolean pay(RepairPayQuery query) {
        // 状态是客服已确认状态
        RepairDO oldRepair = this.getById(query.getId());
        if (oldRepair == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_NULL);
        }
        // 有支付方式的且是客服确认状态的才能收款
        if (!(PostSaleStatus.SERVICE_CONFIRM.name().equals(oldRepair.getStatus())
                && StringUtils.isNotBlank(oldRepair.getPayType())
                || PostSaleStatus.HAVE_PAID.name().equals(oldRepair.getStatus()))) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_PAY);
        }
        RepairDO repairDO = new RepairDO();
        BeanUtils.copyProperties(query, repairDO);
        // 状态变更为已收款状态
        repairDO.setStatus(PostSaleStatus.HAVE_PAID.name());
        return this.updateById(repairDO);
    }

    @Override
    public RepairDO sendOff(RepairSendOffQuery query) {
        RepairDO oldRepair = this.getById(query.getId());
        if (oldRepair == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_NULL);
        }
        if (ReceiveTypeEnum.REPAIR.name().equals(oldRepair.getType())) {
            // 维修的话在已收款或发货完成可以点击
            if (!(PostSaleStatus.HAVE_PAID.name().equals(oldRepair.getStatus())
                    || PostSaleStatus.COMPLETED.name().equals(oldRepair.getStatus()))) {
                throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_SEND);
            }
        } else {
            // 换货的话，在收货送修可以点击
            if (!PostSaleStatus.REPAIR_COMPLETED.name().equals(oldRepair.getStatus())) {
                throw new GlobalDefaultException(ResponseEnum.ERROR_EXCHANGE_SEND);
            }
        }

        RepairDO repairDO = new RepairDO();
        BeanUtils.copyProperties(query, repairDO);
        // 寄出人
        repairDO.setSendName(GlobalContext.getUser().getName());
        // 寄出时间
        repairDO.setSendTime(DateUtil.dateToStr(new Date()));
        // 状态 :发货完成
        repairDO.setStatus(PostSaleStatus.COMPLETED.name());
        // 维修产品发货快递公司,快递单号,发货人
        this.updateById(repairDO);
        return repairDO;
    }

    @Override
    public RepairDO getOneByTid(String platformTradeId, String postSaleStatus) {
        return this.getOne(Wrappers.<RepairDO>lambdaUpdate()
                .eq(RepairDO::getPlatformTradeId, platformTradeId)
                // 不等于
                .ne(StringUtils.isNotBlank(postSaleStatus), RepairDO::getStatus, postSaleStatus)
                .orderByDesc(RepairDO::getCreateTime)
                .last("limit 1"));
    }

    @Override
    public RepairDO getByPlatformTradeId(String platformTradeId) {
        return this.getOne(Wrappers.<RepairDO>lambdaUpdate()
                .eq(RepairDO::getPlatformTradeId, platformTradeId)
                .orderByDesc(RepairDO::getCreateTime)
                .last("limit 1"));
    }

    @Override
    public Boolean deleteById(Long id) {
        // 状态是客服已确认状态
        RepairDO oldRepair = this.getById(id);
        if (oldRepair == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_NULL);
        }
        // 删除明细
        List<RepairExchangeDO> RepairExchangeList = this.repairExchangeService.list(Wrappers.<RepairExchangeDO>lambdaQuery().eq(RepairExchangeDO::getPlatformTradeId, oldRepair.getPlatformTradeId()));
        if (CollectionUtils.isNotEmpty(RepairExchangeList)) {
            List<Long> repairExchangeIds = RepairExchangeList.stream().map(repairExchangeDO -> repairExchangeDO.getId()).collect(Collectors.toList());
            this.repairExchangeService.removeByIds(repairExchangeIds);
        }

        // 判断客服下单、收货送修、送修完成
        if (!(PostSaleStatus.SERVICE_ORDER.name().equals(oldRepair.getStatus())
                || PostSaleStatus.RECEIVE_REPAIR.name().equals(oldRepair.getStatus())
                || PostSaleStatus.REPAIR_COMPLETED.name().equals(oldRepair.getStatus()))) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_DELETE);
        }
        return this.removeById(id);
    }

    @Override
    public PrintVO repairPrint(Long id) {
        RepairDO oldRepair = this.getById(id);
        if (oldRepair == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_NULL);
        }
        RepairVO repairVO = new RepairVO();
        BeanUtils.copyProperties(oldRepair, repairVO);
        repairVO.setType(ReceiveTypeEnum.REPAIR.name().equals(repairVO.getType()) ? "维修" : "检测");

        // 获取打印模板
        PrintTemplateDO printTemplateDO = this.printTemplateService.getOneByResourceName("维修送修单");
        if (printTemplateDO == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_TEMPLATE);
        }
        PrintVO printVO = new PrintVO();
        printVO.setTemplateURL(printTemplateDO.getResourceUrl());
        printVO.setData(repairVO);
        return printVO;
    }

    @Override
    public PrintVO<?> sendOutPrint(Long id, String expressCompany) {
        RepairDO oldRepair = this.getById(id);
        if (oldRepair == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_NULL);
        }
        // 不是维修类型不允许操作
        if (!ReceiveTypeEnum.REPAIR.name().equals(oldRepair.getType())) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_TYPE);
        }
        // 状态不是收款完成或发货完成不允许操作
        if (!PostSaleStatus.HAVE_PAID.name().equals(oldRepair.getStatus())
                && !PostSaleStatus.COMPLETED.name().equals(oldRepair.getStatus())) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_STATUS_PRINT);
        }
        PlatformDO platform = this.platformService.getPlatformById(oldRepair.getPlatformId());

        // 1.创建订单
        TradeDO tradeDO = this.getTradeByRepair(oldRepair);

        // 2.创建明细
        List<OrderDO> orderDOS = this.getOrderByRepair(oldRepair);

        // 3.生成快递单号（保存快递信息）
        ExpressVO expressVO = this.logisticsService.createOrder(platform, tradeDO, orderDOS, expressCompany);

        // 4.保存快递信息
        this.expressLogService.saveExpressLog(tradeDO, ExpressTypeEnum.REPAIR.name(), 0);

        // 5.更新维修订单
        this.updateRepair(id, expressVO.getWayBillNo(), expressCompany);

        // 6.返回快递打印信息
        return this.tradeService.getExpressPrintData(platform, tradeDO, expressVO);
    }

    @Override
    public void agentSaleExchangeGoods(AgentSaleExchangeGoodsQuery query) {
        // 查询t_admin_role_platform配置的全部代销平台，加入oppo
        Set<Integer> agentSalePlatforms = this.rolePlatformService.list().stream()
                .map(p -> Integer.parseInt(p.getPlatformId())).collect(Collectors.toSet());
        agentSalePlatforms.add(2013);
        // 新订单号是否存在
        TradeDO newTrade = this.tradeService.getTradeByTradeId(query.getNewPlatformTradeId());
        if (newTrade != null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_EXCHANGE_EXIST);
        }
        // 判断新退货单号是否存在
        String newRefundId = query.getPlatformTradeId() + "41";
        ReturnMasterDO returnMaster = this.returnMasterService.getReturnMasterByRefundId(newRefundId);
        if (returnMaster != null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_RETURN_EXCHANGE_EXIST);
        }
        // 是否存在维修换货
        RepairDO oldRepair = this.getOneByTid(query.getPlatformTradeId(), null);
        if (oldRepair == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_NULL);
        }
        // 换货类型
        if (!ReceiveTypeEnum.EXCHANGE.name().equals(oldRepair.getType())) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_NOT_EXCHANGE);
        }
        // 代销平台
        if (!agentSalePlatforms.contains(oldRepair.getPlatformId())) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_NOT_PLATFORM_OPPO);
        }
        // 是否是客服下单状态,收货送修状态
        if (!(PostSaleStatus.SERVICE_ORDER.name().equals(oldRepair.getStatus())
                || PostSaleStatus.RECEIVE_REPAIR.name().equals(oldRepair.getStatus())
                || PostSaleStatus.REPAIR_COMPLETED.name().equals(oldRepair.getStatus()))) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_UPDATE);
        }
        // 退货数量和发货数量一致
        int exchangeTotalNum = query.getGiftInfoQueries().stream().mapToInt(GiftInfoQuery::getNum).sum();
        int returnTotalNum = query.getAgentSaleReturnDetailQueries().stream().mapToInt(AgentSaleReturnDetailQuery::getReturnNum).sum();
        if (exchangeTotalNum != returnTotalNum || returnTotalNum == 0) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_EXCHANGEGOODS_NUM);
        }

        List<AgentSaleReturnDetailQuery> agentSaleReturnDetailQueries = query.getAgentSaleReturnDetailQueries();
        // 获取所有退货物料代码
        List<String> erpCodes = agentSaleReturnDetailQueries.stream()
                .map(AgentSaleReturnDetailQuery::getErpCode).collect(Collectors.toList());
        // 根据物料代码获取物料信息
        List<ProductInfoDO> productInfoDOS = this.productInfoService.getByErpCodes(erpCodes);
        // 获取所有机器物料信息
        List<ProductInfoDO> jqProductInfoDOS = productInfoDOS.stream()
                .filter(productInfoDO -> productInfoDO.getMaterialType() == 1).collect(Collectors.toList());
        if (jqProductInfoDOS.size() > 0) {
            // 所有退货机器的物料代码
            List<String> jqErpCodes = jqProductInfoDOS.stream().map(ProductInfoDO::getProductId).collect(Collectors.toList());
            // 获取退货机器信息
            List<AgentSaleReturnDetailQuery> jqReturnDetails = agentSaleReturnDetailQueries.stream()
                    .filter(o -> jqErpCodes.contains(o.getErpCode())).collect(Collectors.toList());
            // 是否有退货条码
            AgentSaleReturnDetailQuery agentSaleReturnDetailQuery = jqReturnDetails.stream()
                    .filter(r -> StringUtils.isBlank(r.getBarcode())).findAny().orElse(null);
            if (agentSaleReturnDetailQuery != null) {
                throw new GlobalDefaultException(ResponseEnum.ERROR_EXCHANGE_BARCODE_EMPTY);
            }
            for (AgentSaleReturnDetailQuery jqReturnDetail : jqReturnDetails) {
                // 根据条码找发货记录
                OrderBarcodeDO orderBarcodeDO = this.orderBarcodeService.getLastOneByBarcode(jqReturnDetail.getBarcode());
                if (orderBarcodeDO == null) {
                    // 根据退货条码找箱号
                    ProductDO productDO = this.productService.getProductBarcode(jqReturnDetail.getBarcode());
                    if (productDO == null) {
                        // 找不到条码
                        throw new GlobalDefaultException(ResponseEnum.ERROR_BARCODE_NOT_EXIST);
                    }
                    if (StringUtils.isBlank(productDO.getCartonNo())) {
                        // 没有箱号
                        throw new GlobalDefaultException(ResponseEnum.ERROR_CARTONNO_NOT_EXIST);
                    }
                    // 根据箱号找到原始订单号
                    orderBarcodeDO = this.orderBarcodeService.getLastOneByBarcode(productDO.getCartonNo());
                    if (orderBarcodeDO == null) {
                        // 没有发货记录
                        throw new GlobalDefaultException(ResponseEnum.ERROR_CARTONNO_NOT_SHIP);
                    }
                }
                // 根据原始订单号找到订单判断是否是代销平台
                TradeDO existTradeDO = this.tradeService.getExistTradeDO(orderBarcodeDO.getPlatformTradeId());
                if (!agentSalePlatforms.contains(existTradeDO.getPlatformId())) {
                    // 原始订单不是代销平台的订单
                    throw new GlobalDefaultException(ResponseEnum.ERROR_ORIGINAL_NOT_OPPO);
                }
            }
        }

        // 1.生成换货退货单(生成退货主表,生成退货明细)
        ReturnMasterDO returnMasterDO = this.createReturnMaster(oldRepair, newRefundId);
        //代销换货生成的退货订单也是部分状态无需处理的
        returnMasterDO.setPartReturnStatus(PartReturnStatus.NOT_NEED.name());
        List<ReturnDetailsDO> returnDetailsDOS = this.CreateReturnDetails(query, oldRepair);
        this.returnMasterService.save(returnMasterDO);
        this.returnDetailsService.saveBatch(returnDetailsDOS);

        // 2.生成换货发货单
        TradeDO oldTrade = this.createOldTrade(oldRepair);
        this.tradeService.createExchangeGoods(query, oldTrade);

        // 3.更新维修换货状态

    }

    @Override
    public void repairCompleted(RepairCompletedQuery query) {
        RepairDO oldRepair = this.getById(query.getId());
        if (oldRepair == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_NULL);
        }
        // 收货送修只能在收货送修+送修完成可以点击
        if (!(PostSaleStatus.RECEIVE_REPAIR.name().equals(oldRepair.getStatus())
                || PostSaleStatus.REPAIR_COMPLETED.name().equals(oldRepair.getStatus()))) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_REPAIR_RECEIVE);
        }
        RepairDO repairDO = new RepairDO();
        BeanUtils.copyProperties(query, repairDO);
        repairDO.setIsExchange(ReturnTypeEnum.EXCHANGE.name().equals(oldRepair.getType()) ? query.getIsExchange() : null);
        repairDO.setSalesAfterName(GlobalContext.getUser().getName());
        // 售后时间
        repairDO.setSalesAfterTime(DateUtil.dateToStr(new Date()));
        // 状态 :收货送修
        repairDO.setStatus(PostSaleStatus.REPAIR_COMPLETED.name());
        this.updateById(repairDO);
    }

    @Override
    public List<RepairExchangeDO> getRepairExchangeList(RepairExchangeListQuery query) {
        return this.repairExchangeService.list(Wrappers.<RepairExchangeDO>lambdaQuery()
                .eq(RepairExchangeDO::getPlatformTradeId, query.getPlatformTradeId())
                .orderByDesc(RepairExchangeDO::getId));
    }

    /**
     * 创建退货明细信息
     *
     * @param query
     * @return
     */
    private List<ReturnDetailsDO> CreateReturnDetails(AgentSaleExchangeGoodsQuery query, RepairDO repairDO) {
        List<ReturnDetailsDO> returnDetailsDOS = Lists.newArrayList();
        for (AgentSaleReturnDetailQuery agentSaleReturnDetailQuery : query.getAgentSaleReturnDetailQueries()) {
            ReturnDetailsDO returnDetailsDO = new ReturnDetailsDO();
            returnDetailsDO.setPlatformId(repairDO.getPlatformId());
            returnDetailsDO.setPlatformName(repairDO.getPlatformName());
            returnDetailsDO.setPlatformTradeId(repairDO.getPlatformTradeId());
            returnDetailsDO.setPlatformOrderId(GlobalUtil.getUUID());
            returnDetailsDO.setBarcode(StringUtils.isNotBlank(agentSaleReturnDetailQuery.getBarcode()) ? agentSaleReturnDetailQuery.getBarcode() : agentSaleReturnDetailQuery.getErpCode());
            returnDetailsDO.setErpCode(agentSaleReturnDetailQuery.getErpCode());
            returnDetailsDO.setErpName(agentSaleReturnDetailQuery.getErpName());
            returnDetailsDO.setUnit(agentSaleReturnDetailQuery.getUnit());
            returnDetailsDO.setNum(agentSaleReturnDetailQuery.getReturnNum());
            returnDetailsDO.setPrice(BigDecimal.ZERO);
            returnDetailsDO.setPriceTotal(BigDecimal.ZERO);
            returnDetailsDO.setPayment(BigDecimal.ZERO);
            returnDetailsDO.setPaymentTime(DateUtil.strToDate(repairDO.getPaymentTime()));
            returnDetailsDO.setIsReturn(1);
            returnDetailsDO.setReturnNum(agentSaleReturnDetailQuery.getReturnNum());
            returnDetailsDOS.add(returnDetailsDO);
        }
        return returnDetailsDOS;
    }

    /**
     * 创建退货主信息
     *
     * @param oldRepair
     * @param newRefundId
     * @return
     */
    private ReturnMasterDO createReturnMaster(RepairDO oldRepair, String newRefundId) {
        ReturnMasterDO returnMasterDO = new ReturnMasterDO();
        returnMasterDO.setPlatformId(oldRepair.getPlatformId());
        returnMasterDO.setPlatformName(oldRepair.getPlatformName());
        returnMasterDO.setRefundId(newRefundId);
        returnMasterDO.setPlatformTradeId(oldRepair.getPlatformTradeId());
        returnMasterDO.setPaymentTime(oldRepair.getPaymentTime());
        returnMasterDO.setBuyerNickname(oldRepair.getBuyerNick());
        returnMasterDO.setReceiverName(oldRepair.getReceiverName());
        returnMasterDO.setScanner(GlobalContext.getUser().getName());
        returnMasterDO.setRefundStatus(RefundStatusEnum.NOT.name());
        returnMasterDO.setErpPostStatus(0);
        returnMasterDO.setPayment(BigDecimal.ZERO);
        returnMasterDO.setApplyAmount(BigDecimal.ZERO);
        returnMasterDO.setAutoRefundStatus(AutoRefundStatusEnum.NOT.name());
        returnMasterDO.setRealAmount(BigDecimal.ZERO);
        returnMasterDO.setType(ReturnTypeEnum.EXCHANGE.name());
        returnMasterDO.setErpPostManual(0);
        returnMasterDO.setIsAllReturn(1);
        returnMasterDO.setExpressTrackingNo(oldRepair.getBackExpressTrackingNo());
        returnMasterDO.setIsEndProductStore(1);
        returnMasterDO.setCreateTime(DateUtil.dateToStr(new Date()));
        this.returnMasterService.checkExchangeAndSetRemark(returnMasterDO);
        return returnMasterDO;
    }

    /**
     * 创建原始订单
     *
     * @param oldRepair
     * @return
     */
    private TradeDO createOldTrade(RepairDO oldRepair) {
        TradeDO tradeDO = new TradeDO();
        // 旧订单号
        tradeDO.setPlatformTradeId(oldRepair.getPlatformTradeId());
        tradeDO.setPlatformName(oldRepair.getPlatformName());
        tradeDO.setPlatformId(oldRepair.getPlatformId());
        tradeDO.setBuyerNickname(oldRepair.getBuyerNick());
        tradeDO.setReceiverName(oldRepair.getReceiverName());
        tradeDO.setReceiverMobile(oldRepair.getTelephone());
        tradeDO.setReceiverProvince(oldRepair.getProvince());
        tradeDO.setReceiverCity(oldRepair.getCity());
        tradeDO.setReceiverDistrict(oldRepair.getDistrict());
        tradeDO.setReceiverAddress(oldRepair.getAddress());
//        tradeDO.setInvoiceTitle(oldTrade.getInvoiceTitle());
//        tradeDO.setInvoiceRegisterNo(oldTrade.getInvoiceRegisterNo());
//        tradeDO.setInvoiceBankAccount(oldTrade.getInvoiceBankAccount());
//        tradeDO.setInvoiceAddress(oldTrade.getInvoiceAddress());
        return tradeDO;
    }

    /**
     * 更新维修订单
     *
     * @param id             维修主键id
     * @param wayBillNo      快递单号
     * @param expressCompany 快递公司
     */
    private void updateRepair(Long id, String wayBillNo, String expressCompany) {
        RepairDO update = new RepairDO();
        update.setId(id);
        update.setExpressCompany(expressCompany);
        update.setExpressTrackingNo(wayBillNo);
        update.setSendName(GlobalContext.getUser().getName());
        update.setSendTime(DateUtil.dateToStr(new Date()));
        update.setStatus(PostSaleStatus.COMPLETED.name());
        this.updateById(update);
    }

    /**
     * 根据维修信息创建寄出订单信息
     *
     * @param repair 维修信息
     * @return 订单信息
     */
    private TradeDO getTradeByRepair(RepairDO repair) {
        TradeDO tradeDO = new TradeDO();
        tradeDO.setPlatformTradeId(repair.getPlatformTradeId());
        PlatformDO platform = this.platformService.getPlatformByName(repair.getPlatformName());
        tradeDO.setPlatformId(platform != null ? platform.getPlatformId() : null);
        tradeDO.setPlatformName(repair.getPlatformName());
        tradeDO.setBuyerNickname(repair.getBuyerNick());
        tradeDO.setReceiverName(repair.getReceiverName());
        tradeDO.setReceiverMobile(repair.getTelephone());
        tradeDO.setReceiverProvince(repair.getProvince());
        tradeDO.setReceiverCity(repair.getCity());
        tradeDO.setReceiverDistrict(repair.getDistrict());
        tradeDO.setReceiverAddress(repair.getAddress());
        return tradeDO;
    }

    /**
     * 根据维修信息创建订单明细信息
     *
     * @param repair 维修信息
     * @return 订单明细信息
     */
    private List<OrderDO> getOrderByRepair(RepairDO repair) {
        OrderDO orderDO = new OrderDO();
        PlatformDO platform = this.platformService.getPlatformByName(repair.getPlatformName());
        orderDO.setPlatformId(platform != null ? platform.getPlatformId() : null);
        orderDO.setPlatformName(repair.getPlatformName());
        orderDO.setPlatformTradeId(repair.getPlatformTradeId());
        orderDO.setPlatformOrderId(GlobalUtil.getUUID());
        orderDO.setErpName(repair.getProductName());
        orderDO.setNum(repair.getNum());
        orderDO.setOrderType(1);
        orderDO.setMaterialType(1);
        orderDO.setPrice(BigDecimal.ZERO);
        return Lists.newArrayList(orderDO);
    }

    @Override
    public RepairDO result(RepairResultQuery query) {
        RepairDO repairDO = new RepairDO();
        BeanUtils.copyProperties(query, repairDO);
        // 送修后的处理结果以及判定是否收费
        this.updateById(repairDO);
        return repairDO;
    }

    /**
     * 导出excel文件到minio
     *
     * @param data     数据
     * @param clazz    导出实体
     * @param fileName 文件名
     * @param <T>      实体
     * @return 文件路径
     */
    private <T> String exportExcelToMinio(List<T> data, Class<T> clazz, String fileName) {
        // 文件名无需带上根路径 "/"
        String objectName = "download/" + DateUtil.nowDateStr("yyyyMMdd") + "/" + fileName + ".xlsx";
        ByteArrayInputStream byteArrayInputStream = ExcelUtil.writeDataToStream(data, clazz);
        try {
            this.minioManager.putObject(objectName, byteArrayInputStream);
        } catch (Exception e) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_APPOINTMENT_FILE_UPLOAD, e);
        }
        return objectName;
    }

}
