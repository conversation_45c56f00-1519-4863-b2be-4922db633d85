package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.pdd.pop.sdk.http.PopClient;
import com.pdd.pop.sdk.http.PopHttpClient;
import com.pdd.pop.sdk.http.api.pop.request.PddNextoneLogisticsWarehouseUpdateRequest;
import com.pdd.pop.sdk.http.api.pop.request.PddNextoneLogisticsWarehouseUpdateRequest.Request;
import com.pdd.pop.sdk.http.api.pop.request.PddRefundAgreeRequest;
import com.pdd.pop.sdk.http.api.pop.response.PddNextoneLogisticsWarehouseUpdateResponse;
import com.pdd.pop.sdk.http.api.pop.response.PddRefundAgreeResponse;
import com.taobao.api.domain.Order;
import com.taobao.api.domain.Refund;
import com.taobao.api.domain.Trade;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.enums.*;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.executor.command.WorkWechatAlertSenderCmdExe;
import com.xtc.onlineretailers.mapper.master.ReturnMasterMapper;
import com.xtc.onlineretailers.pojo.entity.*;
import com.xtc.onlineretailers.pojo.query.*;
import com.xtc.onlineretailers.pojo.vo.LogisticInfoVO;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.pojo.vo.PartReturnDetailVO;
import com.xtc.onlineretailers.refactor.executor.query.LogisticsRouteQryExe;
import com.xtc.onlineretailers.refactor.job.BaseTask;
import com.xtc.onlineretailers.repository.ReceiveMasterDao;
import com.xtc.onlineretailers.repository.RepairDao;
import com.xtc.onlineretailers.service.*;
import com.xtc.onlineretailers.util.*;
import com.xtc.springboot.pojo.entity.GlobalContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class ReturnMasterServiceImpl extends ServiceImpl<ReturnMasterMapper, ReturnMasterDO> implements ReturnMasterService {

    private static String paramName = "all_refund_message";

    @Resource
    private PlatformRefundService platformRefundService;

    @Resource
    private ReturnDetailsService returnDetailsService;

    @Resource
    private PlatformService platformService;

    @Resource
    private OrderService orderService;

    @Resource
    private WarehouseService warehouseService;

    @Resource
    private TradeService tradeService;

    @Resource
    private PlatformRefundServiceImpl platformRefundServiceImpl;

    @Resource
    private RepairDao repairsDao;

    @Resource
    private SystemParamService systemParamService;

    @Resource
    private AppointmentDownloadService appointmentDownloadService;

    @Resource
    @Lazy
    private AsyncService asyncService;

    @Resource
    private ReturnMasterMapper returnMasterMapper;

    @Resource
    private ReceiveMasterService receiveMasterService;

    @Resource
    private ServiceInformationService serviceInformationService;

    @Resource
    private LogisticsRouteQryExe logisticsRouteQryExe;

    @Resource
    private TicTokUtil ticTokUtil;

    @Resource
    private WorkWechatAlertSenderCmdExe workWechatAlertSenderCmdExe;

    @Resource
    private ProductInfoService productInfoService;

    @Resource
    private ReceiveMasterDao receiveMasterDao;

    @Override
    public PaginationVO<ReturnMasterDO> getList(ReturnMasterQuery query) {
        // 根据条码查询退货订单号
        if (StringUtils.isNotBlank(query.getBarcode())) {
            List<String> returnPlatformTradeIds = getPlatformTradeIds(query.getBarcode());
            if (CollectionUtils.isNotEmpty(returnPlatformTradeIds)) {
                query.setReturnPlatformTradeIds(returnPlatformTradeIds);
            }
        }
        IPage<ReturnMasterDO> result = this.page(query.createPage(), this.getReturnMasterDOWrapper(query));
        return PaginationVO.newVO(result);
    }

    @Override
    public void syncRefundInfo() {
        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .isNull(ReturnMasterDO::getApplyAmount)
                .orderByAsc(ReturnMasterDO::getId)
                .last("limit 100");
        List<ReturnMasterDO> refunds = this.list(wrapper);
        if (refunds == null) {
            return;
        }

        List<ReturnMasterDO> updateList = refunds.stream().map(refund -> {
            Wrapper<PlatformRefundDO> platformRefundWrapper = Wrappers.<PlatformRefundDO>lambdaQuery()
                    .eq(PlatformRefundDO::getPlatformTradeId, refund.getPlatformTradeId())
                    .orderByDesc(PlatformRefundDO::getRefundCreateTime)
                    .last("limit 1");
            PlatformRefundDO platformRefund = this.platformRefundService.getOne(platformRefundWrapper);
            if (platformRefund != null) {
                ReturnMasterDO update = new ReturnMasterDO();
                update.setId(refund.getId());
                update.setApplyAmount(platformRefund.getRefundFee());
                update.setReason(platformRefund.getReason());
                update.setRefundTime(platformRefund.getRefundCreateTime());
                return refund;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        this.updateBatchById(updateList);
    }

    @Override
    public void delete(String platformTradeId) {
        // 获取主表信息
        ReturnMasterDO returnMasterDO = this.getReturnMasterByTradeId(platformTradeId);

        // 操作限制
        List<String> allowStatus = Lists.newArrayList(PartReturnStatus.NOT_NEED.name(),
                PartReturnStatus.STORE_SCAN.name(), PartReturnStatus.SERVE_CONFIRM.name());
        if (returnMasterDO == null
                || returnMasterDO.getErpPostManual() == 1
                || returnMasterDO.getErpPostStatus() == 1
                || RefundStatusEnum.HAVE_REFUNDED.name().equals(returnMasterDO.getRefundStatus())
                || AutoRefundStatusEnum.AUTO.name().equals(returnMasterDO.getAutoRefundStatus())
                || !allowStatus.contains(returnMasterDO.getPartReturnStatus())) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_NOT_ALLOW_DELETE_RETURN);
        }

        // 删除主和明细表操作
        this.removeById(returnMasterDO.getId());
        this.returnDetailsService.remove(Wrappers.<ReturnDetailsDO>lambdaQuery()
                .eq(ReturnDetailsDO::getPlatformTradeId, platformTradeId));
        //还原扫描表记录
        ReceiveMasterDO receiveMasterDO = this.receiveMasterService.getOne(Wrappers.<ReceiveMasterDO>lambdaQuery().eq(ReceiveMasterDO::getPlatformTradeId, returnMasterDO.getPlatformTradeId()));
        if (receiveMasterDO != null) {
            receiveMasterDO.setType(ReceiveTypeEnum.SCAN.name());
            receiveMasterDO.setScanner("");
            receiveMasterDO.setPlatformTradeId("");
            this.receiveMasterService.updateById(receiveMasterDO);
        }
        //更新主表状态
        TradeDO tradeDO = this.tradeService.getTradeByTradeId(returnMasterDO.getPlatformTradeId());
        if (tradeDO == null) {
            return;
        }
        if (TradeTypeEnum.EXCHANGE_GOODS.name().equals(tradeDO.getType())) {
            return;
        }
        tradeDO.setStatusOrder(OrderStatus.HAS_SHIP.name());
        this.tradeService.updateTrade(tradeDO);
        log.info("订单 {}，删除退货单，更新订单已发货", tradeDO.getPlatformTradeId());
    }

    @Override
    public void addRemark(Long id, String remark) {
        ReturnMasterDO returnMasterDO = new ReturnMasterDO();
        returnMasterDO.setId(id);
        returnMasterDO.setMemoOperation(remark);
        this.updateById(returnMasterDO);
    }

    @Override
    public void updateErpInfo(TradeUpdateErpQuery query) {
        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .eq(ReturnMasterDO::getPlatformTradeId, query.getPlatformTradeId())
                .eq(ReturnMasterDO::getPlatformId, query.getPlatformId());
        ReturnMasterDO refund = new ReturnMasterDO();
        BeanUtils.copyProperties(query, refund);
        this.update(refund, wrapper);
    }

    @Override
    public List<String> getSyncErpIdRefundIds() {
        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .select(ReturnMasterDO::getRefundId)
                .ne(ReturnMasterDO::getPlatformId, GlobalConstant.PLATFORM_XTC_MALL_AGENT)
                .eq(ReturnMasterDO::getErpPostStatus, 1)
                .and(trade -> trade.isNull(ReturnMasterDO::getErpId).or().eq(ReturnMasterDO::getErpId, "0"))
                .orderByAsc(ReturnMasterDO::getPaymentTime)
                .last(BaseTask.getShardingLimitSql(1000));
        return this.listObjs(wrapper, Object::toString);
    }

    @Override
    public void updateReturnMaster(ReturnMasterDO update) {
        this.update(update, Wrappers.<ReturnMasterDO>lambdaQuery().eq(ReturnMasterDO::getRefundId, update.getRefundId()));
    }

    @Override
    public List<ReturnMasterDO> getErpPostRefund(String erpPostTimeStart, String
            erpPostTimeEnd, List<Integer> platformIds, String account) {
        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .gt(ReturnMasterDO::getErpPostTime, erpPostTimeStart)
                .le(ReturnMasterDO::getErpPostTime, erpPostTimeEnd)
                .eq(ReturnMasterDO::getErpPostStatus, 1)
                .eq(ReturnMasterDO::getType, "RETURN")
                .eq(StringUtils.isNotBlank(account), ReturnMasterDO::getBuyerNickname, account)
                .in(ReturnMasterDO::getPlatformId, platformIds);

        return this.list(wrapper);
    }

    @Override
    public void checkExchangeAndSetRemark(ReturnMasterDO returnMasterDO) {
        // 检查是否换货
        this.repairsDao.getByTradeId(returnMasterDO.getPlatformTradeId())
                .ifPresent(repairDO -> returnMasterDO.setIsExchange(1));
        // 查询原单设置卖家备注
        TradeDO tradeDO = this.tradeService.getTradeByTradeId(returnMasterDO.getPlatformTradeId());
        if (tradeDO != null) {
            returnMasterDO.setSellerRemark(tradeDO.getSellerRemark());
        }
    }

    @Override
    public void refundNotification() {
        ArrayList<String> expressCompanyList = Lists.newArrayList("圆通速递", "顺丰速运", "京东快递", "EMS");
        Wrapper<TradeDO> wrapper = Wrappers.<TradeDO>lambdaQuery()
                .eq(TradeDO::getStatusOrder, OrderStatus.HAS_SHIP)
                .eq(TradeDO::getType, TradeTypeEnum.NORMAL)
                .isNotNull(TradeDO::getExpressCompany)
                .isNotNull(TradeDO::getExpressTrackingNo)
                .orderByDesc(TradeDO::getPaymentTime)
                .last("limit 500");
        List<TradeDO> list = this.tradeService.list(wrapper);

        // 企业发送通知人
        String paramValue = this.systemParamService.getSystemParamByName(paramName);
        list.forEach(tradeDO -> {
            try {
                List<PlatformRefundDO> refundList = platformRefundService.getRefundList(tradeDO.getPlatformTradeId());
                if (CollectionUtils.isEmpty(refundList)) {
                    log.warn("订单号:{}，没有退款信息", tradeDO.getPlatformTradeId());
                    return;
                }
                if (!expressCompanyList.contains(tradeDO.getExpressCompany())) {
                    log.warn("订单号:{}，快递公司不是圆通速递，京东快递，顺丰速运或EMS,不发全退通知", tradeDO.getPlatformTradeId());
                    return;
                }
                // 统计退款金额
                BigDecimal refundPayment = refundList.stream()
                        .filter(r -> GlobalConstant.REFUND_STATUS_COMPLETED.contains(r.getRefundStatus()))
                        .filter(r -> ObjectUtils.isNotEmpty(r.getRefundFee()))
                        .map(PlatformRefundDO::getRefundFee)
                        .reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO);

                // 付款金额+积分
                BigDecimal payment = Stream.of(tradeDO.getPayment(), tradeDO.getPointAlipayJf(), tradeDO.getPointPlatform())
                        .reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO);
                // 订单全退
                if (refundPayment.compareTo(payment) == 0) {
                    List<LogisticInfoVO> routes = logisticsRouteQryExe.execute(tradeDO.getExpressCompany(), tradeDO.getExpressTrackingNo());
                    routes.stream()
                            .filter(meno -> meno.getMemo().contains("签收") && !meno.getMemo().contains("退回"))
                            .findAny()
                            .ifPresent(m -> XtcMessageSender.cardTextMessage(paramValue, "订单号：" + tradeDO.getPlatformTradeId() + "，订单已签收但已完成全额退款，请核实"));
                }
            } catch (Exception e) {
                log.error("全退已签收订单，通知异常，订单号：{}，message：{}", tradeDO.getPlatformTradeId(), e.getMessage(), e);
            }
        });
    }

    /**
     * 查询需要处理碎屏宝的退货分页列表
     * <p>筛选出符合条件的订单：已收货、已过账、退货类型</p>
     *
     * @param current             分页大小
     * @param pageSize            分页数量
     * @param erpPostStartTime    过账开始时间
     * @param erpPostStartEndTime 过账结束时间
     * @return 退货分页列表
     */
    @Override
    public IPage<ReturnMasterDO> refundExtendedWarrantyHandle(int current, int pageSize,
                                                              LocalDateTime erpPostStartTime, LocalDateTime erpPostStartEndTime) {
        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .between(ReturnMasterDO::getErpPostTime, erpPostStartTime, erpPostStartEndTime)
                .eq(ReturnMasterDO::getIsEndProductStore, 1)
                .eq(ReturnMasterDO::getErpPostStatus, 1)
                .eq(ReturnMasterDO::getType, "RETURN")
                .orderByDesc(ReturnMasterDO::getErpPostTime);
        Page<ReturnMasterDO> page = new Page<>(current, pageSize, false);
        return this.page(page, wrapper);
    }

    /**
     * 查询需要处理延保的退货分页列表
     * <p>筛选出符合条件的订单：已收货、已过账、换货类型</p>
     *
     * @param current             分页大小
     * @param pageSize            分页数量
     * @param erpPostStartTime    过账开始时间
     * @param erpPostStartEndTime 过账结束时间
     * @return 退货分页列表
     */
    @Override
    public IPage<ReturnMasterDO> exchangeExtendedWarrantyJob(int current, int pageSize,
                                                             LocalDateTime erpPostStartTime, LocalDateTime erpPostStartEndTime) {
        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .between(ReturnMasterDO::getErpPostTime, erpPostStartTime, erpPostStartEndTime)
                .eq(ReturnMasterDO::getIsEndProductStore, 1)
                .eq(ReturnMasterDO::getErpPostStatus, 1)
                .eq(ReturnMasterDO::getType, "EXCHANGE")
                .orderByDesc(ReturnMasterDO::getErpPostTime);
        Page<ReturnMasterDO> page = new Page<>(current, pageSize, false);
        return this.page(page, wrapper);
    }

    @Override
    public void throwERP(ReturnThrowERPQuery query) {
        // 获取仓库信息
        String locationId;
        String subinvCode;
        if (GlobalConstant.WAREHOUSE_LOCAL.equals(query.getWarehouseStorage())) {
            locationId = GlobalConstant.DEFAULT_LOCAL_ID;
            subinvCode = GlobalConstant.DEFAULT_SUBINV_CODE;
        } else {
            if (GlobalConstant.WAREHOUSE_JD.equals(query.getWarehouseStorage())) {
                locationId = GlobalConstant.WAREHOUSE_JD;
                subinvCode = GlobalConstant.WAREHOUSE_SF;
            } else if (query.getWarehouseStorage().equals("4598")) {
                locationId = "4598";
                subinvCode = GlobalConstant.DEFAULT_SUBINV_CODE;
            } else {
                // 获取仓库信息
                WarehouseDO warehouseDO = this.warehouseService.getWarehouseByStorage(query.getWarehouseStorage());
                // 判断仓库是否在用
                if (warehouseDO == null || !"1".equals(warehouseDO.getIsUse())) {
                    throw new GlobalDefaultException(ResponseEnum.ERROR_WAREHOUSE_NULL, "或者仓库没有使用,请检查!");
                }
                locationId = warehouseDO.getLocationId();
                subinvCode = GlobalConstant.WAREHOUSE_SF;
            }
        }

        List<ReturnMasterDO> returnMasterDOS = Lists.newArrayList();
        for (Integer id : query.getIds()) {
            ReturnMasterDO returnMasterDO = this.getById(id);
            if (returnMasterDO == null) {
                throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "该退货数据不存在,请检查!");
            }
            if (returnMasterDO.getType().equals("EXCHANGE") ||
                    (returnMasterDO.getIsAllReturn() != 1 &&
                            !PartReturnStatus.ADJUST.name().equals(returnMasterDO.getPartReturnStatus()))) {
                throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "换货会自动过账，部分退货需要确认订单后才能抛账，无需抛ERP!");
            }
            // 已过账
            if (returnMasterDO.getErpPostManual() == 1 || returnMasterDO.getErpPostStatus() == 1) {
                throw new GlobalDefaultException(ResponseEnum.UPDATE_FAIL, "订单号为" + returnMasterDO.getPlatformTradeId() + "的订单已过账,请检查!");
            }
            // 已收货，校验收货仓库
            if (returnMasterDO.getIsEndProductStore() == 1) {
                if ("顺丰仓库".equals(returnMasterDO.getReceiptWarehouse()) && !query.getWarehouseStorage().equals("4598")) {
                    String message = String.format("订单%s 顺丰仓库收货只能选择网销顺丰坏料仓过账,请检查!", returnMasterDO.getPlatformTradeId());
                    throw new GlobalDefaultException(ResponseEnum.UPDATE_FAIL, message);
                }
                if ("本地仓库".equals(returnMasterDO.getReceiptWarehouse()) && !query.getWarehouseStorage().equals("005")) {
                    String message = String.format("订单%s 本地仓库收货只能选择本地仓库过账,请检查!", returnMasterDO.getPlatformTradeId());
                    throw new GlobalDefaultException(ResponseEnum.UPDATE_FAIL, message);
                }
            }
            ReturnMasterDO update = new ReturnMasterDO();
            update.setId(returnMasterDO.getId());
            update.setErpPostManual(1);
            update.setErpPostManualTime(new Date());
            update.setLocationId(locationId);
            update.setSubinvCode(subinvCode);
            if (query.getWarehouseStorage().equals("4598")) {
                update.setReceiptWarehouse("顺丰仓库");
            }
            returnMasterDOS.add(update);
        }
        this.updateBatchById(returnMasterDOS);
    }

    @Override
    public ReturnMasterDO getReturnMasterByTradeId(String platformTradeId) {
        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .eq(ReturnMasterDO::getPlatformTradeId, platformTradeId)
                .orderByDesc(ReturnMasterDO::getId)
                .last("limit 1");
        return this.getOne(wrapper);
    }

    @Override
    public void excelExport(ReturnMasterQuery query) {
        // 当导出条件都为空时默认只导出过账时间一周的数据
        if (ObjectUtils.isEmpty(query)) {
            query.setErpPostTimeBegin(DateUtil.beginDayOfCurrentMonth());
            query.setErpPostTimeEnd(DateUtil.lastDayOfCurrentMonth());
        }
        // 是否满足查询条件
        Wrapper<ReturnMasterDO> wrapper = this.getReturnMasterDOWrapper(query);
        // 文件名
        String fileName = StringUtils.isNotBlank(query.getFileName()) ? query.getFileName() + System.currentTimeMillis() : "" + System.currentTimeMillis();
        // 保存预约信息
        Long appointmentDownloadId = this.appointmentDownloadService.saveAppointmentDownload(fileName, AppointmentDownloadType.RETURN.name());
        // 异步导出Excel到本地
        this.asyncService.returnInfoExcelExport(wrapper, appointmentDownloadId, GlobalContext.getUser(), fileName);
    }

    @Override
    public void receiptExport(ReturnMasterQuery query) {
        // 当导出条件都为空时默认只导出过账时间一周的数据
        if (ObjectUtils.isEmpty(query)) {
            query.setErpPostTimeBegin(DateUtil.beginDayOfCurrentMonth());
            query.setErpPostTimeEnd(DateUtil.lastDayOfCurrentMonth());
        }
        // 是否满足查询条件
        Wrapper<ReturnMasterDO> wrapper = this.getReturnMasterDOWrapper(query);
        // 文件名
        String fileName = StringUtils.isNotBlank(query.getFileName()) ? query.getFileName() + System.currentTimeMillis() : "" + System.currentTimeMillis();
        // 保存预约信息
        Long appointmentDownloadId = this.appointmentDownloadService.saveAppointmentDownload(fileName, AppointmentDownloadType.RETURN.name());
        // 异步导出Excel到本地
        this.asyncService.receiptExport(wrapper, appointmentDownloadId, GlobalContext.getUser(), fileName);
    }

    @Override
    public void updateMemo(ReturnUpdateMemoQuery query) {
        List<ReturnMasterDO> returnMasterDOS = Lists.newArrayList();
        for (Long id : query.getIds()) {
            ReturnMasterDO returnMasterDO = this.getById(id);
            // 是否存在
            if (returnMasterDO == null) {
                throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "主键id为" + id + "的退货单不存在,请检查!");
            }
            ReturnMasterDO update = new ReturnMasterDO();
            update.setId(id);
            update.setMemoOperation(query.getMemoOperation());
            update.setMemoWarehouse(query.getMemoWarehouse());
            returnMasterDOS.add(update);
        }
        this.updateBatchById(returnMasterDOS);
    }

    @Override
    public void generateAdjustOrder(ReturnAdjustQuery query) {
        TradeDO oldTrade = this.tradeService.getTradeByTradeId(query.getNewPlatformTradeId());
        if (oldTrade != null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_ADJUST_EXIST);
        }
        // 获取订单号
        TradeDO tradeDO = this.tradeService.getExistTradeDO(query.getPlatformTradeId());
        // 获取订单明细
        List<OrderDO> orderDOS = this.orderService.getOrderDOS(query.getPlatformTradeId());
        // 获取订单的退货信息
        ReturnMasterDO oldMaster = this.getReturnMasterByTradeId(query.getPlatformTradeId());
        if (oldMaster == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_RETURNMASTER_NULL);
        }
        // 如果已全部退货,不允许生成调单
        if (oldMaster.getIsAllReturn() == 1) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_IS_ALL_RETURN);
        }
        List<ReturnDetailsDO> returnDetails = this.returnDetailsService.getReturnDetails(query.getPlatformTradeId());
        if (returnDetails.size() < 1) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_RETURNDETAILS_NULL);
        }
        // 从退货明细中获取没有全部退货的明细
        List<ReturnDetailsDO> newDetails = returnDetails.stream().filter(i -> i.getNum() - i.getReturnNum() > 0).collect(Collectors.toList());
        if (newDetails.size() < 1) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_RETURNDETAILS_NULL, "根据id集合获取的明细信息为空,请检查!");
        }
        // 分销订单不可以抛单
        PlatformDO platform = platformService.getPlatformById(tradeDO.getPlatformId());
        if ("OMNICHANNEL".equals(platform.getPlatformType()) && TradeTypeEnum.NORMAL.name().equals(tradeDO.getType())) {
            throw new GlobalDefaultException("分销订单，生成退货调单");
        }
        // 按照明细单号分组
        Map<String, List<ReturnDetailsDO>> map = returnDetails.stream().collect(Collectors.groupingBy(ReturnDetailsDO::getPlatformOrderId));
        List<OrderDO> newOrders = Lists.newArrayList();
        for (String platformOrderId : map.keySet()) {
            int num = map.get(platformOrderId).stream().mapToInt(detailsDO -> detailsDO.getReturnNum()).sum();
            OrderDO orderDO = orderDOS.stream().filter(o -> platformOrderId.equals(o.getPlatformOrderId())).findAny().orElse(null);
            if (orderDO == null) {
                throw new GlobalDefaultException(ResponseEnum.ORDER_EMPTY_OID);
            }
            orderDO.setNum(orderDO.getNum() - num);
            orderDO.setPriceTotal(orderDO.getPrice().multiply(new BigDecimal(orderDO.getNum())));
            orderDO.setPayment(orderDO.getPriceTotal());
            newOrders.add(orderDO);
        }
        // 生成调单
        this.tradeService.createAdjustOrder(query.getAdjustRemark(), query.getNewPlatformTradeId(), tradeDO, newOrders);
    }

    /**
     * 退款状态刷新
     */
    @Override
    public void refundStatusRefresh() {
        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .eq(ReturnMasterDO::getRefundStatus, RefundStatusEnum.NOT)
                .in(ReturnMasterDO::getPlatformId, GlobalConstant.PLATFORM_TAOBAO);
        List<ReturnMasterDO> records = this.list(wrapper);
        List<PlatformRefundDO> refunds = new ArrayList<>();
        for (ReturnMasterDO master : records) {
            TradeDO trade = this.tradeService.getTradeByTradeId(master.getPlatformTradeId());
            // 排除系统生成的单
            if (StringUtils.isNotEmpty(trade.getOldPlatformTradeId())) {
                continue;
            }
            PlatformDO platform = this.platformService.getPlatformById(trade.getPlatformId());
            // 查询详细订单
            Trade taobaoTrade = this.tradeService.getTaobaoTradeDetail(platform, trade.getPlatformTradeId());
            if (taobaoTrade == null) {
                log.error("刷新未退款订单退款数据任务，查询订单详情失败，订单号：{}", trade.getPlatformTradeId());
                continue;
            }
            // 收集退货id
            List<String> refundIds = taobaoTrade.getOrders().stream()
                    .map(Order::getRefundId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            // 查询退款记录
            for (String refundId : refundIds) {
                Refund refund = TaoBaoUtil.getRefundByRefundId(platform, refundId);
                if (refund != null) {
                    PlatformRefundDO platformRefund = this.platformRefundServiceImpl.buildTaobaoPlatformRefund(platform, refund);
                    refunds.add(platformRefund);
                }
            }
        }
        if (refunds.size() > 0) {
            this.platformRefundService.saveOrUpdateBatch(refunds);
        }
    }

    @Override
    public void generateExchangeGoods(GenerateExchangeGoodsQuery query) {
        // 新订单号是否存在
        TradeDO newTrade = this.tradeService.getTradeByTradeId(query.getNewPlatformTradeId());
        if (newTrade != null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_EXCHANGE_EXIST);
        }

        // 获取原订单
        TradeDO oldTrade = this.tradeService.decrypt(query.getPlatformTradeId());

        // 获取退货主信息
        ReturnMasterDO returnMasterDO = this.getReturnMasterByTradeId(query.getPlatformTradeId());
        if (returnMasterDO == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_RETURNMASTER_NULL);
        }

        // 是否是退货类型
        if (!ReturnTypeEnum.RETURN.name().equals(returnMasterDO.getType())) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_EXCHANGEGOODS_TYPE);
        }

        // 已过账和手动抛转的不能生成
        if (returnMasterDO.getErpPostStatus() == 1 || returnMasterDO.getErpPostManual() == 1) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_EXCHANGEGOODS_ERPPOSTSTATUS);
        }

        // 获取退货明细信息
        List<ReturnDetailsDO> returnDetails = this.returnDetailsService.getReturnDetails(query.getPlatformTradeId());
        if (returnDetails.size() < 1) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_RETURNDETAILS_NULL);
        }

        // 生成换货的明细数量是否与退货的数量一致
        int exchangeTotalNum = query.getGiftInfoQueries().stream().mapToInt(GiftInfoQuery::getNum).sum();
        int returnTotalNum = returnDetails.stream().mapToInt(ReturnDetailsDO::getReturnNum).sum();
        if (exchangeTotalNum != returnTotalNum || returnTotalNum == 0) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_EXCHANGEGOODS_NUM);
        }

        // 判断新退货单号是否存在
        String newRefundId = query.getPlatformTradeId() + "41";
        ReturnMasterDO returnMaster = this.getReturnMasterByRefundId(newRefundId);
        if (returnMaster != null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_RETURN_EXCHANGE_EXIST);
        }
        // 修改退货信息
        updateReturnById(returnMasterDO.getId(), newRefundId, query.getPlatformTradeId());
        // 修改退货明细
        updateReturnDetails(returnDetails, query.getPlatformTradeId());
        // 修改扫描记录订单号
        String newTradeId = query.getPlatformTradeId() + "3";
        Optional<ReceiveMasterDO> receiveMasterDO = this.receiveMasterDao.getByTradeId(query.getPlatformTradeId());
        receiveMasterDO.ifPresent(r -> {
            ReceiveMasterDO updateMasterDO = new ReceiveMasterDO();
            updateMasterDO.setId(r.getId());
            updateMasterDO.setPlatformTradeId(newTradeId);
            this.receiveMasterDao.updateById(updateMasterDO);
        });
        TradeDO exchangeTrade = new TradeDO();
        BeanUtils.copyProperties(oldTrade, exchangeTrade);
        //查询换货列表中的地址
        RepairDO repairDO = repairsDao.getByTradeId(query.getPlatformTradeId()).orElse(null);
        if (repairDO != null && repairDO.getType().equals("EXCHANGE") && repairDO.getIsExchange() == 1) {
            exchangeTrade.setReceiverName(repairDO.getReceiverName());
            exchangeTrade.setReceiverMobile(repairDO.getTelephone());
            exchangeTrade.setReceiverAddress(repairDO.getAddress());
            exchangeTrade.setReceiverDistrict(repairDO.getDistrict());
            exchangeTrade.setReceiverCity(repairDO.getCity());
            exchangeTrade.setReceiverProvince(repairDO.getProvince());
        }
        // 生成换货
        this.tradeService.createExchangeGoods(query, exchangeTrade);

        // 更新原订单状态为已发货
        this.tradeService.updateTradeOrderStatusById(oldTrade.getId(), OrderStatus.HAS_SHIP.name());

        // 把新订单号更新到维修换货的换货单上
        if (repairDO != null && repairDO.getType().equals("EXCHANGE") && repairDO.getIsExchange() == 1) {
            RepairDO repairDO1 = new RepairDO();
            repairDO1.setId(repairDO.getId());
            repairDO1.setExchangeGoodsOrder(newTradeId);
            repairDO1.setIsGenerate(1);
            this.repairsDao.updateById(repairDO1);
        }
        // 订单如果存在仓库自管物料，发送企业微信提醒
        this.sendWorkWechat(oldTrade);
        // 修改备注在卖家备注末尾新增备注文案：【注意：此单已做换货处理】,更新卖家备注到淘宝
        this.tradeService.updateMemoAfterAdjustOrExchange(oldTrade, TradeTypeEnum.EXCHANGE_GOODS);
    }

    /**
     * 发送企业通知
     *
     * @param oldTrade 订单
     */
    private void sendWorkWechat(TradeDO oldTrade) {
        try {
            List<OrderDO> orders = orderService.getOrderByTid(oldTrade.getPlatformTradeId());
            List<String> productIds = orders.stream().map(OrderDO::getErpCode).collect(Collectors.toList());
            long count = productInfoService.countByProductIdsAndIsKeepAccounts(productIds);
            if (count <= 0) {
                return;
            }
            // 获取企业微信用户工号
            List<String> users = systemParamService.getByName(paramName)
                    .filter(systemParam -> StringUtils.isNotBlank(systemParam.getParamValue()))
                    .map(systemParam -> Splitter.on(",").omitEmptyStrings().trimResults().splitToList(systemParam.getParamValue()))
                    .orElseGet(Collections::emptyList);
            workWechatAlertSenderCmdExe.execute(users, "订单 %s 存在仓库自管物料，请核实", oldTrade.getPlatformTradeId());
        } catch (Exception e) {
            log.warn("换货订单 {}，发送企业微信异常 {}", oldTrade.getPlatformTradeId(), e.getMessage(), e);
        }
    }

    @Override
    public ReturnMasterDO getReturnMasterByRefundId(String refundId) {
        return this.getOne(Wrappers.<ReturnMasterDO>lambdaQuery()
                .eq(ReturnMasterDO::getRefundId, refundId)
                .orderByDesc(ReturnMasterDO::getId)
                .last("limit 1"));
    }

    @Override
    public void isHasRefund(String platformTradeId, String type) {
        String newRefundId = ReceiveTypeEnum.RETURN.name().equals(type) ? platformTradeId + "1" : platformTradeId + "41";
        ReturnMasterDO returnMaster = this.getReturnMasterByRefundId(newRefundId);
        if (returnMaster != null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_RETURN_EXIST);
        }
    }

    @Override
    public List<ReturnMasterDO> getReturnByCreateDate(String startDate, String endDate) {
        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .ge(StringUtils.isNotBlank(startDate), ReturnMasterDO::getCreateTime, startDate + " 00:00:00")
                .le(StringUtils.isNotBlank(endDate), ReturnMasterDO::getCreateTime, endDate + " 23:59:59");
        return this.list(wrapper);
    }

    @Override
    public PaginationVO<PartReturnDetailVO> getPartRefundList(ReturnMasterQuery query) {
        query.setBeginDate(query.getBeginDate() == null ? "" : (query.getBeginDate() + " 00:00:00"));
        query.setEndDate(query.getEndDate() == null ? "" : (query.getEndDate() + " 23:59:59"));
        Page<PartReturnDetailVO> page = new Page<>(query.getIndex(), query.getSize());
        return new PaginationVO<>(this.returnMasterMapper.getPartRefundList(page, query));
    }

    @Override
    public void exportPartRefundDetails(ReturnMasterQuery query, HttpServletResponse response) {
        query.setBeginDate(query.getBeginDate() == null ? null : (query.getBeginDate() + " 00:00:00"));
        query.setEndDate(query.getEndDate() == null ? null : (query.getEndDate() + " 23:59:59"));
        String fileName = DateUtil.nowDateStr() + "_部分退款报表导出";
        Page<PartReturnDetailVO> page = new Page<>(1, -1);
        IPage<PartReturnDetailVO> result = this.returnMasterMapper.getPartRefundList(page, query);
        ExcelUtil.exportExcel(fileName, result.getRecords(), PartReturnDetailVO.class, response);
    }

    /**
     * 更新退货明细信息
     *
     * @param returnDetails
     */
    private void updateReturnDetails(List<ReturnDetailsDO> returnDetails, String platformTradeId) {
        for (ReturnDetailsDO returnDetail : returnDetails) {
            // 退货数量为0的直接删除
            if (returnDetail.getReturnNum() == 0) {
                this.returnDetailsService.removeById(returnDetail.getId());
            } else {
                ReturnDetailsDO updateDetail = new ReturnDetailsDO();
                updateDetail.setId(returnDetail.getId());
                //换货单不占用原订单单号
                updateDetail.setPlatformTradeId(platformTradeId + "3");
                // 金额为0
                updateDetail.setPayment(BigDecimal.ZERO);
                updateDetail.setPriceTotal(BigDecimal.ZERO);
                updateDetail.setPrice(BigDecimal.ZERO);
                // returnNum 为0的删除,不为0的修改num等于returnNum
                if (!returnDetail.getReturnNum().equals(returnDetail.getNum())) {
                    updateDetail.setNum(returnDetail.getReturnNum());
                }
                this.returnDetailsService.updateById(updateDetail);
            }
        }
    }

    /**
     * 根据主键id更新退货主信息
     *
     * @param id
     * @param refundId
     */
    private void updateReturnById(Long id, String refundId, String platformTradeId) {
        ReturnMasterDO updateReturn = new ReturnMasterDO();
        updateReturn.setId(id);
        //换货单不占用原单单号
        updateReturn.setPlatformTradeId(platformTradeId + "3");
        updateReturn.setRefundId(refundId);
        // 主退货单类型为换货
        updateReturn.setType(ReturnTypeEnum.EXCHANGE.name());
        // 金额为0
        updateReturn.setApplyAmount(BigDecimal.ZERO);
        updateReturn.setPayment(BigDecimal.ZERO);
        updateReturn.setRealAmount(BigDecimal.ZERO);
        this.updateById(updateReturn);
    }

    /**
     * 创建新退货明细
     *
     * @param newDetails
     * @param newPlatformTradeId
     * @return
     */
    private BigDecimal createReturnDetails(List<ReturnDetailsDO> newDetails, String newPlatformTradeId) {
        BigDecimal total = new BigDecimal("0.00");
        for (ReturnDetailsDO newDetail : newDetails) {
            newDetail.setId(null);
            newDetail.setUpdateTime(null);
            newDetail.setCreateTime(null);
            newDetail.setPlatformTradeId(newPlatformTradeId);
            total = total.add(newDetail.getPriceTotal());
        }
        return total;
    }

    /**
     * 创建新退货单
     *
     * @param oldMaster
     * @param newDetails
     * @param total
     * @param newPlatformTradeId
     * @param adjustRemark
     * @return
     */
    private ReturnMasterDO createReturnMaster(ReturnMasterDO oldMaster, List<ReturnDetailsDO> newDetails, BigDecimal total, String newPlatformTradeId, String adjustRemark) {
        ReturnMasterDO returnMasterDO = new ReturnMasterDO();
        returnMasterDO.setPlatformId(oldMaster.getPlatformId());
        returnMasterDO.setPlatformName(oldMaster.getPlatformName());
        returnMasterDO.setPaymentTime(oldMaster.getPaymentTime());
        returnMasterDO.setBuyerNickname(oldMaster.getBuyerNickname());
        returnMasterDO.setReceiverName(oldMaster.getReceiverName());
        returnMasterDO.setReason(oldMaster.getReason());
        returnMasterDO.setIsEndProductStore(oldMaster.getIsEndProductStore());
        returnMasterDO.setPostTrackingNo(oldMaster.getPostTrackingNo());
        returnMasterDO.setExpressTrackingNo(oldMaster.getExpressTrackingNo());
        returnMasterDO.setExpressCompany(oldMaster.getExpressCompany());
        // 调单类型
        returnMasterDO.setType(ReturnTypeEnum.ADJUST.name());
        returnMasterDO.setPlatformTradeId(newPlatformTradeId);
        returnMasterDO.setRefundId(newPlatformTradeId + "1");
        returnMasterDO.setErpPostStatus(0);
        returnMasterDO.setErpPostManual(0);
        returnMasterDO.setRefundStatus(RefundStatusEnum.NOT.name());
        returnMasterDO.setAutoRefundStatus(AutoRefundStatusEnum.NOT.name());
        returnMasterDO.setPayment(total);
        returnMasterDO.setApplyAmount(total);
        returnMasterDO.setRealAmount(total);
        returnMasterDO.setIsAllReturn(newDetails.stream().filter(r -> r.getIsReturn() != 1).findAny().orElse(null) == null ? 1 : -1);
//        returnMasterDO.setRefundTime();
//        returnMasterDO.setMemoOperation();
//        returnMasterDO.setErpId();
//        returnMasterDO.setErpPostOrg();
//        returnMasterDO.setErpPostTime();
//        returnMasterDO.setErpPostManualTime();
//        returnMasterDO.setLocationId();
//        returnMasterDO.setSubinvCode();
        return returnMasterDO;
    }

    /**
     * 获取需要退款的订单
     *
     * @return 需要退款的订单
     */
    private List<ReturnMasterDO> getReturnList() {
        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .eq(ReturnMasterDO::getAutoRefundStatus, AutoRefundStatusEnum.NOT)
                .in(ReturnMasterDO::getPlatformId, GlobalConstant.PLATFORM_REFUND_AUTO)
                .orderByAsc(ReturnMasterDO::getCreateTime)
                .last("limit 100");
        return this.list(wrapper);
    }

    /**
     * 拼多多自动退库
     *
     * @param refund   退款
     * @param platform 平台
     * @param opTime   操作时间
     * @return 结果
     */
    public boolean pddAutoWarehouse(PlatformRefundDO refund, PlatformDO platform, long opTime) {
        try {
            PopClient client = new PopHttpClient(platform.getAppKey(), platform.getAppSecret());
            PddNextoneLogisticsWarehouseUpdateRequest request = new PddNextoneLogisticsWarehouseUpdateRequest();
            Request param = new Request();
            param.setAfterSalesId(Long.parseLong(refund.getPlatformRefundId()));
            param.setOperateTime(opTime);
            param.setOrderSn(refund.getPlatformTradeId());
            param.setReverseTrackingNumber(refund.getExpressWaybillNo());
            param.setWarehouseStatus(1);
            request.setRequest(param);
            PddNextoneLogisticsWarehouseUpdateResponse response = client.syncInvoke(request, platform.getSessionKey());
            if (response.getErrorResponse() != null) {
                log.warn("订单 {} 退款单 {} 退货入仓异常 {}",
                        refund.getPlatformTradeId(), refund.getPlatformRefundId(), response.getErrorResponse().getErrorMsg());
                return false;
            }
            log.info("订单号 {} 退款单号 {} 申请退货入仓成功", refund.getPlatformTradeId(), refund.getPlatformRefundId());
            // 自动同意退款
            this.pddAutoRefund(refund, platform);
            return true;
        } catch (Exception e) {
            log.warn("订单 {} 退款单 {} 退货入仓异常 {}",
                    refund.getPlatformTradeId(), refund.getPlatformRefundId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 系统自动退款
     *
     * @param refund   退款信息
     * @param platform 平台
     */
    private void pddAutoRefund(PlatformRefundDO refund, PlatformDO platform) {
        try {
            PopClient client = new PopHttpClient(platform.getAppKey(), platform.getAppSecret());
            PddRefundAgreeRequest refundAgreeRequest = new PddRefundAgreeRequest();
            PddRefundAgreeRequest.Request pddRefundAgreeRequest = new PddRefundAgreeRequest.Request();
            pddRefundAgreeRequest.setAfterSalesId(Long.parseLong(refund.getPlatformRefundId()));
            pddRefundAgreeRequest.setOperateDesc("系统同意退款");
            pddRefundAgreeRequest.setOrderSn(refund.getPlatformTradeId());
            refundAgreeRequest.setRequest(pddRefundAgreeRequest);
            PddRefundAgreeResponse pddRefundAgreeResponse = client.syncInvoke(refundAgreeRequest, platform.getSessionKey());
            PddRefundAgreeResponse.ResponseResult result = pddRefundAgreeResponse.getResponse().getResult();
            if (Boolean.FALSE.equals(result.getSucc())) {
                log.warn("订单号 {} 退款单号 {} 自动退款异常", refund.getPlatformTradeId(), result.getMessage());
                return;
            }
            log.info("订单号 {} 退款单号 {} 自动退款成功，退款金额 {}", refund.getPlatformTradeId(), refund.getPlatformRefundId(), refund.getRefundFee());
        } catch (Exception e) {
            log.warn("订单号 {} 退款单号 {} 自动退款异常", refund.getPlatformTradeId(), e.getMessage(), e);
        }
    }

    /**
     * 更新退款状态
     *
     * @param success        退款成功标识
     * @param returnMasterId 退款信息id
     */
    private void updateReturnMasterStatus(int success, long returnMasterId) {
        ReturnMasterDO updateMaster = new ReturnMasterDO();
        updateMaster.setId(returnMasterId);
        if (success == 1) {
            updateMaster.setAutoRefundStatus(AutoRefundStatusEnum.AUTO.name());
        } else if (success == 0) {
            updateMaster.setAutoRefundStatus(AutoRefundStatusEnum.NO_PROCESSING.name());
        } else {
            updateMaster.setAutoRefundStatus(AutoRefundStatusEnum.FAILED.name());
        }
        this.updateById(updateMaster);
    }

    /**
     * 获取查询条件
     *
     * @param query
     * @return
     */
    public Wrapper<ReturnMasterDO> getReturnMasterDOWrapper(ReturnMasterQuery query) {
        return Wrappers.<ReturnMasterDO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(query.getPlatformIds()), ReturnMasterDO::getPlatformId, query.getPlatformIds())
                .eq(StringUtils.isNotBlank(query.getReceiptWarehouse()), ReturnMasterDO::getReceiptWarehouse, query.getReceiptWarehouse())
                .eq(StringUtils.isNotBlank(query.getOrderTag()), ReturnMasterDO::getOrderTag, query.getOrderTag())
                .eq(query.getErpPostStatus() != null, ReturnMasterDO::getErpPostStatus, query.getErpPostStatus())
                .eq(query.getIsEndProductStore() != null, ReturnMasterDO::getIsEndProductStore, query.getIsEndProductStore())
                .eq(query.getIsAllReturn() != null, ReturnMasterDO::getIsAllReturn, query.getIsAllReturn())
                .eq(query.getErpPostManual() != null, ReturnMasterDO::getErpPostManual, query.getErpPostManual())
                .eq(StringUtils.isNotBlank(query.getPlatformTradeId()), ReturnMasterDO::getPlatformTradeId, query.getPlatformTradeId())
                .in(StringUtils.isNotBlank(query.getBarcode()) && CollectionUtils.isNotEmpty(getPlatformTradeIds(query.getBarcode())),
                        ReturnMasterDO::getPlatformTradeId, getPlatformTradeIds(query.getBarcode()))
                .eq(StringUtils.isNotBlank(query.getLocationId()), ReturnMasterDO::getLocationId, query.getLocationId())
                .eq(StringUtils.isNotBlank(query.getRefundId()), ReturnMasterDO::getRefundId, query.getRefundId())
                .and(StringUtils.isNotBlank(query.getBuyerNickname()), i -> i.like(ReturnMasterDO::getBuyerNickname, query.getBuyerNickname())
                        .or().like(ReturnMasterDO::getReceiverName, query.getBuyerNickname()))
                .eq(StringUtils.isNotBlank(query.getExpressTrackingNo()), ReturnMasterDO::getExpressTrackingNo, query.getExpressTrackingNo())
                .eq(StringUtils.isNotBlank(query.getRefundStatus()), ReturnMasterDO::getRefundStatus, query.getRefundStatus())
                .eq(StringUtils.isNotBlank(query.getAutoRefundStatus()), ReturnMasterDO::getAutoRefundStatus, query.getAutoRefundStatus())
                .eq(StringUtils.isNotBlank(query.getType()), ReturnMasterDO::getType, query.getType())
                .eq(StringUtils.isNotBlank(query.getPartReturnStatus()), ReturnMasterDO::getPartReturnStatus, query.getPartReturnStatus())
                .like(StringUtils.isNotBlank(query.getReason()), ReturnMasterDO::getReason, query.getReason())
                .ge(StringUtils.isNotBlank(query.getErpPostTimeBegin()), ReturnMasterDO::getErpPostTime, query.getErpPostTimeBegin() + " 00:00:00")
                .le(StringUtils.isNotBlank(query.getErpPostTimeEnd()), ReturnMasterDO::getErpPostTime, query.getErpPostTimeEnd() + " 23:59:59")
                .ge(StringUtils.isNotBlank(query.getPaymentTimeBegin()), ReturnMasterDO::getPaymentTime, query.getPaymentTimeBegin() + " 00:00:00")
                .le(StringUtils.isNotBlank(query.getPaymentTimeEnd()), ReturnMasterDO::getPaymentTime, query.getPaymentTimeEnd() + " 23:59:59")
                .ge(StringUtils.isNotBlank(query.getBeginDate()), ReturnMasterDO::getCreateTime, query.getBeginDate() + " 00:00:00")
                .le(StringUtils.isNotBlank(query.getEndDate()), ReturnMasterDO::getCreateTime, query.getEndDate() + " 23:59:59")
                .eq(query.getIsUserRefusal() != null, ReturnMasterDO::getIsUserRefusal, query.getIsUserRefusal())
                .eq(query.getIsExchange() != null, ReturnMasterDO::getIsExchange, query.getIsExchange())
                .orderByDesc(ReturnMasterDO::getCreateTime);
    }

    /**
     * 根据条码获取退货订单号
     *
     * @param barcode
     * @return
     */
    private List<String> getPlatformTradeIds(String barcode) {
        Wrapper<ReturnDetailsDO> wrapper = Wrappers.<ReturnDetailsDO>lambdaQuery()
                .select(ReturnDetailsDO::getPlatformTradeId)
                .eq(ReturnDetailsDO::getBarcode, barcode);
        return this.returnDetailsService.listObjs(wrapper, i -> (String) i);
    }

    @Override
    public void storeConfirm(PartStoreConfirmQuery query) {
        List<ReturnMasterDO> returnMasterDOS = Lists.newArrayList();
        for (Long id : query.getIds()) {
            ReturnMasterDO returnMasterDO = this.getById(id);
            // 是否存在
            if (returnMasterDO == null) {
                throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "主键id为" + id + "的退货单不存在,请检查!");
            }
            if (returnMasterDO.getErpPostStatus() == 0) {
                throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "退货订单尚未过账不能归位,请先确认!");
            }
            if (PartReturnStatus.ADJUST.name().equals(returnMasterDO.getPartReturnStatus())) {
                ReturnMasterDO update = new ReturnMasterDO();
                update.setId(id);
                update.setStoreConfirmUser(GlobalContext.getUser().getName());
                update.setStoreConfirmTime(new Date());
                update.setPartReturnStatus(PartReturnStatus.STORE_CONFIRM.name());
                returnMasterDOS.add(update);
            } else {
                throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "退货单号" + returnMasterDO.getRefundId() + "的退货单状态不对,不能归位!");
            }
        }
        this.updateBatchById(returnMasterDOS);
    }

    @Override
    public void partConfirm(PartReturnConfirmQuery query) {
        List<ReturnMasterDO> returnMasterDOS = Lists.newArrayList();
        ReturnMasterDO returnMasterDO = this.getById(query.getId());
        // 是否存在
        if (returnMasterDO == null) {
            throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "主键id为" + query.getId() + "的退货单不存在,请检查!");
        }
        if (PartReturnStatus.STORE_SCAN.name().equals(returnMasterDO.getPartReturnStatus()) && returnMasterDO.getIsAllReturn() != 1) {
            ReturnMasterDO update = new ReturnMasterDO();
            update.setId(query.getId());
            update.setPartConfirmUser(GlobalContext.getUser().getName());
            update.setPartConfirmTime(new Date());
            update.setPartReturnMemo(query.getPartReturnMemo());
            update.setPartReturnStatus(PartReturnStatus.SERVE_CONFIRM.name());
            returnMasterDOS.add(update);
        } else {
            throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "只有仓库已扫描切且部分退货才能进行客服备注!");
        }
        this.updateBatchById(returnMasterDOS);
    }

    @Override
    public void partAdjust(PartReturnAdjustQuery query) {
        ReturnMasterDO returnMasterDO = this.getById(query.getId());
        if (returnMasterDO == null) {
            throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "主键id为" + query.getId() + "的退货单不存在，请检查!");
        }
        if (returnMasterDO.getIsAllReturn() == 1
                || !PartReturnStatus.SERVE_CONFIRM.name().equals(returnMasterDO.getPartReturnStatus())) {
            throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "只有客服已确认切且部分退货才能进行确认调单!");
        }
        if (!"NOT_NEED".equals(query.getAdjustType())) {
            // 获取所有已发货的订单明细
            String[] tradeIds = query.getAdjustTrades().split(",");
            List<OrderDO> orders = this.getOrdersByTradeIds(tradeIds);

            // 已发货的订单明细汇总
            Map<String, Integer> orderMap = orders.stream()
                    .collect(Collectors.groupingBy(OrderDO::getErpCode, Collectors.summingInt(OrderDO::getNum)));

            // 未退回的退货明细汇总
            List<ReturnDetailsDO> returnDetails = this.returnDetailsService.getReturnDetails(returnMasterDO.getPlatformTradeId());
            Map<String, Integer> returnMap = returnDetails.stream()
                    .filter(detail -> detail.getIsReturn() != 1)
                    .collect(Collectors.groupingBy(ReturnDetailsDO::getErpCode,
                            Collectors.reducing(0,
                                    (ReturnDetailsDO detail) -> detail.getNum() - detail.getReturnNum(),
                                    Integer::sum)));

            // 调单物料差异校验
            boolean partAdjustDiff = this.partAdjustDiffCheck(orderMap, returnMap);
            if (partAdjustDiff) {
                throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "调单物料存在差异！");
            }
        }

        // 更新调单信息
        ReturnMasterDO update = new ReturnMasterDO();
        update.setId(query.getId());
        update.setAdjustUser(GlobalContext.getUser().getName());
        update.setAdjustTime(new Date());
        update.setAdjustType(query.getAdjustType());
        update.setAdjustTrades(query.getAdjustTrades());
        update.setPartReturnStatus(PartReturnStatus.ADJUST.name());
        this.updateById(update);
    }

    /**
     * 根据订单id列表，获取所有已发货的订单明细，并且排除虚拟物料
     *
     * @param tradeIds 订单id列表
     * @return 订单明细
     */
    private List<OrderDO> getOrdersByTradeIds(String[] tradeIds) {
        List<OrderDO> orders = Lists.newArrayList();
        for (String tradeId : tradeIds) {
            TradeDO trade = this.tradeService.getTradeByTradeId(tradeId);
            if (trade.getStatusOrder().equals("HAS_SHIP")) {
                List<OrderDO> tradeOrders = this.orderService.getOrderDOS(tradeId);
                // 排除虚拟物料
                orders.addAll(tradeOrders.stream().filter(orderDO -> orderDO.getMaterialType() != 3).collect(Collectors.toList()));
            }
        }
        return orders;
    }

    /**
     * 调单物料差异校验
     *
     * @param orderMap  已发货的订单明细
     * @param returnMap 未退回的退货明细
     * @return 校验结果
     */
    private boolean partAdjustDiffCheck(Map<String, Integer> orderMap, Map<String, Integer> returnMap) {
        if (orderMap.size() != returnMap.size()) {
            return true;
        }

        for (String erpCode : returnMap.keySet()) {
            int notReturnNum = returnMap.get(erpCode);
            int orderNum = orderMap.getOrDefault(erpCode, 0);
            if (orderNum != notReturnNum) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<ReturnMasterDO> getRefundAutoPostErpList() {
        return this.returnMasterMapper.getRefundAutoPostErpList();
    }

    @Override
    public List<ReturnMasterDO> getRefundManualPostErpList() {
        return this.returnMasterMapper.getRefundManualPostErpList();
    }

    @Override
    public List<ReturnMasterDO> getRefundExchangePostErpList() {
        return this.returnMasterMapper.getRefundExchangePostErpList();
    }

    @Override
    public List<ReturnMasterDO> getRefundAgentSalePostErpList(Set<Integer> agentSalePlatforms) {
        return this.returnMasterMapper.getRefundAgentSalePostErpList(agentSalePlatforms);
    }

    @Override
    public List<String> getExpressNoManyTrade(String expressNo) {
        return this.returnMasterMapper.getExpressNoManyTrade(expressNo);
    }

}
