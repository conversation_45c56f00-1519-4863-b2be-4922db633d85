package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.mapper.master.OrderInterceptSetMapper;
import com.xtc.onlineretailers.pojo.entity.MaterialPriceDO;
import com.xtc.onlineretailers.pojo.entity.OrderInterceptSetDO;
import com.xtc.onlineretailers.pojo.query.OrderInterceptSetAddQuery;
import com.xtc.onlineretailers.pojo.query.OrderInterceptSetQuery;
import com.xtc.onlineretailers.pojo.query.OrderInterceptSetUpdateQuery;
import com.xtc.onlineretailers.pojo.query.PaginationQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.OrderInterceptSetService;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.ExcelUtil;
import com.xtc.onlineretailers.util.MyToolUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

@Service
public class OrderInterceptSetServiceImpl extends ServiceImpl<OrderInterceptSetMapper, OrderInterceptSetDO> implements OrderInterceptSetService {

    /**
     * @param query
     * @return
     * @Description 订单拦截配置
     */
    public PaginationVO<OrderInterceptSetDO> getOrderInterceptSetList(OrderInterceptSetQuery query) {

        Page<OrderInterceptSetDO> page = PaginationQuery.createPage(query.getIndex(), query.getSize());
        IPage<OrderInterceptSetDO> mapIPage = this.page(page, queryToWrapper(query));
        return PaginationVO.newVO(mapIPage);
    }

    public void exportInterceptSet(OrderInterceptSetQuery query, HttpServletResponse response) {

        List<OrderInterceptSetDO> data = this.list(queryToWrapper(query));
        String fileName = "exportInterceptSet" + DateUtil.createIdByTime();
        ExcelUtil.exportExcel(fileName, data, MaterialPriceDO.class, response);
    }

    public Wrapper<OrderInterceptSetDO> queryToWrapper(OrderInterceptSetQuery query) {
        Wrapper<OrderInterceptSetDO> wrapper = Wrappers.<OrderInterceptSetDO>lambdaQuery()
                .eq(OrderInterceptSetDO::getIsUse, "1")
                .like(MyToolUtil.notEmpty(query.getInterceptDescription()), OrderInterceptSetDO::getInterceptDescription, query.getInterceptDescription())
                .like(MyToolUtil.notEmpty(query.getProductId()), OrderInterceptSetDO::getProductIds, query.getProductId())
                .eq(MyToolUtil.notEmpty(query.getPlatformName()), OrderInterceptSetDO::getPlatformName, query.getPlatformName())
                .ge(MyToolUtil.notEmpty(query.getStartDate()), OrderInterceptSetDO::getStartTime, query.getStartDate() + " 00:00:00")
                .le(MyToolUtil.notEmpty(query.getEndDate()), OrderInterceptSetDO::getEndTime, query.getEndDate() + " 23:59:59")
                .orderByDesc(OrderInterceptSetDO::getId);
        return wrapper;
    }

    /**
     * @param query
     * @return
     * @Description 更新订单拦截配置
     */
    public OrderInterceptSetDO updateOrderInterceptSet(OrderInterceptSetUpdateQuery query) {
        OrderInterceptSetDO orderInterceptSet = new OrderInterceptSetDO();
        BeanUtils.copyProperties(query, orderInterceptSet);
        if (this.updateById(orderInterceptSet)) {
            return this.getById(orderInterceptSet.getId());
        } else {
            //更新失败
            throw new GlobalDefaultException(ResponseEnum.NOT_EXIST);
        }
    }

    /**
     * @param query
     * @return
     * @Description 新增订单拦截配置
     */
    public OrderInterceptSetDO addOrderInterceptSet(OrderInterceptSetAddQuery query) {
        OrderInterceptSetDO agentInfoDO = new OrderInterceptSetDO();
        BeanUtils.copyProperties(query, agentInfoDO);
        agentInfoDO.setAddTime(new Date());
        this.save(agentInfoDO);
        return agentInfoDO;
    }

    /**
     * @param id
     * @return
     * @Description 删除订单拦截配置
     */
    public boolean deleteOrderInterceptSet(int id) {
        return this.removeById(id);
    }

}
