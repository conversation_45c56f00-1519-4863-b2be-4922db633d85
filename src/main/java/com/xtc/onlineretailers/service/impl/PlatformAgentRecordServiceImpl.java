package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.mapper.master.PlatformAgentRecordMapper;
import com.xtc.onlineretailers.pojo.entity.PlatformAgentDO;
import com.xtc.onlineretailers.pojo.entity.PlatformAgentRecordDO;
import com.xtc.onlineretailers.pojo.query.PlatformAgentRecordQuery;
import com.xtc.onlineretailers.pojo.query.PlatformAgentRecordUpdateQuery;
import com.xtc.onlineretailers.pojo.query.platformAgentRecordAddQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.PlatformAgentRecordService;
import com.xtc.onlineretailers.service.PlatformAgentService;
import com.xtc.onlineretailers.service.SmsService;
import com.xtc.onlineretailers.util.GsonUtil;
import com.xtc.onlineretailers.util.SMSUtil;
import com.xtc.springboot.pojo.entity.GlobalContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

@Service
@Slf4j
public class PlatformAgentRecordServiceImpl extends ServiceImpl<PlatformAgentRecordMapper, PlatformAgentRecordDO> implements PlatformAgentRecordService {

    @Resource
    private PlatformAgentRecordMapper platformAgentRecordMapper;

    @Resource
    private PlatformAgentService platformAgentService;

    @Resource
    private SmsService smsService;

    @Override
    @Transactional
    public void add(platformAgentRecordAddQuery query) {
        PlatformAgentRecordDO proxyPaymentDO = new PlatformAgentRecordDO();
        BeanUtils.copyProperties(query, proxyPaymentDO);
        proxyPaymentDO.setCreateName(GlobalContext.getUser().getName());
        proxyPaymentDO.setPayment(query.getPayment().setScale(2, BigDecimal.ROUND_HALF_UP));
        this.save(proxyPaymentDO);
        // 平台余额计算
        PlatformAgentDO platformAgentDO = platformAgentService.getOne(query.getAccount());
        if (platformAgentDO == null) {
            throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "根据id不能获取代销贷款信息,请检查参数");
        }
        // 更新平台余额
        PlatformAgentDO update = new PlatformAgentDO();
        update.setId(platformAgentDO.getId());
        update.setBalance(platformAgentDO.getBalance().add(query.getPayment()).setScale(2, BigDecimal.ROUND_HALF_UP));
        this.platformAgentService.updateById(update);
        // 判断余额是否小于阈值
        if (update.getBalance().compareTo(platformAgentDO.getAlarmThreshold()) < 0) {
            // 发送短息
            platformAgentDO.setBalance(update.getBalance());
            this.sendMessage(platformAgentDO);
        }
    }

    @Override
    @Transactional
    public void update(PlatformAgentRecordUpdateQuery query) {
        // 更新打款记录
        PlatformAgentRecordDO proxyPaymentDO1 = this.getById(query.getId());
        if (proxyPaymentDO1 == null) {
            throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "根据id不能获取代销贷款信息,请检查参数");
        }
        String name = GlobalContext.getUser().getName();
        if (!name.equals(proxyPaymentDO1.getCreateName())) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_NAME_NOT_SAME, "只能编辑自己的代销打款");
        }
        PlatformAgentRecordDO proxyPaymentDO = new PlatformAgentRecordDO();
        BeanUtils.copyProperties(query, proxyPaymentDO);
        proxyPaymentDO.setPayment(query.getPayment().setScale(2, BigDecimal.ROUND_HALF_UP));
        proxyPaymentDO.setId(proxyPaymentDO1.getId());
        this.updateById(proxyPaymentDO);
        // 更新平台账号余额
        PlatformAgentDO platformAgentDO = this.platformAgentService.getOne(proxyPaymentDO1.getAccount());
        if (platformAgentDO == null) {
            throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "根据平台账号名获取不到平台账号信息");
        }
        PlatformAgentDO update = new PlatformAgentDO();
        update.setId(platformAgentDO.getId());
        update.setBalance(platformAgentDO.getBalance().subtract(proxyPaymentDO1.getPayment()).add(query.getPayment()).setScale(2, BigDecimal.ROUND_HALF_UP));
        this.platformAgentService.updateById(update);
    }

    @Override
    @Transactional
    public void delete(Long id) {
        PlatformAgentRecordDO proxyPaymentDO1 = this.getById(id);
        if (proxyPaymentDO1 == null) {
            throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "根据id不能获取代销贷款信息,请检查参数");
        }
        String name = GlobalContext.getUser().getName();
        if (!name.equals(proxyPaymentDO1.getCreateName())) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_NAME_NOT_SAME, "只能删除自己的代销打款");
        }
        PlatformAgentDO platformAgentDO = this.platformAgentService.getOne(proxyPaymentDO1.getAccount());
        if (platformAgentDO == null) {
            throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR, "根据平台账号名获取不到平台账号信息");
        }
        PlatformAgentDO update = new PlatformAgentDO();
        update.setId(platformAgentDO.getId());
        update.setBalance(platformAgentDO.getBalance().subtract(proxyPaymentDO1.getPayment()));
        this.platformAgentService.updateById(update);
        this.removeById(id);
    }

    @Override
    public PaginationVO<PlatformAgentRecordDO> getPage(PlatformAgentRecordQuery query) {
        Wrapper<PlatformAgentRecordDO> wrapper = Wrappers.<PlatformAgentRecordDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getAccount()), PlatformAgentRecordDO::getAccount, query.getAccount())
                .eq(StringUtils.isNotBlank(query.getCreateName()), PlatformAgentRecordDO::getCreateName, query.getCreateName())
                .orderByDesc(PlatformAgentRecordDO::getCreateTime);
        Page<PlatformAgentRecordDO> page = this.page(query.createPage(), wrapper);
        return PaginationVO.newVO(page);
    }

    @Override
    public void sendMessage(PlatformAgentDO platformAgentDO) {
        log.info("platformAgentDO : {}", GsonUtil.objectToJson(platformAgentDO));
        List<String> mobiles = Lists.newArrayList(platformAgentDO.getAlarmMobile().split(","));
        String content = "您的" + platformAgentDO.getAccount() + "平台当前可用余额是: " + platformAgentDO.getBalance() + "元,余额款项不足，请及时充值！";
        SMSUtil.sendSms(mobiles, content);

    }

}
