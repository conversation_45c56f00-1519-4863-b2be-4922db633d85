package com.xtc.onlineretailers.service.impl;

import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.enums.AppointmentDownloadStatusEnum;
import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.mapper.master.AppointmentDownloadMapper;
import com.xtc.onlineretailers.pojo.entity.AppointmentDownloadDO;
import com.xtc.onlineretailers.pojo.query.AppointmentDownloadQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.AppointmentDownloadService;
import com.xtc.onlineretailers.util.MinioManager;
import com.xtc.springboot.pojo.entity.GlobalContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.util.Optional;
import java.util.stream.Stream;

@Service
@Slf4j
public class AppointmentDownloadServiceImpl extends ServiceImpl<AppointmentDownloadMapper, AppointmentDownloadDO> implements AppointmentDownloadService {

    @Resource
    private MinioManager minioManager;

    @Override
    public PaginationVO<AppointmentDownloadDO> list(AppointmentDownloadQuery query) {
        Wrapper<AppointmentDownloadDO> wrapper = Wrappers.<AppointmentDownloadDO>lambdaQuery()
                .eq(AppointmentDownloadDO::getBookingPerson, GlobalContext.getUser().getName())
                .eq(AppointmentDownloadDO::getType, query.getType())
                .orderByDesc(AppointmentDownloadDO::getCreateTime);
        Page<AppointmentDownloadDO> page = this.page(query.createPage(), wrapper);
        return PaginationVO.newVO(page);
    }

    @Override
    public ResponseEntity download(String url, HttpServletResponse response) {
//        if (url == null || !url.startsWith(this.INVOICE_FILE_SAVE_PATH)) {
//            throw new GlobalDefaultException(ResponseEnum.ERROR_URL);
//        }
        // 获取文件
        org.springframework.core.io.Resource resource;
        try {
            // 设置响应编码格式
            response.setCharacterEncoding(StandardCharsets.UTF_8.toString());
            // 获取文件名
            String fileName = new File(url.trim()).getName();
            // 设置文件名编码
            String encode = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            response.setHeader("Content-disposition", "attachment;filename=" + encode);
            resource = new UrlResource(Paths.get(url).toUri());
            if (resource.getFilename() == null || !resource.exists()) {
                throw new FileNotFoundException("File not found.");
            }
        } catch (Exception e) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_FILE_DOWNLOAD, e);
        }

        return ResponseEntity.ok()
                // 文件格式
                .contentType(new MediaType("application", "vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
                .body(resource);
    }

    @Override
    public ResponseEntity<org.springframework.core.io.Resource> downloadFromMinio(String objectName) {
        try {
            // 获取文件
            InputStream stream = this.minioManager.getObject(objectName);

            // 读取文件
            org.springframework.core.io.Resource resource = new InputStreamResource(stream);

            // 提取文件名并从浏览器下载
            Optional<String> fileName = Stream.of(objectName.split("/")).filter(p -> p.contains(ExcelTypeEnum.XLS.getValue())).findFirst();
            if (fileName.isPresent()) {
                MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
                headers.add("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName.get(), StandardCharsets.UTF_8.toString()));
                headers.add("contentType", MediaType.APPLICATION_OCTET_STREAM_VALUE);
                return new ResponseEntity<>(resource, headers, HttpStatus.OK);
            }
        } catch (Exception e) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_APPOINTMENT_FILE_DOWNLOAD, e);
        }
        throw new GlobalDefaultException(ResponseEnum.ERROR_APPOINTMENT_FILE_DOWNLOAD);
    }

    @Override
    public Long saveAppointmentDownload(String fileName, String type) {
        AppointmentDownloadDO appointmentDownloadDO = new AppointmentDownloadDO();
        appointmentDownloadDO.setBookingPerson(GlobalContext.getUser().getName());
        appointmentDownloadDO.setType(type);
        appointmentDownloadDO.setFileName(fileName);
        appointmentDownloadDO.setStatus(AppointmentDownloadStatusEnum.PROCESSING.name());
        this.save(appointmentDownloadDO);
        return appointmentDownloadDO.getId();
    }

}
