package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.jd.open.api.sdk.domain.refundapply.RefundApplySoaService.response.queryPageList.QueryResult;
import com.jd.open.api.sdk.domain.refundapply.RefundApplySoaService.response.queryPageList.RefundApplyVo;
import com.jd.open.api.sdk.response.refundapply.PopAfsSoaRefundapplyQueryPageListResponse;
import com.kuaishou.merchant.open.api.domain.order.OrderDetail;
import com.kuaishou.merchant.open.api.domain.refund.MerchantRefundDetailDataView;
import com.kuaishou.merchant.open.api.domain.refund.MerchantRefundInfoView;
import com.kuaishou.merchant.open.api.response.refund.OpenSellerOrderRefundPcursorListResponse;
import com.taobao.api.domain.Refund;
import com.taobao.api.domain.Trade;
import com.taobao.api.response.RefundsReceiveGetResponse;
import com.xiaohongshu.fls.opensdk.entity.BaseResponse;
import com.xiaohongshu.fls.opensdk.entity.afterSale.response.GetAfterSaleDetailResponse;
import com.xiaohongshu.fls.opensdk.entity.afterSale.response.GetAfterSaleListResponse;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.marketing.orderservice.serviceorder.ServiceOrderFeignClient;
import com.xtc.marketing.orderservice.serviceorder.dto.ServiceOrderDTO;
import com.xtc.marketing.orderservice.serviceorder.dto.query.ServiceOrderPageQry;
import com.xtc.marketing.orderservice.serviceorder.enums.ServiceState;
import com.xtc.marketing.orderservice.serviceorder.enums.ServiceType;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.enums.*;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.mapper.master.PlatformRefundMapper;
import com.xtc.onlineretailers.pojo.bo.SFInterceptBO;
import com.xtc.onlineretailers.pojo.dto.*;
import com.xtc.onlineretailers.pojo.entity.*;
import com.xtc.onlineretailers.pojo.vo.LogisticInfoVO;
import com.xtc.onlineretailers.refactor.executor.command.SaveTradeInterceptCmdExe;
import com.xtc.onlineretailers.refactor.executor.command.WarehouseOutboundCancelCmdExe;
import com.xtc.onlineretailers.refactor.executor.query.LogisticsRouteQryExe;
import com.xtc.onlineretailers.refactor.job.BaseTask;
import com.xtc.onlineretailers.repository.BaseDaoConstant;
import com.xtc.onlineretailers.service.*;
import com.xtc.onlineretailers.tradecheck.ServiceOrderRefundChecker;
import com.xtc.onlineretailers.util.*;
import com.xtc.onlineretailers.util.appStore.AppStoreUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service

public class PlatformRefundServiceImpl extends ServiceImpl<PlatformRefundMapper, PlatformRefundDO> implements PlatformRefundService {

    // 步步高抖音旗舰店id
    private static final String BBK_TICTOK_SHOP_ID = "18102775";
    // 小天才抖音旗舰店id
    private static final String XTC_TICTOK_SHOP_ID = "17794271";

    @Resource
    private SystemParamService systemParamService;
    @Resource
    private PlatformService platformService;
    @Resource
    private TradeService tradeService;
    @Resource
    private SmsService smsService;
    @Resource
    private ReturnMasterService returnMasterService;
    @Resource
    private ServiceOrderFeignClient serviceOrderFeignClient;
    @Resource
    private OrderService orderService;
    @Resource
    private PlatformRefundService platformRefundService;
    @Resource
    private ServiceOrderRefundChecker serviceOrderRefundChecker;
    @Resource
    private WarehouseOutboundCancelCmdExe warehouseOutboundCancelCmdExe;
    @Resource
    private SaveTradeInterceptCmdExe saveTradeInterceptCmdExe;
    @Resource
    private LogisticsRouteQryExe logisticsRouteQryExe;
    @Resource
    private TicTokUtil ticTokUtil;

    @Override
    public void syncTaobaoRefunds(int platformId, String systemParam) {
        this.syncRefundsData(platformId, systemParam, (startTime, endTime) -> {
            // 获取平台信息
            PlatformDO platform = this.platformService.getPlatformById(platformId);
            // 初始化参数
            BaseQueryDTO query = new BaseQueryDTO(startTime, endTime, 0, 100);
            for (int i = 0; i < 10000; i++) {
                // 获取数据
                query.setPageNo(query.getPageNo() + 1);
                log.info("query: {}", GsonUtil.objectToJson(query));
                RefundsReceiveGetResponse response = TaoBaoUtil.getRefundList(platform, query);
                if (response == null) {
                    throw new GlobalDefaultException(ResponseEnum.ERROR_SYNC_REFUND_TAOBAO);
                }

                // 保存数据
                if (response.getRefunds() != null && response.getRefunds().size() > 0) {
                    List<PlatformRefundDO> refunds = response.getRefunds().stream()
                            .map(taobaoRefund -> this.buildTaobaoPlatformRefund(platform, taobaoRefund))
                            .collect(Collectors.toList());
                    this.saveOrUpdateBatch(refunds);
                }

                // 判断下一页是否有数据
                if (!response.getHasNext()) {
                    break;
                }
            }
            // 更新数据同步时间
            this.systemParamService.updateSystemParam(systemParam, DateUtil.dateToStr(endTime));
        });
    }

    @Override
    public void syncTicTokRefunds(int platformId, String systemParam) {
        this.syncRefundsData(platformId, systemParam, (startTime, endTime) -> {
            int pageIndex = 0;
            int pageSize = 100;
            List<PlatformRefundDO> refunds = new ArrayList<>();
            // 获取平台信息K
            PlatformDO platform = this.platformService.getPlatformById(platformId);
            String shopId = "抖音小天才".equals(platform.getPlatformName()) ? XTC_TICTOK_SHOP_ID : BBK_TICTOK_SHOP_ID;
            long refundStartTime = startTime.getTime() / 1000;
            long refundEndTime = endTime.getTime() / 1000;
            String result = ticTokUtil.getTicTokRefundOrders(shopId, pageIndex, pageSize, refundStartTime, refundEndTime);
            if (StringUtils.isNotBlank(result)) {
                JsonObject refundList = GsonUtil.jsonToBean(result, JsonObject.class);
                int total = refundList.get("data").getAsJsonObject().get("total").getAsInt();
                parseRefundOrders(refundList, refunds, platform);
                for (int i = 1; i <= total / pageSize + 1; i++) {
                    String resultNew = ticTokUtil.getTicTokRefundOrders(shopId, i, pageSize, refundStartTime, refundEndTime);
                    JsonObject refundListNext = GsonUtil.jsonToBean(resultNew, JsonObject.class);
                    parseRefundOrders(refundListNext, refunds, platform);
                }
                this.saveOrUpdateBatch(refunds);
            }
            // 更新数据同步时间
            this.systemParamService.updateSystemParam(systemParam, DateUtil.dateToStr(endTime));
        });
    }

    /**
     * 抖音售后订单收集
     *
     * @param refundList 售后列表jsonobject
     * @param refunds    收集列表
     * @param platform   平台
     */
    public void parseRefundOrders(JsonObject refundList, List<PlatformRefundDO> refunds, PlatformDO platform) {
        JsonArray jsonArray = refundList.get("data").getAsJsonObject().get("items").getAsJsonArray();
        jsonArray.forEach(p -> {
            JsonObject jsonObject = p.getAsJsonObject();
            // 售后申请时间
            long applyTime = jsonObject.get("aftersale_info").getAsJsonObject().get("apply_time").getAsLong();
            Date applyTimeDate = new Date(applyTime * 1000);

            long updateTime = jsonObject.get("aftersale_info").getAsJsonObject().get("update_time").getAsLong();
            Date getRefundDate = DateUtil.strToDate("2021-09-05 00:00:00", "yyyy-MM-dd HH:mm:ss");
            if (applyTimeDate.after(getRefundDate)) {
                PlatformRefundDO platformRefundDO = new PlatformRefundDO();
                platformRefundDO.setIsProcess(0);
                platformRefundDO.setTotalFee(new BigDecimal(0));
                platformRefundDO.setRefundFee(new BigDecimal(0));
                platformRefundDO.setPayment(new BigDecimal(0));
                platformRefundDO.setPlatformId(platform.getPlatformId());
                platformRefundDO.setPlatformName(platform.getPlatformName());
                platformRefundDO.setPlatformTradeId(jsonObject.get("order_info").getAsJsonObject()
                        .get("shop_order_id").getAsString());
                platformRefundDO.setPlatformRefundId(jsonObject.get("aftersale_info").getAsJsonObject()
                        .get("aftersale_id").getAsString());

                // 售后状态，枚举为6(待商家同意),7(待买家退货),11(待商家二次同意),12(售后成功)
                // ,13(换货待买家收货),14(换货成功),27(商家一次拒绝),28(售后失败),29(商家二次拒绝)
                String aftersaleStatus = jsonObject.get("aftersale_info").getAsJsonObject().get("aftersale_status").getAsString();
                platformRefundDO.setRefundStatus(aftersaleStatus);
                platformRefundDO.setHasGoodReturn(0);
                JsonArray products = jsonObject.get("order_info").getAsJsonObject().get("related_order_info").getAsJsonArray();
                products.forEach(t -> {
                    // 商品名称
                    String product_name = t.getAsJsonObject().get("product_name").getAsString();
                    platformRefundDO.setGoodTitle(product_name);
                    // 商品数量
                    int num = t.getAsJsonObject().get("item_num").getAsInt();
                    platformRefundDO.setGoodNum(num);
                    // 支付金额 单位分
                    BigDecimal payAmount = t.getAsJsonObject().get("pay_amount").getAsBigDecimal();
                    platformRefundDO.setPayment(platformRefundDO.getPayment().add(payAmount.divide(new BigDecimal(100), 2, RoundingMode.UNNECESSARY)));
                    // 退款金额 单位分
                    BigDecimal refundAmount = t.getAsJsonObject().get("aftersale_pay_amount").getAsBigDecimal();
                    platformRefundDO.setTotalFee(platformRefundDO.getTotalFee().add(refundAmount.divide(new BigDecimal(100), 2, RoundingMode.UNNECESSARY)));
                    platformRefundDO.setRefundFee(platformRefundDO.getRefundFee().add(refundAmount.divide(new BigDecimal(100), 2, RoundingMode.UNNECESSARY)));
                });
                platformRefundDO.setRefundModifiedTime(DateFormatUtils.format(new Date(updateTime * 1000), "yyyy-MM-dd HH:mm:ss"));
                platformRefundDO.setRefundCreateTime(DateFormatUtils.format(new Date(applyTime * 1000), "yyyy-MM-dd HH:mm:ss"));
                platformRefundDO.setReason(jsonObject.get("text_part").getAsJsonObject().get("reason_text").getAsString());
                platformRefundDO.setExplanation("");

                String expressCompany = jsonObject.get("aftersale_info").getAsJsonObject().get("return_logistics_company_name").getAsString();
                String expressWaybillNo = jsonObject.get("aftersale_info").getAsJsonObject().get("return_logistics_code").getAsString();
                platformRefundDO.setExpressCompany(expressCompany);
                platformRefundDO.setExpressWaybillNo(expressWaybillNo);

                // 判断退货记录是否存在，存在则设置退货记录id
                this.refundIsExist(platformRefundDO, platform.getPlatformId());
                refunds.add(platformRefundDO);
            }
        });
    }

    public List<PlatformRefundDO> kuaishouRefundsOrder(List<MerchantRefundInfoView> kuaishouRefunds, PlatformDO platform) {
        List<PlatformRefundDO> refunds = new ArrayList<>();
        for (MerchantRefundInfoView merchantRefundInfoView : kuaishouRefunds) {
            Date submitTime = new Date(merchantRefundInfoView.getSubmitTime());
            Date getRefundDate = DateUtil.strToDate("2021-11-30 00:00:00", "yyyy-MM-dd HH:mm:ss");
            if (submitTime.after(getRefundDate)) {
                // PlatformRefundDO existRefund = this.getOne(Wrappers.<PlatformRefundDO>lambdaQuery()
                //         .eq(PlatformRefundDO::getPlatformRefundId, merchantRefundInfoView.getRefundId().toString())
                //         .eq(PlatformRefundDO::getPlatformId, platform.getPlatformId())
                //         .last("limit 1"));
                //if (existRefund == null) {
                PlatformRefundDO platformRefundDO = new PlatformRefundDO();
                platformRefundDO.setIsProcess(0);
                platformRefundDO.setTotalFee(new BigDecimal(0));
                platformRefundDO.setRefundFee(new BigDecimal(0));
                platformRefundDO.setPayment(new BigDecimal(0));
                platformRefundDO.setPlatformId(platform.getPlatformId());
                platformRefundDO.setPlatformName(platform.getPlatformName());
                platformRefundDO.setPlatformTradeId(merchantRefundInfoView.getOid().toString());
                platformRefundDO.setPlatformRefundId(merchantRefundInfoView.getRefundId().toString());
                platformRefundDO.setHasGoodReturn(merchantRefundInfoView.getReceiptStatus());
                //退款状态，枚举：[10, "买家仅退款申请"] [11, "买家退货退款申请"] [20, "平台介入-买家仅退款申请"] [21, "平台介入-买家退货退款申请"] [22, "平台介入-已确认退货退款"] [30, "商品回寄信息待买家更新"] [40, "商品回寄信息待卖家确认"] [50, "退款执行中"] [60, "退款成功"] [70, "退款失败"]
                platformRefundDO.setRefundStatus(merchantRefundInfoView.getStatus().toString());
                platformRefundDO.setRefundModifiedTime(DateUtil.dateToStr(new Date(merchantRefundInfoView.getUpdateTime()), "yyyy-MM-dd HH:mm:ss"));
                platformRefundDO.setReason(merchantRefundInfoView.getRefundReason() + "");
                KuaishouParamsDTO kuaishouParamsDTO = new KuaishouParamsDTO(platform);
                OrderDetail orderDetail = KuaishouUtil.getKuaishouTradeDetail(kuaishouParamsDTO, merchantRefundInfoView.getOid());
                if (orderDetail != null) {
                    platformRefundDO.setBuyerNickname(orderDetail.getOrderBaseInfo().getBuyerNick());
                    platformRefundDO.setRefundCreateTime(DateUtil.dateToStr(new Date(merchantRefundInfoView.getCreateTime()), "yyyy-MM-dd HH:mm:ss"));
                    platformRefundDO.setRefundFee(new BigDecimal(merchantRefundInfoView.getRefundFee()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_UNNECESSARY));
                    long payment = orderDetail.getOrderBaseInfo().getTotalFee();
                    platformRefundDO.setPayment(new BigDecimal(payment).divide(new BigDecimal(100), 2, BigDecimal.ROUND_UNNECESSARY));
                    platformRefundDO.setTotalFee(new BigDecimal(payment).divide(new BigDecimal(100), 2, BigDecimal.ROUND_UNNECESSARY));
                }
                MerchantRefundDetailDataView merchantRefundDetailDataView = KuaishouUtil.getKuaishouRefundDetail(kuaishouParamsDTO, merchantRefundInfoView.getRefundId());
                platformRefundDO.setExpressWaybillNo(merchantRefundDetailDataView.getLogisticsInfo() != null ? merchantRefundDetailDataView.getLogisticsInfo().getExpressNo() : "");
                platformRefundDO.setExpressCompany(merchantRefundDetailDataView.getLogisticsInfo() != null ? merchantRefundDetailDataView.getLogisticsInfo().getExpressCode() + "" : "");
                platformRefundDO.setExplanation(merchantRefundInfoView.getRefundDesc());
                refunds.add(platformRefundDO);
                //  }
            }
        }
        return refunds;
    }

    @Override
    public void syncKuaishouRefunds(int platformId, String systemParam) {
        this.syncRefundsData(platformId, systemParam, (startTime, endTime) -> {
            List<PlatformRefundDO> refunds = new ArrayList<>();
            // 获取平台信息
            PlatformDO platform = this.platformService.getPlatformById(platformId);
            KuaishouParamsDTO kuaishouParamsDTO = new KuaishouParamsDTO(platform);
            long beginTime = startTime.getTime();
            long endTime2 = endTime.getTime();
            OpenSellerOrderRefundPcursorListResponse response = KuaishouUtil.getRefundList(kuaishouParamsDTO, beginTime, endTime2, "", 1L);
            if (response.getResult() == 1) {
                List<PlatformRefundDO> refundsOne = kuaishouRefundsOrder(response.getData().getRefundOrderInfoList(), platform);
                refunds.addAll(refundsOne);
                long total = response.getData().getTotalPage();
                String pcursor = response.getData().getPcursor();
                for (int i = 1; i <= total; i++) {
                    OpenSellerOrderRefundPcursorListResponse response2 = KuaishouUtil.getRefundList(kuaishouParamsDTO, beginTime, endTime2, pcursor, Long.parseLong(i + ""));
                    if (response2.getResult() == 1) {
                        List<PlatformRefundDO> refundsTwo = kuaishouRefundsOrder(response2.getData().getRefundOrderInfoList(), platform);
                        refunds.addAll(refundsTwo);
                        pcursor = response2.getData().getPcursor();
                    }
                }
            } else if (response.getResult() == 28) {
                String json = KuaishouUtil.refreshToken(kuaishouParamsDTO);
                JsonObject jsonObject = com.xtc.springboot.util.GsonUtil.jsonToBean(json, JsonObject.class);
                if (jsonObject.get("result").getAsString().equals("1")) {
                    String access_token = jsonObject.get("access_token").getAsString();
                    String refresh_token = jsonObject.get("refresh_token").getAsString();
                    platform.setSessionKey(access_token);
                    platform.setRefreshToken(refresh_token);
                    this.platformService.updateById(platform);
                }
            }
            this.saveOrUpdateBatch(refunds);
            // 更新数据同步时间
            this.systemParamService.updateSystemParam(systemParam, DateUtil.dateToStr(endTime));
        });
    }

    @Override
    public List<PlatformRefundDO> getRefundList(String platformTradeId) {
        Wrapper<PlatformRefundDO> wrapper = Wrappers.<PlatformRefundDO>lambdaQuery()
                .eq(PlatformRefundDO::getPlatformTradeId, platformTradeId);
        return this.list(wrapper);
    }

    @Override
    public void syncAppRefunds(int platformId, String systemParam) {

        PlatformDO platform = this.platformService.getPlatformById(platformId);
        this.syncRefundsData(platformId, systemParam, (startTime, endTime) -> {
            List<PlatformRefundDO> platformRefundDOS = new ArrayList<>();
            ServiceOrderPageQry serviceOrderPageQry = ServiceOrderPageQry.builder()
                    .bizCode("xtc-shop")
                    .pageSize(30)
                    .pageIndex(1)
                    .updateTimeEnd(DateUtil.dateToLocalDateTime(endTime))
                    .updateTimeStart(DateUtil.dateToLocalDateTime(startTime))
                    .build();

            PageResponse<ServiceOrderDTO> services = serviceOrderFeignClient.pageServices(serviceOrderPageQry);
            if (services.isSuccess()) {
                // 总记录数
                int totalCount = services.getTotalCount();
                if (totalCount > 30) {
                    for (int i = 1; i <= totalCount / 30; i++) {
                        serviceOrderPageQry.setPageSize(30);
                        serviceOrderPageQry.setPageIndex(i);
                        PageResponse<ServiceOrderDTO> serviceOrderDTOPageResponse = serviceOrderFeignClient.pageServices(serviceOrderPageQry);
                        fileAppData(platformId, platform, platformRefundDOS, serviceOrderDTOPageResponse);
                    }
                } else {
                    fileAppData(platformId, platform, platformRefundDOS, services);
                }
            }

            // 更新数据同步时间
            this.systemParamService.updateSystemParam(systemParam, DateUtil.dateToStr(endTime));
        });
    }

    /**
     * 填充app 订单数据
     *
     * @param platformId        平台id
     * @param platform          平台信息
     * @param platformRefundDOS 保存数据
     * @param services          退款信息
     */
    private void fileAppData(int platformId, PlatformDO platform, List<PlatformRefundDO> platformRefundDOS, PageResponse<ServiceOrderDTO> services) {
        List<ServiceOrderDTO> serviceOrderDTOList = services.getData();
        for (ServiceOrderDTO serviceOrderDTO : serviceOrderDTOList) {
            PlatformRefundDO platformRefundDO = new PlatformRefundDO();
            platformRefundDO.setTotalFee(BigDecimal.ZERO);
            platformRefundDO.setRefundFee(BigDecimal.ZERO);
            platformRefundDO.setPayment(BigDecimal.ZERO);

            platformRefundDO.setPlatformId(platformId);
            platformRefundDO.setPlatformName(platform.getPlatformName());
            // 交易订单号
            platformRefundDO.setPlatformTradeId(serviceOrderDTO.getOrderNo());
            // 订单号
            platformRefundDO.setPlatformOrderId(serviceOrderDTO.getOrderItemNo());
            // 退款订单号
            platformRefundDO.setPlatformRefundId(serviceOrderDTO.getServiceNo());
            // 退款金额
            if (ObjectUtils.isNotEmpty(serviceOrderDTO.getPayment()) && serviceOrderDTO.getPayment() > 0) {
                platformRefundDO.setTotalFee(new BigDecimal(serviceOrderDTO.getPayment()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_UNNECESSARY));
                platformRefundDO.setRefundFee(new BigDecimal(serviceOrderDTO.getPayment()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_UNNECESSARY));
                platformRefundDO.setPayment(new BigDecimal(serviceOrderDTO.getPayment()).divide(new BigDecimal(100), 2, BigDecimal.ROUND_UNNECESSARY));
            }

            // 退款创建时间
            platformRefundDO.setRefundCreateTime(DateUtil.localDateTimeToStr(serviceOrderDTO.getCreateTime()));
            String refundModifiedTime = DateUtil.localDateTimeToStr(serviceOrderDTO.getCreateTime());
            if (serviceOrderDTO.getRefundTime() != null) {
                refundModifiedTime = DateUtil.localDateTimeToStr(serviceOrderDTO.getRefundTime());
            } else {
                if (serviceOrderDTO.getAuditTime() != null) {
                    refundModifiedTime = DateUtil.localDateTimeToStr(serviceOrderDTO.getAuditTime());
                }
            }
            platformRefundDO.setRefundModifiedTime(refundModifiedTime);
            // 退款原因
            platformRefundDO.setReason(serviceOrderDTO.getApplyReason());
            // 退款人
            platformRefundDO.setBuyerNickname("微信用户");
            // 退款创建时间
            platformRefundDO.setCreateTime(DateUtil.localDateTimeToStr(serviceOrderDTO.getCreateTime()));
            // 退货订单号
            platformRefundDO.setExpressWaybillNo(serviceOrderDTO.getReturnWaybillNo());
            // 商品标题
            platformRefundDO.setGoodTitle(serviceOrderDTO.getProductName());
            // 商品数量
            platformRefundDO.setGoodNum(serviceOrderDTO.getNum());

            platformRefundDO.setRefundStatus(serviceOrderDTO.getServiceState().name());

            platformRefundDO.setExpressCompany(serviceOrderDTO.getReturnExpressCompany());

            platformRefundDO.setExpressWaybillNo(serviceOrderDTO.getReturnWaybillNo());

            platformRefundDO.setIsProcess(0);
            // 支付金额
            platformRefundDOS.add(platformRefundDO);

            PlatformRefundDO platformRefund = this.platformRefundService.getOne(Wrappers.<PlatformRefundDO>lambdaQuery().eq(PlatformRefundDO::getPlatformRefundId, platformRefundDO.getPlatformRefundId()));
            if (ObjectUtils.isEmpty(platformRefund)) {
                this.platformRefundService.save(platformRefundDO);
            } else {
                platformRefundDO.setId(platformRefund.getId());
                this.platformRefundService.updateById(platformRefundDO);
            }
        }
    }

    @Override
    public void partRefundProcessJob() {
        List<TradeDO> tradeDOS = this.tradeService.getPartRefundProcess();
        for (TradeDO tradeDO : tradeDOS) {
            PlatformDO platform = this.platformService.getPlatformById(tradeDO.getPlatformId());
            TaobaoParamsDTO params = new TaobaoParamsDTO(platform);
            params.setOaid(tradeDO.getReceiverOaid());
            params.setTid(new Long(tradeDO.getPlatformTradeId()));
            // 查询订单数据
            Trade trade = TaoBaoUtil.getFullTradeInfo(params);
            if (new BigDecimal(trade.getPayment()).compareTo(tradeDO.getPayment().add(tradeDO.getPointPlatform()).add(tradeDO.getPointAlipayJf())) == 0) {
                String content = tradeDO.getPlatformTradeId() + "，这个订单存在部分退款又发货的现象，请核实！";
                String sendUsers = this.systemParamService.getSystemParamByName(GlobalConstant.SYSTEM_PAY_NOT_SHIPPING_NOTIFY);
                XtcMessageSender.cardTextMessage(sendUsers, content);
//                this.smsService.sendSms(tradeDO.getPlatformTradeId(), "部分退款发货提醒", content);
            }
        }
    }

    @Override
    public void refundsDataProcessJob() {
        // 获取未处理的退货数据(不处理会员商城)
        List<PlatformRefundDO> refunds = this.list(Wrappers.<PlatformRefundDO>lambdaQuery()
                .eq(PlatformRefundDO::getIsProcess, 0)
                .notIn(PlatformRefundDO::getPlatformId, GlobalConstant.PLATFORM_XTC_MALL)
                .ge(PlatformRefundDO::getUpdateTime, LocalDateTime.now().minusDays(16))
                .orderByDesc(PlatformRefundDO::getUpdateTime)
                .last(BaseTask.getShardingLimitSql(5000)));

        for (PlatformRefundDO refund : refunds) {
            log.info("订单 {} 退款单 {}，平台名称 {}，退款数据处理", refund.getPlatformTradeId(), refund.getPlatformRefundId(), refund.getPlatformName());
            boolean process = false;
            boolean notProcess = GlobalConstant.REFUND_STATUS_NOT_PROCESS.contains(refund.getRefundStatus());
            if (!notProcess) {
                // 获取需要退货处理的订单信息
                TradeDO trade = this.tradeService.getOne(Wrappers.<TradeDO>lambdaQuery().eq(TradeDO::getPlatformTradeId, refund.getPlatformTradeId()));
                if (trade != null) {
                    // 订单已经发货，但是又有退款记录进来，则发送短息
//                    if (OrderStatus.HAS_SHIP.name().equals(trade.getStatusOrder())) {
//                        String content = "订单已发货，订单：" + trade.getPlatformTradeId();
//                        this.smsService.sendSms(trade.getPlatformTradeId(), "退款处理异常", content);
//                    }
                    // 付款时间在90天之前的订单，不做处理
                    long paymentDays = Duration.between(DateUtil.dateToLocalDateTime(trade.getPaymentTime()), LocalDateTime.now()).toDays();
                    if (paymentDays > 90) {
                        process = true;
                        log.info("付款时间在90天之前的订单，不做处理，订单号：{}", trade.getPlatformTradeId());
                    } else {
                        process = isProcess(refund, process, trade);
                    }
                }
            }

            String time = refund.getPlatformId() == GlobalConstant.PLATFORM_TICTOK_XTC || refund.getPlatformId() == GlobalConstant.PLATFORM_TICTOK_BBK ? refund.getCreateTime() : refund.getRefundModifiedTime();
            int compareDay = refund.getPlatformId() == GlobalConstant.PLATFORM_TICTOK_XTC || refund.getPlatformId() == GlobalConstant.PLATFORM_TICTOK_BBK ? 16 : 14;
            // 未过帐前退款的订单，14天之内的退款数据默认未处理，超过14天标记为已处理
            long days = Duration.between(DateUtil.strToLocalDateTime(time), LocalDateTime.now()).toDays();
            // 判断是否处理完毕
            process = process || notProcess || days > compareDay;

            // 更新退款处理状态
            PlatformRefundDO updateRefund = new PlatformRefundDO();
            updateRefund.setId(refund.getId());
            updateRefund.setIsProcess(process ? 1 : 0);
            this.updateById(updateRefund);
        }
    }

    private boolean isProcess(PlatformRefundDO refund, boolean process, TradeDO trade) {
        if ((OrderStatus.RETURNED.name().equals(trade.getStatusOrder())
                || OrderStatus.HAS_CANCEL.name().equals(trade.getStatusOrder())
                || OrderStatus.HAS_SHIP.name().equals(trade.getStatusOrder()))) {
            return true;
        }
        // 判断部分退货
        Optional<Boolean> partRefund = this.partRefund(trade.getPlatformId(), trade.getPlatformTradeId());
        if (partRefund.isPresent()) {
            OrderStatus status = null;
            SFStatus sfStatus = null;
            if (SFStatus.NOT_SF.name().equals(trade.getStatusSf())) {
                status = this.notSfProcess(trade, refund);
            } else {
                sfStatus = this.sfProcess(trade, refund);
            }
            // 更新订单状态   顺丰状态: 取消成功
            boolean cancel = this.refundProcessUpdate(trade, refund, status, sfStatus, partRefund.get());
            // 判断是否处理完毕
            process = partRefund.get() || cancel;
        } else {
            log.info("查询部分退款异常，不做处理，订单号：{}", trade.getPlatformTradeId());
        }
        return process;
    }

    /**
     * 顺丰订单处理
     *
     * @param trade  订单信息
     * @param refund 退款信息
     * @return 顺丰状态
     */
    private SFStatus sfProcess(TradeDO trade, PlatformRefundDO refund) {
        if (!GlobalConstant.REFUND_STATUS_APPLY.contains(refund.getRefundStatus())
                && !GlobalConstant.REFUND_STATUS_REFUND.contains(refund.getRefundStatus())) {
            return null;
        }
        SFStatus sfStatus = SFStatus.valueOf(trade.getStatusSf());
        if (sfStatus == SFStatus.WAIT_THROW || sfStatus == SFStatus.CANCEL_OUTBOUNDS) {
            return SFStatus.CANCEL_OUTBOUNDS;
        }
        try {
            // 仓库出库取消，并更新数据状态（订单、出库单）
            warehouseOutboundCancelCmdExe.execute(trade.getPlatformTradeId());
            return SFStatus.CANCEL_OUTBOUNDS;
        } catch (Exception e) {
            log.warn("取消顺丰出库失败 msg: {}", e.getMessage(), e);
            // 保存订单拦截记录
            String content = "处理退款变更时，订单号 " + trade.getPlatformTradeId() + " 取消顺丰出库失败，请核实！";
            saveTradeInterceptCmdExe.execute(trade, "取消顺丰出库失败", content);
            return null;
        }
    }

    /**
     * 非顺丰订单处理
     *
     * @param trade  订单信息
     * @param refund 退款信息
     * @return 订单状态
     */
    private OrderStatus notSfProcess(TradeDO trade, PlatformRefundDO refund) {
        // 允许进行退款处理的仓库状态
        List<String> warehouseStatus = Lists.newArrayList(WarehouseStatus.HAVE_CONFIRMED.name(),
                WarehouseStatus.HAVE_DISTRIBUTED.name(), WarehouseStatus.HAVE_PRINTED.name(), WarehouseStatus.HAVE_REJECTED.name());
        // 允许进行退款处理的订单状态，订单冻结后续不再允许直接打回
        List<String> rejectStatus = Lists.newArrayList(OrderStatus.ORDER_CONFIRMED.name(),
                OrderStatus.WAIT_SHIP.name());

        // 退款状态：已申请
        // 订单状态：已确认、待发货、订单冻结，仓库状态：已确认、已配货、已打清单 已打回
        // 状态修改，订单状态：已打回，处理状态：已处理
        if (GlobalConstant.REFUND_STATUS_APPLY.contains(refund.getRefundStatus())
                && rejectStatus.contains(trade.getStatusOrder())
                && warehouseStatus.contains(trade.getWarehouseStatus())) {
            return OrderStatus.REJECT;
        }

        // 退款状态：已退款
        // 订单状态：已确认、待发货、订单冻结，仓库状态：已确认、已配货、已打清单 已打回
        // 状态修改，订单状态：已打回，处理状态：未处理
        if (GlobalConstant.REFUND_STATUS_REFUND.contains(refund.getRefundStatus())
                && rejectStatus.contains(trade.getStatusOrder())
                && warehouseStatus.contains(trade.getWarehouseStatus())) {
            return OrderStatus.REJECT;
        }

        // 退款状态：已退款
        // 订单状态：已打回，仓库状态：已打回
        // 状态修改，订单状态：过帐前退款，处理状态：已处理
        OrderStatus status = null;
        if (GlobalConstant.REFUND_STATUS_REFUND.contains(refund.getRefundStatus())
                && OrderStatus.REJECT.name().equals(trade.getStatusOrder())
                && WarehouseStatus.HAVE_REJECTED.name().equals(trade.getWarehouseStatus())) {
            status = OrderStatus.HAS_CANCEL;
        }
        // 退款状态：已退款
        // 订单状态：订单冻结，仓库状态：已处理
        // 状态修改，订单状态：过帐前退款，处理状态：已处理
        if (GlobalConstant.REFUND_STATUS_REFUND.contains(refund.getRefundStatus())
                && OrderStatus.ORDER_FREEZE.name().equals(trade.getStatusOrder())
                && WarehouseStatus.HAVE_CONFIRMED.name().equals(trade.getWarehouseStatus())) {
            status = OrderStatus.HAS_CANCEL;
        }
        return status;
    }

    @Override
    public PlatformRefundDO getByTradeIdAndPlatformId(String platformTradeId, Integer platformId) {
        return this.getOne(Wrappers.<PlatformRefundDO>lambdaQuery()
                .eq(PlatformRefundDO::getPlatformTradeId, platformTradeId)
                .eq(PlatformRefundDO::getPlatformId, platformId)
                .orderByDesc(PlatformRefundDO::getUpdateTime)
                .last("limit 1"));
    }

    @Override
    public void updateInterceptRouting() {
        List<SFInterceptBO> list = this.smsService.getRouteParseList();
        for (SFInterceptBO sfInterceptBO : list) {
            try {
                if ("顺丰速运".equals(sfInterceptBO.getExpressCompany())) {
                    List<LogisticInfoVO> routes = logisticsRouteQryExe.execute("顺丰速运", sfInterceptBO.getExpressTrackingNo());
                    boolean anyCancel = routes.stream()
                            .anyMatch(o -> o.getMemo().contains("退回快件已签收") || o.getMemo().contains("已取消寄件"));
                    boolean anyReturn = routes.stream()
                            .anyMatch(o -> o.getMemo().contains("退回中"));

                    if (anyCancel) {
                        SmsDO updateSms = new SmsDO();
                        updateSms.setPlatformTradeId(sfInterceptBO.getPlatformTradeId());
                        updateSms.setIsIntercept(3);
                        this.smsService.updateByTradeId(updateSms);
                    } else if (anyReturn) {
                        SmsDO updateSms = new SmsDO();
                        updateSms.setPlatformTradeId(sfInterceptBO.getPlatformTradeId());
                        updateSms.setIsIntercept(2);
                        this.smsService.updateByTradeId(updateSms);
                    }
                } else if ("圆通速递".equals(sfInterceptBO.getExpressCompany())) {
                    List<YuanTongLogisticsVO> yuanTongLogisticsList = YuanTongUtil.trackQueryAdapter(sfInterceptBO.getExpressTrackingNo());
                    for (YuanTongLogisticsVO yuanTongLogisticsVO : yuanTongLogisticsList) {
                        if (YuanTongLogIsticsEnum.SIGNED.name().equals(yuanTongLogisticsVO.getInfoContent())) {
                            SmsDO updateSms = new SmsDO();
                            updateSms.setPlatformTradeId(sfInterceptBO.getPlatformTradeId());
                            updateSms.setIsIntercept(3);
                            this.smsService.updateByTradeId(updateSms);
                        } else if (YuanTongLogIsticsEnum.TMS_RETURN.name().equals(yuanTongLogisticsVO.getInfoContent())) {
                            SmsDO updateSms = new SmsDO();
                            updateSms.setPlatformTradeId(sfInterceptBO.getPlatformTradeId());
                            updateSms.setIsIntercept(2);
                            this.smsService.updateByTradeId(updateSms);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("查看快递公司物流信息异常,物流公司{},运单号{}", sfInterceptBO.getExpressCompany(), sfInterceptBO.getExpressTrackingNo(), e);
            }
        }
    }

    @Override
    public void sfAutoIntercept() {
        // 顺丰拦截
        sfIntercept();
        // 更新快递状态
        updateTradeStatus();
    }

    /**
     * 顺丰拦截
     */
    public void sfIntercept() {
        List<SFInterceptBO> list = this.smsService.sfNeedInterceptList();
        for (SFInterceptBO bo : list) {
            try {
                if ("顺丰速运".equals(bo.getExpressCompany())) {
                    String monthCard = StringUtils.isBlank(bo.getMonthCard()) ? GlobalConstant.SF_MONTH_CARD : bo.getMonthCard();
                    String result = SFUtil.intercept(bo.getExpressTrackingNo(), monthCard);
                    if (result.contains("<Head>OK</Head>")) {
                        SmsDO updateSms = new SmsDO();
                        updateSms.setPlatformTradeId(bo.getPlatformTradeId());
                        updateSms.setResponseMessage(result);
                        updateSms.setIsIntercept(1);
                        updateSms.setTradeStatus(1);
                        this.smsService.updateByTradeId(updateSms);
                    }
                } else if ("圆通速递".equals(bo.getExpressCompany())) {
                    YuanTongInterceptDTO yuanTongInterceptDTO = new YuanTongInterceptDTO();
                    yuanTongInterceptDTO.setWaybillNo(bo.getExpressTrackingNo());
                    String result = YuanTongUtil.wantedChangeAdapter(yuanTongInterceptDTO);
                    if (StringUtils.isNotBlank(result)) {
                        String statusCode = GsonUtil.jsonToObject(result).get("statusCode").getAsString();
                        if ("0".equals(statusCode)) {
                            SmsDO updateSms = new SmsDO();
                            updateSms.setPlatformTradeId(bo.getPlatformTradeId());
                            updateSms.setResponseMessage(result);
                            updateSms.setIsIntercept(1);
                            updateSms.setTradeStatus(1);
                            this.smsService.updateByTradeId(updateSms);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("快递拦截失败,物流公司{},运单号{}", bo.getExpressCompany(), bo.getExpressTrackingNo(), e);
            }
        }
    }

    /**
     * 修改顺丰状态
     */
    public void updateTradeStatus() {
        List<SFInterceptBO> list = this.smsService.getRouteParseList();
        for (SFInterceptBO sfInterceptBO : list) {
            try {
                if ("顺丰速运".equals(sfInterceptBO.getExpressCompany())) {
                    String routing = SFUtil.getRouting(sfInterceptBO.getExpressTrackingNo());
                    Element root = DocumentHelper.parseText(routing).getRootElement();
                    if ("OK".equals(root.elementText("Head"))) {
                        List<Element> elements = root.element("Body").element("RouteResponse").elements();
                        for (Element element : elements) {
                            String remark = element.attributeValue("remark");
                            if (remark.contains("退回快件已签收") || remark.contains("已取消寄件")) {
                                SmsDO updateSms = new SmsDO();
                                updateSms.setPlatformTradeId(sfInterceptBO.getPlatformTradeId());
                                updateSms.setIsIntercept(3);
                                this.smsService.updateByTradeId(updateSms);
                            } else if (remark.contains("退回中")) {
                                SmsDO updateSms = new SmsDO();
                                updateSms.setPlatformTradeId(sfInterceptBO.getPlatformTradeId());
                                updateSms.setIsIntercept(2);
                                this.smsService.updateByTradeId(updateSms);
                            }
                        }
                    }
                } else if ("圆通速递".equals(sfInterceptBO.getExpressCompany())) {
                    List<YuanTongLogisticsVO> yuanTongLogisticsList = YuanTongUtil.trackQueryAdapter(sfInterceptBO.getExpressTrackingNo());
                    for (YuanTongLogisticsVO yuanTongLogisticsVO : yuanTongLogisticsList) {
                        if (YuanTongLogIsticsEnum.SIGNED.name().equals(yuanTongLogisticsVO.getInfoContent())) {
                            SmsDO updateSms = new SmsDO();
                            updateSms.setPlatformTradeId(sfInterceptBO.getPlatformTradeId());
                            updateSms.setIsIntercept(3);
                            this.smsService.updateByTradeId(updateSms);
                        } else if (YuanTongLogIsticsEnum.TMS_RETURN.name().equals(yuanTongLogisticsVO.getInfoContent())) {
                            SmsDO updateSms = new SmsDO();
                            updateSms.setPlatformTradeId(sfInterceptBO.getPlatformTradeId());
                            updateSms.setIsIntercept(2);
                            this.smsService.updateByTradeId(updateSms);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("查看快递公司物流信息异常,物流公司{},运单号{}", sfInterceptBO.getExpressCompany(), sfInterceptBO.getExpressTrackingNo(), e);
            }
        }
    }

    /**
     * 同步退款数据
     *
     * @param platformId  平台id
     * @param systemParam 系统参数
     * @param function    数据保存方法
     */
    private void syncRefundsData(int platformId, String systemParam, SyncRefundsFunction function) {
        // 只允许拉取1分钟之前的数据
        int beforeMinutes = 1;

        // 获取数据同步时间
        LocalDateTime syncTime = this.getRefundSyncTime(platformId, systemParam);
        if (syncTime.plusMinutes(beforeMinutes).isAfter(LocalDateTime.now())) {
            throw new RuntimeException("只允许拉取1分钟之前的退款数据");
        }

        // 设置时间跨度，默认取60分钟的数据
        Date startTime = DateUtil.localDateTimeToDate(syncTime.minusMinutes(1));
        Date endTime = this.getRefundSyncEndTime(syncTime, beforeMinutes);
        function.run(startTime, endTime);
    }

    /**
     * 获取退货数据同步时间
     *
     * @param platformId  平台id
     * @param systemParam 系统参数
     * @return 退货数据同步时间
     */
    private LocalDateTime getRefundSyncTime(int platformId, String systemParam) {
        // 查询系统参数
        String syncTime = this.systemParamService.getSystemParamByName(systemParam);
        // 查询最后一条退货数据的更新时间
        if (StringUtils.isBlank(syncTime)) {
            Wrapper<PlatformRefundDO> wrapper = Wrappers.<PlatformRefundDO>lambdaQuery()
                    .select(PlatformRefundDO::getRefundModifiedTime)
                    .eq(PlatformRefundDO::getPlatformId, platformId)
                    .orderByDesc(PlatformRefundDO::getRefundModifiedTime)
                    .last("limit 1");
            syncTime = this.getObj(wrapper, value -> (String) value);
        }
        // 查询系统开始时间
        if (StringUtils.isBlank(syncTime)) {
            syncTime = GlobalConstant.SYSTEM_START_TIME;
        }
        return DateUtil.strToLocalDateTime(syncTime);
    }

    /**
     * 获取退货数据同步的结束时间
     *
     * @param syncTime      退货数据同步时间
     * @param beforeMinutes 拉取数据的截止时间
     * @return 退货数据同步的结束时间
     */
    private Date getRefundSyncEndTime(LocalDateTime syncTime, int beforeMinutes) {
        LocalDateTime endTime;
        if (syncTime.plusDays(1).isAfter(LocalDateTime.now())) {
            long durationMinutes = Duration.between(syncTime, LocalDateTime.now()).toMinutes();
            endTime = syncTime.plusMinutes(durationMinutes - beforeMinutes);
        } else {
            endTime = syncTime.plusDays(1);
        }
        return DateUtil.localDateTimeToDate(endTime);
    }

    /**
     * 获取APP商城退货数据
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param pageCount 页数
     * @return APP商城退货数据
     */
    private String getAppRefunds(Date startTime, Date endTime, int pageCount) {
        return AppStoreUtil.getRefundList(startTime.getTime() / 1000 + "", endTime.getTime() / 1000 + "",
                "100", pageCount + "", "", "", "", "");
    }

    @Override
    public PlatformRefundDO buildTaobaoPlatformRefund(PlatformDO platform, Refund taobaoRefund) {
        PlatformRefundDO platformRefund = new PlatformRefundDO();
        platformRefund.setIsProcess(0);
        platformRefund.setPlatformId(platform.getPlatformId());
        platformRefund.setPlatformName(platform.getPlatformName());
        platformRefund.setPlatformRefundId(taobaoRefund.getRefundId());
        platformRefund.setPlatformTradeId(taobaoRefund.getTid().toString());
        platformRefund.setPlatformOrderId(taobaoRefund.getOid().toString());
        platformRefund.setTotalFee(new BigDecimal(taobaoRefund.getTotalFee()));
        platformRefund.setPayment(new BigDecimal(taobaoRefund.getPayment()));
        platformRefund.setRefundFee(new BigDecimal(taobaoRefund.getRefundFee()));
        platformRefund.setRefundCreateTime(DateUtil.dateToStr(taobaoRefund.getCreated()));
        platformRefund.setRefundModifiedTime(DateUtil.dateToStr(taobaoRefund.getModified()));
        platformRefund.setRefundStatus(taobaoRefund.getStatus());
        platformRefund.setTradeStatus(taobaoRefund.getOrderStatus());
        platformRefund.setGoodStatus(taobaoRefund.getGoodStatus());
        platformRefund.setHasGoodReturn(taobaoRefund.getHasGoodReturn() ? 1 : 0);
        platformRefund.setReason(taobaoRefund.getReason());
        platformRefund.setExplanation(taobaoRefund.getDesc());
        platformRefund.setBuyerNickname(taobaoRefund.getBuyerNick());
        platformRefund.setGoodTitle(taobaoRefund.getTitle());
        platformRefund.setGoodNum(taobaoRefund.getNum().intValue());
        platformRefund.setExpressCompany(taobaoRefund.getCompanyName());
        platformRefund.setExpressWaybillNo(taobaoRefund.getSid());

        // 判断退货记录是否存在，存在则设置退货记录id
        this.refundIsExist(platformRefund, platform.getPlatformId());
        return platformRefund;
    }

    /**
     * @param platformId  平台id
     * @param systemParam 系统参数
     */
    @Override
    public void syncJingDongRefunds(int platformId, String systemParam) {

        this.syncRefundsData(platformId, systemParam, ((startTime, endTime) -> {
            try {
                QueryWrapper<PlatformDO> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("platform_id", platformId);
                PlatformDO platformDO = platformService.getOne(queryWrapper);
                int page = 1;
                int pageSize = 50;
                if (ObjectUtils.isEmpty(platformDO)) {
                    log.error("找不到相关平台信息 ", platformId);
                    return;
                }
                //获取退款列表
                List<Integer> tiemTypeList = Lists.newArrayList(1, 2);
                for (Integer timeType : tiemTypeList) {
                    PopAfsSoaRefundapplyQueryPageListResponse returnList = JingDongUtil.getReturnList(platformDO, page, pageSize, startTime, endTime, timeType);
                    if (ObjectUtils.isEmpty(returnList)) {
                        log.error("获取退款列表失败");
                    }
                    QueryResult queryResult = returnList.getQueryResult();
                    //总记录数
                    Long totalCount = queryResult.getTotalCount();
                    if (totalCount > pageSize) {
                        for (int i = 1; i <= totalCount / pageSize + 1; i++) {
                            PopAfsSoaRefundapplyQueryPageListResponse response = JingDongUtil.getReturnList(platformDO, i, pageSize, startTime, endTime, timeType);
                            if (ObjectUtils.isEmpty(response) || !response.getQueryResult().getSuccess()) {
                                log.error("获取京东退款列表失败");
                                return;
                            }
                            List<RefundApplyVo> result = response.getQueryResult().getResult();
                            result.forEach(r -> {
                                tradeService.saveReturnOrderList(response.getQueryResult(), platformDO);
                            });
                        }
                    } else {
                        tradeService.saveReturnOrderList(returnList.getQueryResult(), platformDO);
                    }
                }
                this.systemParamService.updateSystemParam(systemParam, DateUtil.dateToStr(endTime));
            } catch (Exception e) {
                log.error("同步京东退款列表异常");
            }
        }));
    }

    @Override
    public List<PlatformRefundDO> listByPlatformTradeIds(List<String> tradeIds) {
        LambdaQueryWrapper<PlatformRefundDO> wrapper = Wrappers.<PlatformRefundDO>lambdaQuery().in(PlatformRefundDO::getPlatformTradeId, tradeIds)
                .eq(PlatformRefundDO::getRefundStatus, "SUCCESS");
        return this.list(wrapper);
    }

    @Override
    public void syncXiaoHongShuRefundsJob(int platformId, String systemParma) {
        PlatformDO platform = this.platformService.getPlatformById(platformId);
        Date endDate = new Date();
        Date startDate = DateUtils.addHours(endDate, -16);
        long beginTime = startDate.getTime() / 100;
        long finishTime = endDate.getTime() / 100;
        try {
            BaseResponse<GetAfterSaleListResponse> afterSaleList = XiaoHongShuUtil.getAfterSaleList(platform, beginTime, finishTime, 1, 1, 100);
            if (afterSaleList.isSuccess()) {
                GetAfterSaleListResponse data = afterSaleList.getData();
                // 总数
                int total = data.getTotal();
                // 页数
                int pageSize = data.getPageSize();
                if (total > 0) {
                    for (int i = 0; i <= total / pageSize; i++) {
                        BaseResponse<GetAfterSaleListResponse> responseBaseResponse = XiaoHongShuUtil.getAfterSaleList(platform, beginTime, finishTime, 1, i + 1, 100);
                        List<GetAfterSaleListResponse.SimpleAfterSaleDTO> simpleAfterSaleList = responseBaseResponse.getData().getSimpleAfterSaleList();
                        // 退款集合
                        List<PlatformRefundDO> platformRefunds = new ArrayList<>();
                        for (GetAfterSaleListResponse.SimpleAfterSaleDTO simpleAfterSaleDTO : simpleAfterSaleList) {
                            PlatformRefundDO platformRefundServiceOne = this.platformRefundService.getOne(Wrappers.<PlatformRefundDO>lambdaQuery()
                                    .eq(PlatformRefundDO::getPlatformRefundId, simpleAfterSaleDTO.getReturnsId())
                                    .last("limit 1"));
                            if (ObjectUtils.isEmpty(platformRefundServiceOne)) {
                                // 填充退款属性
                                PlatformRefundDO platformRefundDO = new PlatformRefundDO();
                                fillPlatformRefundDO(platform, simpleAfterSaleDTO, platformRefundDO);
                                // 填充退款商品信息
                                fillRefundGoods(platform, simpleAfterSaleDTO, platformRefundDO);
                                // 保存数据
                                platformRefunds.add(platformRefundDO);
                            } else {
                                platformRefundServiceOne.setUpdateTime(DateUtil.dateToStr(new Date(simpleAfterSaleDTO.getUpdateTime())));
                                platformRefundServiceOne.setRefundStatus(simpleAfterSaleDTO.getRefundStatus() + "");
                                platformRefundService.updateById(platformRefundServiceOne);
                            }
                        }
                        // 保存数据
                        platformRefundService.saveBatch(platformRefunds);
                    }
                }
            }
        } catch (Exception e) {
            log.error("小红书平台同步退货数据异常{}", e);
        }
    }

    /**
     * 获取退款列表
     *
     * @param duration  间隔时间
     * @param limitSize 数量大小
     * @return 退款列表
     */
    @Override
    public List<PlatformRefundDO> listByExpressWayBillNoAndUpdateTime(Duration duration, String limitSize) {
        // 计算时间范围
        LocalDateTime updateTimeEnd = LocalDateTime.now();
        LocalDateTime updateTimeStart = updateTimeEnd.minus(duration);

        Wrapper<PlatformRefundDO> wrapper = Wrappers.<PlatformRefundDO>lambdaQuery()
                .isNotNull(PlatformRefundDO::getExpressWaybillNo)
                .between(PlatformRefundDO::getUpdateTime, updateTimeStart, updateTimeEnd)
                .orderByDesc(PlatformRefundDO::getId)
                .last(limitSize);
        return this.list(wrapper);
    }

    /**
     * 填充退款中的商品属性
     *
     * @param platform           平台信息
     * @param simpleAfterSaleDTO 订单数据
     * @param platformRefundDO   退款信息
     * @throws IOException 异常
     */
    private void fillRefundGoods(PlatformDO platform, GetAfterSaleListResponse.SimpleAfterSaleDTO simpleAfterSaleDTO, PlatformRefundDO platformRefundDO) throws IOException {
        BaseResponse<GetAfterSaleDetailResponse> afterSaleDetail = XiaoHongShuUtil.getAfterSaleDetail(platform, simpleAfterSaleDTO.getReturnsId());
        if (afterSaleDetail.isSuccess()) {
            List<GetAfterSaleDetailResponse.AfterSaleSKu> skus = afterSaleDetail.getData().getSkus();
            for (GetAfterSaleDetailResponse.AfterSaleSKu afterSaleSku : skus) {
                platformRefundDO.setGoodNum(afterSaleSku.getBoughtCount());
                platformRefundDO.setRefundFee(new BigDecimal(afterSaleSku.getReturnPrice() + ""));
                platformRefundDO.setTotalFee(new BigDecimal(afterSaleSku.getPrice() + ""));
                platformRefundDO.setPlatformTradeId(simpleAfterSaleDTO.getOrderId());
                platformRefundDO.setPayment(new BigDecimal(afterSaleSku.getPrice() + ""));
                platformRefundDO.setPlatformOrderId(afterSaleSku.getSkuId());
            }
        }
    }

    /**
     * 填充退款属性
     *
     * @param platform           平台吗名称
     * @param simpleAfterSaleDTO 订单列表
     * @param platformRefundDO   退款信息
     */
    private void fillPlatformRefundDO(PlatformDO platform, GetAfterSaleListResponse.SimpleAfterSaleDTO simpleAfterSaleDTO, PlatformRefundDO platformRefundDO) {
        platformRefundDO.setPlatformId(platform.getPlatformId());
        platformRefundDO.setPlatformName(platform.getPlatformName());
        platformRefundDO.setRefundStatus(simpleAfterSaleDTO.getRefundStatus() + "");
        platformRefundDO.setPlatformRefundId(simpleAfterSaleDTO.getReturnsId());
        platformRefundDO.setPlatformTradeId(simpleAfterSaleDTO.getOrderId());
        platformRefundDO.setCreateTime(DateUtil.dateToStr(new Date(simpleAfterSaleDTO.getCreatedTime())));
        platformRefundDO.setReason(simpleAfterSaleDTO.getReason());
    }

    /**
     * 构建平台退货单
     *
     * @param platform 平台信息
     * @param refund   APP商城退货信息
     * @param order    APP商城退货商品信息
     * @return 平台退货单
     */
    private PlatformRefundDO buildAppPlatformRefund(PlatformDO platform, JsonObject refund, JsonObject order) {
        PlatformRefundDO platformRefund = new PlatformRefundDO();
        platformRefund.setIsProcess(0);
        platformRefund.setPlatformId(platform.getPlatformId());
        platformRefund.setPlatformName(platform.getPlatformName());
        platformRefund.setPlatformRefundId(refund.get("id") != null ? refund.get("id").getAsString() : null);
        platformRefund.setPlatformTradeId(refund.get("order_sn") != null ? refund.get("order_sn").getAsString() : null);
        platformRefund.setPlatformOrderId(order.get("refund_sn") != null ? order.get("refund_sn").getAsString() : null);
        platformRefund.setTotalFee(new BigDecimal(refund.get("refund_price") != null ? refund.get("refund_price").getAsString() : "0.00"));
        platformRefund.setPayment(new BigDecimal(refund.get("refund_price") != null ? refund.get("refund_price").getAsString() : "0.00"));
        platformRefund.setRefundFee(platformRefund.getPayment());
        platformRefund.setRefundCreateTime(refund.get("create_time") != null ? DateUtil.secondToStr(refund.get("create_time").getAsLong()) : null);
        platformRefund.setRefundModifiedTime(refund.get("last_update_time") != null ? DateUtil.secondToStr(refund.get("last_update_time").getAsLong()) : null);
        platformRefund.setRefundStatus(refund.get("refund_status") != null ? refund.get("refund_status").getAsString() : null);
        platformRefund.setBuyerNickname(refund.get("member_name") != null ? refund.get("member_name").getAsString() : null);
        platformRefund.setGoodTitle(order.get("goods_name") != null ? order.get("goods_name").getAsString() : null);
        platformRefund.setGoodNum(order.get("return_num") != null ? order.get("return_num").getAsInt() : null);
        platformRefund.setReason(refund.get("refund_reason") != null ? refund.get("refund_reason").getAsString() : null);
        platformRefund.setExplanation(refund.get("customer_remark") != null ? refund.get("customer_remark").getAsString() : null);
        String refuseType = refund.get("refuse_type") != null ? refund.get("refuse_type").getAsString() : "";
        platformRefund.setHasGoodReturn("RETURN_GOODS".equals(refuseType) ? 1 : 0);
        platformRefund.setExpressCompany(refund.get("refund_ship_id") != null ? refund.get("refund_ship_id").getAsString() : null);
        platformRefund.setExpressWaybillNo(refund.get("refund_ship_sn") != null && !refund.get("refund_ship_sn").isJsonNull() ? refund.get("refund_ship_sn").getAsString() : null);

        // 判断退货记录是否存在，存在则设置退货记录id
        this.refundIsExist(platformRefund, platform.getPlatformId());
        return platformRefund;
    }

    /**
     * 判断退货记录是否存在，存在则设置退货记录id
     *
     * @param platformRefund 退货记录
     * @param platformId     平台id
     */
    public void refundIsExist(PlatformRefundDO platformRefund, int platformId) {
        PlatformRefundDO exist = this.getOne(Wrappers.<PlatformRefundDO>lambdaQuery()
                .eq(PlatformRefundDO::getPlatformRefundId, platformRefund.getPlatformRefundId())
                .eq(PlatformRefundDO::getPlatformId, platformId)
                .last("limit 1"));
        if (exist != null) {
            platformRefund.setId(exist.getId());
        }
    }

    /**
     * 退款处理的数据更新
     *
     * @param trade      订单信息
     * @param refund     订单退款信息
     * @param status     新的订单状态
     * @param sfStatus   新的顺丰状态
     * @param partRefund 是否部分退款
     * @return 是否取消订单
     */
    private boolean refundProcessUpdate(TradeDO trade, PlatformRefundDO refund, OrderStatus status, SFStatus sfStatus, boolean partRefund) {
        // 是否取消订单
        boolean cancel = false;
        // 更新订单数据
        if (status != null || sfStatus != null) {
            TradeDO updateTrade = new TradeDO();
            updateTrade.setId(trade.getId());
            if (status != null) {
                // 部分退货打回订单
                updateTrade.setStatusOrder(partRefund ? OrderStatus.REJECT.name() : status.name());
                // 如果仓库状态为已确认，则直接改为已打回
                if (WarehouseStatus.HAVE_CONFIRMED.name().equals(trade.getWarehouseStatus())) {
                    updateTrade.setWarehouseStatus(WarehouseStatus.HAVE_REJECTED.name());
                    updateTrade.setRejectTime(DateUtil.dateToStr(new Date()));
                }
            }
            if (sfStatus != null) {
                String orderStatus = trade.getStatusOrder();
                // 部分退货打回订单
                if (GlobalConstant.REFUND_STATUS_APPLY.contains(refund.getRefundStatus())
                        || GlobalConstant.REFUND_STATUS_NOT_PROCESS.contains(refund.getRefundStatus())) {
                    orderStatus = OrderStatus.WAIT_SHIP.name();
                }
                if (GlobalConstant.REFUND_STATUS_REFUND.contains(refund.getRefundStatus())) {
                    orderStatus = partRefund ? OrderStatus.WAIT_SHIP.name() : OrderStatus.HAS_CANCEL.name();
                }
                updateTrade.setStatusOrder(orderStatus);
            }
            this.tradeService.updateById(updateTrade);
            log.info("订单：{}，新订单状态：{}，新仓库状态：{}, 新顺丰状态: {}", trade.getPlatformTradeId(),
                    updateTrade.getStatusOrder(), updateTrade.getWarehouseStatus(), updateTrade.getStatusSf());
            cancel = OrderStatus.HAS_CANCEL.name().equals(updateTrade.getStatusOrder());
        }
        return cancel;
    }

    @Override
    public void updateRefundStatus() {
        Wrapper<ReturnMasterDO> wrapper = Wrappers.<ReturnMasterDO>lambdaQuery()
                .eq(ReturnMasterDO::getRefundStatus, RefundStatusEnum.NOT)
                .eq(ReturnMasterDO::getType, "RETURN")
                .ge(ReturnMasterDO::getCreateTime, LocalDateTime.now().minusDays(4));
        // 更新退货数据
        List<ReturnMasterDO> returnMasters = this.returnMasterService.list(wrapper);
        returnMasters.forEach(returnMaster -> {
            try {
                log.info("订单 {} 退货数据状态更新", returnMaster.getPlatformTradeId());
                Wrapper<PlatformRefundDO> platformReturnWrapper = Wrappers.<PlatformRefundDO>lambdaQuery()
                        .eq(PlatformRefundDO::getPlatformTradeId, returnMaster.getPlatformTradeId())
                        .in(PlatformRefundDO::getRefundStatus, GlobalConstant.REFUND_STATUS_COMPLETED);
                List<PlatformRefundDO> refundList = this.list(platformReturnWrapper);

                if (CollectionUtils.isEmpty(refundList)) {
                    log.info("订单 {} 退款数据为空更新", returnMaster.getPlatformTradeId());
                    return;
                }

                // 处理金额
                List<PlatformRefundDO> platformRefunds = refundList.stream()
                        .filter(platformRefundDO -> platformRefundDO.getRefundFee().compareTo(BigDecimal.ZERO) != 0)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(platformRefunds)) {
                    return;
                }

                // 处理退款备注
                String explanation = platformRefunds.stream().map(PlatformRefundDO::getExplanation)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(","));

                // 处理快递单号
                String expressWaybillNo = platformRefunds.stream().map(PlatformRefundDO::getExpressWaybillNo)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.joining(","));

                // 计算总退款金额
                BigDecimal refundPayment = platformRefunds.stream()
                        .map(PlatformRefundDO::getRefundFee)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 处理退款时间
                List<String> refundModifiedTime = platformRefunds.stream().map(PlatformRefundDO::getRefundModifiedTime)
                        .filter(StringUtils::isNotBlank)
                        .sorted(Comparator.reverseOrder())
                        .collect(Collectors.toList());

                // 更新退款状态
                ReturnMasterDO updateReturn = new ReturnMasterDO();
                updateReturn.setId(returnMaster.getId());
                updateReturn.setReason(explanation);
                updateReturn.setPostTrackingNo(expressWaybillNo);
                updateReturn.setRealAmount(refundPayment);
                updateReturn.setRefundStatus(RefundStatusEnum.HAVE_REFUNDED.name());
                if (CollectionUtils.isNotEmpty(refundModifiedTime) && StringUtils.isNotBlank(refundModifiedTime.get(0))) {
                    updateReturn.setRefundTime(refundModifiedTime.get(0));
                }
                log.info("订单 {}，退货数据更新 备注 -> {}，快递单号 -> {}，退款金额 -> {}，退款状态 -> {}",
                        returnMaster.getPlatformTradeId(), explanation,
                        expressWaybillNo, refundPayment, RefundStatusEnum.HAVE_REFUNDED.name());
                this.returnMasterService.updateById(updateReturn);
            } catch (Exception e) {
                log.warn("订单 {} 退货数据状态更新异常{}", returnMaster.getPlatformTradeId(), e.getMessage(), e);
            }
        });
    }

    @Override
    public Optional<Boolean> partRefund(int platformId, String tradeId) {
        try {
            boolean partRefund = true;
            // 排除不是天猫,京东，抖音的订单
            if (!GlobalConstant.PLATFORM_PART_REFUDN_CHECK.contains(platformId)) {
                return Optional.empty();
            }
            boolean isAllRefundByMainStatus = this.serviceOrderRefundChecker.isAllRefundByMainStatus(platformId, tradeId);
            if (isAllRefundByMainStatus) {
                return Optional.of(false);
            }
            if (GlobalConstant.PLATFORM_JD_BBK == platformId || GlobalConstant.PLATFORM_JD_XTC == platformId) {
                List<PlatformRefundDO> partList = this.platformRefundService.getRefundList(tradeId).stream().filter(p -> p.getRefundStatus().equals("JD-3")).collect(Collectors.toList());
                if (partList != null && partList.size() == 0) {
                    return Optional.empty();
                } else {
                    return Optional.of(false);
                }
            }
            if (GlobalConstant.PLATFORM_TAOBAO.contains(platformId)) {
                // 查询详细订单
                PlatformDO platform = this.platformService.getPlatformById(platformId);
                Trade taobaoTrade = this.tradeService.getTaobaoTradeDetail(platform, tradeId);
                if (taobaoTrade == null || taobaoTrade.getOrders() == null) {
                    return Optional.empty();
                }

                long refund = taobaoTrade.getOrders().stream()
                        .filter(order -> ("TRADE_CLOSED".equals(order.getStatus())
                                && order.getRefundStatus() != null
                                && "SUCCESS,NO_REFUND".contains(order.getRefundStatus()))
                        ).count();
                if (refund != 0) {
                    partRefund = refund != taobaoTrade.getOrders().size();
                    log.info("部分退款判断结果: {}, refund: {}, orders size: {}, orders: {}",
                            partRefund, refund, taobaoTrade.getOrders().size(), GsonUtil.objectToJson(taobaoTrade.getOrders()));
                    return Optional.of(partRefund);
                } else {
                    return Optional.empty();
                }
            }
            List<OrderDO> orderDOS = this.orderService.getOrderDOS(tradeId)
                    .stream().filter(orderDO -> orderDO.getOrderType() == 1 || orderDO.getOrderType() == 4)
                    .collect(Collectors.toList());
            // 订单数量
            int orderSize = 0;
            if (orderDOS != null && orderDOS.size() != 0) {
                orderSize = orderDOS.stream().mapToInt(OrderDO::getNum).sum();
            }
            if (GlobalConstant.PLATFORM_APP_XTC == platformId) {
                // 调用服务单接口
                ServiceOrderPageQry query = ServiceOrderPageQry.builder().
                        orderNo(tradeId)
                        .pageSize(20)
                        .pageIndex(1)
                        .bizCode("xtc-shop")
                        .serviceState(ServiceState.COMPLETED)
                        .serviceType(ServiceType.REFUND)
                        .build();
                PageResponse<ServiceOrderDTO> serviceOrderDTOPageResponse = this.serviceOrderFeignClient.pageServices(query);
                List<ServiceOrderDTO> serviceOrderDTOS = serviceOrderDTOPageResponse.getData();
                if (CollectionUtils.isNotEmpty(serviceOrderDTOS)) {
                    // 退款数量
                    int serviceSize = serviceOrderDTOS.stream().mapToInt(ServiceOrderDTO::getNum).sum();
                    int sum = serviceOrderDTOS.stream().mapToInt(ServiceOrderDTO::getRefundAmount).sum();
                    BigDecimal payment = new BigDecimal(sum).divide(new BigDecimal(100));
                    partRefund = isPartRefund(tradeId, orderSize, payment, serviceSize);
                } else {
                    return Optional.empty();
                }
            }

            if (GlobalConstant.PLATFORM_TICTOK.contains(platformId)) {
                //用最简单的方法，判断我们数据的退货数据
                List<PlatformRefundDO> partList = this.platformRefundService.getRefundList(tradeId).stream().filter(p -> p.getRefundStatus().equals("12")).collect(Collectors.toList());
                int refundSize = partList.size();
                BigDecimal refundMoney = BigDecimal.ZERO;
                if (partList != null && partList.size() != 0) {
                    for (PlatformRefundDO refund : partList) {
                        refundMoney = refundMoney.add(refund.getRefundFee());
                    }
                    partRefund = isPartRefund(tradeId, orderDOS.size(), refundMoney, refundSize);
                } else {
                    return Optional.empty();
                }
            }
            if (GlobalConstant.PLATFORM_PDD.contains(platformId)) {
                List<PlatformRefundDO> partList = this.platformRefundService.getRefundList(tradeId);
                // 不存在退款信息 直接不处理
                if (CollectionUtils.isEmpty(partList)) {
                    return Optional.empty();
                }
                // 存在退款成功的记录 直接判断全退
                List<PlatformRefundDO> refunds = partList.stream()
                        .filter(p -> GlobalConstant.REFUND_STATUS_REFUND.contains(p.getRefundStatus()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(refunds)) {
                    return Optional.of(true);
                }
            }
            // 视频号校验是否全退
            if (GlobalConstant.WECHAT_CHANNELS_SHOP.contains(platformId)) {
                List<PlatformRefundDO> partList = this.platformRefundService.getRefundList(tradeId);
                // 不存在退款信息 直接不处理
                if (CollectionUtils.isEmpty(partList)) {
                    return Optional.empty();
                }
                // 存在退款成功的记录 直接判断全退
                List<PlatformRefundDO> refunds = partList.stream()
                        .filter(p -> GlobalConstant.REFUND_STATUS_REFUND.contains(p.getRefundStatus()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(refunds)) {
                    return Optional.of(true);
                }
            }

            log.info("退货数据处理成功,订单号" + tradeId);
            return Optional.of(partRefund);
        } catch (Exception e) {
            log.error("退货数据处理异常,订单号" + tradeId);
            return Optional.empty();
        }
    }

    /**
     * 判断是否部分退款（金额和订单数量）
     *
     * @param tradeId    订单号
     * @param number     订单数量
     * @param payment    付款金额
     * @param refundSize 退款数量
     * @return
     */
    private boolean isPartRefund(String tradeId, int number, BigDecimal payment, int refundSize) {
        //false代表不是部分退款，true代表部分退款
        boolean partRefund = number != refundSize;
        //比较金额，金额一致代表不是部分退款
        TradeDO trade = this.tradeService.getTradeByTradeId(tradeId);
        if (trade != null) {
            // 付款金额>退款金额并且订单数量不等于退款数量（部分退款）
            BigDecimal tradePayment = trade.getPayment();
            if (tradePayment.compareTo(payment) <= 0) {
                partRefund = false;
            } else {
                partRefund = true;
            }
        }
        return partRefund;
    }

    /**
     * 计算总退款金额
     *
     * @param tradeId 订单号
     * @return 总退款金额
     */
    private BigDecimal sumRefundFee(String tradeId) {
        Wrapper<PlatformRefundDO> wrapper = Wrappers.<PlatformRefundDO>lambdaQuery()
                .eq(PlatformRefundDO::getPlatformTradeId, tradeId)
                .in(PlatformRefundDO::getRefundStatus, GlobalConstant.REFUND_STATUS_COMPLETED);
        List<PlatformRefundDO> refunds = this.list(wrapper);
        return refunds.stream().map(PlatformRefundDO::getRefundFee).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public List<PlatformRefundDO> listByExpressNo(String expressNo) {
        if (StringUtils.isBlank(expressNo)) {
            return Collections.emptyList();
        }
        Wrapper<PlatformRefundDO> wrapper = Wrappers.<PlatformRefundDO>lambdaQuery()
                .eq(PlatformRefundDO::getExpressWaybillNo, expressNo)
                .orderByDesc(PlatformRefundDO::getId)
                .last(BaseDaoConstant.DEFAULT_LIST_SIZE_LIMIT);
        return this.list(wrapper);
    }

    /**
     * 退款数据同步方法
     */
    @FunctionalInterface
    public interface SyncRefundsFunction {

        void run(Date startTime, Date endTime);

    }

}
