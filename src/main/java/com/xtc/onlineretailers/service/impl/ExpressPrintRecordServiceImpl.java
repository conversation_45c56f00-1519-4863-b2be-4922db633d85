package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.mapper.master.ExpressPrintRecordMapper;
import com.xtc.onlineretailers.pojo.entity.ExpressPrintRecordDO;
import com.xtc.onlineretailers.service.ExpressPrintRecordService;
import org.springframework.stereotype.Service;

@Service
public class ExpressPrintRecordServiceImpl extends ServiceImpl<ExpressPrintRecordMapper, ExpressPrintRecordDO> implements ExpressPrintRecordService {

    @Override
    public String getPrintInfo(String platformTradeId) {
        return this.getObj(Wrappers.<ExpressPrintRecordDO>lambdaQuery()
                .select(ExpressPrintRecordDO::getPrintInfo)
                .eq(ExpressPrintRecordDO::getPlatformTradeId, platformTradeId)
                .last("limit 1")
                .orderByDesc(ExpressPrintRecordDO::getCreateTime), i -> (String) i);
    }

}
