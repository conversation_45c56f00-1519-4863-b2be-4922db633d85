package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.enums.*;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.executor.command.ReceiptWarehouseCmdExe;
import com.xtc.onlineretailers.executor.command.WorkWechatAlertSenderCmdExe;
import com.xtc.onlineretailers.mapper.master.ReceiveMasterMapper;
import com.xtc.onlineretailers.pojo.entity.*;
import com.xtc.onlineretailers.pojo.query.*;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.pojo.vo.ReceiveMasterExportVO;
import com.xtc.onlineretailers.pojo.vo.TradeOrderInfoVO;
import com.xtc.onlineretailers.refactor.executor.command.TradeRemarkCmdExe;
import com.xtc.onlineretailers.refactor.pojo.entity.DistributorBarcodeDO;
import com.xtc.onlineretailers.refactor.repository.AdminUsersRepository;
import com.xtc.onlineretailers.repository.AdminRoleUserDao;
import com.xtc.onlineretailers.repository.DistributorBarcodeDao;
import com.xtc.onlineretailers.repository.RolesDao;
import com.xtc.onlineretailers.repository.WarehouseBarcodeDao;
import com.xtc.onlineretailers.service.*;
import com.xtc.onlineretailers.util.CollectionCopier;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.ExcelUtil;
import com.xtc.springboot.pojo.entity.GlobalContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class ReceiveMasterServiceImpl extends ServiceImpl<ReceiveMasterMapper, ReceiveMasterDO> implements ReceiveMasterService {

    private final RepairService repairService;
    private final ReceiveDetailsService receiveDetailsService;
    private final TradeService tradeService;
    private final ReturnMasterService returnMasterService;
    private final ReturnDetailsService returnDetailsService;
    private final OrderService orderService;
    private final PlatformRefundService platformRefundService;
    private final SmsService smsService;
    private final OvertimeService overtimeService;
    private final RepairExchangeService repairExchangeService;
    private final TradeRemarkCmdExe tradeRemarkCmdExe;
    private final PlatformService platformService;
    private final DistributorBarcodeDao distributorBarcodeDao;
    private final WorkWechatAlertSenderCmdExe workWechatAlertSenderCmdExe;
    private final SystemParamService systemParamService;
    private final WarehouseBarcodeDao warehouseBarcodeDao;
    private final ReceiptWarehouseCmdExe receiptWarehouseCmdExe;

    @Override
    public void add(ReceiveMasterAddQuery query) {
        Wrapper<ReceiveMasterDO> wrapper = Wrappers.<ReceiveMasterDO>lambdaQuery()
                .eq(ReceiveMasterDO::getExpressTrackingNo, query.getExpressTrackingNo())
                .last("limit 1");
        ReceiveMasterDO receiveMasterDO1 = this.getOne(wrapper);
        if (receiveMasterDO1 != null) {
            throw new GlobalDefaultException(ResponseEnum.EXPRESS_EXIST);
        }
        // 获取退款列表
        List<PlatformRefundDO> platformRefundDOS = platformRefundService.listByExpressNo(query.getExpressTrackingNo());

        // 获取拼接‘订单号’
        String tradeId = this.getTradeId(query.getExpressTrackingNo(), platformRefundDOS);

        ReceiveMasterDO receiveMasterDO = new ReceiveMasterDO();
        BeanUtils.copyProperties(query, receiveMasterDO);
        platformRefundDOS.stream().findAny().ifPresent(p -> {
            receiveMasterDO.setPlatformTradeId(tradeId);
            receiveMasterDO.setPlatformName(p.getPlatformName());
            receiveMasterDO.setPlatformId(p.getPlatformId());
        });
        // 类型
        receiveMasterDO.setType(ReceiveTypeEnum.SCAN.name());
        // 收货人
        receiveMasterDO.setRecipient(GlobalContext.getUser().getName());
        // 收货仓库
        String receiptWarehouse = receiptWarehouseCmdExe.execute();
        receiveMasterDO.setReceiptWarehouse(receiptWarehouse);
        this.save(receiveMasterDO);
    }

    /**
     * 获取订单号
     *
     * @param expressTrackingNo 快递单号
     * @param platformRefundDOS 退款列表
     * @return 订单号
     */
    private String getTradeId(String expressTrackingNo, List<PlatformRefundDO> platformRefundDOS) {
        Map<String, PlatformRefundDO> platformRefundMap = platformRefundDOS.stream()
                .collect(Collectors.toMap(PlatformRefundDO::getPlatformTradeId, Function.identity(), (v1, v2) -> v1));
        if (platformRefundMap.isEmpty()) {
            return "";
        }
        if (platformRefundMap.size() > 1) {
            // 获取企业微信用户工号
            List<String> users = systemParamService.getByName("all_refund_message")
                    .filter(systemParam -> StringUtils.isNotBlank(systemParam.getParamValue()))
                    .map(systemParam -> Splitter.on(",").omitEmptyStrings().trimResults().splitToList(systemParam.getParamValue()))
                    .orElseGet(Collections::emptyList);
            workWechatAlertSenderCmdExe.execute(users, "快递单号 %s 此单存在多平台上传单号情况，请核实", expressTrackingNo);
        }
        // 订单号去重
        Set<String> tradeIds = platformRefundMap.keySet();
        // 订单号拼接
        return String.join(",", tradeIds);
    }

    @Override
    public PaginationVO<ReceiveMasterDO> getList(ReceiveMasterQuery query) {
        Wrapper<ReceiveMasterDO> wrapper = getReceiveMasterConditionQueryWrapper(query);
        return PaginationVO.newVO(this.page(query.createPage(), wrapper));
    }

    private Wrapper<ReceiveMasterDO> getReceiveMasterConditionQueryWrapper(ReceiveMasterQuery query) {
        return Wrappers.<ReceiveMasterDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getType()), ReceiveMasterDO::getType, query.getType())
                .eq(StringUtils.isNotBlank(query.getReceiptWarehouse()), ReceiveMasterDO::getReceiptWarehouse, query.getReceiptWarehouse())
                .eq(StringUtils.isNotBlank(query.getExpressCompany()), ReceiveMasterDO::getExpressCompany, query.getExpressCompany())
                .in(CollectionUtils.isNotEmpty(query.getPlatformIds()), ReceiveMasterDO::getPlatformId, query.getPlatformIds())
                .eq(StringUtils.isNotBlank(query.getExpressTrackingNo()), ReceiveMasterDO::getExpressTrackingNo, query.getExpressTrackingNo())
                .eq(StringUtils.isNotBlank(query.getCourier()), ReceiveMasterDO::getCourier, query.getCourier())
                .eq(StringUtils.isNotBlank(query.getRecipient()), ReceiveMasterDO::getRecipient, query.getRecipient())
                .eq(StringUtils.isNotBlank(query.getScanner()), ReceiveMasterDO::getScanner, query.getScanner())
                .ge(StringUtils.isNotBlank(query.getBeginDate()), ReceiveMasterDO::getCreateTime, query.getBeginDate() + " 00:00:00")
                .le(StringUtils.isNotBlank(query.getEndDate()), ReceiveMasterDO::getCreateTime, query.getEndDate() + " 23:59:59")
                .orderByDesc(ReceiveMasterDO::getCreateTime);
    }

    @Override
    public void reject(ReceiveMasterRejectQuery query) {
        ReceiveMasterDO receiveMasterDO = new ReceiveMasterDO();
        BeanUtils.copyProperties(query, receiveMasterDO);
        receiveMasterDO.setType(ReceiveTypeEnum.REJECT.name());
        receiveMasterDO.setRejectPerson(GlobalContext.getUser().getName());
        receiveMasterDO.setRejectDate(DateUtil.dateToStr(new Date()));
        this.updateById(receiveMasterDO);
    }

    @Override
    @Transactional
    public String createReturnGoods(ReceiveMasterGoodsQuery query) {
        // 获取收货信息
        ReceiveMasterDO receiveMasterDO = this.checkExpressExist(query.getExpressTrackingNo());

        // 根据订单号从维修列表中获取维修信息
        RepairDO oldRepair = this.repairService.getOneByTid(query.getPlatformTradeId(), PostSaleStatus.COMPLETED.name());

        String changeAndReturnResult = "收货成功，生成退货单成功!";
        // 判断快递类型(退货,换货,维修)
        String type = oldRepair == null ? ReceiveTypeEnum.RETURN.name() : oldRepair.getType();

        // 根据退货单号判断是否存在退货
        if (ReceiveTypeEnum.RETURN.name().equals(type) || ReceiveTypeEnum.EXCHANGE.name().equals(type)) {
            this.returnMasterService.isHasRefund(query.getPlatformTradeId(), type);
        }
        // 是否生成退货(退货和确认换机的才生成退货)
        boolean isCreateReturn = ReceiveTypeEnum.RETURN.name().equals(type)
                || (ReceiveTypeEnum.EXCHANGE.name().equals(type) && oldRepair != null && oldRepair.getIsExchange() == 1);

        // 修改收货主表类型，主表信息与订单号关联，生成收货明细
        this.saveOrUpdateReceiveInfo(query, receiveMasterDO, type);

        // 根据订单号获取订单信息和明细信息
        TradeOrderInfoVO tradeOrderInfoVO = this.tradeService.getTradeAndDetailsByTradeId(query.getPlatformTradeId());

        if (isCreateReturn) {
            String createTime = DateUtil.dateToStr(new Date());
            // 生成退货
            List<ReturnDetailsDO> returnDetailsDOS = this.createAndSaveReturnInfo(ReceiveTypeEnum.RETURN.name(), query, receiveMasterDO, tradeOrderInfoVO.getTradeDO(), createTime, query.getReturnType());

            TradeDO oldTrade = tradeOrderInfoVO.getTradeDO();

            // 更新平台备注
            // 如果有换货维修必须加上备注
            this.updateRemark(receiveMasterDO, type, createTime, oldTrade);

            // 更新短信
            this.updateSms(type, oldTrade);

            // 修改订单状态为已退货
            this.updateTrade(oldTrade);

            // 更新订单明细退货数量
            this.updateOrderRefundInfo(returnDetailsDOS, tradeOrderInfoVO.getOrderDOS());
            // 超期任务收货
            OvertimeDO oldOvertimeDo = this.overtimeService.getTradeByPlatformTradeId(oldTrade.getPlatformTradeId());
            if (oldOvertimeDo != null) {
                if (oldOvertimeDo.getOvertimeStatus().equals(OvertimeStatus.SERVE_CONFIRM.name()) || oldOvertimeDo.getOvertimeStatus().equals(OvertimeStatus.STORE_SCAN.name())) {
                    OvertimeDO overtimeDO = new OvertimeDO();
                    overtimeDO.setId(oldOvertimeDo.getId());
                    overtimeDO.setOvertimeStatus(OvertimeStatus.STORE_SCAN.name());
                    overtimeDO.setScanner(GlobalContext.getUser().getName());
                    overtimeDO.setScanTime(new Date());
                    this.overtimeService.updateOvertime(overtimeDO);
                }
            }
            boolean isAutoCreateExange = true;
            if (oldRepair != null && oldRepair.getType().equals("EXCHANGE")) {
                Wrapper<PlatformRefundDO> wrapper = Wrappers.<PlatformRefundDO>lambdaQuery()
                        .eq(PlatformRefundDO::getPlatformTradeId, query.getPlatformTradeId())
                        .eq(PlatformRefundDO::getExpressWaybillNo, query.getExpressTrackingNo());
                List<PlatformRefundDO> platformRefundDOS = this.platformRefundService.list(wrapper);
                if (platformRefundDOS != null && platformRefundDOS.size() > 0) {
                    changeAndReturnResult = "生成退货单成功，有换货任务，同时后台申请过退款，请核实，不自动生成换货单!";
                    isAutoCreateExange = false;
                }
            }
            // 存在换货任务且有换货明细则生成退货单同时生成换货单
            if (isAutoCreateExange && oldRepair != null && "NORMAL_TYPE".equals(oldRepair.getExchangeGoodsType()) && oldRepair.getIsGenerate() == 0 && oldRepair.getIsExchange() == 1) {
                List<RepairExchangeDO> exchangeList = this.repairExchangeService.list(Wrappers.<RepairExchangeDO>lambdaQuery()
                        .eq(RepairExchangeDO::getPlatformTradeId, query.getPlatformTradeId()));
                if (CollectionUtils.isNotEmpty(exchangeList)) {
                    GenerateExchangeGoodsQuery exchangeGoodsQuery = new GenerateExchangeGoodsQuery();
                    exchangeGoodsQuery.setPlatformTradeId(query.getPlatformTradeId());
                    exchangeGoodsQuery.setNewPlatformTradeId(query.getPlatformTradeId() + "3");
                    exchangeGoodsQuery.setReceiverName(oldRepair.getReceiverName());
                    exchangeGoodsQuery.setGiftInfoQueries(CollectionCopier.copy(exchangeList, GiftInfoQuery::new));
                    try {
                        this.returnMasterService.generateExchangeGoods(exchangeGoodsQuery);
                        // 更新字段
                        RepairDO repairDO = this.repairService.getByPlatformTradeId(exchangeGoodsQuery.getPlatformTradeId());
                        repairDO.setIsGenerate(1);
                        repairDO.setExchangeGoodsOrder(exchangeGoodsQuery.getNewPlatformTradeId());
                        this.repairService.updateById(repairDO);
                        changeAndReturnResult = "收货成功，有换货任务，同时自动生成换货单成功!";
                    } catch (Exception e) {
                        log.info("自动生成换货异常");
                    }
                }
            }
        } else {
            String result = ReceiveTypeEnum.EXCHANGE.name().equals(type) ? "不生成退货单，订单未确认换机!" : "不生成退货单，订单属于维修类型!";
            throw new GlobalDefaultException(ResponseEnum.ERROR_REFUND_RECEIVE_GOOD, result);
        }

        // 根据类型返回提示语
        return changeAndReturnResult;
    }

    /**
     * 生成基础换货单
     *
     * @param oldRepair 维修换货记录
     * @return 基础换货单
     */
    private TradeDO createOldTrade(RepairDO oldRepair) {
        TradeDO tradeDO = new TradeDO();
        // 旧订单号
        tradeDO.setPlatformTradeId(oldRepair.getPlatformTradeId());
        tradeDO.setPlatformName(oldRepair.getPlatformName());
        tradeDO.setPlatformId(oldRepair.getPlatformId());
        tradeDO.setBuyerNickname(oldRepair.getBuyerNick());
        tradeDO.setReceiverName(oldRepair.getReceiverName());
        tradeDO.setReceiverMobile(oldRepair.getTelephone());
        tradeDO.setReceiverProvince(oldRepair.getProvince());
        tradeDO.setReceiverCity(oldRepair.getCity());
        tradeDO.setReceiverDistrict(oldRepair.getDistrict());
        tradeDO.setReceiverAddress(oldRepair.getAddress());
        return tradeDO;
    }

    /**
     * 更新订单明细的退货信息
     *
     * @param returnDetailsDOS
     * @param orderDOS
     */
    private void updateOrderRefundInfo(List<ReturnDetailsDO> returnDetailsDOS, List<OrderDO> orderDOS) {
        List<OrderDO> list = Lists.newArrayList();
        for (OrderDO orderDO : orderDOS) {
            // 虚拟物料不处理
            if (orderDO.getMaterialType() == 3) {
                continue;
            }

            List<ReturnDetailsDO> detailsDOS = returnDetailsDOS.stream()
                    .filter(details -> details.getPlatformOrderId().equals(orderDO.getPlatformOrderId()))
                    .collect(Collectors.toList());
            if (detailsDOS.size() > 0) {
                int refundNum = detailsDOS.stream().mapToInt(ReturnDetailsDO::getReturnNum).sum();

                OrderDO update = new OrderDO();
                update.setId(orderDO.getId());
                update.setRefundNum(refundNum);
                update.setIsRefund(update.getRefundNum() > 0 ? 1 : 0);
                list.add(update);
            }
        }
        this.orderService.updateBatchById(list);
    }

    /**
     * 更新短信 已收货
     *
     * @param type
     * @param oldTrade
     */
    private void updateSms(String type, TradeDO oldTrade) {
        // 更新短信状态
        SmsDO smsDO = this.smsService.getByTradeId(oldTrade.getPlatformTradeId());
        if (smsDO != null) {
            SmsDO update = new SmsDO();
            update.setId(smsDO.getId());
            update.setTradeStatus(2);
            this.smsService.updateById(update);
        }
    }

    /**
     * 更新平台备注
     *
     * @param receiveMasterDO
     * @param type
     * @param createTime
     * @param oldTrade
     */
    private void updateRemark(ReceiveMasterDO receiveMasterDO, String type, String createTime, TradeDO oldTrade) {
        if (oldTrade == null || !TradeTypeEnum.NORMAL.name().equals(oldTrade.getType())) {
            return;
        }

        // 获取原始订单号
        String oldPlatformTradeId = this.tradeService.getOldPlatformTradeId(oldTrade);
        if (StringUtils.isBlank(oldPlatformTradeId)) {
            return;
        }

        // 组装备注内容
        String typeText;
        boolean note = false;
        switch (type) {
            case "EXCHANGE":
                typeText = "换货";
                note = true;
                break;
            case "REPAIR":
                typeText = "维修";
                note = true;
                break;
            default:
                typeText = "退货";
        }
        StringBuilder remark = new StringBuilder();
        remark.append("【")
                .append(typeText).append("-仓库扫描-")
                .append(receiveMasterDO.getExpressCompany()).append(receiveMasterDO.getExpressTrackingNo())
                .append("-").append(createTime).append("-").append(GlobalContext.getUser().getName())
                .append("】");
        if (BooleanUtils.isTrue(note)) {
            remark.append("【此单有").append(typeText).append("任务，谨慎退款】");
        }

        // 添加备注
        String updateRemark = tradeRemarkCmdExe.execute(oldTrade, remark.toString());
        oldTrade.setSellerRemark(updateRemark);
    }

    /**
     * 根据订单号获取收货
     *
     * @param platformTradeId
     * @return
     */
    private ReceiveMasterDO getOneByTid(String platformTradeId) {
        return this.getOne(Wrappers.<ReceiveMasterDO>lambdaQuery()
                .eq(ReceiveMasterDO::getPlatformTradeId, platformTradeId)
                .last("limit 1"));
    }

    /**
     * 修改订单状态为已退货
     *
     * @param oldTrade
     */
    private void updateTrade(TradeDO oldTrade) {
        TradeDO update = new TradeDO();
        update.setId(oldTrade.getId());
        update.setStatusOrder(OrderStatus.RETURNED.name());
        update.setSellerRemark(oldTrade.getSellerRemark());
        this.tradeService.updateById(update);
    }

    @Override
    public ReceiveMasterDO checkExpressExist(String expressTrackingNo) {
        ReceiveMasterDO receiveMasterDO = this.getOne(Wrappers.<ReceiveMasterDO>lambdaQuery().eq(ReceiveMasterDO::getExpressTrackingNo, expressTrackingNo));
        if (receiveMasterDO == null) {
            throw new GlobalDefaultException(ResponseEnum.EXPRESS_NOT_EXIST);
        }
        if (!ReceiveTypeEnum.SCAN.name().equals(receiveMasterDO.getType())
                && !ReceiveTypeEnum.REPAIR.name().equals(receiveMasterDO.getType())
                && !ReceiveTypeEnum.EXCHANGE.name().equals(receiveMasterDO.getType())) {
            throw new GlobalDefaultException(ResponseEnum.EXPRESS_NOT_SCAN);
        }
        return receiveMasterDO;
    }

    @Override
    public void update(ReceiveMasterUpdateQuery query) {
        // 判断类型:只有扫码未确认的才可以修改
        ReceiveMasterDO receiveMasterDO1 = this.getById(query.getId());
        if (receiveMasterDO1 == null) {
            throw new GlobalDefaultException(ResponseEnum.EXPRESS_NULL);
        }
        if (!ReceiveTypeEnum.SCAN.name().equals(receiveMasterDO1.getType())) {
            throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR);
        }
        // 判断是否修改了快递单号;如果修改则校验快递单号是否存在
        if (!receiveMasterDO1.getExpressTrackingNo().equals(query.getExpressTrackingNo())) {
            ReceiveMasterDO receiveMasterDO2 = this.getOne(Wrappers.<ReceiveMasterDO>lambdaQuery().eq(ReceiveMasterDO::getExpressTrackingNo, query.getExpressTrackingNo()));
            if (receiveMasterDO2 != null) {
                throw new GlobalDefaultException(ResponseEnum.EXPRESSTRACKINGNO_EXIST);
            }
        }
        ReceiveMasterDO receiveMasterDO = new ReceiveMasterDO();
        BeanUtils.copyProperties(receiveMasterDO, query);
    }

    @Override
    public boolean deleteExpressById(long id) {
        ReceiveMasterDO receiveMasterDO = this.getById(id);
        // 判断待删除的快递类型是否为扫描未确认
        if (receiveMasterDO != null && ReceiveTypeEnum.SCAN.name().equals(receiveMasterDO.getType())) {
            return this.removeById(id);
        }
        throw new GlobalDefaultException(ResponseEnum.EXPRESS_NOT_SCAN);
    }

    @Override
    public void scan(ReturnScanQuery query) {
        // 获取主表信息
        ReturnMasterDO returnMasterDO = this.returnMasterService.getReturnMasterByTradeId(query.getPlatformTradeId());

        // 操作限制
        this.scanCheck(returnMasterDO);

        // 统计已经退货的数量
        List<OrderDO> orders = this.orderService.getOrderByTid(query.getPlatformTradeId());
        Map<String, Integer> refundNumMap = orders.stream()
                .filter(order -> order.getRefundNum() != null && order.getRefundNum() > 0)
                .collect(Collectors.groupingBy(OrderDO::getPlatformOrderId, Collectors.summingInt(OrderDO::getRefundNum)));

        List<ReturnDetailsDO> returnDetails = this.returnDetailsService.getReturnDetails(query.getPlatformTradeId());
        returnDetails.forEach(detail -> {
            int returnNumBarcode = query.getItems().stream()
                    .filter(item -> detail.getPlatformOrderId().equals(item.getPlatformOrderId())
                            && detail.getBarcode().equals(item.getBarcode()))
                    .mapToInt(ReturnScanItemQuery::getReturnNum)
                    .sum();
            if (returnNumBarcode > 0) {
                // 更新退货明细
                this.scanUpdateReturnDetail(detail, returnNumBarcode);

                // 更新订单明细退货数量
                this.scanUpdateOrder(query.getItems(), detail, refundNumMap);
            }
        });

        // 更新退货信息，货物退还状态（全部、部分）
        this.scanUpdateReturnMaster(returnMasterDO);
    }

    @Override
    public void export(ReceiveMasterQuery query, HttpServletResponse response) {
        IPage<ReceiveMasterDO> result = this.page(new Page<>(1, -1), getReceiveMasterConditionQueryWrapper(query));
        List<ReceiveMasterExportVO> exportList = result.getRecords().parallelStream().map(p -> {
            ReceiveMasterExportVO receiveMasterExportVO = new ReceiveMasterExportVO();
            BeanUtils.copyProperties(p, receiveMasterExportVO);
            receiveMasterExportVO.setType(ReceiveTypeEnum.getDescriptionByName(p.getType()));
            return receiveMasterExportVO;
        }).collect(Collectors.toList());
        ExcelUtil.exportExcel("快递收件信息导出", exportList, ReceiveMasterExportVO.class, response);
    }

    /**
     * 二次扫描，更新退货信息，货物退还状态（全部、部分）
     *
     * @param returnMasterDO 退货信息
     */
    private void scanUpdateReturnMaster(ReturnMasterDO returnMasterDO) {
        List<ReturnDetailsDO> newDetails = this.returnDetailsService.getReturnDetails(returnMasterDO.getPlatformTradeId());
        boolean allReturn = newDetails.stream().noneMatch(detail -> detail.getIsReturn() != 1);
        if (allReturn) {
            ReturnMasterDO updateMaster = new ReturnMasterDO();
            updateMaster.setRefundId(returnMasterDO.getRefundId());
            updateMaster.setIsAllReturn(1);
            this.returnMasterService.updateReturnMaster(updateMaster);
        }
    }

    /**
     * 二次扫描，更新订单明细退货数量
     *
     * @param queryItems   扫描明细
     * @param detail       退货明细
     * @param refundNumMap 已经退货的数量
     */
    private void scanUpdateOrder(List<ReturnScanItemQuery> queryItems, ReturnDetailsDO detail, Map<String, Integer> refundNumMap) {
        // 计算退货数量
        OrderDO order = this.orderService.getOrderByOrderId(detail.getPlatformTradeId(), detail.getPlatformOrderId());
        int returnNumOrder = queryItems.stream()
                .filter(item -> order.getPlatformOrderId().equals(item.getPlatformOrderId()))
                .mapToInt(ReturnScanItemQuery::getReturnNum)
                .sum();
        int refundNum = returnNumOrder + refundNumMap.getOrDefault(detail.getPlatformOrderId(), 0);

        OrderDO updateOrder = new OrderDO();
        updateOrder.setPlatformTradeId(detail.getPlatformTradeId());
        updateOrder.setPlatformOrderId(detail.getPlatformOrderId());
        updateOrder.setIsRefund(1);
        updateOrder.setRefundNum(refundNum);
        this.orderService.update(updateOrder);
    }

    /**
     * 二次扫描，更新退货明细
     *
     * @param detail  退货明细
     * @param scanNum 扫描数量
     */
    private void scanUpdateReturnDetail(ReturnDetailsDO detail, int scanNum) {
        int returnNum = detail.getReturnNum() + scanNum;
        int isReturn = detail.getNum() == returnNum ? 1 : detail.getIsReturn();

        ReturnDetailsUpdaterQuery updateQuery = new ReturnDetailsUpdaterQuery();
        updateQuery.setPlatformTradeId(detail.getPlatformTradeId());
        updateQuery.setPlatformOrderId(detail.getPlatformOrderId());
        updateQuery.setBarcode(detail.getBarcode());
        updateQuery.setIsReturn(isReturn);
        updateQuery.setReturnNum(returnNum);
        this.returnDetailsService.updateReturnNum(updateQuery);
    }

    /**
     * 二次扫描，操作限制
     *
     * @param returnMasterDO 退货信息
     */
    private void scanCheck(ReturnMasterDO returnMasterDO) {
        List<String> allowStatus = Lists.newArrayList(PartReturnStatus.STORE_SCAN.name(), PartReturnStatus.SERVE_CONFIRM.name());
        if (returnMasterDO == null
                || returnMasterDO.getErpPostManual() == 1
                || returnMasterDO.getErpPostStatus() == 1
                // || RefundStatusEnum.HAVE_REFUNDED.name().equals(returnMasterDO.getRefundStatus())
                || AutoRefundStatusEnum.AUTO.name().equals(returnMasterDO.getAutoRefundStatus())
                || !allowStatus.contains(returnMasterDO.getPartReturnStatus())) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_NOT_ALLOW_RETURN_SCAN);
        }
    }

    /**
     * 获取收货类型
     *
     * @param repairDO        维修信息
     * @param exchangeGoodsDO 换货信息
     * @return
     */
    private String getReceiveType(RepairDO repairDO, ExchangeGoodsDO exchangeGoodsDO) {
        String type;
        if (repairDO != null && exchangeGoodsDO == null) {
            // 是维修,修改维修列表客服下单状态为收货送修
            type = ReceiveTypeEnum.REPAIR.name();
        } else if (exchangeGoodsDO != null && repairDO == null) {
            // 是换货,修改换货列表客服下单状态为收货送修,生成退货单(主退货单,退货明细)
            type = ReceiveTypeEnum.EXCHANGE.name();
        } else if (repairDO == null) {
            // 是退货,生成退货单(主退货单,退货明细)
            type = ReceiveTypeEnum.RETURN.name();
        } else {
            // 维修和换货列表中都存在
            throw new GlobalDefaultException(ResponseEnum.REPAIR_EXCHANGE_EXIST);
        }
        return type;
    }

    /**
     * 更新收货主表信息,保存收货明细信息
     *
     * @param query           收货信息
     * @param receiveMasterDO 收货主信息
     * @param type            收货类型
     */
    private void saveOrUpdateReceiveInfo(ReceiveMasterGoodsQuery query, ReceiveMasterDO receiveMasterDO, String type) {
        receiveMasterDO.setType(type);
        receiveMasterDO.setPlatformTradeId(query.getPlatformTradeId());
        // 收货商品扫描人
        receiveMasterDO.setScanner(GlobalContext.getUser().getName());
        // 更新主表信息
        this.updateById(receiveMasterDO);

        // 保存收货明细
        List<ReceiveDetailsDO> list = new ArrayList<>();
        for (ReceiveDetailsQuery receiveDetailsQuery : query.getReceiveDetailsQueries()) {
            ReceiveDetailsDO receiveDetailsDO = new ReceiveDetailsDO();
            BeanUtils.copyProperties(receiveDetailsQuery, receiveDetailsDO);
            receiveDetailsDO.setExpressTrackingNo(query.getExpressTrackingNo());
            list.add(receiveDetailsDO);
        }
        this.receiveDetailsService.saveBatch(list);
    }

    /**
     * 保存退货主信息和明细信息
     *
     * @param type          退货类型
     * @param query         查询
     * @param receiveMaster 收件人
     * @param tradeDO       订单
     * @param createTime    创建时间
     */
    private List<ReturnDetailsDO> createAndSaveReturnInfo(String type, ReceiveMasterGoodsQuery query, ReceiveMasterDO receiveMaster, TradeDO tradeDO, String createTime, String returnType) {
        // 创建退货主信息
        ReturnMasterDO returnMasterDO = this.createReturnMaster(type, receiveMaster, createTime, tradeDO, query);

        // 设置收货仓库
        String receiptWarehouse = receiptWarehouseCmdExe.execute();
        returnMasterDO.setReceiptWarehouse(receiptWarehouse);

        // 设置用户填写的退货快递单号和退货原因
        this.setPlatformRefundInfo(returnMasterDO);

        // 获取发货条码
        List<OrderBarcodeDO> orderBarcodes = this.getOrderBarcodes(query, tradeDO);

        // 获取收货明细
        List<ReturnDetailsDO> list = this.getReturnDetails(type, query.getReceiveDetailsQueries(), tradeDO, orderBarcodes);

        // 是否全部退货
        boolean allReturn = list.stream().noneMatch(r -> r.getIsReturn() != 1);
        returnMasterDO.setIsAllReturn(allReturn ? 1 : -1);
        if (returnType != null && returnType.equals(ReturnScanTypeEnum.MISS_PART.name())) {
            returnMasterDO.setIsAllReturn(-1);
        }
        // 保存退货主信息和明细信息
        this.returnMasterService.save(returnMasterDO);
        this.returnDetailsService.saveBatch(list);

        return list;
    }

    /**
     * 获取发货条码
     *
     * @param query   查询
     * @param tradeDO 订单
     * @return 发货条码
     */
    private List<OrderBarcodeDO> getOrderBarcodes(ReceiveMasterGoodsQuery query, TradeDO tradeDO) {
        PlatformDO platform = platformService.getPlatformById(tradeDO.getPlatformId());
        if ("OMNICHANNEL".equals(platform.getPlatformType()) && tradeDO.getType().equals(TradeTypeEnum.NORMAL.name())) {
            List<DistributorBarcodeDO> distributorBarcodeDOS = distributorBarcodeDao.listByTradeId(tradeDO.getPlatformTradeId());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(distributorBarcodeDOS)) {
                throw GlobalDefaultException.of("订单 %s,找不到发货信息", tradeDO.getPlatformTradeId());
            }
            return distributorBarcodeDOS.stream()
                    .map(distributorBarcodeDO -> {
                        OrderBarcodeDO orderBarcodeDO = new OrderBarcodeDO();
                        orderBarcodeDO.setBarcode(distributorBarcodeDO.getBarcode());
                        orderBarcodeDO.setNum(distributorBarcodeDO.getNum());
                        orderBarcodeDO.setUnit(distributorBarcodeDO.getUnit());
                        orderBarcodeDO.setPlatformOrderId(distributorBarcodeDO.getPlatformOrderId());
                        orderBarcodeDO.setErpCode(distributorBarcodeDO.getErpCode());
                        orderBarcodeDO.setCreateTime(DateUtil.localDateTimeToDate(distributorBarcodeDO.getCreateTime()));
                        orderBarcodeDO.setSorter(distributorBarcodeDO.getCreator());
                        orderBarcodeDO.setPrice(distributorBarcodeDO.getPrice());
                        orderBarcodeDO.setErpName(distributorBarcodeDO.getErpName());
                        orderBarcodeDO.setPlatformTradeId(distributorBarcodeDO.getPlatformTradeId());
                        return orderBarcodeDO;
                    })
                    .collect(Collectors.toList());
        } else {
            // 获取本地或顺丰发货信息
            return this.tradeService.getLocalOrSfOrderBarcode(query.getPlatformTradeId());
        }
    }

    /**
     * @param type                  类型
     * @param receiveDetailsQueries 收货明细
     * @param tradeDO               订单
     * @param orderBarcodeDOS       发货条码
     * @return 退货明细
     */
    private List<ReturnDetailsDO> getReturnDetails(String type, List<ReceiveDetailsQuery> receiveDetailsQueries, TradeDO tradeDO, List<OrderBarcodeDO> orderBarcodeDOS) {
        // 退货明细表信息
        List<ReturnDetailsDO> list = new ArrayList<>();
        if ("006".equals(tradeDO.getWarehouseCode()) && "4638".equals(tradeDO.getWarehouseStorage())) {
            List<WarehouseBarcodeDO> warehouseBarcodeDOS = warehouseBarcodeDao.listByPlatformTradeId(tradeDO.getPlatformTradeId());
            warehouseBarcodeDOS.forEach(warehouseBarcodeDO -> {
                // 创建退货明细
                ReturnDetailsDO returnDetailsDO = this.createReturnDetails(type, tradeDO, warehouseBarcodeDO);
                this.setReturnNum(receiveDetailsQueries, warehouseBarcodeDO, returnDetailsDO);

                // 是否退货
                returnDetailsDO.setIsReturn(warehouseBarcodeDO.getNum().equals(returnDetailsDO.getReturnNum()) ? 1 : -1);

                list.add(returnDetailsDO);
            });
        } else {
            for (OrderBarcodeDO orderBarcodeDO : orderBarcodeDOS) {
                // 创建退货明细
                ReturnDetailsDO returnDetailsDO = this.createReturnDetails(type, tradeDO, orderBarcodeDO);

                // 设置退货数量
                this.setReturnNum(receiveDetailsQueries, orderBarcodeDO, returnDetailsDO);

                // 是否退货
                returnDetailsDO.setIsReturn(orderBarcodeDO.getNum().equals(returnDetailsDO.getReturnNum()) ? 1 : -1);
                list.add(returnDetailsDO);
            }
        }
        return list;
    }

    /**
     * 设置退货明细的退货数量
     *
     * @param receiveDetailsQueries 收货明细
     * @param orderBarcodeDO        发货条码
     * @param returnDetailsDO       退货明细
     */
    private void setReturnNum(List<ReceiveDetailsQuery> receiveDetailsQueries, WarehouseBarcodeDO orderBarcodeDO, ReturnDetailsDO returnDetailsDO) {
        // 获取对应条码和数量的收货明细
        ReceiveDetailsQuery existReceiveDetails = receiveDetailsQueries.stream().filter(r -> orderBarcodeDO.getBarcode().equals(r.getBarcode()) && orderBarcodeDO.getNum().equals(r.getReturnNum()))
                .findAny().orElse(null);

        Iterator<ReceiveDetailsQuery> iterator = receiveDetailsQueries.iterator();
        while (iterator.hasNext()) {
            ReceiveDetailsQuery receiveDetailsQuery = iterator.next();
            if (existReceiveDetails != null) {
                if (orderBarcodeDO.getBarcode().equals(receiveDetailsQuery.getBarcode()) && orderBarcodeDO.getNum().equals(receiveDetailsQuery.getReturnNum())) {
                    returnDetailsDO.setReturnNum(receiveDetailsQuery.getReturnNum());
                    // 删除掉该收货明细
                    iterator.remove();
                    break;
                }
            } else {
                if (orderBarcodeDO.getBarcode().equals(receiveDetailsQuery.getBarcode())) {
                    List<ReceiveDetailsQuery> queryList = receiveDetailsQueries.stream().filter(r -> orderBarcodeDO.getBarcode().equals(r.getBarcode()) && r.getReturnNum() < orderBarcodeDO.getNum())
                            .collect(Collectors.toList());
                    if (queryList.size() == 0) {
                        throw new GlobalDefaultException(ResponseEnum.ERROR_RETURNNUM_SHIPNUM);
                    }
                    // 获取退货数量最大的
                    ReceiveDetailsQuery maxReturnNum = queryList.stream().max(Comparator.comparingInt(ReceiveDetailsQuery::getReturnNum)).orElse(null);
                    if (maxReturnNum.getReturnNum().equals(receiveDetailsQuery.getReturnNum())) {
                        returnDetailsDO.setReturnNum(receiveDetailsQuery.getReturnNum());
                        // 删除掉该收货明细
                        iterator.remove();
                        break;
                    }
                }
            }
        }
    }

    /**
     * 设置退货明细的退货数量
     *
     * @param receiveDetailsQueries 收货明细
     * @param orderBarcodeDO        发货条码
     * @param returnDetailsDO       退货明细
     */
    private void setReturnNum(List<ReceiveDetailsQuery> receiveDetailsQueries, OrderBarcodeDO orderBarcodeDO, ReturnDetailsDO returnDetailsDO) {
        // 获取对应条码和数量的收货明细
        ReceiveDetailsQuery existReceiveDetails = receiveDetailsQueries.stream().filter(r -> orderBarcodeDO.getBarcode().equals(r.getBarcode()) && orderBarcodeDO.getNum().equals(r.getReturnNum()))
                .findAny().orElse(null);

        Iterator<ReceiveDetailsQuery> iterator = receiveDetailsQueries.iterator();
        while (iterator.hasNext()) {
            ReceiveDetailsQuery receiveDetailsQuery = iterator.next();
            if (existReceiveDetails != null) {
                if (orderBarcodeDO.getBarcode().equals(receiveDetailsQuery.getBarcode()) && orderBarcodeDO.getNum().equals(receiveDetailsQuery.getReturnNum())) {
                    returnDetailsDO.setReturnNum(receiveDetailsQuery.getReturnNum());
                    // 删除掉该收货明细
                    iterator.remove();
                    break;
                }
            } else {
                if (orderBarcodeDO.getBarcode().equals(receiveDetailsQuery.getBarcode())) {
                    List<ReceiveDetailsQuery> queryList = receiveDetailsQueries.stream().filter(r -> orderBarcodeDO.getBarcode().equals(r.getBarcode()) && r.getReturnNum() < orderBarcodeDO.getNum())
                            .collect(Collectors.toList());
                    if (queryList.size() == 0) {
                        throw new GlobalDefaultException(ResponseEnum.ERROR_RETURNNUM_SHIPNUM);
                    }
                    // 获取退货数量最大的
                    ReceiveDetailsQuery maxReturnNum = queryList.stream().max(Comparator.comparingInt(ReceiveDetailsQuery::getReturnNum)).orElse(null);
                    if (maxReturnNum.getReturnNum().equals(receiveDetailsQuery.getReturnNum())) {
                        returnDetailsDO.setReturnNum(receiveDetailsQuery.getReturnNum());
                        // 删除掉该收货明细
                        iterator.remove();
                        break;
                    }
                }
            }
        }
    }

    /**
     * 根据收货信息,退货类型,订单信息创建退货明细信息
     *
     * @param type           退货类型
     * @param tradeDO        订单信息
     * @param orderBarcodeDO 发货条码信息
     * @return 退货明细
     */
    private ReturnDetailsDO createReturnDetails(String type, TradeDO tradeDO, WarehouseBarcodeDO orderBarcodeDO) {
        ReturnDetailsDO returnDetailsDO = new ReturnDetailsDO();
        BeanUtils.copyProperties(orderBarcodeDO, returnDetailsDO);
        returnDetailsDO.setId(null);
        returnDetailsDO.setCreateTime(null);
        returnDetailsDO.setUpdateTime(null);
        returnDetailsDO.setPlatformId(tradeDO.getPlatformId());
        returnDetailsDO.setPlatformName(tradeDO.getPlatformName());
        returnDetailsDO.setPriceTotal(orderBarcodeDO.getPrice().multiply(new BigDecimal(orderBarcodeDO.getNum())).setScale(2, BigDecimal.ROUND_HALF_UP));
        returnDetailsDO.setPayment(returnDetailsDO.getPriceTotal());
        if (ReceiveTypeEnum.EXCHANGE.name().equals(type)) {
            returnDetailsDO.setPrice(BigDecimal.ZERO);
            returnDetailsDO.setPriceTotal(BigDecimal.ZERO);
            returnDetailsDO.setPayment(BigDecimal.ZERO);
        }
        returnDetailsDO.setPaymentTime(tradeDO.getPaymentTime());
        return returnDetailsDO;
    }

    /**
     * 根据收货信息,退货类型,订单信息创建退货明细信息
     *
     * @param type           退货类型
     * @param tradeDO        订单信息
     * @param orderBarcodeDO 发货条码信息
     * @return
     */
    private ReturnDetailsDO createReturnDetails(String type, TradeDO tradeDO, OrderBarcodeDO orderBarcodeDO) {
        ReturnDetailsDO returnDetailsDO = new ReturnDetailsDO();
        BeanUtils.copyProperties(orderBarcodeDO, returnDetailsDO);
        returnDetailsDO.setId(null);
        returnDetailsDO.setCreateTime(null);
        returnDetailsDO.setUpdateTime(null);
        returnDetailsDO.setPlatformId(tradeDO.getPlatformId());
        returnDetailsDO.setPlatformName(tradeDO.getPlatformName());
        returnDetailsDO.setPriceTotal(orderBarcodeDO.getPrice().multiply(new BigDecimal(orderBarcodeDO.getNum())).setScale(2, BigDecimal.ROUND_HALF_UP));
        returnDetailsDO.setPayment(returnDetailsDO.getPriceTotal());
        if (ReceiveTypeEnum.EXCHANGE.name().equals(type)) {
            returnDetailsDO.setPrice(BigDecimal.ZERO);
            returnDetailsDO.setPriceTotal(BigDecimal.ZERO);
            returnDetailsDO.setPayment(BigDecimal.ZERO);
        }
        returnDetailsDO.setPaymentTime(tradeDO.getPaymentTime());
        return returnDetailsDO;
    }

    /**
     * 平台退货信息
     *
     * @param returnMasterDO
     */
    private void setPlatformRefundInfo(ReturnMasterDO returnMasterDO) {
        PlatformRefundDO platformRefund = this.platformRefundService.getByTradeIdAndPlatformId(returnMasterDO.getPlatformTradeId(), returnMasterDO.getPlatformId());
        if (platformRefund != null) {
            returnMasterDO.setReason(platformRefund.getReason());
            returnMasterDO.setPostTrackingNo(platformRefund.getExpressWaybillNo());
            // 退款状态为已完成则设置退款状态为已退款
            if (GlobalConstant.REFUND_STATUS_COMPLETED.contains(platformRefund.getRefundStatus())) {
                returnMasterDO.setRefundStatus(RefundStatusEnum.HAVE_REFUNDED.name());
            }
        }
    }

    /**
     * 创建退货主信息
     *
     * @param type          退货类型
     * @param receiveMaster 收货主信息
     * @param createTime    退货时间
     * @param tradeDO       订单信息
     * @param query         生成退货单参数
     * @return
     */
    private ReturnMasterDO createReturnMaster(String type, ReceiveMasterDO receiveMaster, String createTime, TradeDO tradeDO, ReceiveMasterGoodsQuery query) {
        ReturnMasterDO returnMasterDO = new ReturnMasterDO();
        returnMasterDO.setScanner(GlobalContext.getUser().getName());
        returnMasterDO.setPlatformId(tradeDO.getPlatformId());
        returnMasterDO.setPlatformName(tradeDO.getPlatformName());
        returnMasterDO.setPlatformTradeId(tradeDO.getPlatformTradeId());
        returnMasterDO.setMemoWarehouse(query.getMemoWarehouse());
        returnMasterDO.setPartReturnStatus(PartReturnStatus.STORE_SCAN.name());
        returnMasterDO.setPaymentTime(DateUtil.dateToStr(tradeDO.getPaymentTime()));
        returnMasterDO.setBuyerNickname(tradeDO.getBuyerNickname());
        returnMasterDO.setReceiverName(tradeDO.getReceiverName());
        returnMasterDO.setPayment(tradeDO.getPayment());
        returnMasterDO.setExpressTrackingNo(receiveMaster.getExpressTrackingNo());
        returnMasterDO.setExpressCompany(receiveMaster.getExpressCompany());
        returnMasterDO.setErpPostManual(0);
        returnMasterDO.setErpPostStatus(0);
        returnMasterDO.setCreateTime(createTime);
        // 判断退货快递跟原订单快递是否一致
        if (query.getExpressTrackingNo().equals(tradeDO.getExpressTrackingNo())) {
            returnMasterDO.setIsUserRefusal(1);
        }
        // 已收货
        returnMasterDO.setIsEndProductStore(1);
        // 申请退款金额
        returnMasterDO.setApplyAmount(tradeDO.getPayment());
        // 退货单号
        returnMasterDO.setRefundId(tradeDO.getPlatformTradeId() + "1");
        if (ReceiveTypeEnum.EXCHANGE.name().equals(type)) {
            // 退货单号
            returnMasterDO.setRefundId(tradeDO.getPlatformTradeId() + "41");
            returnMasterDO.setApplyAmount(BigDecimal.ZERO);
            returnMasterDO.setPayment(BigDecimal.ZERO);
        }
        // 退货类型
        returnMasterDO.setType(ReceiveTypeEnum.RETURN.name().equals(type) ? ReturnTypeEnum.RETURN.name() : ReturnTypeEnum.EXCHANGE.name());
        // 状态是未退款
        returnMasterDO.setRefundStatus(RefundStatusEnum.NOT.name());
        // 自动退款状态
        returnMasterDO.setAutoRefundStatus(AutoRefundStatusEnum.NOT.name());
        // 退货备注
        returnMasterDO.setSellerRemark(tradeDO.getSellerRemark());
        returnMasterDO.setOrderTag(tradeDO.getOrderTag());
        return returnMasterDO;
    }

}
