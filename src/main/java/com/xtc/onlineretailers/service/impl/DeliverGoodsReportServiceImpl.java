package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.mapper.master.DeliverGoodsReportMapper;
import com.xtc.onlineretailers.pojo.entity.DeliverGoodsReportDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.query.DeliverGoodsReportPageQuery;
import com.xtc.onlineretailers.pojo.query.DeliverGoodsReportUpdateQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.DeliverGoodsReportService;
import com.xtc.onlineretailers.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class DeliverGoodsReportServiceImpl extends ServiceImpl<DeliverGoodsReportMapper, DeliverGoodsReportDO> implements DeliverGoodsReportService {

    @Override
    public PaginationVO<DeliverGoodsReportDO> getDeliverGoodsReports(DeliverGoodsReportPageQuery query) {
        Wrapper<DeliverGoodsReportDO> deliverGoodsReportsWrapper = getDeliverGoodsReportsWrapper(query);
        Page<DeliverGoodsReportDO> page = this.page(query.createPage(), deliverGoodsReportsWrapper);
        return PaginationVO.newVO(page);

    }

    @Override
    public void updateStatus(DeliverGoodsReportUpdateQuery query) {
        DeliverGoodsReportDO deliverGoodsReportDO = this.getById(query.getId());
        if (ObjectUtils.isEmpty(deliverGoodsReportDO)) {
            throw new GlobalDefaultException("异常报表不存在");
        }
        DeliverGoodsReportDO updateDeliverGoodsReport = new DeliverGoodsReportDO();
        updateDeliverGoodsReport.setId(query.getId());
        updateDeliverGoodsReport.setStatus(query.getStatus());
        this.updateById(updateDeliverGoodsReport);

    }

    @Override
    public void reportData(TradeDO trade, String type, String content, Integer interceptStatus, String status, int platformSize, int orderSize) {
        DeliverGoodsReportDO deliverGoodsReportDO = this.getOne(Wrappers.<DeliverGoodsReportDO>lambdaQuery().eq(DeliverGoodsReportDO::getPlatformTradeId, trade.getPlatformTradeId())
                .eq(DeliverGoodsReportDO::getQuestionType, type));
        if (ObjectUtils.isEmpty(deliverGoodsReportDO)) {
            DeliverGoodsReportDO reportDO = new DeliverGoodsReportDO();
            reportDO.setPlatformId(trade.getPlatformId());
            reportDO.setPlatformName(trade.getPlatformName());
            reportDO.setPlatformTradeId(trade.getPlatformTradeId());
            reportDO.setPaymentTime(DateUtil.dateToLocalDateTime(trade.getPaymentTime()));
            reportDO.setShippingTime(DateUtil.dateToLocalDateTime(trade.getShippingTime()));
            reportDO.setExpressTrackingNo(trade.getExpressTrackingNo());
            reportDO.setExpressCompany(trade.getExpressCompany());
            reportDO.setProductDetail(trade.getProductDetail());
            reportDO.setQuestionType(type);
            reportDO.setQuestionDescribe(content);
            reportDO.setInterceptStatus(interceptStatus.toString());
            reportDO.setRemake(trade.getSellerRemark());
            reportDO.setCreateTime(LocalDateTime.now());
            reportDO.setStatus(status);
            reportDO.setPlatformNum(platformSize);
            reportDO.setBusinessNum(orderSize);
            this.save(reportDO);
        }

    }

    private Wrapper<DeliverGoodsReportDO> getDeliverGoodsReportsWrapper(DeliverGoodsReportPageQuery query) {
        return Wrappers.<DeliverGoodsReportDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getPlatformName()), DeliverGoodsReportDO::getPlatformName, query.getPlatformName())
                .eq(StringUtils.isNotBlank(query.getStatus()), DeliverGoodsReportDO::getStatus, query.getStatus())
                .eq(StringUtils.isNotBlank(query.getQuestionType()), DeliverGoodsReportDO::getQuestionType, query.getQuestionType())
                .eq(ObjectUtils.isNotEmpty(query.getPlatformTradeId()), DeliverGoodsReportDO::getPlatformTradeId, query.getPlatformTradeId())
                .in(CollectionUtils.isNotEmpty(query.getPlatformIds()), DeliverGoodsReportDO::getPlatformId, query.getPlatformIds())
                .between(ObjectUtils.isNotEmpty(query.getCreateStartTime()) && ObjectUtils.isNotEmpty(query.getCreateEndTime())
                        , DeliverGoodsReportDO::getCreateTime, query.getCreateStartTime()+" 00:00:00", query.getCreateEndTime()+" 23:59:59")
                .between(ObjectUtils.isNotEmpty(query.getShippingStartTime()) && ObjectUtils.isNotEmpty(query.getShippingEndTime())
                        , DeliverGoodsReportDO::getShippingTime, query.getShippingStartTime()+" 00:00:00", query.getShippingEndTime()+" 23:59:59")
                .orderByDesc(DeliverGoodsReportDO::getShippingTime);
    }

}
