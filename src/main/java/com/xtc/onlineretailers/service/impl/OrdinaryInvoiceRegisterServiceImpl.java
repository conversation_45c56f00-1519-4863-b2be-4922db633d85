package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.enums.OrdinaryInvoiceRegisterStatusEnum;
import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.mapper.master.OrdinaryInvoiceRegisterMapper;
import com.xtc.onlineretailers.pojo.entity.OrdinaryInvoiceRegisterDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.query.*;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.OrdinaryInvoiceRegisterService;
import com.xtc.onlineretailers.service.TradeService;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.springboot.pojo.entity.GlobalContext;
import com.xtc.springboot.util.GsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
public class OrdinaryInvoiceRegisterServiceImpl extends ServiceImpl<OrdinaryInvoiceRegisterMapper, OrdinaryInvoiceRegisterDO>
        implements OrdinaryInvoiceRegisterService {

    @Resource
    private TradeService tradeService;

    @Override
    public void add(OrdinaryInvoiceRegisterAddQuery query) {
        // 检查该订单是否存在未处理完成订单
        List<Integer> statusList = Lists.newArrayList(0, 1, 2);
        Optional<OrdinaryInvoiceRegisterDO> optional = getOneByTradeIdAndStatus(query.getPlatformTradeId(), statusList);
        if (optional.isPresent()) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_ORDINARY_INVOICE_REGISTER_RECORD_EXIST);
        }
        // 校验订单信息
        TradeDO trade = this.tradeService.getTradeByTradeId(query.getPlatformTradeId());
        if(OrderStatus.RETURNED.name().equals(trade.getStatusOrder())){
            throw new GlobalDefaultException("订单:" + trade.getPlatformTradeId() + "是已退货状态,不可以登记");
        }

        OrdinaryInvoiceRegisterDO ordinaryInvoiceRegisterDO = new OrdinaryInvoiceRegisterDO();
        BeanUtils.copyProperties(query, ordinaryInvoiceRegisterDO);
        ordinaryInvoiceRegisterDO.setStatus(OrdinaryInvoiceRegisterStatusEnum.HAS_REGISTERED.getStatus());
        ordinaryInvoiceRegisterDO.setCreator(GlobalContext.getUser().getName());
        this.save(ordinaryInvoiceRegisterDO);
    }

    @Override
    public PaginationVO<OrdinaryInvoiceRegisterDO> pageBy(OrdinaryInvoiceRegisterQuery query) {
        Page<OrdinaryInvoiceRegisterDO> page = PaginationQuery.createPage(query.getIndex(), query.getSize());
        IPage<OrdinaryInvoiceRegisterDO> result = this.page(page, getConditionWrapper(query));
        return PaginationVO.newVO(result);
    }

    private Wrapper<OrdinaryInvoiceRegisterDO> getConditionWrapper(OrdinaryInvoiceRegisterQuery query) {
        return Wrappers.<OrdinaryInvoiceRegisterDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getPlatformTradeId()), OrdinaryInvoiceRegisterDO::getPlatformTradeId,
                        query.getPlatformTradeId())
                .eq(query.getPlatformId() != null, OrdinaryInvoiceRegisterDO::getPlatformId, query.getPlatformId())
                .eq(query.getStatus() != null, OrdinaryInvoiceRegisterDO::getStatus, query.getStatus())
                .eq(StringUtils.isNotBlank(query.getCreator()), OrdinaryInvoiceRegisterDO::getCreator, query.getCreator())
                .ge(StringUtils.isNotBlank(query.getCreateTimeStart()), OrdinaryInvoiceRegisterDO::getCreateTime,
                        query.getCreateTimeStart() + " 00:00:00")
                .le(StringUtils.isNotBlank(query.getCreateTimeEnd()), OrdinaryInvoiceRegisterDO::getCreateTime,
                        query.getCreateTimeEnd() + " 23:59:59")
                .eq(StringUtils.isNotBlank(query.getConfirmOperator()), OrdinaryInvoiceRegisterDO::getConfirmOperator,
                        query.getConfirmOperator())
                .ge(StringUtils.isNotBlank(query.getConfirmTimeStart()), OrdinaryInvoiceRegisterDO::getConfirmTime,
                        query.getConfirmTimeStart() + " 00:00:00")
                .le(StringUtils.isNotBlank(query.getConfirmTimeEnd()), OrdinaryInvoiceRegisterDO::getConfirmTime,
                        query.getConfirmTimeEnd() + " 23:59:59")
                .orderByDesc(OrdinaryInvoiceRegisterDO::getCreateTime);
    }

    @Override
    public Optional<OrdinaryInvoiceRegisterDO> getOneByTradeIdAndStatus(String platformTradeId, List<Integer> status) {
        return Optional.ofNullable(this.getOne(Wrappers.<OrdinaryInvoiceRegisterDO>lambdaQuery()
                .eq(OrdinaryInvoiceRegisterDO::getPlatformTradeId, platformTradeId)
                .in(OrdinaryInvoiceRegisterDO::getStatus, status)
                .last("limit 1")));
    }

    @Override
    public void update(OrdinaryInvoiceRegisterUpdateQuery query) {
        checkDataExist(query.getId());
        OrdinaryInvoiceRegisterDO ordinaryInvoiceRegisterDO = new OrdinaryInvoiceRegisterDO();
        BeanUtils.copyProperties(query, ordinaryInvoiceRegisterDO);
        this.updateById(ordinaryInvoiceRegisterDO);
    }

    private void checkDataExist(Long id) {
        OrdinaryInvoiceRegisterDO existRecord = this.getById(id);
        if (existRecord == null) {
            throw new GlobalDefaultException(ResponseEnum.NOT_EXIST);
        }
    }

    @Override
    public boolean delete(long id) {
        checkDataExist(id);
        return this.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void confirm(OrdinaryInvoiceRegisterConfirmQuery query) {
        query.getIds().forEach(id -> {
            // 更新原订单发票信息
            checkDataExist(id);
            OrdinaryInvoiceRegisterDO existRecord = this.getById(id);
            TradeDO existTrade = this.tradeService.getTradeByTradeId(existRecord.getPlatformTradeId());
            if (existTrade == null) {
                throw new GlobalDefaultException(ResponseEnum.NOT_EXIST);
            }
            if(OrderStatus.RETURNED.name().equals(existTrade.getStatusOrder())){
                throw new GlobalDefaultException("订单:" + existTrade.getPlatformTradeId() + "是已退货状态,不可以确认");
            }

            TradeDO tradeDO = GsonUtil.jsonToBean(existRecord.getModifyContent(), TradeDO.class);
            tradeDO.setId(existTrade.getId());
            this.tradeService.updateById(tradeDO);

            // 更新普票登记记录状态
            OrdinaryInvoiceRegisterDO ordinaryInvoiceRegisterDO = new OrdinaryInvoiceRegisterDO();
            ordinaryInvoiceRegisterDO.setId(existRecord.getId());
            ordinaryInvoiceRegisterDO.setStatus(OrdinaryInvoiceRegisterStatusEnum.HAS_CONFIRMED.getStatus());
            ordinaryInvoiceRegisterDO.setConfirmOperator(GlobalContext.getUser().getName());
            ordinaryInvoiceRegisterDO.setConfirmTime(DateUtil.nowDateTimeStr());
            this.updateById(ordinaryInvoiceRegisterDO);
        });
    }

}
