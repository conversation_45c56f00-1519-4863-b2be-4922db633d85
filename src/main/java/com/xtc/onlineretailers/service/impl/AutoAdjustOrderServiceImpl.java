package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.mapper.master.AutoAdjustOrderMapper;
import com.xtc.onlineretailers.pojo.entity.AutoAdjustOrderDO;
import com.xtc.onlineretailers.pojo.entity.OrderDO;
import com.xtc.onlineretailers.pojo.entity.ReturnMasterDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.query.AutoAdjustOrderQuery;
import com.xtc.onlineretailers.pojo.query.PaginationQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.refactor.executor.command.InvoiceApplySyncCmdExe;
import com.xtc.onlineretailers.refactor.executor.command.InvoiceCreateRedCmdExe;
import com.xtc.onlineretailers.service.AutoAdjustOrderService;
import com.xtc.onlineretailers.service.ReturnMasterService;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.springboot.pojo.entity.GlobalContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AutoAdjustOrderServiceImpl extends ServiceImpl<AutoAdjustOrderMapper, AutoAdjustOrderDO>
        implements AutoAdjustOrderService {

    private final Lock adjustOrderTaskLock = new ReentrantLock();

    @Resource
    private TradeServiceImpl tradeService;

    @Resource
    private OrderServiceImpl orderService;

    @Resource
    private ReturnMasterService returnMasterService;

    @Resource
    private AutoAdjustOrderService autoAdjustOrderService;

    @Resource
    private AutoAdjustOrderMapper autoAdjustOrderMapper;
    @Resource
    private InvoiceCreateRedCmdExe invoiceCreateRedCmdExe;
    @Resource
    private InvoiceApplySyncCmdExe invoiceApplySyncCmdExe;

    @Override
    public PaginationVO<AutoAdjustOrderDO> page(AutoAdjustOrderQuery query) {
        Page<AutoAdjustOrderDO> page = PaginationQuery.createPage(query.getIndex(), query.getSize());
        IPage<AutoAdjustOrderDO> pageResult = this.page(page, getConditionWrapper(query));
        return PaginationVO.newVO(pageResult);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAdjust(List<Integer> ids) {
        // 获取锁，并发下只执行一次
        if (adjustOrderTaskLock.tryLock()) {
            try {
                // 获取未调单的记录进行自动调单
                List<AutoAdjustOrderDO> processingList = this.autoAdjustOrderMapper.selectAdjustOrders(ids);
                startAutoAdjustOrder(processingList);
                return true;
            } catch (Exception e) {
                log.error("自动调单发生异常", e);
            } finally {
                adjustOrderTaskLock.unlock();
            }
        } else {
            throw new GlobalDefaultException(ResponseEnum.ERROR_AUTO_ADJUST_ORDER_PROCESSING);
        }
        throw new GlobalDefaultException(ResponseEnum.ERROR_AUTO_ADJUST_ORDER);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncAutoAdjustOrders() {
        // 同步前1h内的退款记录
        Date endTime = DateUtils.truncate(new Date(), 10);
        Date startTime = DateUtils.addHours(endTime, -1);
        List<AutoAdjustOrderDO> initialList = this.autoAdjustOrderMapper.syncAutoAdjustOrders(DateUtil.dateToStr(startTime),
                DateUtil.dateToStr(endTime));
        // 订单号+退款时间+退款金额去重
        List<String> tradeIds = initialList.stream().map(AutoAdjustOrderDO::getPlatformTradeId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tradeIds)) {
            Set<String> existRecord = this.autoAdjustOrderService.listByTradeIds(tradeIds);
            List<AutoAdjustOrderDO> finalList = initialList.stream()
                    .filter(p -> !existRecord.contains(p.getPlatformTradeId() + p.getRefundTime() + p.getRefundFee().toString()))
                    .collect(Collectors.toList());
            this.saveBatch(finalList);
        }

        /// 暂时关闭
        // 获取未调单的记录进行自动调单
//        List<AutoAdjustOrderDO> processingList = this.autoAdjustOrderMapper.selectAdjustOrders(null);
//        startAutoAdjustOrder(processingList);
    }

    @Override
    public Set<String> listByTradeIds(List<String> tradeIds) {
        List<AutoAdjustOrderDO> list = this.list(Wrappers.<AutoAdjustOrderDO>lambdaQuery()
                .in(AutoAdjustOrderDO::getPlatformTradeId, tradeIds));
        return list.stream()
                .map(p -> p.getPlatformTradeId() + p.getRefundTime() + p.getRefundFee().toString())
                .collect(Collectors.toSet());
    }

    /**
     * 自动调单
     *
     * @param processingList 待调单列表
     */
    private void startAutoAdjustOrder(List<AutoAdjustOrderDO> processingList) {
        processingList.parallelStream().forEach(p -> {
            // 查询订单详情
            TradeDO existTradeDO = this.tradeService.getExistTradeDO(p.getPlatformTradeId());
            List<OrderDO> orderDOList = this.orderService.getOrderDOS(p.getPlatformTradeId());

            // 检查自动调单操作是否符合条件
            if (!checkAdjustConditionSatisfied(p, existTradeDO)) {
                this.update(getUpdateWrapper(p));
                return;
            }

            // 创建退货记录
            this.tradeService.createReturn("【此单收款差异调账】", existTradeDO, orderDOList, "RETURN",
                    "NOT_NEED", "NO_PROCESSING", p.getPlatformTradeId() + "1");
            TradeDO tradeDO = new TradeDO();
            tradeDO.setId(existTradeDO.getId());
            tradeDO.setStatusOrder(OrderStatus.RETURNED.name());
            this.tradeService.updateById(tradeDO);

            // 退货记录更新009仓库
            this.returnMasterService.update(Wrappers.<ReturnMasterDO>lambdaUpdate()
                    .set(ReturnMasterDO::getSubinvCode, "009")
                    .set(ReturnMasterDO::getErpPostManual, 1)
                    .eq(ReturnMasterDO::getRefundId, p.getPlatformTradeId() + "1"));

            // 生成调单
            String newPlatformTradeId = p.getPlatformTradeId() + "8";
            BigDecimal adjustMoney = p.getRefundFee();
            existTradeDO.setPayment(existTradeDO.getPayment().subtract(adjustMoney));
            orderDOList.stream().filter(orderDO -> "折扣".equals(orderDO.getErpName())).forEach(orderDO -> {
                orderDO.setPayment(orderDO.getPayment().subtract(adjustMoney));
            });
            this.tradeService.createAdjustOrder("【此单收款差异调账】", newPlatformTradeId, existTradeDO, orderDOList);

            // 发货仓库更新为009
            this.tradeService.update(Wrappers.<TradeDO>lambdaUpdate()
                    .set(TradeDO::getWarehouseCode, "009")
                    .set(TradeDO::getWarehouseStorage, "009")
                    .eq(TradeDO::getPlatformTradeId, newPlatformTradeId));

            // 设置成功处理状态
            setOperateResult(p, 1, "");
            this.update(getUpdateWrapper(p));

            invoiceCreateRedCmdExe.executeBySystem(existTradeDO.getPlatformTradeId(), "自动调单冲红原单发票");
            invoiceApplySyncCmdExe.executeUsingClone(existTradeDO.getPlatformTradeId(), newPlatformTradeId, "自动调单开新票");
        });
    }

    private LambdaUpdateWrapper<AutoAdjustOrderDO> getUpdateWrapper(AutoAdjustOrderDO autoAdjustOrderDO) {
        return Wrappers.<AutoAdjustOrderDO>lambdaUpdate()
                .set(AutoAdjustOrderDO::getStatus, autoAdjustOrderDO.getStatus())
                .set(AutoAdjustOrderDO::getFailReason, autoAdjustOrderDO.getFailReason())
                .set(AutoAdjustOrderDO::getOperator, autoAdjustOrderDO.getOperator())
                .set(AutoAdjustOrderDO::getOperateTime, autoAdjustOrderDO.getOperateTime())
                .eq(AutoAdjustOrderDO::getStatus, 0)
                .eq(AutoAdjustOrderDO::getPlatformTradeId, autoAdjustOrderDO.getPlatformTradeId());
    }

    /**
     * 检查记录是否符合自动调单条件
     *
     * @param autoAdjustOrderDO 待调单记录
     * @param existTradeDO      原单
     * @return 结果
     */
    private boolean checkAdjustConditionSatisfied(AutoAdjustOrderDO autoAdjustOrderDO, TradeDO existTradeDO) {
        // 原订单找不到
        if (existTradeDO == null) {
            setOperateResult(autoAdjustOrderDO, 2, "原订单找不到!");
            return false;
        }

        // 原订单状态不是已发货状态
        if (!existTradeDO.getStatusOrder().equals(OrderStatus.HAS_SHIP.name())) {
            setOperateResult(autoAdjustOrderDO, 2, "原订单状态不是已发货状态!");
            return false;
        }

        // 原订单已开过纸票
        if (StringUtils.isNotBlank(existTradeDO.getPaperInvoiceNo())) {
            setOperateResult(autoAdjustOrderDO, 2, "原订单已开过纸票!");
            return false;
        }

        // 订单实付-退款金额<=0
        if (autoAdjustOrderDO.getTotalFee().compareTo(autoAdjustOrderDO.getRefundFee()) <= 0) {
            setOperateResult(autoAdjustOrderDO, 2, "实付金额小于等于退款金额!");
            return false;
        }

        // 原订单存在对应的调单（通过buyerNick查找大于原订单的调单）
        TradeDO existAdjustOrder = this.tradeService.getOne(Wrappers.<TradeDO>lambdaQuery()
                .eq(TradeDO::getBuyerNickname, existTradeDO.getBuyerNickname())
                .eq(TradeDO::getType, "ADJUST_ORDER")
                .ge(TradeDO::getPlatformTradeId, existTradeDO.getPlatformTradeId())
                .last("limit 1"));
        if (existAdjustOrder != null) {
            setOperateResult(autoAdjustOrderDO, 2, "原订单存在对应的调单!");
            return false;
        }
        return true;
    }

    /**
     * 自动调单执行结果设置
     *
     * @param autoAdjustOrderDO 调单记录
     * @param status            调单状态
     * @param failReason        调单失败原因
     */
    private void setOperateResult(AutoAdjustOrderDO autoAdjustOrderDO, Integer status, String failReason) {
        autoAdjustOrderDO.setStatus(status);
        autoAdjustOrderDO.setFailReason(failReason);
        autoAdjustOrderDO.setOperator(GlobalContext.getUser() == null ? "admin" : GlobalContext.getUser().getName());
        autoAdjustOrderDO.setOperateTime(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss"));
    }

    private Wrapper<AutoAdjustOrderDO> getConditionWrapper(AutoAdjustOrderQuery query) {
        return Wrappers.<AutoAdjustOrderDO>lambdaQuery()
                .in(CollectionUtils.isNotEmpty(query.getPlatformIds()), AutoAdjustOrderDO::getPlatformId, query.getPlatformIds())
                .eq(StringUtils.isNotBlank(query.getPlatformTradeId()), AutoAdjustOrderDO::getPlatformTradeId, query.getPlatformTradeId())
                .eq(query.getStatus() != null, AutoAdjustOrderDO::getStatus, query.getStatus())
                .ge(StringUtils.isNotBlank(query.getRefundTimeStart()), AutoAdjustOrderDO::getRefundTime,
                        query.getRefundTimeStart() + " 00:00:00")
                .le(StringUtils.isNotBlank(query.getRefundTimeEnd()), AutoAdjustOrderDO::getRefundTime,
                        query.getRefundTimeEnd() + " 23:59:59")
                .ge(StringUtils.isNotBlank(query.getOperateTimeStart()), AutoAdjustOrderDO::getOperateTime,
                        query.getOperateTimeStart() + " 00:00:00")
                .le(StringUtils.isNotBlank(query.getOperateTimeEnd()), AutoAdjustOrderDO::getOperateTime,
                        query.getOperateTimeEnd() + " 23:59:59")
                .orderByDesc(AutoAdjustOrderDO::getId);
    }

}
