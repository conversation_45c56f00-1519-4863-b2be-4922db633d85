package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.mapper.master.BarcodeExchangeLogMapper;
import com.xtc.onlineretailers.pojo.entity.BarcodeExchangeLogDO;
import com.xtc.onlineretailers.pojo.query.BarcodeDisplaceQuery;
import com.xtc.onlineretailers.service.BarcodeExchangeLogService;
import com.xtc.springboot.pojo.entity.GlobalContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class BarcodeExchangeLogServiceImpl extends ServiceImpl<BarcodeExchangeLogMapper, BarcodeExchangeLogDO> implements BarcodeExchangeLogService {

    @Override
    public void save(BarcodeDisplaceQuery query, String sendTradeId, String returnTradeId) {
        BarcodeExchangeLogDO barcodeExchangeLogDO = new BarcodeExchangeLogDO();
        BeanUtils.copyProperties(query, barcodeExchangeLogDO);
        barcodeExchangeLogDO.setOperator(GlobalContext.getUser().getName());
        barcodeExchangeLogDO.setSendTradeId(sendTradeId);
        barcodeExchangeLogDO.setReturnTradeId(returnTradeId);
        this.save(barcodeExchangeLogDO);
    }

}
