package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.executor.command.SmsInterceptCmd;
import com.xtc.onlineretailers.mapper.master.SmsMapper;
import com.xtc.onlineretailers.pojo.bo.SFInterceptBO;
import com.xtc.onlineretailers.pojo.entity.SmsDO;
import com.xtc.onlineretailers.pojo.query.PaginationQuery;
import com.xtc.onlineretailers.pojo.query.SmsGetQuery;
import com.xtc.onlineretailers.pojo.query.SmsRemarkQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.pojo.vo.SmsExportVO;
import com.xtc.onlineretailers.pojo.vo.SmsRefundVO;
import com.xtc.onlineretailers.repository.TradeDao;
import com.xtc.onlineretailers.service.SmsService;
import com.xtc.onlineretailers.service.SystemParamService;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class SmsServiceImpl extends ServiceImpl<SmsMapper, SmsDO> implements SmsService {

    @Resource
    private SmsMapper smsMapper;

    @Resource
    private TradeDao tradeDao;

    @Resource
    private SystemParamService systemParamService;

    @Override
    public PaginationVO<SmsDO> sendList(SmsGetQuery query) {

        Page<SmsDO> page = PaginationQuery.createPage(query.getIndex(), query.getSize());
        Wrapper<SmsDO> wrapper = Wrappers.<SmsDO>lambdaQuery()
                .eq(query.getPlatformId() != null, SmsDO::getPlatformId, query.getPlatformId())
                .ge(StringUtils.isNotBlank(query.getStartDate()), SmsDO::getCreateTime, query.getStartDate() + " 00:00:00")
                .le(StringUtils.isNotBlank(query.getEndDate()), SmsDO::getCreateTime, query.getEndDate() + " 23:59:59")
                .orderByDesc(SmsDO::getCreateTime);
        Page<SmsDO> result = this.page(page, wrapper);
        return PaginationVO.newVO(result);
    }

    @Override
    public PaginationVO<SmsRefundVO> refundList(SmsGetQuery query) {
        if (query.getStartDate() != null) {
            query.setStartDate(query.getStartDate() + " 00:00:00");
        }
        if (query.getEndDate() != null) {
            query.setEndDate(query.getEndDate() + " 23:59:59");
        }
        IPage<SmsRefundVO> list = this.smsMapper.selectRefundSmsPage(query.createPage(), query);
        return PaginationVO.newVO(list);
    }

    @Override
    public void exportSms(SmsGetQuery query, HttpServletResponse response) {
        if (query.getStartDate() != null) {
            query.setStartDate(query.getStartDate() + " 00:00:00");
        }
        if (query.getEndDate() != null) {
            query.setEndDate(query.getEndDate() + " 23:59:59");
        }

        List<SmsExportVO> list = this.smsMapper.selectExportSms(query);

        // 获取当前时间
        String newStr = DateUtil.dateToStr(new Date(), "yyyy-MM-dd");
        // 文件名称
        String fileName = newStr + "电子商务系统导出短信发送记录";

        ExcelUtil.exportExcel(fileName, list, SmsExportVO.class, response);
    }

    @Override
    public void sendSms(int platformId, String tradeId, String title, String content) {
        String phones = this.systemParamService.getSystemParamByName(GlobalConstant.SYSTEM_NOTICE_PHONES);
        this.sendSms(platformId, tradeId, phones, title, content, "");
    }

    @Override
    public void sendSms(int platformId, String tradeId, String phones, String title, String content, String sfMessage) {
        if (phones == null) {
            throw new RuntimeException("手机号为空");
        }

        boolean repeat = this.sendRepeat(phones, title, tradeId);
        if (repeat) {
            log.error("短信已发送，请勿重复发送 phones: {}, title: {}, content: {}", phones, title, content);
            return;
        }

        // 获取电话号码集合
        List<String> phoneList = Lists.newArrayList(phones.split(","));

        // 保存数据
        SmsDO smsDO = new SmsDO();
        smsDO.setPlatformTradeId(tradeId);
        smsDO.setPlatformId(platformId);
        smsDO.setTelephone(phones);
        smsDO.setReceiverCount(phoneList.size());
        smsDO.setMessageNumber(1);
        smsDO.setTitle(title);
        smsDO.setContent(content);
        smsDO.setResponseMessage(sfMessage);
        smsDO.setIsIntercept(0);
        smsDO.setTradeStatus(0);
        this.save(smsDO);
    }

    @Override
    public boolean remark(SmsRemarkQuery query) {
        SmsDO smsDO = new SmsDO();
        BeanUtils.copyProperties(query, smsDO);

        return this.updateById(smsDO);
    }

    @Override
    public boolean intercept(SmsInterceptCmd cmd) {
        SmsDO smsDO = this.getById(cmd.getId());
        if (smsDO == null) {
            throw new GlobalDefaultException("拦截记录不存在");
        }
        Integer tradeStatus = null;
        if (smsDO.getTradeStatus() != null && smsDO.getTradeStatus() == 0) {
            tradeStatus = BooleanUtils.isTrue(cmd.getIntercept()) ? 1 : 0;
        }
        SmsDO updateSms = new SmsDO();
        updateSms.setId(cmd.getId());
        updateSms.setTradeStatus(tradeStatus);
        updateSms.setRemark(cmd.getRemake());
        return this.updateById(updateSms);
    }

    /**
     * 判断是否已经发送短信
     *
     * @param phones  手机号
     * @param title   标题
     * @param tradeId 订单号
     * @return 执行结果
     */
    private boolean sendRepeat(String phones, String title, String tradeId) {
        Wrapper<SmsDO> wrapper = Wrappers.<SmsDO>lambdaQuery()
                .eq(SmsDO::getTelephone, phones)
                .eq(SmsDO::getPlatformTradeId, tradeId);
        long count = this.count(wrapper);
        return count > 0;
    }

    @Override
    public SmsDO getByTradeId(String platformTradeId) {
        return this.getOne(Wrappers.<SmsDO>lambdaQuery()
                .eq(SmsDO::getPlatformTradeId, platformTradeId)
                .last("limit 1"));
    }

    @Override
    public List<SFInterceptBO> sfNeedInterceptList() {
        return this.smsMapper.sfNeedInterceptList();
    }

    @Override
    public void updateByTradeId(SmsDO sms) {
        this.update(sms, Wrappers.<SmsDO>lambdaQuery().eq(SmsDO::getPlatformTradeId, sms.getPlatformTradeId()));
    }

    @Override
    public List<SFInterceptBO> getRouteParseList() {
        return this.smsMapper.getRouteParseList();
    }

}
