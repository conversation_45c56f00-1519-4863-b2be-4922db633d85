package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.taobao.api.domain.Order;
import com.taobao.api.domain.Trade;
import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.mapper.master.RebatesMapper;
import com.xtc.onlineretailers.pojo.entity.*;
import com.xtc.onlineretailers.pojo.query.PaginationQuery;
import com.xtc.onlineretailers.pojo.query.RebatesQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.*;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.springboot.pojo.entity.GlobalContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RebatesServiceImpl extends ServiceImpl<RebatesMapper, RebatesDO> implements RebatesService {
    @Resource
    private TradeService tradeService;
    @Resource
    private PlatformRefundService platformRefundService;
    @Resource
    private OrderService orderService;
    @Resource
    private RebatesService rebatesService;
    @Resource
    private RebatesDetailService rebatesDetailService;
    @Resource
    private PlatformService platformService;
    @Resource
    private RebatesLogService rebatesLogService;

    @Override
    public PaginationVO<RebatesDO> list(RebatesQuery query) {
        Page<RebatesDO> page = PaginationQuery.createPage(query.getIndex(), query.getSize());
        Wrapper<RebatesDO> wrapper = getInterceptOrderDOWrapper(query);
        Page<RebatesDO> page1 = this.page(page, wrapper);
        return PaginationVO.newVO(page1);
    }

    @Override
    public void saveRebates() {
        //以第三方退款为准
        List<TradeDO> tradeDOList = this.tradeService.list(Wrappers.<TradeDO>lambdaQuery().in(TradeDO::getStatusOrder, OrderStatus.REJECT.name()).gt(TradeDO::getCreateTime, DateUtil.localDateTimeToDate(LocalDateTime.now().minusDays(7))).lt(TradeDO::getCreateTime, DateUtil.localDateTimeToDate(LocalDateTime.now())));
        for (TradeDO tradeDO : tradeDOList) {
            // 查询第三方退款表(只处理天猫步步高，天猫小天才)
            List<PlatformRefundDO> refundList = this.platformRefundService.getRefundList(tradeDO.getPlatformTradeId());
            for (PlatformRefundDO platformRefundDO : refundList) {
                // 查看订单的所有的订单明细
                List<OrderDO> orderDOList = this.orderService.getOrderDOS(platformRefundDO.getPlatformTradeId());
                // 查询订单的平台信息
                PlatformDO platform = this.platformService.getPlatformById(platformRefundDO.getPlatformId());

                // 根据订单号调用第三接口查询订单详情
                Trade tradeDetail = this.tradeService.getTaobaoTradeDetail(platform, platformRefundDO.getPlatformTradeId());
                // 获取退款订单明细列表
                List<Order> orders = tradeDetail.getOrders();

                // 计算正常明细的订单数
                long normalRefundOrder = orderDOList.stream().count();
                //部分退款订单明细数量
                long portionRefundOrder = orders.stream().filter(order -> order.getRefundId().equals(platformRefundDO.getPlatformRefundId())).count();

                if (portionRefundOrder != normalRefundOrder) {

                    // 记录部分退款主订单信息
                    RebatesDO rebatesDO = new RebatesDO();
                    BeanUtils.copyProperties(tradeDO, rebatesDO);
                    rebatesDO.setIsUpdate(0);
                    this.rebatesService.save(rebatesDO);

                    //保存订单明细信息
                    List<OrderDO> orderDOS = this.orderService.getOrderDOS(platformRefundDO.getPlatformTradeId());
                    for (OrderDO orderDO : orderDOS) {
                        RebatesDetailDO rebatesDetailDO = new RebatesDetailDO();
                        BeanUtils.copyProperties(orderDO, rebatesDetailDO);
                        this.rebatesDetailService.save(rebatesDetailDO);
                    }

                }

            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyOrder(List<TradeDO> tradeDOList) {
        try {
            for (TradeDO tradeDO : tradeDOList) {
                // 获取订单平台信息
                PlatformDO platform = this.platformService.getPlatformById(tradeDO.getPlatformId());

                // 调用第三方接口获取订单明细
                Trade tradeDetail = this.tradeService.getTaobaoTradeDetail(platform, tradeDO.getPlatformTradeId());
                List<Order> returnRefundOrders = tradeDetail.getOrders();

                // 获取部分退款的订单明细
                List<Order> refundDetail = returnRefundOrders.stream().filter(refundOrder -> refundOrder.getRefundId() != null).collect(Collectors.toList());
                for (Order order : refundDetail) {
                    String outerSkuId = order.getOuterSkuId();
                    List<String> codeList = Arrays.asList(outerSkuId.split(","));
                    List<String> distinctCodeList = codeList.stream().distinct().collect(Collectors.toList());

                    // 通过物料code和电商id获取部分订单明细
                    List<OrderDO> list = this.orderService.list(Wrappers.<OrderDO>lambdaQuery().eq(OrderDO::getPlatformTradeId, tradeDO.getPlatformTradeId()).in(OrderDO::getErpCode, distinctCodeList));
                    if (CollectionUtils.isEmpty(list)) {
                        return;
                    }
                    // 处理部分退款订单明细类型
                    disposeOrderType(list);
                }

            }
            // 记录操作人
            RebatesLogDO rebatesLogDO = new RebatesLogDO();
            rebatesLogDO.setAdminName(GlobalContext.getUser().getName());
            rebatesLogDO.setCreateTime(DateUtil.localDateTimeToStr(LocalDateTime.now()));
            rebatesLogService.save(rebatesLogDO);

        } catch (Exception e) {
            log.error("部分退款批量修改失败:{}", e);
        }

    }

    /**
     * 处理订单类型
     *
     * @param orderDOS 部分退款明细
     */
    private void disposeOrderType(List<OrderDO> orderDOS) {

        BigDecimal payment = BigDecimal.ZERO;
        BigDecimal priceTotal = BigDecimal.ZERO;
        BigDecimal freight = BigDecimal.ZERO;
        boolean zhekou = false;
        Integer num = 0;
        for (OrderDO orderDO : orderDOS) {
            //1:正常明细 2:赠品明细  3:折扣明细  4：运费明细
            if (orderDO.getOrderType() == 1) {
                //计算正常明细的单价和总价
                payment = payment.add(orderDO.getPayment());
                priceTotal = priceTotal.add(orderDO.getPriceTotal());

                //商品数量
                num += orderDO.getNum();

                //判断是否存在赠品订单明细
                if (orderDO.getPresentMainCode() != null) {
                    //赠品订单
                    OrderDO presentMainOder = this.orderService.list(Wrappers.<OrderDO>lambdaQuery().eq(OrderDO::getPlatformTradeId, orderDO.getPlatformTradeId()).eq(OrderDO::getErpCode, orderDO.getPresentMainCode())).get(0);
                    payment = payment.add(presentMainOder.getPayment());
                    priceTotal = priceTotal.add(presentMainOder.getPriceTotal());
                    num += presentMainOder.getNum();
                    // 删除赠品
                    this.orderService.removeById(presentMainOder.getId());
                }
                this.orderService.removeById(orderDO.getId());
            } else if (orderDO.getOrderType() == 2) {
                // 计算赠品明细的单价和总价
                payment = payment.add(orderDO.getPayment());
                priceTotal = priceTotal.add(orderDO.getPriceTotal());
                num += orderDO.getNum();
                this.orderService.removeById(orderDO.getId());

            } else if (orderDO.getOrderType() == 3) {
                orderDO.setPayment(orderDO.getPayment().subtract(payment));
                orderDO.setPriceTotal(orderDO.getPriceTotal().subtract(priceTotal));
                zhekou = true;
            } else if (orderDO.getOrderType() == 4) {
                freight = freight.add(orderDO.getPriceTotal());
            }
            // 为订单做一个修改的标志
            RebatesDO rebatesDO = this.rebatesService.getOne(Wrappers.<RebatesDO>lambdaQuery().eq(RebatesDO::getPlatformTradeId, orderDO.getPlatformTradeId()));
            rebatesDO.setIsUpdate(1);
            this.rebatesService.updateById(rebatesDO);
            updateTradeOrder(orderDO.getPlatformTradeId(), priceTotal, freight, num);
        }
//        // 补上折扣明细
//        if (!zhekou) {
//            OrderDO orderDO = new OrderDO();
//            orderDO.setPlatformTradeId(orderDOS.get(0).getPlatformTradeId());
//            orderDO.setOrderType(3);
//            orderDO.setPayment(BigDecimal.ZERO);
//            orderDO.setBuyerNickname("LIULIU");
//            orderDO.setCreateTime(DateUtil.localDateTimeToStr(LocalDateTime.now()));
//            orderDO.setPaymentTime(orderDOS.get(0).getPaymentTime());
//            this.orderService.save(orderDO);
//        }

    }

    /**
     * 重新计算订单相关的金额
     *
     * @param tradeId    订单id
     * @param priceTotal 总金额
     * @param freight    运费
     */
    public void updateTradeOrder(String tradeId, BigDecimal priceTotal, BigDecimal freight, Integer num) {
        TradeDO tradeDO = this.tradeService.getTradeByTradeId(tradeId);
        tradeDO.setPayment(tradeDO.getPayment().subtract(priceTotal));
        tradeDO.setFreight(freight);
        tradeDO.setProductNum(num);

        // 按照金额来对订单明细进行排序
        List<OrderDO> orderDOS = this.orderService.getOrderDOS(tradeId);
        List<OrderDO> orderDetail = orderDOS.stream().filter(orderDO -> orderDO.getOrderType() == 1).sorted(Comparator.comparing(OrderDO::getPayment)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderDetail)) {
            tradeDO.setProductDetail("");
        }
        // 取出金额最大的erp名称作为商品名称
        String erpName = orderDetail.get(0).getErpName();
        tradeDO.setProductDetail(erpName);
        tradeDO.setUpdateTime(DateUtil.localDateTimeToStr(LocalDateTime.now()));
        this.tradeService.updateById(tradeDO);
    }

    private Wrapper<RebatesDO> getInterceptOrderDOWrapper(RebatesQuery query) {
        return Wrappers.<RebatesDO>lambdaQuery()
                .gt(StringUtils.isNotBlank(query.getBeginTime()), RebatesDO::getPaymentTime, query.getBeginTime())
                .lt(StringUtils.isNotBlank(query.getEndTime()), RebatesDO::getPaymentTime, query.getEndTime())
                .eq(ObjectUtils.isNotNull(query.getPlatformId()), RebatesDO::getPlatformId, query.getPlatformId())
                .eq(StringUtils.isNotBlank(query.getPlatformTradeId()), RebatesDO::getPlatformTradeId, query.getPlatformTradeId())
                .orderByDesc(RebatesDO::getPaymentTime);

    }
}
