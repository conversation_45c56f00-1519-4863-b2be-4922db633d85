package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.constant.PlatformConstant;
import com.xtc.onlineretailers.enums.*;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.mapper.master.RolePlatformMapper;
import com.xtc.onlineretailers.pojo.entity.*;
import com.xtc.onlineretailers.pojo.query.ExpressLogQuery;
import com.xtc.onlineretailers.pojo.vo.*;
import com.xtc.onlineretailers.repository.TradeDao;
import com.xtc.onlineretailers.service.*;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.ExcelUtil;
import com.xtc.onlineretailers.util.GsonUtil;
import com.xtc.onlineretailers.util.MinioManager;
import com.xtc.springboot.admin.mapper.AdminUserMapper;
import com.xtc.springboot.pojo.entity.UserDetail;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class AsyncServiceImpl implements AsyncService {

    private final Lock customerSyncTaskLock = new ReentrantLock();

    @Resource
    private ExpressLogService expressLogService;

    @Resource
    private PostService postService;

    @Resource
    private OrderService orderService;

    @Resource
    private ProductInfoService productInfoService;

    @Resource
    private TradeService tradeService;

    @Resource
    private CustomerService customerService;

    @Resource
    private NewsNoticeService newsNoticeService;

    @Resource
    private AppointmentDownloadService appointmentDownloadService;

    @Resource
    private WarehouseService warehouseService;

    @Resource
    private ReturnMasterService returnMasterService;

    @Resource
    private ReturnDetailsService returnDetailsService;

    @Resource
    private PlatformService platformService;

    @Resource
    private CategoryService categoryService;

    @Resource
    private MinioManager minioManager;

    @Resource
    private AdminUserMapper adminUserMapper;

    @Resource
    private SystemParamService systemParamService;
    @Resource
    private RolePlatformMapper rolePlatformMapper;
    @Resource
    private RolePlatformService rolePlatformService;
    @Resource
    private TradeDao tradeDao;

    @Override
    @Async("asyncExecutor")
    public void expressCheck(ExpressLogQuery query, UserDetail user) {
        List<ExpressLogDO> expressLogDOS = this.expressLogService.getList(query);
        // key:物料代码 value:品类
        Map<String, String> erpCodeMap = this.productInfoService.list().stream()
                .filter(p -> StringUtils.isNotBlank(p.getProductId()) && StringUtils.isNotBlank(p.getModelSort()))
                .collect(Collectors.toMap(ProductInfoDO::getProductId, ProductInfoDO::getModelSort, (v1, v2) -> v2));
        // key:平台id value:品牌
        Map<Integer, String> platformBrandMap = this.platformService.list().stream()
                .filter(p -> p.getPlatformId() != null && StringUtils.isNotBlank(p.getBrand()))
                .collect(Collectors.toMap(PlatformDO::getPlatformId, PlatformDO::getBrand, (v1, v2) -> v2));
        for (ExpressLogDO expressLogDO : expressLogDOS) {
            ExpressLogDO update = new ExpressLogDO();
            update.setId(expressLogDO.getId());
            // 1.获取不到快递
            PostDO postDO = null;
            List<PostDO> posts = this.postService.getByExpressTrackingNo(expressLogDO.getExpressTrackingNo());
            if (CollectionUtils.isNotEmpty(posts)) {
                if (posts.size() > 1) {
                    if (expressLogDO.getIsReturn() != null && expressLogDO.getIsReturn() == 1) {
                        // 相同单号多个记录，退货情况下取寄回地址为东莞
                        postDO = posts.stream().filter(p -> p.getToAddress().contains("东莞")).findFirst().orElse(null);
                    } else {
                        // 相同单号多个记录，发货情况下取寄回地址为非东莞
                        postDO = posts.stream().filter(p -> !p.getToAddress().contains("东莞")).findFirst().orElse(null);
                    }
                }
                if (postDO == null) {
                    postDO = posts.get(0);
                }
            }
            if (postDO == null) {
                update.setResult("电商有快递没有");
                this.expressLogService.updateById(update);
                continue;
            }
            // 2.存在快递
            // 根据订单号获取订单明细
            List<OrderDO> orderDOS = this.orderService.getOrderByTid(expressLogDO.getPlatformTradeId());
            // 过滤掉折扣
            List<OrderDO> orders = orderDOS.stream().filter(orderDO -> orderDO.getOrderType() != 3).collect(Collectors.toList());
            // 获取所有物料代码集合
            List<String> erpCodes = orders.stream().map(OrderDO::getErpCode).collect(Collectors.toList());
            if (erpCodes.size() == 0) {
                log.error("快递核对异常，无法查到订单明细，订单号: {}", expressLogDO.getPlatformTradeId());
                continue;
            }
            // 获取物料集合
            List<ProductInfoDO> productInfoDOS = this.productInfoService.getByErpCodes(erpCodes);
            // 物料为空时
            update.setResult("核对成功");
            if (productInfoDOS.size() == 0) {
                update.setShouldPay(postDO.getExpressFee() + "");
                this.expressLogService.updateById(update);
                continue;
            }
            update.setShouldPay(postDO.getExpressFee() + "");

            // 邮费分摊
            Map<String, BigDecimal> map = dividedExpressFee(expressLogDO.getPlatformTradeId(),
                    new BigDecimal(postDO.getExpressFee()), erpCodeMap, platformBrandMap);
            update.setProportionDetail(GsonUtil.objectToJson(map));
            this.expressLogService.updateById(update);
        }
        // 发送通知消息,快递核对成功
        this.newsNoticeService.saveNewsNotice(NewsNoticeTypeEnum.BUSINESS_COMPLETED, NewsNoticeStatusEnum.NO_NEED,
                "快递核对成功", "快递核对完成,可以查看核对数据", user.getName());
    }

    private Map<String, BigDecimal> dividedExpressFee(String platformTradeId, BigDecimal expressFee,
                                                      Map<String, String> erpCodeMap, Map<Integer, String> platformBrandMap) {
        TradeDO trade = null;
        try {
            trade = this.tradeService.getByTrade(platformTradeId);
            if (TradeTypeEnum.GIFT.name().equals(trade.getType())) {
                TradeDO trade2 = this.tradeService.getByTrade(trade.getOldPlatformTradeId());
                if (trade2 != null) {
                    trade = trade2;
                }
            }
        } catch (Exception e) {
            if (trade == null) {
                log.warn("订单 {} 快递核对异常 {}", platformTradeId, e.getMessage(), e);
                return Collections.emptyMap();
            }
        }
        return getExpressProportionMap(expressFee, trade, erpCodeMap, platformBrandMap);
    }

    private Map<String, BigDecimal> getExpressProportionMap(BigDecimal expressFee, TradeDO trade,
                                                            Map<String, String> erpCodeMap, Map<Integer, String> platformBrandMap) {
        // 查询原订单,用原订单的正常明细物料来分配费用
        List<OrderDO> orders = this.orderService.getOrderByTid(trade.getPlatformTradeId());
        List<OrderDO> normalOrders = orders.stream().filter(o -> o.getOrderType() == 1 &&
                !ProductCategoryEnum.COMMON.getCategoryCn().equals(erpCodeMap.get(o.getErpCode()))).collect(Collectors.toList());
        if (TradeTypeEnum.EXCHANGE_GOODS.name().equals(trade.getType())) {
            for (OrderDO orderDO : normalOrders) {
                ProductInfoDO productInfoDO = this.productInfoService.getProductInfoByErpCode(orderDO.getErpCode());
                if (productInfoDO != null) {
                    orderDO.setPriceTotal(productInfoDO.getErpPrice().multiply(new BigDecimal(orderDO.getNum())));
                }
            }
        }
        Optional<BigDecimal> sum = normalOrders.stream().map(OrderDO::getPriceTotal).reduce(BigDecimal::add);
        // 存放对应品类总费用
        Map<String, BigDecimal> categoryTotalFeeMap = new HashMap<>(8);
        // 存放对应品类分摊费用
        Map<String, BigDecimal> categoryProportionMap = new HashMap<>(8);
        if (CollectionUtils.isEmpty(normalOrders)) {
            // 按条件过滤后无明细,通过主订单平台获取品牌进行分摊，步步高->家教机，小天才->电话手表
            this.putCategoryProportionMap(expressFee, trade, platformBrandMap, categoryProportionMap);
        } else if (sum.isPresent() && sum.get().compareTo(BigDecimal.ZERO) == 0
                && (TradeTypeEnum.NORMAL.name().equals(trade.getType())
                || TradeTypeEnum.ADJUST_ORDER.name().equals(trade.getType()))) {
            // 按条件过滤后无明细,通过主订单平台获取品牌进行分摊，步步高->家教机，小天才->电话手表
            this.putCategoryProportionMap(expressFee, trade, platformBrandMap, categoryProportionMap);
        } else if (sum.isPresent() && expressFee.compareTo(BigDecimal.ZERO) > 0) {
            // 计算分类总费用
            normalOrders.forEach(p -> {
                String category = erpCodeMap.get(p.getErpCode());
                ProductCategoryEnum categoryEnum = ProductCategoryEnum.getByCategoryCn(category);
                if (categoryEnum != null) {
                    if (categoryTotalFeeMap.containsKey(categoryEnum.getCategoryEn())) {
                        categoryTotalFeeMap.put(categoryEnum.getCategoryEn(),
                                categoryTotalFeeMap.get(categoryEnum.getCategoryEn()).add(p.getPriceTotal()));
                    } else {
                        categoryTotalFeeMap.put(categoryEnum.getCategoryEn(), p.getPriceTotal());
                    }
                }
            });
            // 计算品类分摊费用
            categoryTotalFeeMap.forEach((key, value) -> categoryProportionMap.put(key, value.divide(sum.get(), 2, RoundingMode.HALF_UP)
                    .multiply(expressFee).setScale(2, RoundingMode.HALF_UP)));
        }
        return categoryProportionMap;
    }

    private void putCategoryProportionMap(BigDecimal expressFee, TradeDO trade, Map<Integer, String> platformBrandMap, Map<String, BigDecimal> categoryProportionMap) {
        String brand = platformBrandMap.get(trade.getPlatformId());
        if (GlobalConstant.LOWERCASE_BRAND_BBK.equals(brand)) {
            categoryProportionMap.put(ProductCategoryEnum.TUTOR_MACHINE.getCategoryEn(), expressFee);
        } else if (GlobalConstant.LOWERCASE_BRAND_XTC.equals(brand)) {
            categoryProportionMap.put(ProductCategoryEnum.CALL_WATCH.getCategoryEn(), expressFee);
        }
    }

    @Override
    @Async("asyncExecutor")
    public void asyncCustomer(String month) {
        // 获取锁，并发下只执行一次
        if (customerSyncTaskLock.tryLock()) {
            try {
                // 获取该月的第一天
                String firstDayOfMonth = month + "-01";
                // 获取该月的最后一天
                String lastDayOfMonth = DateUtil.lastDayOfMonth(firstDayOfMonth);
                // 查询并删除之前同步的当月数据
                int size = 5000;
                IPage<CustomerDO> pageResult = searchAndSaveCustomers(1, size, firstDayOfMonth, lastDayOfMonth);
                int totalPage = new BigDecimal(pageResult.getTotal()).divide(new BigDecimal(pageResult.getSize()), RoundingMode.UP).intValue();
                for (int i = 2; i <= totalPage; i++) {
                    searchAndSaveCustomers(i, size, firstDayOfMonth, lastDayOfMonth);
                }
            } catch (Exception e) {
                log.error("同步客户数据异常", e);
            } finally {
                customerSyncTaskLock.unlock();
            }
        }
    }

    /**
     * 分页查询并保存客户记录
     *
     * @param current         页码
     * @param size            分页大小
     * @param firstDayOfMonth 当月第一天
     * @param lastDayOfMonth  当月最后一天
     * @return 分页查询结果
     */
    private IPage<CustomerDO> searchAndSaveCustomers(int current, int size, String firstDayOfMonth, String lastDayOfMonth) {
        Page<CustomerDO> page = new Page<>(current, size);
        IPage<CustomerDO> pageResult = this.customerService.getCustomersForMonth(page, firstDayOfMonth + " 00:00:00",
                lastDayOfMonth + " 23:59:59");
        this.customerService.saveOrUpdateBatch(pageResult.getRecords());
        return pageResult;
    }

    @Override
    @Async("asyncExecutor")
    public void tradeExport(Wrapper<TradeDO> wrapper, Long id, UserDetail user, String fileName, boolean isContainDetail) {
        // 导出路径
        String downloadUrl;
        String title;
        // 查询处理数据
        List<TradeDO> list = this.tradeService.list(wrapper);
        String name = this.systemParamService.getSystemParamByName("app_export_decode_order_data");
        list.forEach(tradeDO -> {
            if (ObjectUtils.isNotEmpty(tradeDO.getProductDetail()) && tradeDO.getProductDetail().contains(name) && tradeDO.getPlatformName().equals(PlatformConstant.PLATFORM_APP_NAME_EXPORT)) {
                TradeDO trade = tradeService.getTradeByTradeId(tradeDO.getPlatformTradeId());
                BeanUtils.copyProperties(trade, tradeDO);
            }
        });

        // 获取当前角色
        String[] roles = adminUserMapper.getUserRolesByUserName(user.getUserName());
        // 获取原始数据
        List<TradeDetailExportVO> tradeDetailExportList = this.getTradeDetailExportList(list);
        // 隐藏权限
        List<String> roleList = Lists.newArrayList("ROLE_TREASURER", "ROLE_ADMIN", "ROLE_PRICE");
        // 查看代销平台
        List<PlatformDO> platformList = this.platformService.listByAgency();
        List<String> platformNameList = platformList.stream().map(PlatformDO::getPlatformName).collect(Collectors.toList());
        if (isContainDetail) {
            // 获取订单（含明细）导出url
            downloadUrl = getExportDetailData(fileName, roles, tradeDetailExportList, roleList, platformNameList);
            title = fileName + "订单(含明细)导出完成";
        } else {

            // 获取订单(不含明细)数据
            downloadUrl = getExportData(list, roles, fileName, roleList, platformNameList);
            // 导出Excel到minio
            title = fileName + "订单(不含明细)导出完成";
        }

        // 更新预约信息,发送消息通知
        this.updateAppointmentAndNewsNotice(id, user, downloadUrl, title);
    }

    /**
     * 获取不含明细订单列
     *
     * @param list             列表
     * @param roles            当前角色
     * @param fileName         文件名称
     * @param roleList         查看价格权限
     * @param platformNameList 代销平台
     * @return 导出路径
     */
    private String getExportData(List<TradeDO> list, String[] roles, String fileName, List<String> roleList, List<String> platformNameList) {
        List<TradeExportVO> tradeExportVOS = Lists.newArrayList();
        for (TradeDO tradeDO : list) {
            TradeExportVO tradeExportVO = new TradeExportVO();
            BeanUtils.copyProperties(tradeDO, tradeExportVO);
            tradeExportVO.setOverTime(ObjectUtils.isEmpty(tradeDO.getOverTime()) ? "" : DateUtil.localDateTimeToStr(tradeDO.getOverTime()));
            // 返回值数据封装
            this.packageData(tradeDO, tradeExportVO);
            // 处理价格
            tradePriceHide(tradeExportVO, roles, roleList, platformNameList);
            tradeExportVOS.add(tradeExportVO);
        }
        return exportExcelToMinio(tradeExportVOS, TradeExportVO.class, fileName);
    }

    /**
     * 订单价格隐藏
     *
     * @param tradeExportVO    订单
     * @param roles            角色
     * @param roleList         查看价格列表
     * @param platformNameList 代销平台列表
     */
    private void tradePriceHide(TradeExportVO tradeExportVO, String[] roles, List<String> roleList, List<String> platformNameList) {
        if (CollectionUtils.isEmpty(roleList)) {
            return;
        }
        Optional<String> isRoleOptional = Stream.of(roles).filter(roleList::contains).findAny();
        if (platformNameList.contains(tradeExportVO.getPlatformName()) && !isRoleOptional.isPresent()) {
            tradeExportVO.setPayment("***");
            tradeExportVO.setPointPlatform("***");
            tradeExportVO.setPointAlipayJf("***");
        }

        if (platformNameList.contains(tradeExportVO.getPlatformName()) && !isRoleOptional.isPresent()) {
            // 区分代销
            List<String> hideRoles = this.rolePlatformService.getHideRoles();
            Optional<String> any = Stream.of(roles).filter(hideRoles::contains).findAny();
            List<Integer> list = this.rolePlatformService.listByShowHide(roles);
            if (any.isPresent()) {
                tradeExportVO.setPayment("***");
                tradeExportVO.setPointPlatform("***");
                tradeExportVO.setPointAlipayJf("***");
            }
        }
    }

    /**
     * 获取含明细导出路径
     *
     * @param fileName              文件名称
     * @param roles                 当前角色权限
     * @param tradeDetailExportList 导出数据
     * @return 导出路径（含明细）
     */
    private String getExportDetailData(String fileName, String[] roles, List<TradeDetailExportVO> tradeDetailExportList, List<String> roleList, List<String> platformNameList) {
        // 价格隐藏
        List<TradeDetailAgencyExportVO> exportVOList = tradeDetailPriceHide(roles, tradeDetailExportList, roleList, platformNameList);
        return exportExcelToMinio(exportVOList, TradeDetailAgencyExportVO.class, fileName);
    }

    /**
     * 订单明细价格隐藏
     *
     * @param roles                 当前角色
     * @param tradeDetailExportList 数据
     * @param roleList              权限角色
     * @param platformNameList      代销平台
     * @return 文件下载路径
     */
    private List<TradeDetailAgencyExportVO> tradeDetailPriceHide(String[] roles, List<TradeDetailExportVO> tradeDetailExportList, List<String> roleList, List<String> platformNameList) {
        Optional<String> isRoleOptional = Stream.of(roles).filter(roleList::contains).findAny();

        return tradeDetailExportList.stream().map(tradeDetail -> {
            TradeDetailAgencyExportVO tradeDetailAgencyExportVO = new TradeDetailAgencyExportVO();
            BeanUtils.copyProperties(tradeDetail, tradeDetailAgencyExportVO);
            if (platformNameList.contains(tradeDetail.getPlatformName()) && !isRoleOptional.isPresent()) {
                // 区分代销
                List<String> hideRoles = this.rolePlatformService.getHideRoles();
                Optional<String> any = Stream.of(roles).filter(hideRoles::contains).findAny();
                List<Integer> list = this.rolePlatformService.listByShowHide(roles);
                if (any.isPresent()) {
                    tradeDetailAgencyExportVO.setPayment("***");
                    tradeDetailAgencyExportVO.setPriceTotal("***");
                    tradeDetailAgencyExportVO.setPointAlipayJf("***");
                    tradeDetailAgencyExportVO.setPointPlatform("***");
                } else if (list.contains(2)) {
                    tradeDetailAgencyExportVO.setPayment("***");
                    tradeDetailAgencyExportVO.setPriceTotal("***");
                    tradeDetailAgencyExportVO.setPointAlipayJf("***");
                    tradeDetailAgencyExportVO.setPointPlatform("***");
                } else {
                    tradeDetailAgencyExportVO.setPayment(ObjectUtils.isNotEmpty(tradeDetail.getPayment()) ? tradeDetail.getPayment().toString() : "");
                    tradeDetailAgencyExportVO.setPriceTotal(ObjectUtils.isNotEmpty(tradeDetail.getPriceTotal()) ? tradeDetail.getPriceTotal().toString() : "");
                    tradeDetailAgencyExportVO.setPointAlipayJf(ObjectUtils.isNotEmpty(tradeDetail.getPointAlipayJf()) ? tradeDetail.getPointAlipayJf().toString() : "");
                    tradeDetailAgencyExportVO.setPointPlatform(ObjectUtils.isNotEmpty(tradeDetail.getPointPlatform()) ? tradeDetail.getPointPlatform().toString() : "");
                }
            } else {
                tradeDetailAgencyExportVO.setPayment(ObjectUtils.isNotEmpty(tradeDetail.getPayment()) ? tradeDetail.getPayment().toString() : "");
                tradeDetailAgencyExportVO.setPriceTotal(ObjectUtils.isNotEmpty(tradeDetail.getPriceTotal()) ? tradeDetail.getPriceTotal().toString() : "");
                tradeDetailAgencyExportVO.setPointAlipayJf(ObjectUtils.isNotEmpty(tradeDetail.getPointAlipayJf()) ? tradeDetail.getPointAlipayJf().toString() : "");
                tradeDetailAgencyExportVO.setPointPlatform(ObjectUtils.isNotEmpty(tradeDetail.getPointPlatform()) ? tradeDetail.getPointPlatform().toString() : "");
            }
            return tradeDetailAgencyExportVO;
        }).collect(Collectors.toList());
    }

    /**
     * 导出excel文件到minio
     *
     * @param data     数据
     * @param clazz    导出实体
     * @param fileName 文件名
     * @param <T>      实体
     * @return 文件路径
     */
    private <T> String exportExcelToMinio(List<T> data, Class<T> clazz, String fileName) {
        // 文件名无需带上根路径 "/"
        String objectName = "download/" + DateUtil.nowDateStr("yyyyMMdd") + "/" + fileName + ".xlsx";
        ByteArrayInputStream byteArrayInputStream = ExcelUtil.writeDataToStream(data, clazz);
        try {
            this.minioManager.putObject(objectName, byteArrayInputStream);
        } catch (Exception e) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_APPOINTMENT_FILE_UPLOAD, e);
        }
        return objectName;
    }

    @Override
    public void returnInfoExcelExport(Wrapper<ReturnMasterDO> wrapper, Long id, UserDetail user, String fileName) {
        String title = fileName + "退货信息导出完成";
        // 查询处理数据(代理金额隐藏)
        String roles = adminUserMapper.getUserRolesByUserName(user.getUserName())[0];
        List<ReturnExportVO> returnExportVOS = this.getReturnExportVOS(wrapper);
        List<String> hideRoles = rolePlatformMapper.getHideRoles();
        List<ReturnExportVO> jLAgencyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(hideRoles) && hideRoles.contains(roles)) {
            jLAgencyList = returnExportVOS.stream().peek(returnExportVO -> {
                returnExportVO.setPayment(new BigDecimal("0"));
                returnExportVO.setPriceTotal(new BigDecimal("0"));
                returnExportVO.setRealAmount(new BigDecimal("0"));
            }).collect(Collectors.toList());
            String downloadUrl = exportExcelToMinio(jLAgencyList, ReturnExportVO.class, fileName);
            // 更新预约信息,发送消息通知
            this.updateAppointmentAndNewsNotice(id, user, downloadUrl, title);
            return;
        }
        // 导出Excel到minio
        String downloadUrl = exportExcelToMinio(returnExportVOS, ReturnExportVO.class, fileName);
        // 更新预约信息,发送消息通知
        this.updateAppointmentAndNewsNotice(id, user, downloadUrl, title);
    }

    @Override
    public void receiptExport(Wrapper<ReturnMasterDO> wrapper, Long appointmentDownloadId, UserDetail user, String fileName) {
        String title = fileName + "收货数据导出完成";
        // 查询处理数据(代理金额隐藏)
        List<ReceiptExportVO> returnExportVOS = this.getReceiptData(wrapper);
        // 导出Excel到minio
        String downloadUrl = exportExcelToMinio(returnExportVOS, ReceiptExportVO.class, fileName);
        // 更新预约信息,发送消息通知
        this.updateAppointmentAndNewsNotice(appointmentDownloadId, user, downloadUrl, title);
    }

    /**
     * 获取收货数据
     *
     * @param wrapper 条件
     * @return 收货数据
     */
    private List<ReceiptExportVO> getReceiptData(Wrapper<ReturnMasterDO> wrapper) {
        Map<String, ProductInfoDO> productInfoDOMap = productInfoService.list()
                .stream()
                .collect(Collectors.toMap(ProductInfoDO::getProductId, Function.identity()));
        List<ReturnMasterDO> returnMasterDOS = this.returnMasterService.list(wrapper);
        List<ReceiptExportVO> returnExportVOS = Lists.newArrayList();
        for (ReturnMasterDO returnMasterDO : returnMasterDOS) {
            List<ReturnDetailsDO> returnDetailsDOS = this.returnDetailsService.getReturnDetails(returnMasterDO.getPlatformTradeId());
            for (ReturnDetailsDO returnDetailsDO : returnDetailsDOS) {
                ReceiptExportVO returnExportVO = new ReceiptExportVO();
                returnExportVO.setIsEndProductStore(returnMasterDO.getIsEndProductStore() == 1 ? "已收货" : "无需收货");
                returnExportVO.setErpCode(returnDetailsDO.getErpCode());
                returnExportVO.setErpName(returnDetailsDO.getErpName());
                returnExportVO.setNum(returnDetailsDO.getNum());
                BeanUtils.copyProperties(returnMasterDO, returnExportVO);
                returnExportVO.setErpPostTime(DateUtil.dateToStr(returnMasterDO.getErpPostTime(), "yyyy-MM-dd HH:mm:ss"));
                returnExportVO.setReturnType(returnMasterDO.getType() != null ? ReturnTypeEnum.valueOf(returnMasterDO.getType()).getDescription() : "");
                returnExportVO.setRefundExpressNo(returnMasterDO.getExpressTrackingNo());
                if (PartReturnStatus.NOT_NEED.name().equals(returnExportVO.getPartReturnStatus())) {
                    returnExportVO.setPartReturnStatus("无需要处理");
                } else if (PartReturnStatus.STORE_SCAN.name().equals(returnExportVO.getPartReturnStatus())) {
                    returnExportVO.setPartReturnStatus("仓库扫描");
                } else if (PartReturnStatus.SERVE_CONFIRM.name().equals(returnExportVO.getPartReturnStatus())) {
                    returnExportVO.setPartReturnStatus("客服确认");
                } else if (PartReturnStatus.ADJUST.name().equals(returnExportVO.getPartReturnStatus())) {
                    returnExportVO.setPartReturnStatus("确认调单");
                } else if (PartReturnStatus.STORE_CONFIRM.name().equals(returnExportVO.getPartReturnStatus())) {
                    returnExportVO.setPartReturnStatus("仓库归位");
                }
                // 订单标签
                if (StringUtils.isNotBlank(returnExportVO.getOrderTag())) {
                    returnExportVO.setOrderTag("国补订单");
                } else {
                    returnExportVO.setOrderTag("");
                }
                // 设置原单卖家备注
                ProductInfoDO productInfoDO = productInfoDOMap.get(returnDetailsDO.getErpCode());
                returnExportVO.setSfBarcode(productInfoDO == null ? null : productInfoDO.getBarcode());
                returnExportVO.setSellerRemark(returnMasterDO.getSellerRemark());
                returnExportVO.setReturnNum(returnDetailsDO.getReturnNum());
                returnExportVO.setProductSn(returnDetailsDO.getBarcode());
                returnExportVO.setShippingDate(returnDetailsDO.getCreateTime());
                returnExportVOS.add(returnExportVO);
            }
        }
        return returnExportVOS;
    }

    /**
     * 异步任务结果检查
     *
     * @param futures 异步任务返回结果
     * @return 检查结果
     */
    private boolean asyncDoneCheck(List<Future> futures) {
        long startTime = System.currentTimeMillis();
        while (true) {
            // 30分钟后超时
            long endTime = System.currentTimeMillis();
            long expired = 30 * 60 * 1000;
            if (endTime - startTime > expired) {
                log.info("异步任务超时");
                return false;
            }

            boolean allDone = futures.stream().allMatch(Future::isDone);
            if (allDone) {
                log.info("异步任务执行完毕");
                return true;
            }

            try {
                // 休眠1秒
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("线程休眠异常", e);
            }
        }
    }

    /**
     * 查询退货数据并处理退货数据
     *
     * @param wrapper 查询条件包装类
     * @return
     */
    private List<ReturnExportVO> getReturnExportVOS(Wrapper<ReturnMasterDO> wrapper) {
        List<ReturnMasterDO> returnMasterDOS = this.returnMasterService.list(wrapper);
        List<ReturnExportVO> returnExportVOS = Lists.newArrayList();
        for (ReturnMasterDO returnMasterDO : returnMasterDOS) {
            TradeDO tradeDO = tradeDao.getByTradeId(returnMasterDO.getPlatformTradeId()).orElse(null);
            if (tradeDO != null) {
                List<OrderDO> orderDOS = this.orderService.getOrderByTid(returnMasterDO.getPlatformTradeId());
                List<ReturnDetailsDO> returnDetailsDOS = this.returnDetailsService.getReturnDetails(returnMasterDO.getPlatformTradeId());
                Map<String, ReturnDetailsDO> detailsMap = Maps.newHashMap();
                for (ReturnDetailsDO returnDetailsDO : returnDetailsDOS) {
                    ReturnDetailsDO exist = detailsMap.get(returnDetailsDO.getPlatformOrderId());
                    if (exist != null) {
                        exist.setBarcode(exist.getBarcode() + "," + returnDetailsDO.getBarcode());
                        exist.setReturnNum(exist.getReturnNum() + returnDetailsDO.getReturnNum());
                    } else {
                        detailsMap.put(returnDetailsDO.getPlatformOrderId(), returnDetailsDO);
                    }
                }
                for (int i = 0; i < orderDOS.size(); i++) {
                    OrderDO orderDO = orderDOS.get(i);
                    ReturnDetailsDO returnDetailsDO = detailsMap.get(orderDO.getPlatformOrderId());
                    ReturnExportVO returnExportVO = new ReturnExportVO();
                    returnExportVO.setIsEndProductStore(returnMasterDO.getIsEndProductStore() == 1 ? "已收货" : "无需收货");
                    if (returnDetailsDO != null) {
                        BeanUtils.copyProperties(returnDetailsDO, returnExportVO);
                        returnExportVO.setNum(orderDO.getNum());
                    } else if (ReturnTypeEnum.EXCHANGE.name().equals(returnMasterDO.getType())) {
                        if (i >= returnDetailsDOS.size()) {
                            continue;
                        }
                        ReturnDetailsDO detailsDO = returnDetailsDOS.get(i);
                        returnExportVO.setBarcode(detailsDO.getBarcode());
                        returnExportVO.setErpCode(detailsDO.getErpCode());
                        returnExportVO.setErpName(detailsDO.getErpName());
                        returnExportVO.setNum(detailsDO.getNum());
                        returnExportVO.setReturnNum(detailsDO.getNum());
                        returnExportVO.setPriceTotal(detailsDO.getPriceTotal());
                    } else {
                        returnExportVO.setBarcode("虚拟商品,无发货条码");
                        returnExportVO.setErpCode(orderDO.getErpCode());
                        returnExportVO.setErpName(orderDO.getErpName());
                        returnExportVO.setNum(orderDO.getNum());
                        returnExportVO.setReturnNum(orderDO.getNum());
                        returnExportVO.setPriceTotal(orderDO.getPriceTotal());
                    }
                    if (orderDO.getOrderType() == 1) {
                        returnExportVO.setOrderType("正常明细");
                    } else if (orderDO.getOrderType() == 2) {
                        returnExportVO.setOrderType("赠品明细");
                    } else if (orderDO.getOrderType() == 3) {
                        returnExportVO.setOrderType("折扣明细");
                    } else if (orderDO.getOrderType() == 4) {
                        returnExportVO.setOrderType("运费明细");
                    }
                    returnExportVO.setSfBarcode(orderDO.getBarcode());
                    BeanUtils.copyProperties(tradeDO, returnExportVO);
                    returnExportVO.setShipExpressNo(tradeDO.getExpressTrackingNo());
                    returnExportVO.setStatusOrder(StringUtils.isNotBlank(returnExportVO.getStatusOrder()) ? OrderStatus.valueOf(returnExportVO.getStatusOrder()).getDescription() : "");
                    BeanUtils.copyProperties(returnMasterDO, returnExportVO);
                    returnExportVO.setErpPostTime(DateUtil.dateToStr(returnMasterDO.getErpPostTime(), "yyyy-MM-dd HH:mm:ss"));
                    returnExportVO.setReturnType(returnMasterDO.getType() != null ? ReturnTypeEnum.valueOf(returnMasterDO.getType()).getDescription() : "");
                    returnExportVO.setRefundExpressNo(returnMasterDO.getExpressTrackingNo());
                    returnExportVO.setPointAlipayJf(orderDO.getPointAlipayJf() != null ? orderDO.getPointAlipayJf() : BigDecimal.ZERO);
                    returnExportVO.setPointPlatform(orderDO.getPointPlatform() != null ? orderDO.getPointPlatform() : BigDecimal.ZERO);
                    returnExportVO.setPayment(orderDO.getPayment());
                    returnExportVO.setRealAmount(ObjectUtils.isNotEmpty(returnDetailsDO) && returnDetailsDO.getPayment() != null ? returnDetailsDO.getPayment() : BigDecimal.ZERO);
                    if ("2766".equals(returnMasterDO.getLocationId())) {
                        returnMasterDO.setLocationId("顺丰北京仓");
                    } else if ("2767".equals(returnMasterDO.getLocationId())) {
                        returnMasterDO.setLocationId("顺丰上海仓");
                    } else if ("2768".equals(returnMasterDO.getLocationId())) {
                        returnMasterDO.setLocationId("顺丰成都仓");
                    } else if ("3654".equals(returnMasterDO.getLocationId())) {
                        returnMasterDO.setLocationId("顺丰成都仓");
                    } else if ("009".equals(returnMasterDO.getLocationId())) {
                        returnMasterDO.setLocationId("009仓库");
                    } else {
                        returnExportVO.setLocationId("202本地仓");
                    }
                    if (ReturnTypeEnum.EXCHANGE.name().equals(returnMasterDO.getType())) {
                        returnExportVO.setPriceTotal(BigDecimal.ZERO);
                        returnExportVO.setPayment(BigDecimal.ZERO);
                        returnExportVO.setPointAlipayJf(BigDecimal.ZERO);
                        returnExportVO.setPointPlatform(BigDecimal.ZERO);
                    }
                    if (PartReturnStatus.NOT_NEED.name().equals(returnExportVO.getPartReturnStatus())) {
                        returnExportVO.setPartReturnStatus("无需要处理");
                    } else if (PartReturnStatus.STORE_SCAN.name().equals(returnExportVO.getPartReturnStatus())) {
                        returnExportVO.setPartReturnStatus("仓库扫描");
                    } else if (PartReturnStatus.SERVE_CONFIRM.name().equals(returnExportVO.getPartReturnStatus())) {
                        returnExportVO.setPartReturnStatus("客服确认");
                    } else if (PartReturnStatus.ADJUST.name().equals(returnExportVO.getPartReturnStatus())) {
                        returnExportVO.setPartReturnStatus("确认调单");
                    } else if (PartReturnStatus.STORE_CONFIRM.name().equals(returnExportVO.getPartReturnStatus())) {
                        returnExportVO.setPartReturnStatus("仓库归位");
                    }
                    // 订单标签
                    if (StringUtils.isNotBlank(returnExportVO.getOrderTag())) {
                        returnExportVO.setOrderTag("国补订单");
                    } else {
                        returnExportVO.setOrderTag("");
                    }
                    // 设置原单卖家备注
                    returnExportVO.setSellerRemark(tradeDO.getSellerRemark());
                    returnExportVOS.add(returnExportVO);
                }
            }
        }
        return returnExportVOS;
    }

    /**
     * 创建发送条码信息
     *
     * @param orderDO
     * @param orderShipItemInfoDO
     * @return
     */
    private OrderBarcodeDO createOrderBarcode(OrderDO orderDO, OrderShipItemInfoDO orderShipItemInfoDO) {
        OrderBarcodeDO orderBarcodeDO = new OrderBarcodeDO();
        orderBarcodeDO.setPlatformOrderId(orderDO.getPlatformOrderId());
        orderBarcodeDO.setBarcode(orderShipItemInfoDO.getProductSn());
        orderBarcodeDO.setPlatformTradeId(orderDO.getPlatformTradeId());
        orderBarcodeDO.setErpCode(orderDO.getErpCode());
        orderBarcodeDO.setErpName(orderDO.getErpName());
        orderBarcodeDO.setNum(1);
        orderBarcodeDO.setUnit(orderDO.getUnit());
        orderBarcodeDO.setPrice(orderDO.getPrice());
        return orderBarcodeDO;
    }

    /**
     * 创建客户
     *
     * @param tradeDO
     * @param brand
     * @param agentInfo
     * @return
     */
    private CustomerDO createCustomer(TradeDO tradeDO, String brand, AgentInfoDO agentInfo) {
        CustomerDO customerDO = new CustomerDO();
        customerDO.setBrand("BBK".equals(brand) ? "步步高" : "小天才");
        customerDO.setShipStatus("已发货");
        BeanUtils.copyProperties(tradeDO, customerDO);
        customerDO.setId(null);
        customerDO.setAgentCode(agentInfo.getAgentCode());
        customerDO.setAgentName(agentInfo.getAgentName());
        return customerDO;
    }

    /**
     * 根据订单的省市区获取代理信息
     *
     * @param tradeDO
     * @param agentInfoDOS
     * @return
     */
    private AgentInfoDO getAgentInfoByTrade(TradeDO tradeDO, List<AgentInfoDO> agentInfoDOS) {
        // 1.代理省份包含订单的省,代理的管辖城市和剔除的城市都为空
        AgentInfoDO agentInfoDO = agentInfoDOS.stream()
                .filter(agentInfo -> agentInfo.getContainProvince().contains(tradeDO.getReceiverProvince())
                        && StringUtils.isBlank(agentInfo.getContainCity())
                        && StringUtils.isBlank(agentInfo.getExcludeCity()))
                .findAny().orElse(null);
        if (agentInfoDO != null) {
            return agentInfoDO;
        }
        // 2.代理省份不包含订单的省,代理的省外管辖城市不为空并且包含订单的市
        agentInfoDO = agentInfoDOS.stream()
                .filter(agentInfo -> !agentInfo.getContainProvince().contains(tradeDO.getReceiverProvince())
                        && StringUtils.isNotBlank(agentInfo.getOtherContainCity())
                        && StringUtils.isNotBlank(tradeDO.getReceiverCity())
                        && agentInfo.getOtherContainCity().contains(tradeDO.getReceiverCity()))
                .findAny().orElse(null);
        if (agentInfoDO != null) {
            return agentInfoDO;
        }
        // 3.代理省份包含订单的省,代理的管辖城市不为空并且包含订单的市并且代理的省外管辖城市为空
        agentInfoDO = agentInfoDOS.stream()
                .filter(agentInfo -> agentInfo.getContainProvince().contains(tradeDO.getReceiverProvince())
                        && StringUtils.isNotBlank(agentInfo.getContainCity())
                        && StringUtils.isNotBlank(tradeDO.getReceiverCity())
                        && agentInfo.getContainCity().contains(tradeDO.getReceiverCity())
                        && StringUtils.isBlank(agentInfo.getExcludeCity()))
                .findAny().orElse(null);
        if (agentInfoDO != null) {
            return agentInfoDO;
        }
        // 4.代理省份包含订单的省,代理的管辖城市不为空并且包含订单的市并且代理的剔除城市不为空且不包含订单的区
        agentInfoDO = agentInfoDOS.stream()
                .filter(agentInfo -> agentInfo.getContainProvince().contains(tradeDO.getReceiverProvince())
                        && StringUtils.isNotBlank(agentInfo.getContainCity())
                        && StringUtils.isNotBlank(tradeDO.getReceiverCity())
                        && agentInfo.getContainCity().contains(tradeDO.getReceiverCity())
                        && StringUtils.isNotBlank(agentInfo.getExcludeCity())
                        && StringUtils.isNotBlank(tradeDO.getReceiverDistrict())
                        && !agentInfo.getExcludeCity().contains(tradeDO.getReceiverDistrict()))
                .findAny().orElse(null);
        if (agentInfoDO != null) {
            return agentInfoDO;
        }
        // 5.代理省份包含订单的省,剔除的城市不为空且不包含订单的市
        agentInfoDO = agentInfoDOS.stream()
                .filter(agentInfo -> agentInfo.getContainProvince().contains(tradeDO.getReceiverProvince())
                        && StringUtils.isNotBlank(agentInfo.getExcludeCity())
                        && StringUtils.isNotBlank(tradeDO.getReceiverDistrict())
                        && !agentInfo.getExcludeCity().contains(tradeDO.getReceiverDistrict()))
                .findAny().orElse(null);
        if (agentInfoDO != null) {
            return agentInfoDO;
        }
        // 6.代理省份包含订单的省,剔除的城市不为空且包含订单的市且代理的管辖城市不为空且包含订单的区
        agentInfoDO = agentInfoDOS.stream()
                .filter(agentInfo -> agentInfo.getContainProvince().contains(tradeDO.getReceiverProvince())
                        && StringUtils.isNotBlank(agentInfo.getExcludeCity())
                        && StringUtils.isNotBlank(tradeDO.getReceiverCity())
                        && agentInfo.getExcludeCity().contains(tradeDO.getReceiverCity())
                        && StringUtils.isNotBlank(agentInfo.getContainCity())
                        && StringUtils.isNotBlank(tradeDO.getReceiverDistrict())
                        && agentInfo.getContainCity().contains(tradeDO.getReceiverDistrict()))
                .findAny().orElse(null);
        if (agentInfoDO != null) {
            return agentInfoDO;
        }
        return null;
    }

    private void packageData(TradeDO tradeDO, TradeExportVO tradeExportVO) {
        /**
         * 返回前需要处理
         * statusOrderName 订单状态汉字显示;
         * <p>
         * statusLockName 锁定状态，是否锁定订单   -1:未锁定  1:已锁定
         * <p>
         * statusReserveName 预售状态，是否预售订单
         * <p>
         * isProductName 是否机器
         * <p>
         * statusSfName 顺丰操作状态
         * <p>
         * orderConfirmStatusName 订单确认
         * <p>
         * erpPostStatusName  erp过账
         * <p>
         * invoiceStatusName 发票状态
         * <p>
         * detailedAddress 地址拼接省市区加详细地址
         */

        // 订单状态汉字显示
        if (StringUtils.isNotBlank(tradeDO.getStatusOrder())) {
            tradeExportVO.setStatusOrderName(OrderStatus.valueOf(tradeDO.getStatusOrder()).getDescription());
        }
        // 是否机器 : 物料类型 1：机器   2：配件  3：虚拟物料
        if (tradeDO.getIsProduct() != null) {
            if (tradeDO.getIsProduct() == 1) {
                tradeExportVO.setIsProductName("机器");
            } else if (tradeDO.getIsProduct() == 2) {
                tradeExportVO.setIsProductName("配件");
            } else if (tradeDO.getIsProduct() == 3) {
                tradeExportVO.setIsProductName("虚拟物料");
            }
        }
        // 顺丰操作状态
        if (StringUtils.isNotBlank(tradeDO.getStatusSf()) && !"0".equals(tradeDO.getStatusSf())) {
            tradeExportVO.setStatusSfName(SFStatus.valueOf(tradeDO.getStatusSf()).getDescription());
        }
        if (StringUtils.isNotBlank(tradeDO.getWarehouseStatus())) {
            tradeExportVO.setWarehouseStatusName(WarehouseStatus.valueOf(tradeDO.getWarehouseStatus()).getDescription());
        }
        // erp过账
        if (tradeDO.getErpPostStatus() == 1) {
            tradeExportVO.setErpPostStatusName("是");
        } else if (tradeDO.getErpPostStatus() == 0) {
            tradeExportVO.setErpPostStatusName("否");
        } else if (tradeDO.getErpPostStatus() == 2) {
            tradeExportVO.setErpPostStatusName("不过账");
        }
        // invoiceStatusName 发票状态
        if (StringUtils.isNotBlank(tradeDO.getInvoiceStatus()) && !"0".equals(tradeDO.getInvoiceStatus())) {
            tradeExportVO.setInvoiceStatusName(InvoiceStatus.valueOf(tradeDO.getInvoiceStatus()).getDescription());
        }
        // 订单标签
        if (StringUtils.isNotBlank(tradeDO.getOrderTag())) {
            tradeExportVO.setOrderTag("国补订单");
        } else {
            tradeExportVO.setOrderTag("");
        }
        // 订单类型转文字
        tradeExportVO.setType(StringUtils.isNotBlank(tradeDO.getType()) ? TradeTypeEnum.valueOf(tradeDO.getType()).getDescription() : null);
        // 地址拼接省市区加详细地址
        tradeExportVO.setDetailedAddress(tradeDO.getReceiverProvince() + " " + tradeDO.getReceiverCity() + " " + tradeDO.getReceiverDistrict() + " " + tradeDO.getReceiverAddress());
        // 价格处理
        tradeExportVO.setPayment(ObjectUtils.isNotEmpty(tradeDO.getPayment()) ? tradeDO.getPayment().toString() : null);
        tradeExportVO.setPointPlatform(ObjectUtils.isNotEmpty(tradeDO.getPointPlatform()) ? tradeDO.getPointPlatform().toString() : null);
        tradeExportVO.setPointAlipayJf(ObjectUtils.isNotEmpty(tradeDO.getPointAlipayJf()) ? tradeDO.getPointAlipayJf().toString() : null);
    }

    private void packageData(TradeDO tradeDO, TradeAgencyExportVO tradeAgencyExportVO) {
        /**
         * 返回前需要处理
         * statusOrderName 订单状态汉字显示;
         * <p>
         * statusLockName 锁定状态，是否锁定订单   -1:未锁定  1:已锁定
         * <p>
         * statusReserveName 预售状态，是否预售订单
         * <p>
         * isProductName 是否机器
         * <p>
         * statusSfName 顺丰操作状态
         * <p>
         * orderConfirmStatusName 订单确认
         * <p>
         * erpPostStatusName  erp过账
         * <p>
         * invoiceStatusName 发票状态
         * <p>
         * detailedAddress 地址拼接省市区加详细地址
         */

        // 订单状态汉字显示
        if (StringUtils.isNotBlank(tradeDO.getStatusOrder())) {
            tradeAgencyExportVO.setStatusOrderName(OrderStatus.valueOf(tradeDO.getStatusOrder()).getDescription());
        }
        // 是否机器 : 物料类型 1：机器   2：配件  3：虚拟物料
        if (tradeDO.getIsProduct() != null) {
            if (tradeDO.getIsProduct() == 1) {
                tradeAgencyExportVO.setIsProductName("机器");
            } else if (tradeDO.getIsProduct() == 2) {
                tradeAgencyExportVO.setIsProductName("配件");
            } else if (tradeDO.getIsProduct() == 3) {
                tradeAgencyExportVO.setIsProductName("虚拟物料");
            }
        }
        // 顺丰操作状态
        if (StringUtils.isNotBlank(tradeDO.getStatusSf()) && !"0".equals(tradeDO.getStatusSf())) {
            tradeAgencyExportVO.setStatusSfName(SFStatus.valueOf(tradeDO.getStatusSf()).getDescription());
        }
        if (StringUtils.isNotBlank(tradeDO.getWarehouseStatus())) {
            tradeAgencyExportVO.setWarehouseStatusName(WarehouseStatus.valueOf(tradeDO.getWarehouseStatus()).getDescription());
        }
        // erp过账
        if (tradeDO.getErpPostStatus() == 1) {
            tradeAgencyExportVO.setErpPostStatusName("是");
        } else if (tradeDO.getErpPostStatus() == 0) {
            tradeAgencyExportVO.setErpPostStatusName("否");
        } else if (tradeDO.getErpPostStatus() == 2) {
            tradeAgencyExportVO.setErpPostStatusName("不过账");
        }
        // invoiceStatusName 发票状态
        if (StringUtils.isNotBlank(tradeDO.getInvoiceStatus()) && !"0".equals(tradeDO.getInvoiceStatus())) {
            tradeAgencyExportVO.setInvoiceStatusName(InvoiceStatus.valueOf(tradeDO.getInvoiceStatus()).getDescription());
        }
        // 订单类型转文字
        tradeAgencyExportVO.setType(StringUtils.isNotBlank(tradeDO.getType()) ? TradeTypeEnum.valueOf(tradeDO.getType()).getDescription() : null);
        // 地址拼接省市区加详细地址
        tradeAgencyExportVO.setDetailedAddress(tradeDO.getReceiverProvince() + " " + tradeDO.getReceiverCity() + " " + tradeDO.getReceiverDistrict() + " " + tradeDO.getReceiverAddress());
    }

    /**
     * 更新预约信息,发送消息通知
     *
     * @param id          预约主键id
     * @param user        当前用户信息
     * @param downloadUrl Excel下载地址
     * @param title       消息通知主题
     */
    private void updateAppointmentAndNewsNotice(Long id, UserDetail user, String downloadUrl, String title) {
        boolean notBlank = StringUtils.isNotBlank(downloadUrl);
        // 保存Excel下载地址到数据库
        AppointmentDownloadDO update = new AppointmentDownloadDO();
        update.setId(id);
        update.setDownloadUrl(notBlank ? downloadUrl : null);
        update.setStatus(notBlank ? AppointmentDownloadStatusEnum.COMPLETED.name() : AppointmentDownloadStatusEnum.FAIL.name());
        this.appointmentDownloadService.updateById(update);

        if (notBlank) {
            // 发送消息通知
            this.newsNoticeService.saveNewsNotice(NewsNoticeTypeEnum.BUSINESS_COMPLETED,
                    NewsNoticeStatusEnum.NO_NEED, title, downloadUrl, user.getName());
        }
    }

    /**
     * 根据订单集合获取包含订单明细的集合
     *
     * @param list 订单集合
     * @return
     */
    private List<TradeDetailExportVO> getTradeDetailExportList(List<TradeDO> list) {
        // 获取仓库
        List<WarehouseDO> warehouseList = this.warehouseService.getWarehouseListWithoutLocal();
        List<TradeDetailExportVO> tradeDetailExportVOS = Lists.newArrayList();
        // key:物料代码 value:品类
        Map<String, String> erpCodeMap = this.productInfoService.list().stream()
                .filter(p -> StringUtils.isNotBlank(p.getProductId()) && StringUtils.isNotBlank(p.getModelSort()))
                .collect(Collectors.toMap(ProductInfoDO::getProductId, ProductInfoDO::getModelSort, (v1, v2) -> v2));
        for (TradeDO tradeDO : list) {
            List<OrderDO> orderDOS = this.orderService.getOrderByTid(tradeDO.getPlatformTradeId());
            for (OrderDO orderDO : orderDOS) {
                TradeDetailExportVO tradeDetailExportVO = new TradeDetailExportVO();
                BeanUtils.copyProperties(orderDO, tradeDetailExportVO);
                BeanUtils.copyProperties(tradeDO, tradeDetailExportVO);
                tradeDetailExportVO.setPointAlipayJf(orderDO.getPointAlipayJf() != null ? orderDO.getPointAlipayJf() : BigDecimal.ZERO);
                tradeDetailExportVO.setPointPlatform(orderDO.getPointPlatform() != null ? orderDO.getPointPlatform() : BigDecimal.ZERO);
                tradeDetailExportVO.setPayment(orderDO.getPayment());
                tradeDetailExportVO.setAuthorName(orderDO.getAuthorName());
                tradeDetailExportVOS.add(tradeDetailExportVO);
                // 转明细类型
                if (orderDO.getOrderType() != null) {
                    if (orderDO.getOrderType() == 1) tradeDetailExportVO.setOrderTypeName("正常明细");
                    else if (orderDO.getOrderType() == 2) tradeDetailExportVO.setOrderTypeName("赠品明细");
                    else if (orderDO.getOrderType() == 3) tradeDetailExportVO.setOrderTypeName("折扣明细");
                    else if (orderDO.getOrderType() == 4) tradeDetailExportVO.setOrderTypeName("运费明细");
                }
                // 顺丰状态转文字
                tradeDetailExportVO.setStatusSf(StringUtils.isNotBlank(tradeDO.getStatusSf()) ? SFStatus.valueOf(tradeDO.getStatusSf()).getDescription() : null);
                // 发票状态转文字
                tradeDetailExportVO.setInvoiceStatus(StringUtils.isNotBlank(tradeDO.getInvoiceStatus()) && !"0".equals(tradeDO.getInvoiceStatus()) ? InvoiceStatus.valueOf(tradeDO.getInvoiceStatus()).getDescription() : null);
                // 订单状态转文字
                tradeDetailExportVO.setStatusOrder(StringUtils.isNotBlank(tradeDO.getStatusOrder()) ? OrderStatus.valueOf(tradeDO.getStatusOrder()).getDescription() : null);
                // 订单类型转文字
                tradeDetailExportVO.setType(StringUtils.isNotBlank(tradeDO.getType()) ? TradeTypeEnum.valueOf(tradeDO.getType()).getDescription() : null);
                // 仓库代码转仓库名称
                WarehouseDO warehouseDO = warehouseList.stream().filter(i -> i.getWarehouseStorage().equals(tradeDO.getWarehouseStorage())).findAny().orElse(null);
                tradeDetailExportVO.setWarehouseStorage(warehouseDO != null ? warehouseDO.getWarehouseName() : null);
                // 品类
                tradeDetailExportVO.setModelSort(erpCodeMap.get(orderDO.getErpCode()));
                // 完结时间
                tradeDetailExportVO.setOverTime(ObjectUtils.isEmpty(tradeDO.getOverTime()) ? "" : DateUtil.localDateTimeToStr(tradeDO.getOverTime()));
                // 达人id
                tradeDetailExportVO.setAuthorId(orderDO.getAuthorId());
                // 达人名称
                tradeDetailExportVO.setAuthorName(orderDO.getAuthorName());
                // 国补订单
                tradeDetailExportVO.setOrderTag(StringUtils.isNotBlank(tradeDO.getOrderTag()) ? "国补订单" : "");
            }
        }
        return tradeDetailExportVOS;
    }

}
