package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.mapper.master.ExpressSetMapper;
import com.xtc.onlineretailers.pojo.entity.ExpressSetDO;
import com.xtc.onlineretailers.pojo.query.ExpressSetAddQuery;
import com.xtc.onlineretailers.pojo.query.ExpressSetQuery;
import com.xtc.onlineretailers.pojo.query.ExpressSetUpdateQuery;
import com.xtc.onlineretailers.pojo.query.PaginationQuery;
import com.xtc.onlineretailers.pojo.vo.ExpressSetExportVO;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.service.ExpressSetService;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.ExcelUtil;
import com.xtc.onlineretailers.util.MyToolUtil;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ExpressSetServiceImpl extends ServiceImpl<ExpressSetMapper, ExpressSetDO> implements ExpressSetService {

    @Override
    public List<ExpressSetDO> getExpressSetList(String productType, Date paymentTime) {
        Wrapper<ExpressSetDO> wrapper = Wrappers.<ExpressSetDO>lambdaQuery()
                .ge(ExpressSetDO::getEndTime, paymentTime)
                .le(ExpressSetDO::getStartTime, paymentTime)
                .in(ExpressSetDO::getProductType, "ALL", productType)
                .eq(ExpressSetDO::getIsUse, 1);
        return this.list(wrapper);
    }

    @Override
    public PaginationVO<ExpressSetDO> getExpressSetList(ExpressSetQuery query) {
        Page<ExpressSetDO> page = PaginationQuery.createPage(query.getIndex(), query.getSize());
        IPage<ExpressSetDO> mapIPage = this.page(page, queryToWrapper(query));
        return PaginationVO.newVO(mapIPage);
    }

    @Override
    public ExpressSetDO addExpressSet(ExpressSetAddQuery query) {
        ExpressSetDO expressSetDO = new ExpressSetDO();
        BeanUtils.copyProperties(query, expressSetDO);
        expressSetDO.setAddTime(new Date());
        this.save(expressSetDO);
        return expressSetDO;
    }

    @Override
    public ExpressSetDO updateExpressSet(ExpressSetUpdateQuery query) {
        ExpressSetDO expressSetDO = this.getById(query.getId());
        if (null == expressSetDO) {
            throw new GlobalDefaultException(ResponseEnum.UPDATE_FAIL);
        } else {
            BeanUtils.copyProperties(query, expressSetDO);
            this.updateById(expressSetDO);
            return this.getById(expressSetDO.getId());
        }
    }

    @Override
    public void exportExpressSet(ExpressSetQuery query, HttpServletResponse response) {
        List<ExpressSetDO> data = this.list(queryToWrapper(query));
        List<ExpressSetExportVO> exportList = data.parallelStream().map(p -> {
            ExpressSetExportVO exportVO = new ExpressSetExportVO();
            BeanUtils.copyProperties(p, exportVO);
            exportVO.setStartTime(DateFormatUtils.format(p.getStartTime(), "yyyy-MM-dd HH:mm:ss"));
            exportVO.setEndTime(DateFormatUtils.format(p.getEndTime(), "yyyy-MM-dd HH:mm:ss"));
            exportVO.setProductType("PARTS".equals(p.getProductType()) ? "配件" : "机器");
            exportVO.setAddTime(DateFormatUtils.format(p.getAddTime(), "yyyy-MM-dd HH:mm:ss"));
            exportVO.setIsUse("1".equals(p.getIsUse()) ? "使用" : "未使用");
            return exportVO;
        }).collect(Collectors.toList());
        // 文件名称
        String fileName = "exportExpressSet" + DateUtil.createIdByTime();
        ExcelUtil.exportExcel(fileName, exportList, ExpressSetExportVO.class, response);
    }

    private Wrapper<ExpressSetDO> queryToWrapper(ExpressSetQuery query) {
        return Wrappers.<ExpressSetDO>lambdaQuery()
                .eq(ExpressSetDO::getIsUse, "1")
                .like(MyToolUtil.notEmpty(query.getTitle()), ExpressSetDO::getTitle, query.getTitle())
                .like(MyToolUtil.notEmpty(query.getExpressName()), ExpressSetDO::getExpressName, query.getExpressName())
                .eq(MyToolUtil.notEmpty(query.getProvince()), ExpressSetDO::getProvince, query.getProvince())
                .ge(MyToolUtil.notEmpty(query.getStartDate()), ExpressSetDO::getAddTime, query.getStartDate() + " 00:00:00")
                .le(MyToolUtil.notEmpty(query.getEndDate()), ExpressSetDO::getAddTime, query.getEndDate() + " 23:59:59")
                .orderByDesc(ExpressSetDO::getId);
    }

}
