package com.xtc.onlineretailers.service.impl;

import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.onlineretailers.executor.command.ScanLogPageQryExe;
import com.xtc.onlineretailers.pojo.dto.ScanLogDTO;
import com.xtc.onlineretailers.pojo.query.ScanLogPageQry;
import com.xtc.onlineretailers.service.ScanLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ScanLogServiceImpl implements ScanLogService {

    private final ScanLogPageQryExe scanLogPageQryExe;

    @Override
    public PageResponse<ScanLogDTO> pageScanLog(ScanLogPageQry qry) {
        return scanLogPageQryExe.execute(qry);
    }

}
