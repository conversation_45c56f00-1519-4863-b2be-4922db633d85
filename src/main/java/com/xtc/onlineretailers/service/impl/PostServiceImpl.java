package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.mapper.master.PostMapper;
import com.xtc.onlineretailers.pojo.entity.ExpressLogDO;
import com.xtc.onlineretailers.pojo.entity.PostDO;
import com.xtc.onlineretailers.pojo.query.PostAddQuery;
import com.xtc.onlineretailers.pojo.query.PostGetQuery;
import com.xtc.onlineretailers.pojo.query.PostUpdateQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.pojo.vo.PostExportVO;
import com.xtc.onlineretailers.pojo.vo.PostImportVO;
import com.xtc.onlineretailers.service.ExpressLogService;
import com.xtc.onlineretailers.service.PostService;
import com.xtc.onlineretailers.service.TradeService;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.ExcelUtil;
import com.xtc.onlineretailers.util.GlobalUtil;
import com.xtc.onlineretailers.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class PostServiceImpl extends ServiceImpl<PostMapper, PostDO> implements PostService {

    @Resource
    private PostMapper postMapper;

    @Resource
    private TradeService tradeService;

    @Resource
    private ExpressLogService expressLogService;

    @Override
    public PaginationVO<PostDO> list(PostGetQuery query) {

        Wrapper<PostDO> wrapper = getPostDOWrapper(query);
        IPage<PostDO> page = this.page(query.createPage(), wrapper);
        return PaginationVO.newVO(page);
    }

    private void handlerEndTime(PostGetQuery query) {
        // 获取上个月最后一天
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 0);
        //处理日期
        if (query.getEndDate() == null) {
            String endTime = DateUtil.dateToStr(calendar.getTime(), "yyyy-MM-dd");
            // 设置日期为上个月最后一天
            query.setEndDate(endTime);
        } else {
            Date lastDayOfMonth = calendar.getTime();
            Date endTime = DateUtil.strToDate(query.getEndDate(), "yyyy-MM-dd");
            //超过上个月最后一天
            if (lastDayOfMonth.before(endTime)) {
                query.setEndDate(DateUtil.dateToStr(lastDayOfMonth, "yyyy-MM-dd"));
            }
        }
    }

    @Override
    public PostDO add(PostAddQuery query) {
        PostDO oldPost = this.getOneByExpressTrackingNo(query.getExpressTrackingNo());
        if (oldPost != null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_EXPRESSTRACKINGNO_EXIST);
        }
        PostDO post = new PostDO();
        BeanUtils.copyProperties(query, post);
        post.setCreateTime(new Date());
        // 根据快递单号获取TradeDO
        setPostContrastState(post, query.getExpressTrackingNo());
        // 处理expressId
        post.setExpressId(GlobalUtil.getUUID());
        this.postMapper.insert(post);

        return post;
    }

    @Override
    public PostDO update(PostUpdateQuery query) {
        PostDO update = this.getById(query.getId());
        if (update == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_POST_NULL);
        }
        // 编辑时修改了快递单号
        if (!query.getExpressTrackingNo().equals(update.getExpressTrackingNo())) {
            PostDO oldPost = this.getOneByExpressTrackingNo(query.getExpressTrackingNo());
            if (oldPost != null) {
                throw new GlobalDefaultException(ResponseEnum.ERROR_EXPRESSTRACKINGNO_EXIST);
            }
        }
        PostDO post = new PostDO();
        BeanUtils.copyProperties(query, post);
        post.setUpdateTime(new Date());
        // 根据快递单号获取TradeDO
        setPostContrastState(post, query.getExpressTrackingNo());
        this.postMapper.updateById(post);

        return post;
    }

    private void setPostContrastState(PostDO post, String expressTrackingNo) {
        // 修改从快递记录表中比对
//        List<TradeDO> tradeDOS = this.tradeService.getByexpressTrackingNo(expressTrackingNo);
//        if (tradeDOS.size() == 0) {
//            post.setContrastState("快递有电商没");
//        } else if (tradeDOS.size() == 1) {
//            post.setContrastState("快递电商1对1");
//        } else if (tradeDOS.size() > 1) {
//            post.setContrastState("快递电商1对多");
//        }
        Wrapper<ExpressLogDO> wrapper = Wrappers.<ExpressLogDO>lambdaQuery().eq(ExpressLogDO::getExpressTrackingNo, expressTrackingNo);
        List<ExpressLogDO> expressLogDOS = this.expressLogService.list(wrapper);
        if (expressLogDOS.size() == 0) {
            post.setContrastState("快递有电商没");
        } else if (expressLogDOS.size() == 1) {
            post.setContrastState("快递电商1对1");
        } else if (expressLogDOS.size() > 1) {
            post.setContrastState("快递电商1对多");
        }
    }

    @Override
    public void exportPosts(PostGetQuery query, HttpServletResponse response) {

        Wrapper<PostDO> wrapper = getPostDOWrapper(query);
        List<PostDO> postDOS = this.postMapper.selectList(wrapper);

        List<PostExportVO> list = GlobalUtil.copyCollection(postDOS, PostExportVO::new);
        // 获取当前时间
        String newStr = DateUtil.dateToStr(new Date(), "yyyy-MM-dd");
        // 文件名称
        String fileName = newStr + "月度导入报表导出";

        ExcelUtil.exportExcel(fileName, list, PostExportVO.class, response);
    }

    private Wrapper<PostDO> getPostDOWrapper(PostGetQuery query) {
//        handlerEndTime(query);
        return Wrappers.<PostDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getExpressCompany()), PostDO::getExpressCompany, query.getExpressCompany())
                .eq(StringUtils.isNotBlank(query.getContrastState()), PostDO::getContrastState, query.getContrastState())
                .like(StringUtils.isNotBlank(query.getExpressTrackingNo()), PostDO::getExpressTrackingNo, query.getExpressTrackingNo())
                .ge(StringUtils.isNotBlank(query.getStartDate()), PostDO::getSendTime, query.getStartDate())
                .le(StringUtils.isNotBlank(query.getEndDate()), PostDO::getSendTime, query.getEndDate())
                .orderByDesc(PostDO::getCreateTime);
    }

    @Override
    public void importPosts(MultipartFile excel, String expressCompany) {
        if (excel == null) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_FILE_NULL);
        }

        if (StringUtils.isBlank(expressCompany)) {
            throw new GlobalDefaultException(ResponseEnum.EXPRESS_COMPANY_IS_EMPTY, "请填写快递公司名称");
        }

        ExcelUtil.readExcel(excel, PostImportVO.class, list -> this.batchSave(list, expressCompany));

    }

    @Override
    public void batchSave(List<PostImportVO> list, String expressCompany) {
        log.info("月度快递信息Excel表导入解析数据集: {}", GsonUtil.objectToJson(list));
        ArrayList<PostDO> postDOS = new ArrayList<>();
        for (PostImportVO postImportVO : list) {
            // 判断快递是否存在
            PostDO oldPost = this.getOneByExpressTrackingNo(postImportVO.getExpressTrackingNo());
            PostDO post = new PostDO();
            BeanUtils.copyProperties(postImportVO, post);
            if (oldPost != null) {
                post.setId(oldPost.getId());
                post.setExpressId(oldPost.getExpressId());
            } else {
                post.setExpressId(GlobalUtil.getUUID());
            }
            post.setExpressCompany(expressCompany);
            // 根据快递单号获取TradeDO
            setPostContrastState(post, postImportVO.getExpressTrackingNo());
            String newSendTime = post.getSendTime();
            if (post.getSendTime() != null && post.getSendTime().contains(":")) {
                newSendTime = StringUtils.replace(StringUtils.substring(post.getSendTime(), 0, 10), "-", "");
            }
            if (post.getSendTime() != null && post.getSendTime().contains("-")) {
                newSendTime = StringUtils.replace(newSendTime, "-", "");
            }
            post.setSendTime(newSendTime);
            postDOS.add(post);
        }

        log.info("postDOS: {}", GsonUtil.objectToJson(postDOS));
        //批量保存
        this.saveOrUpdateBatch(postDOS);
    }

    @Override
    public PostDO getOneByExpressTrackingNo(String expressTrackingNo) {
        Wrapper<PostDO> wrapper = Wrappers.<PostDO>lambdaQuery()
                .eq(PostDO::getExpressTrackingNo, expressTrackingNo)
                .last("limit 1");
        return this.getOne(wrapper);
    }

    @Override
    public List<PostDO> getByExpressTrackingNo(String expressTrackingNo) {
        return this.list(Wrappers.<PostDO>lambdaQuery()
                .eq(PostDO::getExpressTrackingNo, expressTrackingNo));
    }

}
