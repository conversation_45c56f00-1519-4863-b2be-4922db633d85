package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.mapper.master.ProductColorMapper;
import com.xtc.onlineretailers.pojo.entity.ProductColorDO;
import com.xtc.onlineretailers.service.ProductColorService;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ProductColorServiceImpl extends ServiceImpl<ProductColorMapper, ProductColorDO> implements ProductColorService {

    @Override
    public ProductColorDO getOneByErpCode(String erpCode) {
        return this.getOne(Wrappers.<ProductColorDO>lambdaQuery()
                .eq(ProductColorDO::getErpCode, erpCode)
                .last("limit 1")); // 物料代码
    }

    @Override
    public List<ProductColorDO> getManyByErpCode(String erpCode) {
        return this.list(Wrappers.<ProductColorDO>lambdaQuery()
                .eq(ProductColorDO::getErpCode, erpCode)); // 物料代码;
    }

    @Override
    public ProductColorDO getOneByBarcode(String barcode) {
        return this.getOne(Wrappers.<ProductColorDO>lambdaQuery()
                .eq(ProductColorDO::getBarcode, barcode)
                .last("limit 1")); // 条码;
    }

    @Override
    public List<ProductColorDO> getByErpCodeExcludeBarcode(String erpCode, String barcode) {
        return this.list(Wrappers.<ProductColorDO>lambdaQuery()
                .eq(ProductColorDO::getErpCode, erpCode)
                .ne(ProductColorDO::getBarcode, barcode)
                .orderByAsc(ProductColorDO::getBarcode)); // 物料代码;
    }

}
