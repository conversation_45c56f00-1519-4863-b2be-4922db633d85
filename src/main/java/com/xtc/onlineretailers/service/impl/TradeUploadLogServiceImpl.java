package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.onlineretailers.checker.TradeUploadCheck;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.executor.command.TradeUploadCmdExe;
import com.xtc.onlineretailers.executor.command.UploadBarcodeCmdExe;
import com.xtc.onlineretailers.pojo.dto.AgencyTradeExportDTO;
import com.xtc.onlineretailers.pojo.dto.AgencyTradeImportDTO;
import com.xtc.onlineretailers.pojo.dto.AgencyTradeUpdateDTO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.entity.TradeUploadLogDO;
import com.xtc.onlineretailers.pojo.query.AgencyTradeImportPageQry;
import com.xtc.onlineretailers.repository.TradeUploadLogRepository;
import com.xtc.onlineretailers.service.TradeService;
import com.xtc.onlineretailers.service.TradeUploadLogService;
import com.xtc.onlineretailers.util.CollectionCopier;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.ExcelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class TradeUploadLogServiceImpl implements TradeUploadLogService {

    private final TradeUploadLogRepository tradeUploadLogRepository;
    private final TradeUploadCmdExe tradeUploadCmdExe;
    private final TradeService tradeService;
    private final TradeUploadCheck tradeUploadCheck;
    private final UploadBarcodeCmdExe uploadBarcodeCmdExe;

    @Override
    public PageResponse<AgencyTradeImportDTO> pageTradeUploadLog(AgencyTradeImportPageQry qry) {
        IPage<TradeUploadLogDO> page = tradeUploadLogRepository.pageBy(qry);
        return PageResponse.of(page, AgencyTradeImportDTO::new);
    }

    @Override
    public void lockTrade(List<String> tradeIds) {
        for (String tradeId : tradeIds) {
            // 查询订单
            TradeDO tradeDO = tradeService.getByTrade(tradeId);

            // 校验状态（订单状态：待发货,顺丰状态：本地仓库,快递单号：为空）
            tradeUploadCheck.check(tradeDO);

            // 更新订单
            TradeDO updateTrade = new TradeDO();
            updateTrade.setStatusLock(1);
            updateTrade.setId(tradeDO.getId());
            tradeService.updateById(updateTrade);
        }
    }

    @Override
    public Boolean uploadExpressTrackingNo(MultipartFile excel, HttpServletResponse response) {
        return tradeUploadCmdExe.execute(excel, response);
    }

    @Override
    public Boolean uploadBarcode(MultipartFile excel, HttpServletResponse response) {
        return uploadBarcodeCmdExe.execute(excel, response);
    }

    @Override
    public void updateById(AgencyTradeUpdateDTO agencyTradeUpdateDTO) {
        TradeUploadLogDO tradeUploadLogDO = tradeUploadLogRepository.getById(agencyTradeUpdateDTO.getId());
        if (ObjectUtils.isEmpty(tradeUploadLogDO)) {
            throw GlobalDefaultException.of("条码记录不存在");
        }
        TradeUploadLogDO updateTradeUploadLog = new TradeUploadLogDO();
        updateTradeUploadLog.setId(Long.parseLong(agencyTradeUpdateDTO.getId()));
        updateTradeUploadLog.setRemark(agencyTradeUpdateDTO.getRemark());
        updateTradeUploadLog.setUpdateTime(LocalDateTime.now());
        tradeUploadLogRepository.updateById(updateTradeUploadLog);
    }

    @Override
    public void export(AgencyTradeImportPageQry qry, HttpServletResponse response) {
        IPage<TradeUploadLogDO> page = tradeUploadLogRepository.pageBy(qry);
        List<AgencyTradeExportDTO> list = CollectionCopier.copy(page.getRecords(), AgencyTradeExportDTO::new);
        String fileName = DateUtil.nowDateStr("yyyy-MM-dd") + "代发模块导出";
        ExcelUtil.exportExcel(fileName, list, AgencyTradeExportDTO.class, response);
    }

}
