package com.xtc.onlineretailers.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.mapper.erp.ERPMapper;
import com.xtc.onlineretailers.pojo.entity.*;
import com.xtc.onlineretailers.pojo.query.*;
import com.xtc.onlineretailers.pojo.vo.*;
import com.xtc.onlineretailers.rpc.erp.ErpRpc;
import com.xtc.onlineretailers.rpc.erp.dto.*;
import com.xtc.onlineretailers.rpc.erp.qry.*;
import com.xtc.onlineretailers.service.*;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.xtc.onlineretailers.constant.GlobalConstant.WAREHOUSE_LOCAL;

@Slf4j
@Service
public class ERPServiceImpl implements ERPService {

    @Resource
    private ERPMapper erpMapper;
    @Resource
    private ERPInventoryService erpInventoryService;
    @Resource
    private OrderService orderService;
    @Resource
    private WarehouseService warehouseService;
    @Resource
    private SystemParamService systemParamService;
    @Resource
    private WarehouseProductService warehouseProductService;
    @Resource
    private ProductInfoServiceImpl productInfoService;
    @Resource
    private ErpRpc erpRpc;

    @Override
    public IPage<ErpDataDetailVO> getErpDetail(ERPDetailQuery query) {
        Page<ErpDataDetailVO> page = query.createPage();
        return this.erpMapper.getErpDetail(page, query);
    }

    @Override
    public void detailExport(ErpPostDetailQry qry, HttpServletResponse response) {
        // 文件名日期
        String endDate = DateUtil.dateToStr(new Date(), "yyyy-MM-dd");
        // 文件名称
        String fileName = endDate + "ERP过账明细导出";
        // 不分页
        qry.setIndex(null);
        qry.setSize(null);
        PaginationVO<ErpDataDetailDTO> ecPostDetail = erpRpc.pageByErpDetail(qry);
        ExcelUtil.exportExcel(fileName, ecPostDetail.getItems(), ErpDataDetailVO.class, response);
    }

    @Override
    public IPage<ErpStatisticalVO> getErpStatistical(ERPStatisticalQuery query) {
        Page<ErpStatisticalVO> page = query.createPage();
        return this.erpMapper.getErpStatistical(page, query);
    }

    @Override
    public void statisticalExport(ErpGetEcPostCountQry qry, HttpServletResponse response) {
        // 文件名日期
        String endDate = DateUtil.dateToStr(new Date(), "yyyy-MM-dd");
        // 不分页
        qry.setIndex(null);
        qry.setSize(null);
        PaginationVO<ErpGetEcPostCountDTO> erpGetEcPostCounts = this.erpRpc.pageByErpPostCount(qry);
        // 文件名称
        String fileName = endDate + "ERP过账统计导出";
        ExcelUtil.exportExcel(fileName, erpGetEcPostCounts.getItems(), ErpStatisticalVO.class, response);
    }

    @Override
    public IPage<ErpTakeGoodsVO> getErpTakeGoods(ERPTakeGoodsQuery query) {
        Page<ErpTakeGoodsVO> page = query.createPage();
        return this.erpMapper.getErpTakeGoods(page, query);
    }

    @Override
    public void transferExport(ErpEcTransferQry qry, HttpServletResponse response) {
        // 文件名日期
        String endDate = DateUtil.dateToStr(new Date(), "yyyy-MM-dd");
        // 不分页
        qry.setIndex(null);
        qry.setSize(null);
        PaginationVO<ErpEcTransferDTO> erpEcTransfer = erpRpc.pageByEcTransfer(qry);
        // 文件名称
        String fileName = endDate + "ERP过账提货导出";
        ExcelUtil.exportExcel(fileName, erpEcTransfer.getItems(), ErpTakeGoodsVO.class, response);
    }

    @Override
    public IPage<ErpSendSaveVO> getErpSendSave(ERPSendSaveQuery query) {
        Page<ErpSendSaveVO> page = query.createPage();
        return this.erpMapper.getErpSendSave(page, query);
    }

    @Override
    public void tcxRmaExport(ErpEcTcxRmaQry qry, HttpServletResponse response) {
        // 文件名日期
        String endDate = DateUtil.dateToStr(new Date(), "yyyy-MM-dd");
        // 不分页
        qry.setSize(null);
        qry.setIndex(null);
        PaginationVO<ErpTcxRmaDTO> erpTcxRmas = erpRpc.listByTcxRma(qry);
        // 文件名称
        String fileName = endDate + "ERP过账提货导出";
        ExcelUtil.exportExcel(fileName, erpTcxRmas.getItems(), ErpSendSaveVO.class, response);
    }

    @Override
    public IPage<ErpAllotVO> getErpAllot(ERPAllotQuery query) {
        Page<ErpAllotVO> page = query.createPage();
        return this.erpMapper.getErpAllot(page, query);
    }

    @Override
    public void tcxMoveExport(ErpEcTcxMoveQry qry, HttpServletResponse response) {
        // 文件名日期
        String endDate = DateUtil.dateToStr(new Date(), "yyyy-MM-dd");
        // 不分页
        qry.setSize(null);
        qry.setIndex(null);
        PaginationVO<ErpEcTcxMoveDTO> erpEcTcxMoves = this.erpRpc.pageByEcTcxMove(qry);
        // 文件名称
        String fileName = endDate + "ERP调拨查询导出";
        ExcelUtil.exportExcel(fileName, erpEcTcxMoves.getItems(), ErpAllotVO.class, response);
    }

    @Override
    public IPage<ErpSelfCheckVO> getErpSelfCheck(ERPSelfCheckQuery query) {
        Page<ErpSelfCheckVO> page = query.createPage();
        return this.erpMapper.getErpSelfCheck(page, query);
    }

    @Override
    public void selfCheckExport(ErpEcPendingOrderQry qry, HttpServletResponse response) {
        // 文件名日期
        String endDate = DateUtil.dateToStr(new Date(), "yyyy-MM-dd");
        // 不分页
        qry.setSize(null);
        qry.setIndex(null);
        PaginationVO<EcPendingOrderDTO> ecPendingOrders = erpRpc.pageByEcPendingOrder(qry);
        // 文件名
        String fileName = endDate + "ERP过账自检导出";
        ExcelUtil.exportExcel(fileName, ecPendingOrders.getItems(), ErpSelfCheckVO.class, response);
    }

    @Override
    public Boolean manualFill(String platformTradeId) {
        return this.erpMapper.updateSdbg(platformTradeId) > 0;
    }

    @Override
    public int getErpCount(String tid) {
        return this.erpMapper.getErpCount(tid);
    }

    @Override
    public void insertHeader(ErpHeaderVO header) {
        this.erpMapper.insertHeader(header);
    }

    @Override
    public void insertLine(ErpLineVO line) {
        this.erpMapper.insertLine(line);
    }

    @Override
    public void updateManualSubmit(String platformTradeId) {
        this.erpMapper.updateManualSubmit(platformTradeId);
    }

    @Override
    public void insertOldForNewHeader(ErpOldForNewHeaderVO header) {
        this.erpMapper.insertOldForNewHeader(header);
    }

    @Override
    public void insertOldForNewLine(ErpOldForNewLineVO line) {
        this.erpMapper.insertOldForNewLine(line);
    }

    @Override
    public int getOldForNewHeaderId() {
        return this.erpMapper.getOldForNewHeaderId();
    }

    @Override
    public int getOldForNewCount(String asnno) {
        return this.erpMapper.getOldForNewCount(asnno);
    }

    @Override
    public String getOldForNewErpIdByTradeId(String asnno) {
        return this.erpMapper.getOldForNewErpIdByTradeId(asnno);
    }

    @Override
    public String getErpIdByTradeId(String platformTradeId) {
        return this.erpMapper.getErpIdByTradeId(platformTradeId);
    }

    @Override
    public boolean checkStockEnoughByOrders(boolean limit, List<OrderDO> orders, String subInventory, boolean needReplace, boolean isFictitious, String warehouseName) {
        // 筛选出库存不足的物料
        List<OrderDO> notEnoughOrders = orders.stream()
                .filter(order -> this.isNotEnough(limit, subInventory, order, isFictitious, warehouseName))
                .collect(Collectors.toList());
        if (notEnoughOrders.size() < 1) {
            return true;
        }

        // 是否需要替换物料检查库存
        if (needReplace) {
            // 不足的物料进行物料替代并检查出库是充足
            OrderDO replaceNotEnough = notEnoughOrders.stream()
                    .filter(orderDO -> !this.materielReplaceCheckStock(limit, warehouseName, orderDO))
                    .findAny().orElse(null);
            if (replaceNotEnough != null) {
                log.error("物料替代检查还是库存不足，物料代码：{}，仓库储位：{}", replaceNotEnough.getErpCode()
                        , StringUtils.isNotBlank(warehouseName) ? warehouseName : "本地仓");
                return false;
            }
            return true;
        } else {
            String notEnoughErpCode = notEnoughOrders.stream()
                    .map(OrderDO::getErpCode)
                    .collect(Collectors.toList())
                    .toString();
            notEnoughErpCode = notEnoughErpCode.substring(1, notEnoughErpCode.length() - 1);
            log.error("库存不足，物料代码：{}，仓库储位：{}", notEnoughErpCode,
                    StringUtils.isNotBlank(warehouseName) ? warehouseName : "本地仓");
            return false;
        }
    }

    /**
     * 替换物料代码检查库存
     *
     * @param limit
     * @param warehouseName
     * @param order
     * @return
     */
    private Boolean materielReplaceCheckStock(boolean limit, String warehouseName, OrderDO order) {
        // 查看顺丰表
        if (StringUtils.isEmpty(order.getBarcode())) {
            ProductInfoDO productInfoDO = this.productInfoService.getProductInfoByErpCode(order.getErpCode());
            if (productInfoDO != null) {
                order.setBarcode(productInfoDO.getBarcode());
            }
        }
        WarehouseProductDO warehouseProduct = this.warehouseProductService
                .getOne(Wrappers.<WarehouseProductDO>lambdaQuery()
                        .eq(WarehouseProductDO::getIsUse, 1)
                        .eq(WarehouseProductDO::getProductBarcode, order.getBarcode())
                        .orderByDesc(WarehouseProductDO::getUpdateTime)
                        .last("limit 1"));
        if (ObjectUtils.isEmpty(warehouseProduct)) {
            return false;
        }

        String[] split = warehouseProduct.getProductIds().split(",");
        List<String> arrayList = Lists.newArrayList(split);
        // 去除自己本身的物料代码
        List<String> fileCode = arrayList.stream()
                .filter(erpCode -> !order.getErpCode().equals(erpCode))
                .sorted().collect(Collectors.toList());
        for (String erpCode : fileCode) {
            OrderDO newOrder = new OrderDO();
            BeanUtils.copyProperties(order, newOrder);
            newOrder.setErpCode(erpCode);
            if (!this.isNotEnough(limit, "006", newOrder, false, warehouseName)) {
                order.setErpCode(erpCode);
                return true;
            }
        }
        return false;
    }

    /**
     * 检查库存不充足的物料
     *
     * @param limit
     * @param subInventory
     * @param order
     * @return
     */
    private boolean isNotEnough(boolean limit, String subInventory, OrderDO order, boolean isFictitious, String warehouseName) {
        return this.orderService.isErpOrder(order)
                && this.checkStockNotEnoughByErpCode(limit, order.getErpCode(), subInventory, order.getNum(), isFictitious, warehouseName);
    }

    @Override
    public boolean checkStockNotEnoughByErpCode(boolean limit, String erpCode, String subInventory, int nowNum, boolean isFictitious, String warehouseName) {
        // erp库存判断开关
        String checkStock = this.systemParamService.getSystemParamByName(GlobalConstant.SYSTEM_SWITCH_CHECK_ERP_STOCK_MAIN);
        if (!"1".equals(checkStock)) {
            return true;
        }
        boolean sfStock = false;
        // 本地库存大于10个才算库存充足
        int limitStock = limit ? 10 : 0;
        // ERP 005+ 仓库名称  006+仓库名称
        ErpInventoryQry epInventoryQry = new ErpInventoryQry();
        epInventoryQry.setSubInventory(subInventory);
        epInventoryQry.setStoragePlace(warehouseName);
        epInventoryQry.setProductId(erpCode);
        PaginationVO<EcOnHandDTO> ecOnHands = erpRpc.pageByInventory(epInventoryQry);
        if (CollectionUtils.isEmpty(ecOnHands.getItems())) {
            log.info("物料代码 {} 仓库名称 {} 储位 {} ERP库存为空 默认库存充足", erpCode, warehouseName, subInventory);
            return true;
        }
        EcOnHandDTO ecOnHandDTO = ecOnHands.getItems().get(0);
        // 获取滞留在订单中的物料数量
        if ("006".equals(subInventory)) {
            WarehouseDO warehouseByName = warehouseService.getWarehouseByName(warehouseName);
            warehouseName = warehouseByName.getWarehouseStorage();
            sfStock = true;
        }
        if ("202".equals(subInventory) && "03网销顺丰坏料仓".equals(warehouseName)) {
            warehouseName = "4598";
        }
        if ("202".equals(subInventory) && "02网络销售坏料仓".equals(warehouseName)) {
            warehouseName = "2668";
        }
        int stockInOrders = this.orderService.getStockInOrdersByErpCode(erpCode, sfStock, nowNum, warehouseName);
        log.info(" 物料代码 {} 过账数量 {} 临时占用的库存 {}, 当前: {}", erpCode, nowNum, stockInOrders, ecOnHandDTO.getNum());
        return ecOnHandDTO.getNum() - stockInOrders <= limitStock;
    }

    @Override
    public OrderConfirmVO checkConfirmStockNotEnoughByErpCode(String erpCode, String warehouseName, int nowNum) {
        String subInventory = WAREHOUSE_LOCAL;
        boolean sfStock = false;
        String warehouseStorage = "";
        if (StringUtils.isNotBlank(warehouseName) && !WAREHOUSE_LOCAL.equals(warehouseName)) {
            subInventory = "006";
            sfStock = true;
            WarehouseDO warehouseDO = this.warehouseService.getWarehouseByName(warehouseName);
            warehouseStorage = warehouseDO.getWarehouseStorage();
        }
        ErpInventoryDO erpInventory = this.erpInventoryService.getErpInventoryByProId(subInventory, warehouseName, erpCode);
        // 获取滞留在订单中的物料数量
        //int stockInOrders = limit ? this.orderService.getStockInOrdersByErpCode(erpCode, sfStock) : 0;
        int stockInOrders = this.orderService.getStockInOrdersByErpCode(erpCode, sfStock, 0, warehouseStorage);
        //还需要减掉属于自己的那一部分（会造成订单只剩下最后的一个会过不了账）
        // return erpInventory == null || erpInventory.getNum() - stockInOrders <= limitStock;
        OrderConfirmVO confirmVO = new OrderConfirmVO();
        confirmVO.setErpCode(erpCode);
        int num = 0;
        if (erpInventory != null) {
            num = erpInventory.getNum() - stockInOrders;
        }
        confirmVO.setNum(num);
        return confirmVO;
    }

    @Override
    public IPage<ErpTempReportFormVO> getTempReportsForm(ERPStatisticalQuery query) {
        Page<ErpTempReportFormVO> page = query.createPage();
        if (null != query.getEndDate()) {
            query.setEndDate(query.getEndDate() + " 23:59:59");
        }
        return this.erpMapper.getErpTempReportsForm(page, query);
    }

    @Override
    public void orderTempFormExport(ErpOrderTempQry qry, HttpServletResponse response) {
        // 文件名日期
        String endDate = DateUtil.dateToStr(new Date(), "yyyy-MM-dd");
        PaginationVO<ErpOrderTempDTO> erpOrderTempForm = erpRpc.pageByErpOrderTempForm(qry);
        String fileName = endDate + "ERP临时报表导出";
        ExcelUtil.exportExcel(fileName, erpOrderTempForm.getItems(), ErpTempReportFormVO.class, response);
    }

}
