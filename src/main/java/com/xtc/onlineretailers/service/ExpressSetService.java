package com.xtc.onlineretailers.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xtc.onlineretailers.pojo.entity.ExpressSetDO;
import com.xtc.onlineretailers.pojo.query.ExpressSetAddQuery;
import com.xtc.onlineretailers.pojo.query.ExpressSetQuery;
import com.xtc.onlineretailers.pojo.query.ExpressSetUpdateQuery;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

public interface ExpressSetService extends IService<ExpressSetDO> {

    /**
     * 获取快递设置列表
     *
     * @param productType 产品类型
     * @param paymentTime 付款时间
     * @return 快递设置列表
     */
    List<ExpressSetDO> getExpressSetList(String productType, Date paymentTime);

    /**
     * 获取快递设置列表
     *
     * @param query 筛选条件
     * @return 快递设置列表
     */
    PaginationVO<ExpressSetDO> getExpressSetList(ExpressSetQuery query);

    /**
     * 新增快递设置
     *
     * @param query 快递设置
     * @return 新增的快递设置
     */
    ExpressSetDO addExpressSet(ExpressSetAddQuery query);

    /**
     * 编辑快递设置
     *
     * @param query 快递设置
     * @return 更新后的快递设置
     */
    ExpressSetDO updateExpressSet(ExpressSetUpdateQuery query);

    /**
     * 导出快递设置
     *
     * @param query    筛选条件
     * @param response 请求响应
     */
    void exportExpressSet(ExpressSetQuery query, HttpServletResponse response);

}
