package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@ApiModel("商城库存订单")
@TableName("t_mall_store_order")
public class MallStoreOrderDO extends BaseEntity {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单类型 0:出库 1:入库
     */
    private Integer type;

    /**
     * 订单状态 0:已取消 1:已申请 2:已确认
     */
    private Integer status;

    /**
     * 确认时间
     */
    private String confirmTime;

    /**
     * 取消时间
     */
    private String cancelTime;

    /**
     * 备注
     */
    private String memo;

    /**
     * 取消人
     */
    private String cancelOperator;

    /**
     * 确认人
     */
    private String confirmOperator;

    /**
     * 创建人
     */
    private String creator;

}

