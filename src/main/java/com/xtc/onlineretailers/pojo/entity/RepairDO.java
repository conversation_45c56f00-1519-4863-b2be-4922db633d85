package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 维修模块
 */
@Data
@TableName("t_repair")
public class RepairDO extends BaseEntity {

    /**
     * 电商平台ID
     */
    private Integer platformId;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 维修下单客服姓名
     */
    private String serviceName;

    /**
     * 顾客会员名
     */
    private String buyerNick;

    /**
     * 顾客电话
     */
    private String telephone;

    /**
     * 顾客反馈问题
     */
    private String question;

    /**
     * 原订单付款时间
     */
    private String paymentTime;

    /**
     * 寄回物品
     */
    private String backGoods;

    /**
     * 维修物品数量
     */
    private Integer num;

    /**
     * 送修日期
     */
    private String repairTime;

    /**
     * 送修人
     */
    private String repairName;

    /**
     * 处理结果
     */
    private String result;

    /**
     * 状态
     */
    private String status;

    /**
     * 寄出日期
     */
    private String sendTime;

    /**
     * 寄出快递公司
     */
    private String expressCompany;

    /**
     * 寄出快递单号
     */
    private String expressTrackingNo;

    /**
     * 维修费用
     */
    private String repairCost;

    /**
     * 付款方式
     */
    private String payType;

    /**
     * 维修产品名称
     */
    private String productName;

    /**
     * 顾客姓名
     */
    private String receiverName;

    /**
     * 电商平台交易订单号
     */
    private String platformTradeId;

    /**
     * 省份
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 地址
     */
    private String address;

    /**
     * 收款人
     */
    private String payee;

    /**
     * 售后人姓名
     */
    private String salesAfterName;

    /**
     * 售后日期
     */
    private String salesAfterTime;

    /**
     * 售后的确认客服姓名
     */
    private String confirmServiceName;

    /**
     * 售后的确认客服服务日期
     */
    private String serviceTime;

    /**
     * 付款人姓名
     */
    private String payName;

    /**
     * 支付日期
     */
    private String payTime;

    /**
     * 寄出人姓名
     */
    private String sendName;

    /**
     * 寄回快递单号
     */
    private String backExpressTrackingNo;

    /**
     * 寄回快递公司
     */
    private String backExpressCompany;

    /**
     * 寄回条码
     */
    private String barcode;

    /**
     * 换货维修类型
     */
    private String type;

    /**
     * 确认是否换机,1表示确认,-1表示未确认
     */
    private Integer isExchange;

    /**
     * 换货类型
     */
    private String exchangeGoodsType;

    /**
     * 换货订单号
     */
    private String exchangeGoodsOrder;

    /**
     * 是否自动生成
     */
    private Integer isGenerate;

    /**
     * 是否抛单
     */
    private Integer isConfirm;

}
