package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("优先发货订单详情")
@TableName("t_deliver_goods_details")
public class DeliverGoodsDetailsDO extends BaseEntity {

    /**
     * 电商平台ID
     */
    private Integer platformId;

    /**
     * 电商平台名称
     */
    private String platformName;

    /**
     * 电商交易id
     */
    private String platformTradeId;

    /**
     * 电商订单id
     */
    private String platformOrderId;

    /**
     * ERP物料编码
     */
    private String erpCode;

    /**
     * 顺丰对应69码
     */
    private String barcode;

    /**
     * 物料描述
     */
    private String erpName;

    /**
     * 明细类型 1:正常明细 2:赠品明细  3:折扣明细  4：运费明细
     */
    private Integer orderType;

    /**
     * 物料类型 1：机器   2：配件  3：虚拟物料
     */
    private Integer materialType;

    /**
     * 单位
     */
    private String unit;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 总价
     */
    private BigDecimal priceTotal;

    /**
     * 电商平台积分
     */
    private BigDecimal pointPlatform;

    /**
     * 支付宝的集分宝
     */
    private BigDecimal pointAlipayJf;

    /**
     * 明细总价值
     */
    private BigDecimal payment;

    /**
     * 是否退货
     */
    private Integer isRefund;

    /**
     * 退货数量
     */
    private Integer refundNum;

    /**
     * 赠品对应的商品的物料编码
     */
    private String presentMainCode;

    /**
     * 淘宝获取实际付款金额
     */
    private BigDecimal priceTaobao;

    /**
     * SN码10008
     */
    private String sn;

    /**
     * 用户昵称
     */
    private String buyerNickname;

    /**
     * 付款时间
     */
    private Date paymentTime;
}
