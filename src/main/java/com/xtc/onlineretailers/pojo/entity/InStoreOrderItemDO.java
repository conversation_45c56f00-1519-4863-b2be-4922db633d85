package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_in_store_order_item")
public class InStoreOrderItemDO {

    /**
     * 入库申请明细id
     */
    @TableId(type = IdType.AUTO)
    private Integer itemId;

    /**
     * 入库申请单号
     */
    private String orderId;

    /**
     * 物料代码
     */
    private String productId;

    /**
     * 物料名称
     */
    private String productName;

    /**
     * 顺丰69码
     */
    private String barcode;

    /**
     * 申请入库数量
     */
    private Integer num;

    /**
     * 实际入库数量
     */
    private Integer actualQty;

    /**
     * 新增时间
     */
    private Date addTime;

}
