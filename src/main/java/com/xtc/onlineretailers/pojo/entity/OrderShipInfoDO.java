package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Setter
@Getter
@ToString
@NoArgsConstructor
@TableName("t_order_ship")
public class OrderShipInfoDO {

    /**
     * 订单号
     */
    @TableId
    private String tradeId;

    /**
     * 顺丰出库单号
     */
    private String shipmentId;

    /**
     * 顺丰状态 1：已发货
     */
    private String shipStatus;

    /**
     * 快递单号
     */
    private String shipNo;

    /**
     * 申请出库时间
     */
    private Date applyTime;

    /**
     * 顺丰出库时间
     */
    private Date shipTime;

    /**
     * 请求报文
     */
    private String shipRequestMessage;

    /**
     * 入库回调报文
     */
    private String callbackMessage;

    /**
     * 是否系统订单
     */
    private String isSystem;

    /**
     * 是否解析报文 -1：未解析    1：已解析   0：无报文
     */
    private String isResolve;

    /**
     * 仓库编号
     */
    private String storeCode;

    /**
     * 仓库名称
     */
    private String storeName;

    /**
     * 机器条码列表
     */
    private String productSnList;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 平台id
     */
    private Integer platformId;

}
