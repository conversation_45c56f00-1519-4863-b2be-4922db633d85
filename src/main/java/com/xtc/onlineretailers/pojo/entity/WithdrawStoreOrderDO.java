package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_withdraw_store_order")
public class WithdrawStoreOrderDO {

    /**
     * 退库单号
     */
    @TableId
    private String orderId;

    /**
     * 顺丰退库单号
     */
    private String shipmentId;

    /**
     * 快递单号
     */
    private String shipNo;

    /**
     * 退库单状态  -1：已取消退库 1:申请退库成功 2:申请退库失败  3:顺丰退库成功  4:顺丰退库失败 5:顺丰退库异常
     */
    private String orderStatus;

    /**
     * 仓库编号
     */
    private String storeCode;

    /**
     * 仓库名称
     */
    private String storeName;

    /**
     * 申请退库总数
     */
    private Integer totalNum;

    /**
     * 实际退库总数
     */
    private Integer totalActualQty;

    /**
     * 退库申请请求报文
     */
    private String requestMessage;

    /**
     * 退库回调报文
     */
    private String callbackMessage;

    /**
     * 是否解析报文 -1：未解析  1：已解析  0：无报文
     */
    private String isResolve;

    /**
     * 退库申请人
     */
    private String creatorName;

    /**
     * 顺丰退库时间
     */
    private Date deliveryTime;

    /**
     * 退库申请时间
     */
    private Date applyTime;

    /**
     * 退库申请添加时间
     */
    private Date addTime;

    /**
     * 收件人省份
     */
    private String receiverProvince;

    /**
     * 收件人城市
     */
    private String receiverCity;

    /**
     * 收件人区县
     */
    private String receiverDistrict;

    /**
     * 收件人地址
     */
    private String receiverAddress;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件人手机号码
     */
    private String receiverMobile;

    /**
     * 收件人固话
     */
    private String receiverPhone;

    /**
     * 备注
     */
    private String remake;

}
