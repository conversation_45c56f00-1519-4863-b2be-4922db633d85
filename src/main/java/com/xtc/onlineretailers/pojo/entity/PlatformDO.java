package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 电商平台信息
 */
@Getter
@Setter
@TableName("t_platform")
public class PlatformDO extends BaseEntity {

    /**
     * 品牌
     */
    private String brand;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 电商平台Id
     */
    private Integer platformId;
    /**
     * 电商平台名称
     */
    private String platformName;
    /**
     * 电商平台类型：NORMAL（自营）、AGENT（代销、代发）
     */
    private String platformType;
    /**
     * 平台代码
     */
    private String platformCode;
    /**
     * 店铺代码
     */
    private String shopCode;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 电子面单打印控件
     */
    private String logisticsPrintClient;
    /**
     * EPR客户ID
     */
    private String customerId;
    /**
     * EPR客户代码
     */
    private String customerCode;
    /**
     * 是否需要系统自动同步订单
     */
    private Integer systemSync;
    /**
     * 延迟刷单时间
     */
    private Integer delaySeconds;
    /**
     * 外部接口url
     */
    private String apiUrl;
    /**
     * 平台接口app_key
     */
    private String appKey;
    /**
     * 平台接口app_secret
     */
    private String appSecret;
    /**
     * 平台接口sessionKey
     */
    private String sessionKey;
    /**
     * 平台接口session过期时间
     */
    private Date expireTime;
    /**
     * 排序
     */
    private Integer sn;
    /**
     * 是否在用 -1:不在用  1：在用
     */
    private Integer isUse;
    /**
     * 入驻时间
     */
    private String settledTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 是否代理 0:否 1:是
     */
    private Integer isAgent;
    /**
     * 代销类别
     */
    private String agentCategory;
    /**
     * 发票抬头
     */
    private String invoiceTitle;
    /**
     * 发票税号
     */
    private String invoiceRegisterNo;
    /**
     * 发票银行账号
     */
    private String invoiceBankAccount;
    /**
     * 发票地址
     */
    private String invoiceAddress;
    /**
     * 发票收件地址
     */
    private String invoiceReceiveAddress;
    /**
     * 备注
     */
    private String memo;
    /**
     * 市
     */
    private String city;
    /**
     * 详细地址
     */
    private String detail;
    /**
     * 区
     */
    private String district;
    /**
     * 省
     */
    private String province;
    /**
     * 姓名
     */
    private String name;
    /**
     * 电话
     */
    private String mobile;
    /**
     * 刷新token
     */
    private String refreshToken;
    /**
     * 是否返利
     */
    private String isFanli;
    /**
     * 是否余额提醒
     */
    private Integer isBalance;
    /**
     * 是否抛顺丰
     */
    private Boolean enabledSfWarehouse;

}
