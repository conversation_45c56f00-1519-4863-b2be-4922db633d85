package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 顺丰物料信息
 */
@Setter
@Getter
@NoArgsConstructor
@TableName("t_warehouse_product")
public class WarehouseProductDO extends BaseEntity {

    /**
     * 物料名称
     */
    private String productName;

    /**
     * 物料69条码
     */
    private String productBarcode;

    /**
     * 是否使用
     */
    private Integer isUse;

    /**
     * 物料代码json
     */
    private String productIds;


}
