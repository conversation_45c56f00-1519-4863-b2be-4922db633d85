package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 退机好料归位
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_returned_material_homing")
public class ReturnedMaterialHomingDO extends BaseEntity {

    /**
     * 电商订单号
     */
    private String platformTradeId;

    /**
     * 用户昵称
     */
    private String buyerNickname;

    /**
     * 用户手机
     */
    private String mobile;

    /**
     * 机型
     */
    private String erpName;

    /**
     * 付款时间
     */
    private String paymentTime;

    /**
     * 卖家备注
     */
    private String sellerRemark;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 是否激活
     */
    private Integer isActive;

    /**
     * 是否清除
     */
    private Integer isClear;

    /**
     * 是否仓库归位
     */
    private Integer isStoreHoming;

    /**
     * 创建人
     */
    private String creator;

}
