package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 发货订单表
 */
@Data
@TableName("t_trade_log")
public class TradeLogDO  {


    /**
     * 电商平台ID
     */
    private Integer platformId;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 物料代码
     */
    private String materialCode;

    /**
     * 原物料代码
     */
    private String rawMaterialCode;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 是否完结
     */
    private Integer status;

    /**
     * 能否删除标识符
     */
    private Integer isDelete;




}
