package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 最低价提醒表
 */
@Setter
@Getter
@ToString
@TableName("t_low_price")
public class LowPriceDO extends BaseEntity{


    /**
     * 电商订单号
     */
    private String platformTradeId;

    /**
     * 付款时间
     */
    private String paymentTime;

    /**
     * 付款金额
     */
    private BigDecimal payment;

    /**
     * 天猫积分
     */
    private BigDecimal pointPlatform;

    /**
     * 支付宝集分宝
     */
    private BigDecimal pointAlipayJf;

    /**
     * 最低成交金额
     */
    private BigDecimal minPrice;

    /**
     * 主要商品
     */
    private String productDetail;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 状态
     */
    private String status;

    /**
     * 机型
     */
    private String materialType;





}
