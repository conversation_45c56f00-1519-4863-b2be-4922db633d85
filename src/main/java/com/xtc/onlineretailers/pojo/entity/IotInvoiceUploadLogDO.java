package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * iot发票上传日志表
 */
@Data
@TableName("t_iot_invoice_upload_log")
public class IotInvoiceUploadLogDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    private String tradeId;

    /**
     * 上传状态
     */
    private String uploadStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 发票地址
     */
    private String electronicInvoiceUrl;

}
