package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@ApiModel("退货主表")
@TableName("t_return_master")
public class ReturnMasterDO extends BaseEntity {

    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 退货单号
     */
    private String refundId;

    /**
     * 电商交易id
     */
    private String platformTradeId;

    /**
     * 付款时间
     */
    private String paymentTime;

    /**
     * 用户昵称
     */
    private String buyerNickname;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 运营备注
     */
    private String memoOperation;

    /**
     * 仓库备注
     */
    private String memoWarehouse;

    /**
     * 退货商品扫描人
     */
    private String scanner;

    /**
     * 退款状态(已退款,未退款,打回,确认)
     */
    private String refundStatus;

    /**
     * 退款时间
     */
    private String refundTime;

    /**
     * erp过账状态0：未过账  1：已过账  2：不过账
     */
    private Integer erpPostStatus;

    /**
     * 实收金额
     */
    private BigDecimal payment;

    /**
     * 申请退款金额
     */
    private BigDecimal applyAmount;

    /**
     * 自动退仓状态
     */
    private String autoRefundStatus;

    /**
     * 实际退款金额
     */
    private BigDecimal realAmount;

    /**
     * 退货类型(退货,换货)
     */
    private String type;

    /**
     * 退货原因
     */
    private String reason;

    /**
     * erp单号
     */
    private String erpId;

    /**
     * erp过账组织
     */
    private String erpPostOrg;

    /**
     * erp过账时间
     */
    private Date erpPostTime;

    /**
     * 是否手动确认过账,1表示已确认,0表示未确认
     */
    private Integer erpPostManual;

    /**
     * 订单明细中的商品是否全部退货,1表示全部退货,-1表示没有全部退货
     */
    private Integer isAllReturn;

    /**
     * 手动确认过账时间
     */
    private Date erpPostManualTime;

    /**
     * 用户提交快递单号
     */
    private String postTrackingNo;

    /**
     * 收货快递单号
     */
    private String expressTrackingNo;

    /**
     * 收货快递公司
     */
    private String expressCompany;

    /**
     * 子仓库编码
     */
    private String locationId;

    /**
     * 仓库储位，本地：202，顺丰：006
     */
    private String subinvCode;

    /**
     * 是否对接成品仓:0表示无需收货;1表示已收货
     */
    private Integer isEndProductStore;

    /**
     * 部分退款流程状态
     */
    private String partReturnStatus;

    /**
     * 部分退款客服确认人
     */
    private String partConfirmUser;

    /**
     * 部分退款客服确认时间
     */
    private Date partConfirmTime;

    /**
     * 部分退款客服确认备注
     */
    private String partReturnMemo;

    /**
     * 部分退款调单操作人
     */
    private String adjustUser;

    /**
     * 部分退款调单操作时间
     */
    private Date adjustTime;

    /**
     * 部分退款调单订单号
     */
    private String adjustTrades;

    /**
     * 部分退款仓库归位操作人
     */
    private String storeConfirmUser;

    /**
     * 部分退款调单操作时间
     */
    private Date storeConfirmTime;

    /**
     * 用户拒签 1:是 0:否
     */
    private Integer isUserRefusal;

    /**
     * 是否换货 0:否 1:是
     */
    private Integer isExchange;

    /**
     * 原订单卖家备注
     */
    private String sellerRemark;

    /**
     * 调单类型
     */
    private String adjustType;

    /**
     * 订单标签，英文逗号分割多个标签
     */
    private String orderTag;

    /**
     * 收货仓库
     */
    private String receiptWarehouse;


}
