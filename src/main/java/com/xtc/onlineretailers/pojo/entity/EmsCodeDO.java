package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_ems_code")
public class EmsCodeDO extends BaseEntity {

    private String province;

    private String city;

    private String district;

    private String binCode;

    private String binCodeExtra;

    private String emailCode;

}
