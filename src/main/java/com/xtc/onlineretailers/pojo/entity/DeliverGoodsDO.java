package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("优先发货订单")
@TableName("t_deliver_goods")
public class DeliverGoodsDO  extends BaseEntity{

    /**
     * 电商订单号
     */
    private String platformTradeId;

    /**
     * 原电商订单号
     */
    private String oldPlatformTradeId;

    /**
     * 电商平台ID
     */
    private Integer platformId;

    /**
     * 电商平台名称
     */
    private String platformName;

    /**
     * 支付流水号
     */
    private String alipayNo;

    /**
     * ERP仓库代码，本地：005，顺丰：006
     */
    private String warehouseCode;

    /**
     * 仓库储位，本地：005，顺丰：顺丰仓库编码
     */
    private String warehouseStorage;

    /**
     * 订单类型
     */
    private String type;

    /**
     * 订单状态
     */
    private String statusOrder;

    /**
     * 锁定状态，是否锁定订单   -1:未锁定  1:已锁定
     */
    private Integer statusLock;

    /**
     * 预售状态，是否预售订单
     */
    private Integer statusReserve;

    /**
     * 顺丰操作状态  -1：非顺丰出货 1:顺丰出货  2:抛转到顺丰成功 3:顺丰出库成功 4:顺风取消出库成功
     */
    private String statusSf;

    /**
     * 淘宝状态
     */
    private String statusTaobao;

    /**
     * APP商城、内部购机平台的交易单号（微信支付的商家单号）
     */
    private String appTradeSn;

    /**
     * 用户昵称
     */
    private String buyerNickname;

    /**
     * 收件人淘宝加密信息
     */
    private String receiverOaid;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件人电话
     */
    private String receiverMobile;

    /**
     * 收件人固话
     */
    private String receiverPhone;

    /**
     * 收件人省份
     */
    private String receiverProvince;

    /**
     * 收件人城市
     */
    private String receiverCity;

    /**
     * 收件人区县
     */
    private String receiverDistrict;

    /**
     * 收件人地址
     */
    private String receiverAddress;

    /**
     * 收件人邮政编码
     */
    private String receiverZip;

    /**
     * 是否用户更新收货地址
     */
    private Integer isUpdateAddress;

    /**
     * 买家备注
     */
    private String buyerRemark;

    /**
     * 卖家备注
     */
    private String sellerRemark;

    /**
     * 外部折扣金额
     */
    private BigDecimal outerDiscount;

    /**
     * 内部优惠金额
     */
    private BigDecimal localDiscount;

    /**
     * 红包金额
     */
    private BigDecimal redpack;

    /**
     * 天猫积分
     */
    private BigDecimal pointPlatform;

    /**
     * 支付宝集分宝
     */
    private BigDecimal pointAlipayJf;

    /**
     * 付款金额
     */
    private BigDecimal payment;

    /**
     * 运费
     */
    private BigDecimal freight;

    /**
     * 付款时间
     */
    private Date paymentTime;

    /**
     * 预售订单的付款时间，第一次付款的时间
     */
    private Date paymentTimeAdvance;

    /**
     * 是否发货
     */
    private Integer isShipping;

    /**
     * 发货日期
     */
    private String shippingDate;

    /**
     * 发货时间
     */
    private Date shippingTime;

    /**
     * 快递单号
     */
    private String expressTrackingNo;

    /**
     * 快递公司
     */
    private String expressCompany;

    /**
     * 商品数量
     */
    private Integer productNum;

    /**
     * 商品明细
     */
    private String productDetail;

    /**
     * 是否成品
     */
    private Integer isProduct;

    /**
     * 是否含电池
     */
    private Integer isSendBattery;

    /**
     * 调单单号
     */
    private String adjustOrderId;

    /**
     * 补寄快递单号
     */
    private String supplementTrackingNo;

    /**
     * 补寄快递公司
     */
    private String supplementExpressCompany;

    /**
     * 补寄快递时间
     */
    private String supplementTime;

    /**
     * 订单确认时间
     */
    private String orderConfirmTime;

    /**
     * 订单修改时间
     */
    private Date orderChangeTime;

    /**
     * 订单打回时间
     */
    private String orderReturnTime;

    /**
     * erp单号
     */
    private String erpId;

    /**
     * erp过账状态0：未过账  1：已过账  2：不过账
     */
    private Integer erpPostStatus;

    /**
     * erp过账组织
     */
    private String erpPostOrg;

    /**
     * erp过账时间
     */
    private Date erpPostTime;

    /**
     * 是否手动确认过账
     */
    private Integer erpPostManual;

    /**
     * 手动确认过账时间
     */
    private Date erpPostManualTime;

    /**
     * 发票状态  -1 :未开票  1：已开票  2：已冲红  3：已回传   4：冲红完毕  5：无需回传  6：二次开票 7：二次回传  8：二次冲红  9：已开纸票  10：无需开票  11 ：开票失败  12：冲红失败
     */
    private String invoiceStatus;

    /**
     * 请求发票的唯一标识
     */
    private String invoiceRequestNo;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 发票税号
     */
    private String invoiceRegisterNo;

    /**
     * 发票银行账号
     */
    private String invoiceBankAccount;

    /**
     * 发票地址
     */
    private String invoiceAddress;

    /**
     * 发票信息更新时间
     */
    private Date invoiceUpdateTime;

    /**
     * 电子发票号
     */
    private String electronicInvoiceNo;

    /**
     * 电子发票代码
     */
    private String electronicInvoiceCode;

    /**
     * 电子发票下载地址
     */
    private String electronicInvoiceUrl;

    /**
     * 电子发票开票时间
     */
    private String electronicInvoiceTime;

    /**
     * 电子发票冲红号码
     */
    private String electronicInvoiceRedNo;

    /**
     * 电子发票冲红代码
     */
    private String electronicInvoiceRedCode;

    /**
     * 电子发票冲红下载地址
     */
    private String electronicInvoiceRedUrl;

    /**
     * 电子发票冲红时间
     */
    private String electronicInvoiceRedTime;

    /**
     * 纸质发票号码
     */
    private String paperInvoiceNo;

    /**
     * 纸质发票代码
     */
    private String paperInvoiceCode;

    /**
     * 纸质发票开票时间
     */
    private String paperInvoiceTime;

    /**
     * 纸质发票冲红号码
     */
    private String paperInvoiceRedNo;

    /**
     * 纸质发票冲红代码
     */
    private String paperInvoiceRedCode;

    /**
     * 纸质发票冲红时间
     */
    private String paperInvoiceRedTime;

    /**
     * 仓库状态
     */
    private String warehouseStatus;

    /**
     * 配货单号
     */
    private String allocationNo;

    /**
     * 是否配货
     */
    private Integer isAllocation;

    /**
     * 配货日期
     */
    private Date allocationTime;

    /**
     * 异常状态
     */
    private String exceptionStatus;

    /**
     * 退货原因或调单备注
     */
    private String reason;

    /**
     * 交接单号
     */
    private String handOverNo;

    /**
     * 交接时间
     */
    private String handOverTime;

    /**
     * 仓库打回时间
     */
    private String rejectTime;

    /**
     * 是否定金发货,1表示是,0表示否
     */
    private Integer isAdvanceShip;

    /**
     * 是否揽收 1:是 0:否
     */
    private Integer isCollect;

    /**
     * 揽收日期
     */
    private String collectTime;

    /**
     * 快递是否保价 1:是 0:否
     */
    private Integer useInsurance;

    /**
     * 保价费用
     */
    private BigDecimal insuranceFee;

    /**
     * 虚拟号过期时间
     */
    private String mobileExpiration;

    /**
     * 是否修改
     */
    private Integer status;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 发货任务id
     */
    private Long tradeId;
}
