package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 快递记录
 */
@Data
@TableName("t_express_log")
public class ExpressLogDO extends BaseEntity {

    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 快递单号
     */
    private String expressTrackingNo;

    /**
     * 快递公司
     */
    private String expressCompany;

    /**
     * 电商平台交易id
     */
    private String platformTradeId;

    /**
     * 是否使用(1表示是;-1表示否)
     */
    private Integer isUse;

    /**
     * 是否补寄(1表示是;-1表示否)
     */
    private Integer isSupplyAgain;

    /**
     * 发货日期
     */
    private String shipmentTime;

    /**
     * 教育电子
     */
    private String eduPay;

    /**
     * 电话手表
     */
    private String xtcPay;

    /**
     * 护眼平板
     */
    private String padPay;

    /**
     * 扫描笔
     */
    private String scanPenPay;

    /**
     * 抖音
     */
    private String tictokPay;

    /**
     * 客户姓名
     */
    private String receiverName;

    /**
     * 客户所在地区
     */
    private String receiverCity;

    /**
     * 客户电话
     */
    private String receiverMobile;

    /**
     * 核对结果
     */
    private String result;

    /**
     * 应付金额
     */
    private String shouldPay;

    /**
     * 快递类型
     */
    private String type;

    /**
     * 仓库类型(0,本地仓库，1，顺丰仓库)
     */
    private Integer isLocal;

    /**
     * 快递是否保价 1:是 0:否
     */
    private Integer useInsurance;

    /**
     * 保价费用
     */
    private BigDecimal insuranceFee;

    /**
     * 快递费用分摊明细
     */
    private String proportionDetail;

    /**
     * 是否退货 1:是 0:否
     */
    private Integer isReturn;

    /**
     * 快递类别
     */
    private String expressType ;

}
