package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("顺丰快递路由数据")
@TableName("t_sf_routing")
public class SfRoutingDO extends BaseEntity {

    /**
     * 快递单号
     */
    private String expressTrackingNo;

    /**
     * 路由信息
     */
    private String routingInfo;

    /**
     * 是否揽收 1:是 0:否
     */
    private Integer isCollect;

}
