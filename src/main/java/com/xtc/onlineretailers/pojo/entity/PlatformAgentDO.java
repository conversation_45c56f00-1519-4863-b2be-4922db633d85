package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "代销账号")
@TableName("t_platform_agent")
public class PlatformAgentDO extends BaseEntity {

    /**
     * 账号
     */
    private String account;

    /**
     * 平台id集合
     */
    private String platformIds;

    /**
     * 告警阈值
     */
    private BigDecimal alarmThreshold;

    /**
     * 告警提示手机号
     */
    private String alarmMobile;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 余额
     */
    private BigDecimal balance;

    /**
     * 余额更新时间
     */
    private String balanceUpdateTime;

}
