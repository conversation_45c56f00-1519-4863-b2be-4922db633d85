package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@TableName("t_barcode_log")
@ApiModel(value = "发货条码记录")
public class BarcodeLogDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 条码
     */
    private String sn;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 状态值 1 成功  2 失败
     */
    private Integer status;

}
