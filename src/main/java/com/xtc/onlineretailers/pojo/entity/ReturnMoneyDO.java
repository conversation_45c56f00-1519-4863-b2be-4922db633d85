package com.xtc.onlineretailers.pojo.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 返现信息
 */
@Setter
@Getter
@TableName("t_return_money")
public class ReturnMoneyDO {

    /**
     * 平台名称
     */
    @ExcelProperty(value = "平台类型", index = 0)
    private String platformName;

    /**
     * 类型
     */
    @ExcelProperty(value = "返现类型", index = 1)
    private String type;

    /**
     * 客服姓名
     */
    @ExcelProperty(value = "操作客服", index = 2)
    private String serviceName;

    /**
     * 返现的创建时间
     */
    @ExcelProperty(value = "客服新增时间", index = 3)
    private String createTime;

    /**
     * 返现的编辑时间
     */
    private String updateTime;

    /**
     * 电商交易id
     */
    @ExcelProperty(value = "订单号", index = 4)
    private String platformTradeId;

    /**
     * 产品型号
     */
    @ExcelProperty(value = "机型", index = 5)
    private String model;

    /**
     * 支付时间
     */
    @ExcelProperty(value = "购买日期", index = 6)
    private String payTime;

    /**
     * 返现理由
     */
    @ExcelProperty(value = "返现原因", index = 7)
    private String reason;

    /**
     * 客户姓名
     */
    @ExcelProperty(value = "顾客姓名", index = 8)
    private String receiverName;

    /**
     * 客户淘宝id
     */
    @ExcelProperty(value = "淘宝ID", index = 9)
    private String buyerNick;

    /**
     * 支付账号
     */
    @ExcelProperty(value = "支付账号", index = 10)
    private String alipayNo;

    /**
     * 返现金额
     */
    @ExcelProperty(value = "返现金额", index = 11)
    private String payment;

    /**
     * 是否返现
     */
    @ExcelProperty(value = "是否返现", index = 12)
    private Integer isReturnMoney;

    /**
     * 财务员姓名
     */
    @ExcelProperty(value = "财务人员", index = 13)
    private String treasurerName;

    /**
     * 返现时间
     */
    @ExcelProperty(value = "财务操作时间", index = 14)
    private String returnTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "财务备注", index = 15)
    private String remark;

    @ApiModelProperty("返现id")
    @TableId(type = IdType.ASSIGN_UUID)
    private String returnMoneyId;

    /**
     * 平台id
     */
    private Integer platformId;

}
