package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@ApiModel("Area对象")
@TableName("t_area")
public class AreaDO {

    /**
     * 标准行政区域代码
     */
    private Long id;

    /**
     * 地域名称
     */
    private String name;

    /**
     * 父节点区域标识
     */
    private Long parentId;

    /**
     * 区域类型1;表示国家;2表示省/直辖市/自治区;3表示省下地市;4表示县
     */
    private Long type;

    /**
     * 具体一个地区得邮编
     */
    private String zip;

    /**
     * 创建时间
     */
    private Date createTime;

}
