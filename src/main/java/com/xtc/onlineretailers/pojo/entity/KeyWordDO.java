package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@TableName("t_key_word")
public class KeyWordDO extends BaseEntity {

    /**
     * 业务名称
     */
    private String serviceName;

    /**
     * 值
     */
    private String keyValue;

    /**
     * -1:禁用   1：启用
     */
    private String isUse;

}
