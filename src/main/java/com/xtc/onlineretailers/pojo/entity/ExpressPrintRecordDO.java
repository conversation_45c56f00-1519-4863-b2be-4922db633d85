package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("快递打印记录")
@TableName("t_express_print_record")
public class ExpressPrintRecordDO extends BaseEntity {

    /**
     * 订单号
     */
    private String platformTradeId;

    /**
     * 打印快递信息
     */
    private String printInfo;

}
