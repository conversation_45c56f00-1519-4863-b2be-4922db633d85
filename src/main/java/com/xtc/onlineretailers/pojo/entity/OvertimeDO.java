package com.xtc.onlineretailers.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@ApiModel("Overtime对象")
@TableName("t_overtime")
public class OvertimeDO {

    /**
     * 超期任务Id
     */
    private Long id;

    /**
     * 订单号
     */
    private String platformTradeId;

    /**
     * 付款时间
     */
    private Date paymentTime;

    /**
     * 用户昵称
     */
    private String buyerNickname;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 客服确认
     */
    private String confirmUser;

    /**
     * 客服确认时间
     */
    private Date confirmTime;

    /**
     * 扫描人
     */
    private String scanner;

    /**
     * 扫描时间
     */
    private Date scanTime;

    /**
     * 超期状态
     */
    private String overtimeStatus;

    /**
     * 客服确认备注
     */
    private String confirmMemo;

}
