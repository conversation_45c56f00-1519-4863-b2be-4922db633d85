package com.xtc.onlineretailers.pojo.bo;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 延宝处理任务参数
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@SuperBuilder
public class ExtendedWarrantyParamBO {

    /**
     * 数据初始化时间
     */
    private LocalDateTime dataInitTime;

    /**
     * 延宝数据的时间偏移量（单位：分钟）
     */
    private Integer offsetMinutes;

    /**
     * 获取偏移量的时间间隔
     *
     * @return Duration
     */
    public Duration getOffsetDuration() {
        return Duration.ofMinutes(this.offsetMinutes);
    }

}
