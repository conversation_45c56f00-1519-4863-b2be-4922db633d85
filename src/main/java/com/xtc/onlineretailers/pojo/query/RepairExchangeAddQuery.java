package com.xtc.onlineretailers.pojo.query;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 换货物料明细
 */
@Getter
@Setter
@NoArgsConstructor
public class RepairExchangeAddQuery {

    /**
     * 物料代码
     */
    private String erpCode;

    /**
     * 物料名称
     */
    private String erpName;

    /**
     * 单位
     */
    private String unit;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 单价
     */
    private BigDecimal price;

}
