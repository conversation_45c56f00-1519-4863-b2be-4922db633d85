package com.xtc.onlineretailers.pojo.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class AgentInfoAddOrUpdateQuery {

    /**
     * 步步高：bbk，小天才：xtc
     */
    @NotBlank
    private String brand;

    /**
     *  代码
     */
    @NotBlank
    private String agentCode;

    /**
     * 名称
     */
    @NotBlank
    private String agentName;

    /**
     * 助理
     */
    @NotBlank
    private String assistant;

    /**
     * 电话
     */
    @NotBlank
    private String phone;

    /**
     * 传真
     */
    private String fax;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 公司名称
     */
    private String company;

    /**
     * 公司地址
     */
    private String address;

    /**
     * 管辖的省份
     */
    private String containProvince;

    /**
     * 该省份下仅管辖的城市
     */
    private String containCity;

    /**
     * 剔除的城市
     */
    private String excludeCity;

    /**
     * 该省份之外还管辖的城市
     */
    private String otherContainCity;

    /**
     * 是否在用 -1:不在用  1：在用
     */
    @NotBlank
    private String isUse;

}
