package com.xtc.onlineretailers.pojo.query;

import lombok.Data;

/**
 * 拦截订单查询参数
 */
@Data
public class InterceptOrderQuery extends PaginationQuery {
    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 是否禁用 0开启 1 禁用
     */
    private Integer forbid;

    /**
     * 电商平台id
     */
    private Integer platformId;
}
