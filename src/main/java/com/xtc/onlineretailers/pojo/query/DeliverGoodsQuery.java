package com.xtc.onlineretailers.pojo.query;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 发货任务查询条件
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class DeliverGoodsQuery extends PaginationQuery {

    /**
     * 电商订单号
     */
    private String platformTradeId;

    /**
     * 付款开始时间
     */
    private String beginTime;

    /**
     * 付款结束时间
     */
    private String endTime;

    /**
     * 是否修改
     */
    private Integer status;

    /**
     * 发货任务id
     */
    private Long treadId;

}


