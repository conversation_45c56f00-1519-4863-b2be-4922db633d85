package com.xtc.onlineretailers.pojo.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 折扣编辑
 */
@Data
public class OrderUpdateDiscountQuery {

    /**
     * 电商交易id
     */
    @NotBlank
    private String platformTradeId;

    /**
     * 电商订单id
     */
    @NotBlank
    private String platformOrderId;

    /**
     * 折扣金额
     */
    @NotNull
    private BigDecimal discount;

}
