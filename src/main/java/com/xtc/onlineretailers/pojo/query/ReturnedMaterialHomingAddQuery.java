package com.xtc.onlineretailers.pojo.query;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 退机好料归位添加
 */
@Setter
@Getter
@NoArgsConstructor
public class ReturnedMaterialHomingAddQuery {

    /**
     * 电商订单号
     */
    private String platformTradeId;

    /**
     * 用户昵称
     */
    private String buyerNickname;

    /**
     * 用户手机
     */
    private String mobile;

    /**
     * 付款时间
     */
    private String paymentTime;

    /**
     * 卖家备注
     */
    private String sellerRemark;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 机型
     */
    private String erpName;

    /**
     * 是否激活
     */
    private Integer isActive;

    /**
     * 是否清除
     */
    private Integer isClear;

    /**
     * 是否仓库归位
     */
    private Integer isStoreHoming;

}
