package com.xtc.onlineretailers.pojo.query;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 交接打印获取信息
 */
@Data
public class TradeHandOverInfoQuery {

    /**
     * 配货单号
     */
    @NotNull
    private List<String> allocationNos;

    // 状态后端设置
    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 仓库状态
     */
    private String warehouseStatus;

    /**
     * 顺丰状态
     */
    private String statusSF;

}
