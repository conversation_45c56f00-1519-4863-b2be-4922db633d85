package com.xtc.onlineretailers.pojo.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 编辑异常
 */
@Data
public class ExceptionUpdateQuery {

    /**
     * 异常id
     */
    @NotBlank
    private String exceptionId;

    /**
     * 异常类型
     */
    @NotBlank
    private String type;

    /**
     * 平台名称
     */
    @NotBlank
    private String platformName;

    /**
     * 原订单号
     */
    @NotBlank
    private String platformTradeId;

    /**
     * 客户淘宝Id
     */
    @NotBlank
    private String buyerNick;

    /**
     * 物料描述
     */
    @NotBlank
    private String shortName;

    /**
     * 原价
     */
    @NotBlank
    private String oldPrice;

    /**
     * 数量
     */
    @NotNull
    private Integer num;

    /**
     * 原因
     */
    @NotBlank
    private String reason;

}
