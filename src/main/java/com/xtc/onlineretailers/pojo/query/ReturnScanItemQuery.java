package com.xtc.onlineretailers.pojo.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 退货扫描信息-扫描项
 */
@Data
public class ReturnScanItemQuery {

    /**
     * 订单明细单号
     */
    @NotBlank
    private String platformOrderId;

    /**
     * 条码
     */
    @NotBlank
    private String barcode;

    /**
     * 退货数量
     */
    @NotNull
    private Integer returnNum;

}
