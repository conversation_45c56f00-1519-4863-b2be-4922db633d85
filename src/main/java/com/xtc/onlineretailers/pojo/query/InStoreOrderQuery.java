package com.xtc.onlineretailers.pojo.query;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class InStoreOrderQuery extends PaginationQuery {

    /**
     * 入库申请单号
     */
    private String orderId;

    /**
     * 仓库编号
     */
    private String storeCode;

    /**
     * 入库单状态 1:申请入库成功  2:顺丰入库成功  3:顺丰入库失败
     */
    private String orderStatus;

    /**
     * 入库开始时间
     */
    private String startDate;

    /**
     * 入库截止时间
     */
    private String endDate;

}
