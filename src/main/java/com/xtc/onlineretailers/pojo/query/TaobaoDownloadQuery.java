package com.xtc.onlineretailers.pojo.query;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 淘宝订单下载
 */
@Getter
@Setter
public class TaobaoDownloadQuery {

    /**
     * 平台id
     */
    @NotNull
    @Min(0)
    private Integer platformId;

    /**
     * 订单号列表
     */
    @NotNull
    @Size(min = 1)
    private List<String> platformTradeIds;

}
