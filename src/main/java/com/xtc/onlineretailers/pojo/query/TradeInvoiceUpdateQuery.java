package com.xtc.onlineretailers.pojo.query;

import lombok.Data;

@Data
public class TradeInvoiceUpdateQuery {

    /**
     * 订单号
     */
    private String tradeId;

    /**
     * 电商平台ID
     */
    private int platformId;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 发票税号
     */
    private String invoiceRegisterNo;

    /**
     * 发票银行账号
     */
    private String invoiceBankAccount;

    /**
     * 发票地址
     */
    private String invoiceAddress;

    /**
     * 发票状态
     */
    private String invoiceStatus;

    /**
     * 纸质发票号码
     */
    private String paperInvoiceNo;

    /**
     * 纸质发票代码
     */
    private String paperInvoiceCode;

    /**
     * 纸质发票开票时间
     */
    private String paperInvoiceTime;

    /**
     * 纸质发票冲红号码
     */
    private String paperInvoiceRedNo;

    /**
     * 纸质发票冲红代码
     */
    private String paperInvoiceRedCode;

    /**
     * 纸质发票冲红时间
     */
    private String paperInvoiceRedTime;

}
