package com.xtc.onlineretailers.pojo.query;

import lombok.Data;

@Data
public class MaterialPriceQuery extends PaginationQuery {

    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 临时对应平台
     */
    private String temporaryPlatform;

    /**
     * 物料代码
     */
    private String productId;

    /**
     * 商品名称
     */
    private String shortName;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 截止日期
     */
    private String endDate;

    /**
     * 是否单发
     */
    private Integer monoFat;

    /**
     * 价格隐藏状态 完成 未完成
     */
    private String priceHidingStatus;

    /**
     * 平台分类
     */
    private String agentCategory;

    /**
     * 是否使用  1 是  -1 否
     */
    private Integer isUse;

}
