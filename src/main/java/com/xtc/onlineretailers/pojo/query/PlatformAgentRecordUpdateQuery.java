package com.xtc.onlineretailers.pojo.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 编辑代销打款
 */
@Data
public class PlatformAgentRecordUpdateQuery {

    /**
     * 主键id
     */
    @NotNull
    private Long id;

    /**
     * 打款金额
     */
    @NotNull
    private BigDecimal payment;

    /**
     * 平台
     */
    @NotBlank
    private String account;

}
