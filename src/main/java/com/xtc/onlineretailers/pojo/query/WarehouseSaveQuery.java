package com.xtc.onlineretailers.pojo.query;

import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 仓库信息保存
 */
@Data
public class WarehouseSaveQuery {

    /**
     * 仓库编码
     */
    @NotBlank
    private String warehouseStorage;

    /**
     * 替代仓库编码
     */
    private String backupCode;

    /**
     * 仓库名称
     */
    @NotBlank
    private String warehouseName;

    /**
     * 月卡
     */
    @NotBlank
    private String monthCard;

    /**
     * 地域
     */
    @NotBlank
    private String locationId;

    /**
     * 关闭时间
     */
    @NotBlank
    private String closeTime;

    /**
     * 配送范围
     */
    @NotBlank
    private String area;

    /**
     * 是否在用 -1：否  1：在用
     */
    @NotBlank
    private String isUse;

    /**
     * 发货策略
     */
    @Valid
    private String deliveryStrategies;
}
