package com.xtc.onlineretailers.pojo.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 客服编辑换货下单信息
 */
@Data
public class ExchangeGoodsUpdateQuery {

    /**
     * 换货id
     */
    @NotBlank
    private String exchangeGoodsId;

    /**
     * 平台名称
     */
    @NotBlank
    private String platformName;

    /**
     * 顾客会员名
     */
    @NotBlank
    private String buyerNick;

    /**
     * 顾客电话
     */
    @NotBlank
    private String telephone;

    /**
     * 顾客反馈问题
     */
    @NotBlank
    private String question;

    /**
     * 换货产品数量
     */
    @NotNull
    private Integer num;

    /**
     * 换货产品名称
     */
    @NotBlank
    private String productName;

    /**
     * 顾客姓名
     */
    @NotBlank
    private String receiverName;

    /**
     * 电商平台交易订单号
     */
    @NotBlank
    private String platformTradeId;

    /**
     * 省份
     */
    @NotBlank
    private String province;

    /**
     * 市
     */
    @NotBlank
    private String city;

    /**
     * 区
     */
    @NotBlank
    private String district;

    /**
     * 地址
     */
    @NotBlank
    private String address;

}
