package com.xtc.onlineretailers.pojo.query;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 批量修改快递公司
 */
@Getter
@Setter
public class BatchUpdateExpressCompany {

    /**
     * 快递公司
     */
    @NotBlank
    private String expressCompany;

    /**
     * 订单号列表
     */
    @NotNull
    @Size(min = 1)
    private List<String> platformTradeIds;

    /**
     * 快递类型
     */
    private String expressType;

}
