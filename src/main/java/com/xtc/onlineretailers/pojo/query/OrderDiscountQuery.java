package com.xtc.onlineretailers.pojo.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 新增折扣
 */
@Data
public class OrderDiscountQuery {



    /**
     * 电商交易id
     */
    @NotBlank
    private String platformTradeId;

    /**
     * 折扣金额
     */
    @NotNull
    private BigDecimal discount;


}
