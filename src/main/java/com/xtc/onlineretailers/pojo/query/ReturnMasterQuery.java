package com.xtc.onlineretailers.pojo.query;

import lombok.Data;

import java.util.List;

/**
 * 获取退货列表查询条件
 */
@Data
public class ReturnMasterQuery extends PaginationQuery {

    /**
     * 平台id
     */
    private List<Integer> platformIds;

    /**
     * 订单号
     */
    private String platformTradeId;

    /**
     * 退货单号
     */
    private String refundId;

    /**
     * 用户昵称/姓名
     */
    private String buyerNickname;

    /**
     * 退货快递单号
     */
    private String expressTrackingNo;

    /**
     * erp过账状态0：未过账  1：已过账  2：不过账
     */
    private Integer erpPostStatus;

    /**
     * 退款状态(已退款,未退款,打回,确认)
     */
    private String refundStatus;

    /**
     * 自动退仓状态
     */
    private String autoRefundStatus;

    /**
     * 退货类型(退货,换货)
     */
    private String type;

    /**
     * 仓库查询:本地:2668
     */
    private String locationId;

    /**
     * 退货原因
     */
    private String reason;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 条码退货订单号
     */
    private List<String> returnPlatformTradeIds;

    /**
     * erp过账开始时间
     */
    private String erpPostTimeBegin;

    /**
     * erp过账截止时间
     */
    private String erpPostTimeEnd;

    /**
     * 付款开始时间
     */
    private String paymentTimeBegin;

    /**
     * 付款截止时间
     */
    private String paymentTimeEnd;

    /**
     * 创建开始时间
     */
    private String beginDate;

    /**
     * 创建截止时间
     */
    private String endDate;

    /**
     * 是否对接成品仓:0表示无需收货;1表示已收货
     */
    private Integer isEndProductStore;

    /**
     * 订单明细中的商品是否全部退货,1表示全部退货,-1表示没有全部退货
     */
    private Integer isAllReturn;

    /**
     * 是否手动确认过账,1表示已确认,0表示未确认
     */
    private Integer erpPostManual;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 部分退款流程状态
     */
    private String partReturnStatus;

    /**
     * 用户拒签 1:是 0:否
     */
    private Integer isUserRefusal;

    /**
     * 是否换货 0:否 1:是
     */
    private Integer isExchange;

    /**
     * 调单单号
     */
    private Integer adjustTrades;

    /**
     * 调单类型
     */
    private String adjustType;

    /**
     * 订单标签，英文逗号分割多个标签
     */
    private String orderTag;

    /**
     * 收货仓库
     */
    private String receiptWarehouse;


}
