package com.xtc.onlineretailers.pojo.query;

import com.xtc.onlineretailers.enums.PresentTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 客服编辑补寄下单信息
 */
@Data
public class SupplyAgainUpdateQuery {

    /**
     * 换货id
     */
    @NotBlank
    private String supplyAgainId;

    /**
     * 礼包id
     */
    private Long giftPackageId;

    /**
     * 平台名称
     */
    @NotBlank
    private String platformName;

    /**
     * 补寄类型
     */
    private String supplyType;

    /**
     * 补寄产品名称
     */
    @NotBlank
    private String productName;

    /**
     * 补寄产品数量
     */
    private String num;

    /**
     * 顾客反馈问题
     */
    @NotBlank
    private String question;

    /**
     * 电商平台交易订单号
     */
    @NotBlank
    private String platformTradeId;

    /**
     * 顾客姓名
     */
    @NotBlank
    private String receiverName;

    /**
     * 顾客会员名
     */
    @NotBlank
    private String buyerNick;

    /**
     * 顾客电话
     */
    @NotBlank
    private String telephone;

    /**
     * 机型
     */
    @NotBlank
    private String productDetail;

    /**
     * 付款时间
     */
    @NotBlank
    private String paymentTime;

    /**
     * 省份
     */
    @NotBlank
    private String province;

    /**
     * 市
     */
    @NotBlank
    private String city;

    /**
     * 区
     */
    private String district;

    /**
     * 地址
     */
    @NotBlank
    private String address;

    /**
     * 赠送类型
     */
    @NotNull
    private PresentTypeEnum presentType;

    /**
     * 赠品详情
     */
    private List<SupplyAgainGiftQuery> supplyAgainGiftQueries;

    /**
     * 是否是系统物料
     */
    @NotNull
    private Integer isSysMateriel;

}
