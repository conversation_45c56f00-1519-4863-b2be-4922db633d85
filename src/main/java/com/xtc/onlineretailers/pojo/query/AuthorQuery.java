package com.xtc.onlineretailers.pojo.query;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

@Getter
@Setter
@ToString
public class AuthorQuery extends PaginationQuery {

    /**
     * 平台id
     */
    private List<Integer> platformIds;
    /**
     * 达人id
     */
    private String authorId;
    /**
     * 达人名称
     */
    private String authorName;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 品牌
     */
    private String brand;

}
