package com.xtc.onlineretailers.pojo.query;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 退换货修改
 */
@Data
public class OrderReturnExchangeQuery {

    /**
     * 电商交易id
     */
    @NotBlank
    private String platformTradeId;

    /**
     * 电商订单id
     */
    @NotBlank
    private String platformOrderId;

    /**
     * ERP物料编码10004
     */
    @NotBlank
    private String erpCode;

    /**
     * 数量
     */
    @NotNull
    private Integer num;

}
