package com.xtc.onlineretailers.pojo.query;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 顺丰出库查询
 */
@Setter
@Getter
@NoArgsConstructor
public class OrderShipQuery extends PaginationQuery {

    /**
     * 交易主表订单号
     */
    private String tradeId;

    /**
     * 顺丰状态
     */
    private String shipStatus;

    /**
     * 快递单号
     */
    private String shipNo;

    /**
     * 顺丰出库开始时间
     */
    private String startDate;

    /**
     * 顺丰出库结束时间
     */
    private String endDate;

    /**
     * 仓库编号
     */
    private String storeCode;

    /**
     * 机器条码
     */
    private String productSn;

    /**
     * 平台id
     */
    private List<Integer> platformId;

}
