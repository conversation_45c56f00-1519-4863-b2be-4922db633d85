package com.xtc.onlineretailers.pojo.query;

import lombok.Getter;
import lombok.Setter;

/**
 * 碎屏保查询
 */
@Getter
@Setter
public class ScreenInsuranceQuery extends PaginationQuery {

    /**
     * 订单号
     */
    private String platformTradeId;

    /**
     * 用户昵称
     */
    private String buyerNickname;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 开始购买时间
     */
    private String buyTimeStart;

    /**
     * 截止购买时间
     */
    private String buyTimeEnd;

    /**
     * 状态
     */
    private String status;

    /**
     * 订单平台
     */
    private String platformName;

    /**
     * 主要商品
     */
    private String productDetail;

}
