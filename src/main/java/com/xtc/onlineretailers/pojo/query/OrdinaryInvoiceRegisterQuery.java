package com.xtc.onlineretailers.pojo.query;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 普票登记分页查询
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class OrdinaryInvoiceRegisterQuery extends PaginationQuery {

    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 电商订单号
     */
    private String platformTradeId;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 确认操作人
     */
    private String confirmOperator;

    /**
     * 确认时间(开始)
     */
    private String confirmTimeStart;

    /**
     * 确认时间(结束)
     */
    private String confirmTimeEnd;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 登记时间(开始)
     */
    private String createTimeStart;

    /**
     * 登记时间(结束)
     */
    private String createTimeEnd;

}
