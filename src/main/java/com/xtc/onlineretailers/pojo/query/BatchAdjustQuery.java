package com.xtc.onlineretailers.pojo.query;

import com.xtc.onlineretailers.enums.BatchAdjustStatus;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class BatchAdjustQuery extends PaginationQuery {

    /**
     * 状态
     */
    private BatchAdjustStatus status;

    /**
     * 调单任务
     */
    private String taskName;

    /**
     * 平台
     */
    private String platformName;

    /**
     * 订单号
     */
    private String platformTradeId;

}
