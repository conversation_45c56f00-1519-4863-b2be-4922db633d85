package com.xtc.onlineretailers.pojo.query;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.Valid;
import java.util.List;

/**
 * 代销换货
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AgentSaleExchangeGoodsQuery extends GenerateExchangeGoodsQuery {

    /**
     * 退货明细
     */
    @Valid
    private List<AgentSaleReturnDetailQuery> agentSaleReturnDetailQueries;

}
