package com.xtc.onlineretailers.pojo.query;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class TradeErpQuery {

    private List<String> tradeTypeList;

    private List<String> orderStatusList;

    private Integer platformId;

    /**
     * 顺丰操作状态  -1：非顺丰出货 1:顺丰出货  2:抛转到顺丰成功 3:顺丰出库成功 4:顺风取消出库成功
     */
    private String statusSf;

    /**
     * ERP仓库代码   本地:005   顺丰:006
     */
    private String warehouseStorage;

    /**
     * erp过账状态
     */
    private Integer erpPostStatus;

    /**
     * 是否手动确认过账  1：需要
     */
    private Integer erpPostManual;

    /**
     * 发票状态
     */
    private List<String> invoiceStatus;

    /**
     * 仓库状态
     */
    private List<String> warehouseStatus;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;
}
