package com.xtc.onlineretailers.pojo.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xtc.onlineretailers.pojo.vo.InStoreProductVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class InStoreOrderAddQuery {

    /**
     * 入库申请单号
     */
    private String orderId;

    @ApiModelProperty("申请入库时间")
    @NotNull(message = "入库时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;

    @ApiModelProperty("预计送达时间")
    @NotNull(message = "预计送达时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expectedDeliveryTime;

    @ApiModelProperty("仓库编号")
    @NotNull(message = "仓库编号不能为空")
    private String storeCode;

    @ApiModelProperty("仓库名称")
    @NotNull(message = "仓库编号不能为空")
    private String storeName;

    @ApiModelProperty("入库货品列表")
    @NotNull(message = "入库货品不能为空")
    private List<InStoreProductVO> productList;

    /**
     * 备注
     */
    private String memo;

}
