package com.xtc.onlineretailers.pojo.query;

import com.xtc.onlineretailers.enums.PresentTypeEnum;
import com.xtc.onlineretailers.enums.SupplySyncStatus;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 补寄分页查询条件
 */
@Getter
@Setter
public class SupplyAgainQuery extends PaginationQuery {

    /**
     * 平台
     */
    private String platformName;

    /**
     * 平台id
     */
    private List<Integer> platformIds;

    /**
     * 补寄类型
     */
    private String supplyType;

    /**
     * 客服人员
     */
    private String serviceName;

    /**
     * 售后状态
     */
    private String status;

    /**
     * 是否抛单
     */
    private Integer isThrow;

    /**
     * 补寄确认
     */
    private Integer isSendAgain;

    /**
     * 快递公司
     */
    private String expressCompany;

    /**
     * 换货产品名称
     */
    private String productName;

    /**
     * 会员名/姓名/电话/快递单号/订单号
     */
    private String keyword;

    /**
     * 会员名
     */
    private String buyerNick;

    /**
     * 姓名
     */
    private String receiverName;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 快递单号
     */
    private String expressTrackingNo;

    /**
     * 订单号
     */
    private String platformTradeId;

    /**
     * 按创建时间查询的开始时间
     */
    private String beginCreateTime;

    /**
     * 按创建时间查询的结束时间
     */
    private String endCreateTime;

    /**
     * 按寄出日期查询的开始时间
     */
    private String beginSendTime;

    /**
     * 按寄出日期查询的结束时间
     */
    private String endSendTime;

    /**
     * 是否导出明细
     */
    private Boolean exportDetail;

    /**
     * 是否系统物料
     */
    private Integer system;

    /**
     * 同步状态
     */
    private SupplySyncStatus syncStatus;

    /**
     * 赠送类型
     */
    private PresentTypeEnum presentType;

}
