package com.xtc.onlineretailers.pojo.command;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 修改礼包
 */
@Setter
@Getter
@ToString
public class GiftPackageUpdateCmd {

    /**
     * 礼包id
     */
    @NotNull
    private Long id;

    /**
     * 礼包名称
     */
    @NotBlank
    private String giftPackageName;

    /**
     * 礼包明细
     */
    @Valid
    @NotEmpty
    private List<GiftPackageUpdateDetailCmd> giftPackageDetails;

}
