package com.xtc.onlineretailers.pojo.dto;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import lombok.Getter;
import lombok.Setter;

/**
 * EMS下单xml参数
 */
@Getter
@Setter
@XStreamAlias("printData")
public class EMSCreateOrderDTO {

    /**
     * EMS参数
     */
    @XStreamAlias("bigAccountDataId")
    private String bigAccountDataId;

    /**
     * EMS参数
     */
    @XStreamAlias("businessType")
    private int businessType;

    /**
     * 运单号（快递单号）
     */
    @XStreamAlias("billno")
    private String billno;

    /**
     * 寄件人姓名
     */
    @XStreamAlias("scontactor")
    private String scontactor;

    /**
     * 寄件人电话
     */
    @XStreamAlias("scustMobile")
    private String scustMobile;

    /**
     * 寄件人地址
     */
    @XStreamAlias("scustAddr")
    private String scustAddr;

    /**
     * 寄件人公司
     */
    @XStreamAlias("scustComp")
    private String scustComp;

    /**
     * 收件人姓名
     */
    @XStreamAlias("tcontactor")
    private String tcontactor;

    /**
     * 收件人电话
     */
    @XStreamAlias("tcustMobile")
    private String tcustMobile;

    /**
     * 收件人地址
     */
    @XStreamAlias("tcustAddr")
    private String tcustAddr;

    /**
     * 收件人省份
     */
    @XStreamAlias("tcustProvince")
    private String tcustProvince;

    /**
     * 收件人城市
     */
    @XStreamAlias("tcustCity")
    private String tcustCity;

    /**
     * 收件人区县
     */
    @XStreamAlias("tcustCounty")
    private String tcustCounty;

}
