package com.xtc.onlineretailers.pojo.dto;

import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.enGet.ApiResult;
import com.jd.open.api.sdk.domain.order.OrderQueryJsfService.response.enGet.OrderSearchInfo;
import lombok.Data;

import java.util.List;

/**
 * 京东訂單列表实体类
 */
@Data
public class JingDongReturnDto   {
     ApiResult  apiResult;
    List<OrderSearchInfo> orderInfoList;
    Integer orderTotal;

}
