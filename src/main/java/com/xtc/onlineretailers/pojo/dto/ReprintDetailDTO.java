package com.xtc.onlineretailers.pojo.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 补打明细
 */
@Getter
@Setter
public class ReprintDetailDTO {

    /**
     * ERP物料编码
     */
    private String erpCode;

    /**
     * 物料描述
     */
    private String erpName;

    /**
     * 物料类型 1：机器   2：配件  3：虚拟物料
     */
    private Integer materialType;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 单位
     */
    private String unit;

    /**
     * 发货条码
     */
    private String shipBarcode;

    /**
     * 明细类型 1:正常明细 2:赠品明细  3:折扣明细  4：运费明细
     */
    private Integer orderType;

}
