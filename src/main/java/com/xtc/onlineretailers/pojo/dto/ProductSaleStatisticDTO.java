package com.xtc.onlineretailers.pojo.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 销售统计实体
 */
@Setter
@Getter
public class ProductSaleStatisticDTO {

    /**
     * 唯一标识
     */
    private String id;

    /**
     * 平台id
     */
    private Integer platformId;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 电话手表销售量
     */
    private int watchSaleCount;

    /**
     * 家教机销售量
     */
    private int machineSaleCount;

    /**
     * 扫描笔销售量
     */
    private int scanPenSaleCount;

    /**
     * 耳机销售量
     */
    private int earphoneSaleCount;

    /**
     * 护眼平板销售量
     */
    private int padSaleCount;

    /**
     * 表带销售量
     */
    private int watchBandSaleCount;

    /**
     * 配件销售量
     */
    private int partSaleCount;

    /**
     * 合计销售量
     */
    private int totalSaleCount;

}
