package com.xtc.onlineretailers.pojo.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 单个销售平台详情统计实体
 */
@Setter
@Getter
public class ProductSaleDetailStatisticDTO {

    /**
     * 唯一标识
     */
    private String id;

    /**
     * 销售日期
     */
    private String paymentTime;

    /**
     * 电话手表销售量
     */
    private BigDecimal watchSaleCount;

    /**
     * Q1C销售量
     */
    private BigDecimal q1cSaleCount;

    /**
     * Q1C机型占电话手表总销量比率
     */
    private String q1cInTotal;

    /**
     * Q1A销售量
     */
    private BigDecimal q1aSaleCount;

    /**
     * Q1A机型占电话手表总销量比率
     */
    private String q1aInTotal;

    /**
     * D3销售量
     */
    private BigDecimal d3SaleCount;

    /**
     * D3机型占电话手表总销量比率
     */
    private String d3InTotal;

    /**
     * Z5A销售量
     */
    private BigDecimal z5aSaleCount;

    /**
     * Z5A机型占电话手表总销量比率
     */
    private String z5aInTotal;

    /**
     * Z6销售量
     */
    private BigDecimal z6SaleCount;

    /**
     * Z6机型占电话手表总销量比率
     */
    private String z6InTotal;

    /**
     * Z6巅峰版销售量
     */
    private BigDecimal z6PeakSaleCount;

    /**
     * Z6巅峰版机型占电话手表总销量比率
     */
    private String z6PeakInTotal;

    /**
     * Z7销售量
     */
    private BigDecimal z7SaleCount;

    /**
     * Z7机型占电话手表总销量比率
     */
    private String z7InTotal;

    /**
     * 耳机销售量
     */
    private BigDecimal earphoneSaleCount;

    /**
     * 护眼平板销售量
     */
    private BigDecimal padSaleCount;

    /**
     * 表带销售量
     */
    private BigDecimal watchBandSaleCount;

    /**
     * 配件销售量
     */
    private BigDecimal partSaleCount;

//    @ApiModelProperty("合计销售量")
//    private BigDecimal totalSaleCount;

}
