package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * erp过账自检
 */
@Data
public class ErpSelfCheckVO {

    @ApiModelProperty("组织")
    @ExcelProperty(value = "组织", index = 0)
    private String organization;

    @ApiModelProperty("电商订单号")
    @ExcelProperty(value = "电商订单号", index = 1)
    private String platformTradeId;

    @ApiModelProperty("ERP订单号")
    @ExcelProperty(value = "ERP订单号", index = 2)
    private String erpId;

    @ApiModelProperty("客户")
    @ExcelProperty(value = "客户", index = 3)
    private String customerNumber;

    @ApiModelProperty("客户名称")
    @ExcelProperty(value = "客户名称", index = 4)
    private String customerName;

    @ApiModelProperty("状态")
    @ExcelProperty(value = "状态", index = 5)
    private String status;

    @ApiModelProperty("物料")
    @ExcelProperty(value = "物料", index = 6)
    private String productId;

    @ApiModelProperty("描述")
    @ExcelProperty(value = "描述", index = 7)
    private String shortName;

    @ApiModelProperty("数量")
    @ExcelProperty(value = "数量", index = 8)
    private Integer num;

}
