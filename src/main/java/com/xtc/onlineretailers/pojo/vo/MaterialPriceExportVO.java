package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xtc.onlineretailers.excel.converters.MaterialPriceExportConverter;
import com.xtc.onlineretailers.excel.converters.MaterialPriceStatusExportConverter;
import com.xtc.onlineretailers.excel.converters.YesOrNoConverter;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class MaterialPriceExportVO {

    @ExcelProperty("平台id")
    private Integer platformId;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("临时平台")
    private String temporaryPlatform;

    @ExcelProperty("物料代码")
    private String productId;

    @ExcelProperty("商品名称")
    private String shortName;

    @ExcelProperty(value = "价格", converter = MaterialPriceExportConverter.class)
    private BigDecimal price;

    @ExcelProperty("单位")
    private String unitType;

    @ExcelProperty(value = "是否是机器", converter = YesOrNoConverter.class)
    private Integer productType;

    @ExcelProperty(value = "是否含电池", converter = YesOrNoConverter.class)
    private Integer hasBattery;

    @ExcelProperty(value = "是否在用", converter = YesOrNoConverter.class)
    private Integer isUse;

    @ExcelProperty("创建时间")
    private String createTime;

    @ExcelProperty(value = "价格隐藏状态", converter = MaterialPriceStatusExportConverter.class)
    private String priceHidingStatus;

}
