package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.xtc.onlineretailers.excel.converters.DateTimeConverter;
import com.xtc.onlineretailers.excel.converters.IsOrNotConverter;
import lombok.Data;

import java.util.Date;

/**
 * 仓库报表Excel导出
 */
@Data
public class WarehouseReportFormsExportVO {

    @ExcelProperty("所属平台")
    private String platformName;

    @ExcelProperty("订单号")
    private String platformTradeId;

    @ExcelProperty("配货单号")
    private String allocationNo;

    @ExcelProperty("交接单号")
    private String handOverNo;

    @ExcelProperty("快递公司")
    private String expressCompany;

    @ExcelProperty("快递单号")
    private String expressTrackingNo;

    @ExcelProperty("订单状态")
    private String statusOrder;

    @ExcelProperty("仓库状态")
    private String warehouseStatus;

    @ExcelProperty("下单时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;

    @ExcelProperty("买家姓名")
    private String receiverName;

    @ExcelProperty("ERP物料编码")
    private String erpCode;

    @ExcelProperty("商品描述")
    private String erpName;

    @ExcelProperty("物料类型")
    private String materialType;

    @ExcelProperty("数量")
    private Integer num;

    @ExcelProperty("操作人")
    private String sorter;

    @ExcelProperty("条码")
    private String barcode;

    @ExcelProperty("淘宝id")
    private String buyerNickname;

    @ExcelProperty("扫码时间")
    private String scanTime;

    @ExcelProperty("抛单时间")
    private String orderConfirmTime;

    @ExcelProperty("配货时间")
    private String allocationTime;

    @ExcelProperty(value = "是否揽收", converter = IsOrNotConverter.class)
    private Integer isCollect;

    @ExcelProperty("揽收时间")
    private String collectTime;

    @ExcelProperty(value = "发货时间", converter = DateTimeConverter.class)
    private Date shippingTime;

}
