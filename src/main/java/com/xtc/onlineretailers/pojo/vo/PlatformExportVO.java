package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xtc.onlineretailers.excel.converters.IsOrNotConverter;
import com.xtc.onlineretailers.excel.converters.YesOrNoConverter;
import lombok.Data;

/**
 * 平台信息导出
 */
@Data
public class PlatformExportVO {

    @ExcelProperty("品牌名称")
    private String brandName;

    @ExcelProperty("电商平台名称")
    private String platformName;

    @ExcelProperty("电商平台类型")
    private String platformType;

    @ExcelProperty("店铺名称")
    private String shopName;

    @ExcelProperty("EPR客户ID")
    private String customerId;

    @ExcelProperty("EPR客户代码")
    private String customerCode;

    @ExcelProperty(value = "是否在用", converter = YesOrNoConverter.class)
    private Integer isUse;

    @ExcelProperty("入驻时间")
    private String settledTime;

    @ExcelProperty("结束时间")
    private String endTime;

    @ExcelProperty(value = "是否代理", converter = IsOrNotConverter.class)
    private Integer isAgent;

    @ExcelProperty("代销类别")
    private String agentCategory;

    @ExcelProperty("发票抬头")
    private String invoiceTitle;

    @ExcelProperty("发票税号")
    private String invoiceRegisterNo;

    @ExcelProperty("发票银行账号")
    private String invoiceBankAccount;

    @ExcelProperty("发票地址")
    private String invoiceAddress;

    @ExcelProperty("发票收件地址")
    private String invoiceReceiveAddress;

    @ExcelProperty("备注")
    private String memo;

    @ExcelProperty("市")
    private String city;

    @ExcelProperty("详细地址")
    private String detail;

    @ExcelProperty("区")
    private String district;

    @ExcelProperty("省")
    private String province;

    @ExcelProperty("姓名")
    private String name;

    @ExcelProperty("电话")
    private String mobile;

    /**
     * 是否返利,1表示是,0表示否
     */
    private Integer isFanli;
}
