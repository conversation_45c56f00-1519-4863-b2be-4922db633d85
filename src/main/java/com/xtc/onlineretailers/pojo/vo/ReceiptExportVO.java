package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xtc.onlineretailers.excel.converters.IsOrNotConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 退货信息Excel导出
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReceiptExportVO {

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("收货时间")
    private String shippingDate;

    @ExcelProperty("退货类型")
    private String returnType;

    @ExcelProperty("订单号")
    private String platformTradeId;

    @ExcelProperty("退货单号")
    private String refundId;

    @ExcelProperty("退货快递")
    private String refundExpressNo;

    @ExcelProperty("收件人姓名")
    private String receiverName;

    @ExcelProperty("退货erp单号")
    private String erpId;

    @ExcelProperty("退货过账时间")
    private String erpPostTime;

    @ExcelProperty("成品仓")
    private String isEndProductStore;

    @ExcelProperty("顺丰69码")
    private String sfBarcode;

    @ExcelProperty("物料编码")
    private String erpCode;

    @ExcelProperty("收货条码")
    private String productSn;

    @ExcelProperty("物料描述")
    private String erpName;

    @ExcelProperty("发货数量")
    private Integer num;

    @ExcelProperty("退货数量")
    private Integer returnNum;

    @ExcelProperty("退货原因")
    private String reason;

    @ExcelProperty("部分退货")
    private String partReturnStatus;

    @ExcelProperty(value = "用户拒签", converter = IsOrNotConverter.class)
    private Integer isUserRefusal;

    @ExcelProperty(value = "是否换货", converter = IsOrNotConverter.class)
    private Integer isExchange;

    @ExcelProperty("原订单卖家备注")
    private String sellerRemark;

    @ExcelProperty("国补标识")
    private String orderTag;

    @ExcelProperty("收货仓库")
    private String receiptWarehouse;

    @ExcelProperty("扫描人")
    private String scanner;

}
