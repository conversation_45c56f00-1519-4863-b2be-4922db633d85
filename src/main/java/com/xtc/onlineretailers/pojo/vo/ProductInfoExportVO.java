package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xtc.onlineretailers.excel.converters.*;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 商品信息
 */
@Getter
@Setter
public class ProductInfoExportVO {

    @ExcelProperty("平台类型")
    private String platformType;

    @ExcelProperty("机型分类")
    private String modelSort;

    @ExcelProperty(value = "物料类型", converter = ProductMaterialTypeConverter.class)
    private Integer materialType;

    @ExcelProperty("商品号-物料代码")
    private String productId;

    @ExcelProperty("替换码")
    private String replaceProductId;

    @ExcelProperty("商品名称")
    private String shortName;

    @ExcelProperty("单位")
    private String unitType;

    @ExcelProperty("商品单价")
    private BigDecimal erpPrice;

    @ExcelProperty("最低到手价")
    private BigDecimal minPrice;

    @ExcelProperty(value = "是否使用", converter = YesOrNoConverter.class)
    private Integer isUse;

    @ExcelProperty(value = "抛单类型", converter = ProductOrderFlowTypeConverter.class)
    private String orderFlowType;

    @ExcelProperty(value = "是否成品", converter = YesOrNoConverter.class)
    private Integer isEndProduct;

    @ExcelProperty(value = "是否有电池", converter = YesOrNoConverter.class)
    private Integer hasBattery;

    @ExcelProperty(value = "是否预售物料", converter = YesOrNoConverter.class)
    private Integer isAdvanceSale;

    @ExcelProperty(value = "顺丰出货", converter = YesOrNoConverter.class)
    private Integer isSf;

    @ExcelProperty("顺丰对应69码")
    private String barcode;

    @ExcelProperty(value = "是否录条码", converter = YesOrNoConverter.class)
    private Integer hasProductCode;

    @ExcelProperty(value = "是否图书", converter = YesOrNoConverter.class)
    private Integer isBook;

    @ExcelProperty("税务编码")
    private String taxCode;

    @ExcelProperty("税务名称")
    private String taxName;

    @ExcelProperty("所属事业部:电话手表事业部 ; 教育电子事业部")
    private String businessDepart;

    @ExcelProperty("条码前缀")
    private String prefix;

    @ExcelProperty("第一层分类")
    private String firstClass;

    @ExcelProperty("第一层排序,只能是数字")
    private String firstSort;

    @ExcelProperty("第二层分类")
    private String secondClass;

    @ExcelProperty("第二层排序,只能是数字")
    private String secondSort;

    @ExcelProperty(value = "创建时间", converter = DateTimeConverter.class)
    private Date addTime;

    @ExcelProperty("规格")
    private String specification;

    @ExcelProperty("补寄展示名称")
    private String supplyAgainDisplayName;

    @ExcelProperty("重量")
    private String weight;

    @ExcelProperty("体积")
    private String volume;

    @ExcelProperty("税率")
    private BigDecimal taxRate;

    @ExcelProperty(value = "是否碎屏宝", converter = YesOrNoConverter.class)
    private Integer isInsurance;

    @ExcelProperty(value = "是否隐藏", converter = IsOrNotConverter.class)
    private Integer isHidden;

    @ExcelProperty("分类")
    private String materialCategory;

    @ExcelProperty("展示名称")
    private String materialDisplayName;

    @ExcelProperty("仓库代码")
    private String warehouseCode;

    @ExcelProperty("机型归类")
    private String machineType;
}
