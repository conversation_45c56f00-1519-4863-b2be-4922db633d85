package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xtc.onlineretailers.excel.converters.ReturnRefundStatusConverter;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 退货退款导出
 */
@Data
public class ReturnRefundExportVO {

    @ExcelProperty("电商平台名称")
    private String platformName;

    @ExcelProperty("电商订单号")
    private String platformTradeId;

    @ExcelProperty("买家昵称")
    private String buyerNickname;

    @ExcelProperty("电话号码")
    private String tel;

    @ExcelProperty("发货快递单号")
    private String expressTrackingNo;

    @ExcelProperty("退款申请时间")
    private String refundCreateTime;

    @ExcelProperty("退款完成时间")
    private String refundModifiedTime;

    @ExcelProperty("支付金额")
    private BigDecimal payment;

    @ExcelProperty("退款金额")
    private BigDecimal refundFee;

    @ExcelProperty(value = "状态", converter = ReturnRefundStatusConverter.class)
    private Integer status;

    @ExcelProperty("收货人")
    private String receiver;

    @ExcelProperty("收货时间")
    private String receiveTime;

    @ExcelProperty("备注")
    private String memo;

    @ExcelProperty("更新时间")
    private String updateTime;

    @ExcelProperty("创建时间")
    private String createTime;

}
