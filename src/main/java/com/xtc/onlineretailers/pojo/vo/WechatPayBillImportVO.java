package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 微信支付账单Excel导入
 */
@Data
public class WechatPayBillImportVO {

    @ExcelProperty("交易时间")
    private String tradeTime;

    @ExcelProperty("商户号")
    private String bizAccount;

    @ExcelProperty("微信订单号")
    private String platformOrderNo;

    @ExcelProperty("商户订单号")
    private String orderNo;

    @ExcelProperty("用户标识")
    private String buyerId;

    @ExcelProperty("交易状态")
    private String tradeState;

    @ExcelProperty("应结订单金额")
    private String paymentAmount;

    @ExcelProperty("代金券金额")
    private String discountAmount;

    @ExcelProperty("微信退款单号")
    private String platformRefundId;

    @ExcelProperty("商户退款单号")
    private String refundId;

    @ExcelProperty("退款金额")
    private String refundAmount;

    @ExcelProperty("充值券退款金额")
    private String discountRefundAmount;

    @ExcelProperty("退款状态")
    private String refundState;

    @ExcelProperty("商品名称")
    private String productName;

    @ExcelProperty("手续费")
    private String feeAmount;

    @ExcelProperty("费率")
    private String feeRate;

    @ExcelProperty("订单金额")
    private String orderAmount;

    @ExcelProperty("申请退款金额")
    private String applyRefundAmount;

    @ExcelProperty("费率备注")
    private String feeRateMemo;

}
