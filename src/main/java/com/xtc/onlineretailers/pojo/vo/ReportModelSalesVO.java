package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class ReportModelSalesVO {
    @ExcelProperty("销量时间")
    private String salesTime;

    @ExcelProperty("上报时间")
    private String reportTime;

    @ExcelProperty("机型名称")
    private String modelName;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("物料编码")
    private String erpCode;

    @ExcelProperty("销量")
    private String sales;

    @ExcelProperty("上报状态")
    private String status;

    @ExcelProperty("上报代理")
    private String agency;

    @ExcelProperty("代理编号")
    private String agencyNumber;

    @ExcelProperty("品牌名称")
    private String brandName;






}
