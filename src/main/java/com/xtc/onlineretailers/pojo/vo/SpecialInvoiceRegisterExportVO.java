package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 专票登记导出实体
 */
@Data
@NoArgsConstructor
public class SpecialInvoiceRegisterExportVO {

    @ExcelProperty("平台id")
    private Integer platformId;

    @ExcelProperty("平台名称")
    private String platformName;

    @ExcelProperty("电商订单号")
    private String platformTradeId;

    @ExcelProperty("买家昵称")
    private String buyerNickname;

    @ExcelProperty("收货人姓名")
    private String receiverName;

    @ExcelProperty("登记时间")
    private String registerTime;

    @ExcelProperty("登记客服名称")
    private String registerServiceStaff;

    @ExcelProperty("开票明细")
    private String invoiceDetail;

    @ExcelProperty("发票抬头")
    private String invoiceTitle;

    @ExcelProperty("发票税号")
    private String invoiceRegisterNo;

    @ExcelProperty("发票银行账号")
    private String invoiceBankAccount;

    @ExcelProperty("发票地址")
    private String invoiceAddress;

    @ExcelProperty("收货人手机号")
    private String receiverMobile;
//
//    @ExcelProperty("收货人电话")
//    private String receiverPhone;

    @ExcelProperty("收货人省份")
    private String receiverProvince;

    @ExcelProperty("收货人城市")
    private String receiverCity;

    @ExcelProperty("收货人区县")
    private String receiverDistrict;

    @ExcelProperty("收货人地址")
    private String receiverAddress;

    @ExcelProperty("支付总金额")
    private BigDecimal payment;

    @ExcelProperty("支付时间")
    private String paymentTime;

    @ExcelProperty("单位")
    private String unit;

    @ExcelProperty("备注")
    private String memo;

    @ExcelProperty("开票人")
    private String drawer;

    @ExcelProperty("审核人")
    private String auditor;

    @ExcelProperty("税率")
    private BigDecimal taxRate;

    @ExcelProperty("开票类型")
    private String invoiceType;

    @ExcelProperty("开票状态")
    private String invoiceStatus;

    @ExcelProperty("操作人")
    private String operator;

    @ExcelProperty("操作时间")
    private String operateTime;

    @ExcelProperty("发票号")
    private String invoiceNo;

    @ExcelProperty("发票代码")
    private String invoiceCode;

    @ExcelProperty("快递状态")
    private String expressStatus;

    @ExcelProperty("快递单号")
    private String expressNo;

    @ExcelProperty("邮寄人")
    private String expressSender;

    @ExcelProperty("邮寄时间")
    private String expressTime;

}
