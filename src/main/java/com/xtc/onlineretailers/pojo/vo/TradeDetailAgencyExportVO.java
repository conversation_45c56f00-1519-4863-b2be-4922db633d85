package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.xtc.onlineretailers.excel.converters.IsOrNotConverter;
import lombok.Data;

import java.util.Date;

/**
 * 订单信息Excel导出包含明细信息
 */
@Data
public class TradeDetailAgencyExportVO {

    @ExcelProperty("所属平台")
    private String platformName;

    @ExcelProperty("创建时间")
    private String createTime;

    @ExcelProperty("付款时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;

    @ExcelProperty("抛单时间")
    private String orderConfirmTime;

    @ExcelProperty("发货时间")
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date shippingTime;

    @ExcelProperty("揽收时间")
    private String collectTime;

    @ExcelProperty("快递公司")
    private String expressCompany;

    @ExcelProperty("快递单号")
    private String expressTrackingNo;

    @ExcelProperty("过账日期")
    private Date erpPostTime;

    @ExcelProperty("电商订单号")
    private String platformTradeId;

    @ExcelProperty("原订单号")
    private String oldPlatformTradeId;

    @ExcelProperty("erp订单号")
    private String erpId;

    @ExcelProperty("微信支付单号")
    private String appTradeSn;

    @ExcelProperty("总收入")
    private String priceTotal;

    @ExcelProperty("商城积分")
    private String pointPlatform;

    @ExcelProperty("集分宝")
    private String pointAlipayJf;

    @ExcelProperty("顾客现付")
    private String payment;

    // 字符串传文字
    @ExcelProperty("顺丰操作")
    private String statusSf;

    // 仓库代码转仓库名称
    @ExcelProperty("顺丰仓库")
    private String warehouseStorage;

    @ExcelProperty("物料代码")
    private String erpCode;

    @ExcelProperty("商品名称")
    private String erpName;

    @ExcelProperty("商品数量")
    private Integer num;

    // 数字转文字
    @ExcelProperty("明细状态")
    private String orderTypeName;

    @ExcelProperty("用户ID")
    private String buyerNickname;

    @ExcelProperty("电子发票号")
    private String electronicInvoiceNo;

    @ExcelProperty("发票抬头")
    private String invoiceTitle;

    // 字符串转文字
    @ExcelProperty("订单状态")
    private String statusOrder;

    // 字符串转文字
    @ExcelProperty("订单类型")
    private String type;

    // 字符串转文字
    @ExcelProperty("发票状态")
    private String invoiceStatus;

    @ExcelProperty("开票人")
    private String receiverName;

    @ExcelProperty("收件人省份")
    private String receiverProvince;

    @ExcelProperty("买家备注")
    private String buyerRemark;

    @ExcelProperty("卖家备注")
    private String sellerRemark;

    @ExcelProperty("退货原因或调单备注")
    private String reason;

    @ExcelProperty("收件人城市")
    private String receiverCity;

    @ExcelProperty("收件人区县")
    private String receiverDistrict;

    @ExcelProperty("收件人地址")
    private String receiverAddress;

    @ExcelProperty(value = "预售", converter = IsOrNotConverter.class)
    private Integer statusReserve;

    @ExcelProperty("品类")
    private String modelSort;

    @ExcelProperty("快递类型")
    private String expressType;

    @ExcelProperty("达人id")
    private String authorId;

    @ExcelProperty("达人信息")
    private String authorName;

    @ExcelProperty("订单标签")
    private String orderTag;

}
