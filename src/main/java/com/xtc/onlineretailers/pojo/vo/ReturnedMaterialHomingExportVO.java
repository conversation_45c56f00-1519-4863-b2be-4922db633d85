package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 好机归位报表导出
 */
@Data
public class ReturnedMaterialHomingExportVO {
    @ExcelProperty(value = "订单Id", index = 0)
    private String platformTradeId;

    @ExcelProperty(value = "创建时间", index = 1)
    private String createTime;

    @ExcelProperty(value = "付款时间", index = 2)
    private String paymentTime;

    @ExcelProperty(value = "用户昵称", index = 3)
    private String buyerNickname;

    @ExcelProperty(value = "条码", index = 5)
    private String barcode;

    @ExcelProperty(value = "机型", index = 6)
    private String erpName;

    @ExcelProperty(value = "是否激活", index = 7)
    private String isActiveStr;

    @ExcelProperty(value = "是否清除", index = 8)
    private String isClearStr;

    @ExcelProperty(value = "是否仓库归位", index = 9)
    private String isStoreHomingStr;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人", index = 10)
    private String creator;

    @ExcelProperty(value = "卖家备注", index = 11)
    private String sellerRemark;

}
