package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * erp临时报表记录
 */
@ToString
@Getter
@Setter
public class ErpTempReportFormVO {

    @ExcelProperty(value = "电商订单号", index = 0)
    private String platformTradeId;

    @ExcelProperty(value = "客户代码", index = 1)
    private String customerNumber;

    @ExcelProperty(value = "过账时间", index = 2)
    private String erpPostTime;

    @ExcelProperty(value = "订单类型", index = 3)
    private String tradeType;

    @ExcelProperty(value = "ERP订单号", index = 4)
    private String erpOrderNumber;

    @ExcelProperty(value = "物料代码", index = 5)
    private String erpCode;

    @ExcelProperty(value = "数量", index = 6)
    private Integer num;

    @ExcelProperty(value = "单价", index = 7)
    private BigDecimal price;

    @ExcelProperty(value = "明细类型", index = 8)
    private String orderType;

    @ApiModelProperty("折扣")
    @ExcelProperty(value = "折扣", index = 9)
    private String discount;

}
