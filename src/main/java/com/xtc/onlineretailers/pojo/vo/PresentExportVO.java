package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class PresentExportVO {
    @ExcelProperty("物料代码")
    private String productId;

    @ExcelProperty("物料名称")
    private String productName;

    @ExcelProperty("赠品物料代码")
    private String presentProductId;

    @ExcelProperty("赠品物料名称")
    private String presentName;

    @ExcelProperty("赠送数量")
    private Integer num;

    @ExcelProperty("开始时间")
    private String startTime;

    @ExcelProperty("结束时间")
    private String endTime;

    @ExcelProperty("活动主题")
    private String activityName;

    @ExcelProperty("电商平台id")
    private Integer platformId;

    @ExcelProperty("电商平台名称")
    private String platformName;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("达人信息")
    private String authorName;

    @ExcelProperty("达人id")
    private String authorId;

    @ExcelProperty("停止赠送方式，永不停止：NEVER_END，赠品库存为0自动停止：END_BY_STOCK，按截止时间：END_BY_TIME")
    private String endMode;

}
