package com.xtc.onlineretailers.pojo.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 临时品类分类物料
 */
@Data
public class TempCategoryVO {

    /**
     * 物料代码
     */
    private String erpCode;

    /**
     * 机型分类:家教机，电话手表，护眼平板
     */
    private String modelSort;

    /**
     * 物料类型 1：机器   2：配件  3：虚拟物料
     */
    private Integer materialType;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 金额
     */
    private BigDecimal priceTotal;
}
