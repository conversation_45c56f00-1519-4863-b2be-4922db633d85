package com.xtc.onlineretailers.pojo.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@NoArgsConstructor
public class OldForNewExportVO {

    @ExcelProperty("用户昵称")
    private String buyerNickname;

    @ExcelProperty("新订单号")
    private String newTradeId;

    @ExcelProperty("付款时间")
    private Date paymentTime;

    @ExcelProperty("退货机型")
    private String refundModel;

    @ExcelProperty("客服快递单号")
    private String serveExpressNo;

    @ExcelProperty("退货快递单号")
    private String storeExpressNo;

    @ExcelProperty("退货原因")
    private String refundReason;

    @ExcelProperty("创建人")
    private String createUser;

    @ExcelProperty("状态")
    private String status;

    @ExcelProperty("扫描人")
    private String scanner;

    @ExcelProperty("扫描时间")
    private Date scanTime;

    @ExcelProperty("条码")
    private String barcode;

    @ExcelProperty("物料代码")
    private String erpCode;

    @ExcelProperty("物料名称")
    private String erpName;

    @ExcelProperty("调单新订单号")
    private String adjustTradeId;

    @ExcelProperty("旧机价值")
    private String oldModelMoney;

    @ExcelProperty("调单人员")
    private String adjustUser;

    @ExcelProperty("调单时间")
    private Date adjustTime;

    @ExcelProperty("调单备注")
    private String adjustRemark;

    @ExcelProperty("是否手动确认过账")
    private String erpPostManual;

    @ExcelProperty("手动确认过账时间")
    private Date erpPostManualTime;

    @ExcelProperty("erp单号")
    private String erpId;

    @ExcelProperty("erp过账状态")
    private String erpPostStatus;

    @ExcelProperty("erp过账时间")
    private Date erpPostTime;

    @ExcelProperty("仓库储位，费用仓库204")
    private String subinvCode;

    @ExcelProperty("客服退款人员")
    private String serveReturnUser;

    @ExcelProperty("客服退款时间")
    private Date serveReturnTime;

    @ExcelProperty("客服退款备注")
    private String serveReturnRemark;

    @ExcelProperty("更新时间")
    private String updateTime;

    @ExcelProperty("创建时间")
    private String createTime;
    
}
