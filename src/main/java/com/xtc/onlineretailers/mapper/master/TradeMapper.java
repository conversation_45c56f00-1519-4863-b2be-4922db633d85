package com.xtc.onlineretailers.mapper.master;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.query.*;
import com.xtc.onlineretailers.pojo.vo.ErpForecastVO;
import com.xtc.onlineretailers.pojo.vo.TradeAllocationInfoVO;
import com.xtc.onlineretailers.pojo.vo.TradeAllocationVO;
import com.xtc.onlineretailers.pojo.vo.WarehouseVO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

public interface TradeMapper extends BaseMapper<TradeDO> {

    /**
     * 根据订单状态,顺丰状态以及 仓库状态获取打印列表
     *
     * @param page  分页条件
     * @param query 状态条件
     * @return 分页结果集
     */
    IPage<TradeAllocationVO> getAllocationList(@Param("page") Page<TradeAllocationVO> page, @Param("query") TradeAllocationQuery query);

    /**
     * 打印单据列表获取配货单号信息
     *
     * @return
     */
    List<TradeAllocationInfoVO> getAllocationNo(Map<String, Object> param);

    /**
     * 交接打印页面获取配货单号信息
     *
     * @param query 查询条件
     * @return 结果集
     */
    List<TradeAllocationInfoVO> getPrintAllocationNo(TradeHandOverQuery query);

    /**
     * 快递交接页面获取配货单号信息
     *
     * @param query 查询条件
     * @return 结果集
     */
    List<TradeAllocationInfoVO> getExpressAllocationNo(TradeExpressAllocationNoQuery query);

    /**
     * 获取手动确认退货过账的订单
     *
     * @return 订单列表
     */
    List<TradeDO> getRefundManualPostErpList();

    /**
     * 获取预库存数量
     *
     * @param query
     * @return
     */
    List<ErpForecastVO> getErpForecast(ErpForecastQuery query);

    /**
     * 获取已退货的自动过账订单
     *
     * @return
     */
    List<TradeDO> getRefundAutoPostErpList();

    /**
     * 获取换货自动确认过账的订单
     *
     * @return
     */
    List<TradeDO> getRefundExchangePostErpList();

    /**
     * 获取oppo换货退货过账的订单
     *
     * @return
     */
    List<TradeDO> getRefundOppoPostErpList();

    List<TradeDO> getPartRefundProcess();

    /**
     * 获取单物料配货单列表
     *
     * @param page  分页条件
     * @param query 状态条件
     * @return 分页结果集
     */
    IPage<TradeAllocationVO> singleAllocationList(@Param("page") Page<TradeAllocationVO> page, @Param("query") TradeAllocationQuery query);

    List<String> getManualHandoverList();

    /**
     *
     * @return 发货物流公司列表
     */
    List<String> getSendExpressCompany();

    /**
     * 本地仓库查询
     * @param query 查询条件
     * @return 每天数量以及日期
     */
    List<WarehouseVO> selectEverydayNumber(@Param("query") LocalHostRepositoryQuery query);

    /**
     * 代销订单列表
     * <p>查询条件：待发货，本地仓库，付款时间：三天内</p>
     *
     * @return 代销订单列表
     */
    List<TradeDO> listByAgentConfirm();

    List<TradeDO> getPartRefundAndSend();

    /**
     * 获取平台总金额
     *
     * @param platformId 平台id
     * @param statusOrder 订单状态
     * @param erpPostStatus 过账状态
     * @return 平台总金额
     */
    BigDecimal getPlatformAmount(@Param("platformId") List<Integer> platformId,@Param("statusOrder") List<String> statusOrder,@Param("erpPostStatus") Integer erpPostStatus);

    /**
     * 查询订单号列表
     * @param paymentTime 付款时间
     * @return 订单号列表
     */
    List<String> listTradeIdsByPaymentTime(@Param("paymentTime") LocalDateTime paymentTime);

    /**
     * 查看 已退货 发票已回传的订单号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 订单号集合
     */
    List<String> listTradeIdByOrderStatusAndInvoiceStatus(@Param("startTime") LocalDateTime startTime ,@Param("endTime") LocalDateTime endTime);



}
