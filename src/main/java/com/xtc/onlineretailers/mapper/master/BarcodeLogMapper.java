package com.xtc.onlineretailers.mapper.master;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xtc.onlineretailers.pojo.entity.BarcodeLogDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BarcodeLogMapper  extends BaseMapper<BarcodeLogDO> {
    /**
     * 查询sn
     * @param createTime 时间
     * @return
     */
    List<String> getSnList(@Param("createTime") String createTime);
}
