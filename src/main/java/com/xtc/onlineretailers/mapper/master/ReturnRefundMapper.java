package com.xtc.onlineretailers.mapper.master;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xtc.onlineretailers.pojo.entity.ReturnRefundDO;
import com.xtc.onlineretailers.pojo.query.ReturnRefundPageQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ReturnRefundMapper extends BaseMapper<ReturnRefundDO> {

    List<ReturnRefundDO> syncReturnRefundRecords(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 分页查询
     *
     * @param page  分页条件
     * @param query 查询条件
     * @return 分页结果
     */
    IPage<ReturnRefundDO> pageBy(@Param("page") Page<ReturnRefundDO> page, @Param("query") ReturnRefundPageQuery query);


}
