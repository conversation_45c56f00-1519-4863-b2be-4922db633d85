package com.xtc.onlineretailers.mapper.master;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xtc.onlineretailers.pojo.dto.ReturnedMaterialHomingDTO;
import com.xtc.onlineretailers.pojo.entity.ReturnedMaterialHomingDO;
import com.xtc.onlineretailers.pojo.query.ReturnedMaterialBarcodeQuery;
import org.apache.ibatis.annotations.Param;

public interface ReturnedMaterialHomingMapper extends BaseMapper<ReturnedMaterialHomingDO> {

    /**
     * 查询本地仓条码
     *
     * @param query 条码
     * @return 手表信息
     */
    ReturnedMaterialHomingDTO getWatchSaleInfo(@Param("query") ReturnedMaterialBarcodeQuery query);

    /**
     * 查询顺丰仓条码
     *
     * @param query 条码
     * @return 手表信息
     */
    ReturnedMaterialHomingDTO getSfWatchSaleInfo(@Param("query") ReturnedMaterialBarcodeQuery query);

}
