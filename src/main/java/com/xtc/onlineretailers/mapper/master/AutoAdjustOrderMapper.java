package com.xtc.onlineretailers.mapper.master;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xtc.onlineretailers.pojo.entity.AutoAdjustOrderDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AutoAdjustOrderMapper extends BaseMapper<AutoAdjustOrderDO> {

    List<AutoAdjustOrderDO> syncAutoAdjustOrders(@Param("startTime") String startTime, @Param("endTime") String endTime);

    List<AutoAdjustOrderDO> selectAdjustOrders(@Param("ids") List<Integer> ids);

}
