package com.xtc.onlineretailers.task;

import com.google.common.base.Splitter;
import com.xtc.onlineretailers.executor.command.WorkWechatAlertSenderCmdExe;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.refactor.job.BaseTask;
import com.xtc.onlineretailers.rpc.erp.ErpRpc;
import com.xtc.onlineretailers.rpc.erp.dto.ErpPostOrderErrorDTO;
import com.xtc.onlineretailers.rpc.erp.qry.ErpPostOrderErrorQry;
import com.xtc.onlineretailers.service.SystemParamService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ErpPostOrderErrorNotifyJob {

    @Resource
    private SystemParamService systemParamService;
    @Resource
    private ErpRpc erpRpc;
    @Resource
    private WorkWechatAlertSenderCmdExe workWechatAlertSenderCmdExe;

    private static final String ERP_ERROR_NOTICE_EMPLOYEE = "erp_error_notice_employee";

    /**
     * erp过账异常提醒
     */
    @XxlJob("erpPostOrderErrorNotifyJob")
    public ReturnT<String> erpPostOrderErrorNotifyJob(String param) {
        return BaseTask.run(() -> {
            try {
                ErpPostOrderErrorQry qry = new ErpPostOrderErrorQry();
                PaginationVO<ErpPostOrderErrorDTO> erpPostOrderErrorList = erpRpc.pageByErpOrderError(qry);
                String messages = erpPostOrderErrorList.getItems().stream()
                        .map(ErpPostOrderErrorDTO::getPlatformTradeId)
                        .limit(5)
                        .collect(Collectors.joining("\n"));
                if (StringUtils.isEmpty(messages)) {
                    return;
                }
                // 获取企业微信用户工号
                List<String> users = systemParamService.getByName(ERP_ERROR_NOTICE_EMPLOYEE)
                        .filter(systemParam -> StringUtils.isNotBlank(systemParam.getParamValue()))
                        .map(systemParam -> Splitter.on(",").omitEmptyStrings().trimResults().splitToList(systemParam.getParamValue()))
                        .orElseGet(Collections::emptyList);
                if (CollectionUtils.isEmpty(users)) {
                    log.warn("erp过账异常提醒 系统参数配置错误 {}", ERP_ERROR_NOTICE_EMPLOYEE);
                    return;
                }
                workWechatAlertSenderCmdExe.execute(users, "过账异常（最多显示 5 个订单，具体情况需要二次确认）\n%s", messages);
            } catch (Exception e) {
                log.warn("erp过账提醒异常 {}", e.getMessage(), e);
            }
        });
    }

}
