package com.xtc.onlineretailers.task;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.constant.PlatformConstant;
import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.enums.WarehouseStatus;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.vo.LogisticInfoVO;
import com.xtc.onlineretailers.refactor.executor.query.LogisticsRouteQryExe;
import com.xtc.onlineretailers.refactor.job.BaseTask;
import com.xtc.onlineretailers.service.PlatformService;
import com.xtc.onlineretailers.service.SfRoutingService;
import com.xtc.onlineretailers.service.SystemParamService;
import com.xtc.onlineretailers.service.TradeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Component
@Slf4j
public class SyncLogisticRoutingTask {

    @Resource
    private SystemParamService systemParamService;

    @Resource
    private TradeService tradeService;

    @Resource
    private SfRoutingService sfRoutingService;

    @Resource
    private PlatformService platformService;

    @Resource
    private LogisticsRouteQryExe logisticsRouteQryExe;

    /**
     * 处理数量
     */
    private static final int LIMIT_SIZE = 3000;

    /**
     * 同步顺丰路由数据
     */
    @XxlJob("syncLogisticInfoRouteJob")
    public ReturnT<String> syncLogisticInfoRouteJob(String param) throws Exception {
        return BaseTask.run(this::syncLogisticInfoRouteJob);
    }

    /**
     * 同步顺丰路由数据到数据库
     */
    public void syncLogisticInfoRouteJob() {
        // 设置迁移时间
        int offsetDate = 3;// 前移天数，默认3天
        String offsetDateSys = this.systemParamService.getSystemParamByName(GlobalConstant.SYSTEM_LOGISTIC_ROUTING_OFFSET_DATE);
        if (StringUtils.isNotBlank(offsetDateSys)) {
            try {
                offsetDate = Integer.parseInt(offsetDateSys);
            } catch (NumberFormatException ignored) {
            }
        }
        LocalDateTime now = LocalDateTime.now();// 当前时间
        // 已分拣的快递单，本地仓库发货时间3天以内的快递
        Wrapper<TradeDO> wrapper = Wrappers.<TradeDO>lambdaQuery()
                .between(TradeDO::getShippingTime, now.minusDays(offsetDate), now)
                .eq(TradeDO::getIsCollect, 0)
                .eq(TradeDO::getStatusOrder, OrderStatus.HAS_SHIP.name())
                .eq(TradeDO::getIsWillCallOrder, 0)
                .isNotNull(TradeDO::getExpressTrackingNo)
                .orderByAsc(TradeDO::getShippingTime)
                .last(BaseTask.getShardingLimitSql(LIMIT_SIZE));
        List<TradeDO> list = this.tradeService.list(wrapper);

        for (TradeDO tradeDO : list) {
            log.info("同步快递路由信息，订单号：{}，快递公司：{}", tradeDO.getPlatformTradeId(), tradeDO.getExpressCompany());
            String expressCompany = tradeDO.getExpressCompany();
            // 京东快递需确认是京东小天才还是京东步步高
            if (expressCompany.contains("京东")) {
                PlatformDO platform = platformService.getPlatformByName(tradeDO.getPlatformName());
                expressCompany = platform.getPlatformName().equals(PlatformConstant.PLATFORM_JINGDONG_XTC_NAME) ? "京东小天才" : "京东步步高";
            }
            if (StringUtils.isBlank(tradeDO.getExpressTrackingNo())) {
                continue;
            }
            List<LogisticInfoVO> routes = logisticsRouteQryExe.execute(expressCompany, tradeDO.getExpressTrackingNo());
            routes.stream()
                    .filter(this::hasHandedOver)
                    .findAny()
                    .ifPresent(route -> {
                        TradeDO updateTrade = new TradeDO();
                        updateTrade.setPlatformTradeId(tradeDO.getPlatformTradeId());
                        updateTrade.setIsCollect(1);
                        updateTrade.setCollectTime(route.getTime());
                        // 根据仓库代码获取仓库状态
                        String warehouseStatus = this.getWarehouseStatus(tradeDO.getWarehouseCode());
                        updateTrade.setWarehouseStatus(warehouseStatus);
                        this.tradeService.updateTrade(updateTrade);
                        log.info("订单号：{}，已揽收，仓库状态：{} ->{}", tradeDO.getPlatformTradeId(), tradeDO.getWarehouseStatus(), updateTrade.getWarehouseStatus());
                    });
        }
    }

    private boolean hasHandedOver(LogisticInfoVO route) {
        String memo = route.getMemo();
        return memo.contains("揽收") || memo.contains("已收取");
    }

    /**
     * 获取仓库状态
     *
     * @param warehouseCode 仓库编码
     * @return 仓库状态
     */
    private String getWarehouseStatus(String warehouseCode) {
        return GlobalConstant.WAREHOUSE_LOCAL.equals(warehouseCode) || GlobalConstant.WAREHOUSE_XTC_MALL.equals(warehouseCode)
                ? WarehouseStatus.HAVE_HANDED_OVER.name()
                : WarehouseStatus.HAVE_CONFIRMED.name();
    }

}
