package com.xtc.onlineretailers.task;

import com.xtc.onlineretailers.refactor.job.BaseTask;
import com.xtc.onlineretailers.service.AreaService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class AreaTask {

    @Resource
    private AreaService areaService;

    /**
     * 更新天猫省市区数据
     */
    @XxlJob("areaUpdateJob")
    public ReturnT<String> areaUpdateJob(String param) throws Exception {
        return BaseTask.run(() -> this.areaService.updateArea());
    }

}
