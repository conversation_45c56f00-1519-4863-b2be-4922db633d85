package com.xtc.onlineretailers.task;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.executor.command.WorkWechatAlertSenderCmdExe;
import com.xtc.onlineretailers.pojo.bo.ExtendedWarrantyParamBO;
import com.xtc.onlineretailers.pojo.dto.ExtendedWarrantyParams;
import com.xtc.onlineretailers.pojo.entity.*;
import com.xtc.onlineretailers.repository.ExtendedWarrantyRepository;
import com.xtc.onlineretailers.service.*;
import com.xtc.onlineretailers.task.base.BaseQuest;
import com.xtc.onlineretailers.task.base.QuestExecutor;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.suiPingBao.SuiPingBaoRpc;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 发货延宝服务处理
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class ExtendedWarrantyShippingHandleJob extends QuestExecutor<ExtendedWarrantyParamBO> {

    private final OrderService orderService;
    private final OrderBarcodeService orderBarcodeService;
    private final ExtendedWarrantyRepository extendedWarrantyRepository;
    private final OrderShipItemInfoService orderShipItemInfoService;
    private final WorkWechatAlertSenderCmdExe workWechatAlertSenderCmdExe;
    private final ProductInfoService productInfoService;
    private final TradeService tradeService;
    private final SystemParamService systemParamService;

    /**
     * 任务名称
     */
    private static final String JOB_PARAM = "extendedWarrantyShippingHandleJob";
    /**
     * 重试偏移天数
     */
    private static final int RETRY_OFFSET_DAYS = 2;
    /**
     * 限制数量大小
     */
    private static final int LIMIT_SIZE = 1000;
    /**
     * 上报名称
     */
    private static final String USER_NAME = "刘静";
    /**
     * 上报用户id
     */
    private static final String USER_ID = "bde8174d553545a0ae6559001fffefbc";
    /**
     * 上报类型：舰旗店上报
     */
    private static final String REPORT_TYPE = "ONLINE_SHOP_REPORT";
    /**
     * 企业通知接收人
     */
    private static final String ORDER_FILL_ERP_CODE_ERROR = "order_fill_erp_code_error";

    /**
     * 发货延宝服务处理
     */
    @XxlJob("extendedWarrantyShippingHandleJob")
    public ReturnT<String> extendedWarrantyShippingHandleJob(String param) {
        return BaseQuest.run(param, () -> this.taskExecute(JOB_PARAM, param));
    }

    /**
     * 发货延宝服务处理重试任务
     */
    @XxlJob("extendedWarrantyShippingHandleRetryJob")
    public ReturnT<String> extendedWarrantyShippingHandleJobRetryJob(String param) {
        return BaseQuest.run(() -> this.taskRetry(JOB_PARAM, RETRY_OFFSET_DAYS));
    }

    @Override
    public void job() {
        ExtendedWarrantyParamBO jobParam = jobParamHolder.get();
        this.calculateTaskTime(jobParam.getDataInitTime(), jobParam.getOffsetDuration());
        TaskLogDO taskLog = taskLogHolder.get();
        LocalDateTime startTime = DateUtil.dateToLocalDateTime(taskLog.getSyncStartTime());
        LocalDateTime endTime = DateUtil.dateToLocalDateTime(taskLog.getSyncEndTime());
        for (int loop = 1; loop <= 10; loop++) {
            IPage<TradeDO> pageTrade = tradeService.pageByExtendedWarrantyBuy(loop, LIMIT_SIZE, startTime, endTime);
            if (CollectionUtils.isEmpty(pageTrade.getRecords())) {
                break;
            }
            // 初始化处理失败的订单集合，最多保存 5 个订单
            Map<String, String> failedMap = Maps.newHashMapWithExpectedSize(5);
            pageTrade.getRecords().forEach(tradeDO -> {
                try {
                    this.extendedWarrantyShippingHandle(tradeDO);
                } catch (Exception e) {
                    log.warn("延宝服务购买异常 {}", e.getMessage(), e);
                    this.putPushFailedMessage(failedMap, tradeDO.getPlatformTradeId(), e.getMessage(), e);
                }
            });

            // 发送订单推送失败的企业微信通知
            if (!failedMap.isEmpty()) {
                // 获取企业微信用户工号
                List<String> users = systemParamService.getByName(ORDER_FILL_ERP_CODE_ERROR)
                        .filter(systemParam -> StringUtils.isNotBlank(systemParam.getParamValue()))
                        .map(systemParam -> Splitter.on(",").omitEmptyStrings().trimResults().splitToList(systemParam.getParamValue()))
                        .orElseGet(Collections::emptyList);
                String msg = Joiner.on("\n").withKeyValueSeparator(": ").join(failedMap);
                workWechatAlertSenderCmdExe.execute(users, "延宝服务购买异常（最多显示 5 个订单，具体情况需要二次确认）\n%s", msg);
            }
        }
    }

    /**
     * 发货延宝服务处理
     *
     * @param tradeDO 订单
     */
    public void extendedWarrantyShippingHandle(TradeDO tradeDO) {
        // 发货条码 Map<69码/物料代码, List<发货条码>>
        Map<String, List<String>> barcodeMap = Maps.newHashMapWithExpectedSize(5);
        log.info("订单号：{} 延宝服务处理", tradeDO.getPlatformTradeId());
        // 校验延宝服务购买记录
        this.checkExtendedWarrantyExist(tradeDO.getPlatformTradeId());
        // 获取订单明细
        List<OrderDO> orders = orderService.getOrderByTid(tradeDO.getPlatformTradeId());

        // 物料列表 Map<物料代码, 物料>
        Map<String, ProductInfoDO> productInfoMap = orders.stream()
                .filter(orderDO -> orderDO.getMaterialType() == 1)
                .map(order -> productInfoService.getProductInfo(order.getErpCode()))
                .collect(Collectors.toMap(ProductInfoDO::getProductId, productInfoDO -> productInfoDO, (n, o) -> n));

        // 赠送延宝服务物料
        List<String> insuranceProductIds = productInfoMap.values().stream()
                .map(ProductInfoDO::getExtendedWarrantyProductId)
                .collect(Collectors.toList());

        // 获取机器明细
        List<OrderDO> machineOrders = this.getMachineOrders(orders, productInfoMap);

        // 延宝服务明细
        List<OrderDO> extendedWarrantOrders = this.getExtendedWarrantOrders(orders, insuranceProductIds);

        // 校验机器明细数量
        this.checkMachineOrderNum(tradeDO.getPlatformTradeId(), machineOrders, extendedWarrantOrders);

        // 处理本地发货的延宝服务
        if ("005".equals(tradeDO.getWarehouseCode())) {
            barcodeMap = this.localExtendedWarrantyHandle(tradeDO.getPlatformTradeId());
        }

        // 处理顺丰发货的延宝服务
        if ("006".equals(tradeDO.getWarehouseCode())) {
            barcodeMap = this.sfExtendedWarrantyHandle(tradeDO.getPlatformTradeId());
        }

        // 购买延宝服务
        this.buyExtendedWarranty(tradeDO, machineOrders, barcodeMap, extendedWarrantOrders);
        log.info("订单号：{} 购买延宝服务成功", tradeDO.getPlatformTradeId());
    }

    /**
     * 获取机器明细
     *
     * @param orders         订单明细
     * @param productInfoMap 机器物料
     * @return 机器明细
     */
    private List<OrderDO> getMachineOrders(List<OrderDO> orders, Map<String, ProductInfoDO> productInfoMap) {
        return orders.stream()
                .filter(order -> order.getMaterialType() == 1)
                .filter(order -> productInfoMap.containsKey(order.getErpCode()))
                .filter(order -> {
                    ProductInfoDO productInfoDO = productInfoMap.get(order.getErpCode());
                    return productInfoDO.getIsExtendedWarranty() == 1 && productInfoDO.getIsUse() == 1;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取延宝服务明细
     *
     * @param orders                   明细
     * @param extendedWarrantyProducts 延宝服务物料代码
     * @return 延宝服务明细
     */
    private List<OrderDO> getExtendedWarrantOrders(List<OrderDO> orders, List<String> extendedWarrantyProducts) {
        return orders.stream()
                .filter(order -> StringUtils.isNotBlank(order.getErpCode()))
                .filter(orderDO -> orderDO.getMaterialType() == 3)
                .filter(order -> {
                    ProductInfoDO productInfoDO = productInfoService.getProductInfo(order.getErpCode());
                    return productInfoDO.getShortName().contains("延宝")
                            && productInfoDO.getIsUse() == 1;
                })
                .filter(orderDO -> extendedWarrantyProducts.contains(orderDO.getErpCode()))
                .collect(Collectors.toList());
    }

    /**
     * 校验延宝服务记录是否存在
     *
     * @param tradeId 订单号
     */
    private void checkExtendedWarrantyExist(String tradeId) {
        List<ExtendedWarrantyDO> screenInsuranceDOS = extendedWarrantyRepository.listByTradeId(tradeId);
        if (CollectionUtils.isNotEmpty(screenInsuranceDOS)) {
            throw GlobalDefaultException.of("订单号：%s 延宝服务购买记录已存在", tradeId);
        }
    }

    /**
     * 延宝服务购买
     *
     * @param tradeDO               订单
     * @param machineOrders         机器订单明细
     * @param barcodesMap           发货条码
     * @param screenInsuranceOrders 延宝服务服务明细
     */

    private void buyExtendedWarranty(TradeDO tradeDO, List<OrderDO> machineOrders, Map<String, List<String>> barcodesMap,
                                     List<OrderDO> screenInsuranceOrders) {
        // 延宝服务总数
        int buyNum = 0;
        // 购买延宝服务数量
        int insuranceOrderNum = screenInsuranceOrders.stream().map(OrderDO::getNum).reduce(Integer::sum).orElse(0);
        for (OrderDO machineOrder : machineOrders) {
            String key = tradeDO.getWarehouseCode().equals("005") ? machineOrder.getErpCode() : machineOrder.getBarcode();
            List<String> barcodes = barcodesMap.get(key);
            ProductInfoDO productInfo = productInfoService.getProductInfo(machineOrder.getErpCode());
            if (ObjectUtils.isEmpty(productInfo.getIsExtendedWarranty())) {
                continue;
            }
            ProductInfoDO product = productInfoService.getProductInfo(productInfo.getExtendedWarrantyProductId());
            int extendType = product.getShortName().contains("半年") ? 1 : 2;
            for (String barcode : barcodes) {
                buyNum += this.buyExtendedWarranty(tradeDO, barcode, extendType);
                if (buyNum > insuranceOrderNum) {
                    return;
                }
            }
        }
    }

    /**
     * 转化顺丰仓库发货记录
     *
     * @param tradeId 订单号
     * @return 发货记录 Map<69码, List<发货条码>>
     */
    private Map<String, List<String>> sfExtendedWarrantyHandle(String tradeId) {
        List<OrderShipItemInfoDO> orderShipItemInfos = orderShipItemInfoService.getShipItemByTradeId(tradeId);
        if (CollectionUtils.isEmpty(orderShipItemInfos)) {
            throw GlobalDefaultException.of("顺丰仓库，订单号：%s，没有发货记录", tradeId);
        }
        return orderShipItemInfos.stream()
                .collect(Collectors.groupingBy(OrderShipItemInfoDO::getBarcode,
                        Collectors.mapping(OrderShipItemInfoDO::getProductSn, Collectors.toList())));
    }

    /**
     * 转化本地仓库发货记录
     *
     * @param tradeId 订单号
     * @return 发货记录 Map<物料代码, List<发货条码>>
     */
    private Map<String, List<String>> localExtendedWarrantyHandle(String tradeId) {
        List<OrderBarcodeDO> orderBarcodes = orderBarcodeService.getOrderBarcodeByTradeId(tradeId);
        if (CollectionUtils.isEmpty(orderBarcodes)) {
            throw GlobalDefaultException.of("本地仓库，订单号：%s，没有发货记录", tradeId);
        }
        return orderBarcodes.stream()
                .collect(Collectors.groupingBy(OrderBarcodeDO::getErpCode,
                        Collectors.mapping(OrderBarcodeDO::getBarcode, Collectors.toList())));
    }

    /**
     * 校验机器明细数量
     *
     * @param tradeId               订单号
     * @param machineOrders         机器明细
     * @param screenInsuranceOrders 延宝服务明细
     */
    private void checkMachineOrderNum(String tradeId, List<OrderDO> machineOrders, List<OrderDO> screenInsuranceOrders) {
        if (CollectionUtils.isEmpty(screenInsuranceOrders)) {
            throw GlobalDefaultException.of("订单号：%s 不存在延宝服务明细", tradeId);
        }
        // 延宝服务数量
        Integer screenInsuranceNum = screenInsuranceOrders.stream()
                .map(OrderDO::getNum)
                .reduce(Integer::sum)
                .orElse(0);
        if (CollectionUtils.isEmpty(machineOrders)) {
            throw GlobalDefaultException.of("存在延宝服务明细，但没有维护延宝服务代码，请核实");
        }

        // 获取机器数量
        Integer machineOrderNum = machineOrders.stream()
                .map(OrderDO::getNum)
                .reduce(Integer::sum)
                .orElse(0);
        if (screenInsuranceNum > machineOrderNum) {
            String message = "延宝服务数量：%s 机器数量：%s 数量不一致";
            throw GlobalDefaultException.of(message, screenInsuranceNum, machineOrderNum);
        }
    }

    /**
     * 调用延宝服务接口
     *
     * @param tradeDO    订单
     * @param barcode    条码
     * @param extendType 延保服务类型
     * @return 结果
     */
    private int buyExtendedWarranty(TradeDO tradeDO, String barcode, int extendType) {
        // 创建延宝服务参数
        ExtendedWarrantyParams extendedWarrantyParams = this.createExtendedWarrantyParams(tradeDO, barcode, extendType);

        // 调用延宝服务结果
        SuiPingBaoRpc.buyExtendedWarranty(extendedWarrantyParams);

        // 保存更新延宝服务记录
        ExtendedWarrantyDO extendedWarrantyDO = this.createExtendedWarrantyDO(tradeDO, barcode, extendType);
        extendedWarrantyRepository.saveOrUpdateByBarcodeAndTradeId(extendedWarrantyDO);
        return 1;
    }

    /**
     * 创建延宝服务记录
     *
     * @param tradeDO 订单
     * @param barcode 发货条码
     * @return 延宝服务记录
     */
    private ExtendedWarrantyDO createExtendedWarrantyDO(TradeDO tradeDO, String barcode, int extendType) {
        ExtendedWarrantyDO extendedWarrantyDO = new ExtendedWarrantyDO();
        extendedWarrantyDO.setPlatformTradeId(tradeDO.getPlatformTradeId());
        extendedWarrantyDO.setBuyerNickname(tradeDO.getBuyerNickname());
        extendedWarrantyDO.setBarcode(barcode);
        extendedWarrantyDO.setIsRefund(-1);
        extendedWarrantyDO.setPlatformName(tradeDO.getPlatformName());
        extendedWarrantyDO.setProductDetail(tradeDO.getProductDetail());
        extendedWarrantyDO.setExtendType(extendType);
        extendedWarrantyDO.setIsUser(1);
        return extendedWarrantyDO;
    }

    /**
     * 创建调用延宝服务接口参数
     *
     * @param tradeDO    订单
     * @param barcode    机器条码
     * @param extendType 延保服务类型
     * @return 延宝服务接口参数
     */
    private ExtendedWarrantyParams createExtendedWarrantyParams(TradeDO tradeDO, String barcode, int extendType) {
        ExtendedWarrantyParams extendedWarrantyParams = new ExtendedWarrantyParams();
        extendedWarrantyParams.setGuideId(USER_ID);
        extendedWarrantyParams.setOutOrderId(tradeDO.getPlatformTradeId());
        extendedWarrantyParams.setProductSn(barcode);
        extendedWarrantyParams.setUserId(USER_ID);
        extendedWarrantyParams.setUserName(USER_NAME);
        extendedWarrantyParams.setReportType(REPORT_TYPE);
        extendedWarrantyParams.setServiceType("EXTEND");
        extendedWarrantyParams.setTimeType(extendType);
        // 获取秘钥信息
        String sign = SuiPingBaoRpc.getSign(extendedWarrantyParams, extendedWarrantyParams.getUserId());
        extendedWarrantyParams.setSign(sign);
        return extendedWarrantyParams;
    }

    /**
     * 记录处理失败的订单
     *
     * @param listFailed 推送失败的订单集合
     * @param tradeId    订单号
     * @param failedMsg  失败信息
     * @param e          异常
     */
    private void putPushFailedMessage(Map<String, String> listFailed, String tradeId, String failedMsg, Exception e) {
        if (failedMsg.contains("不存在延宝服务明细")) {
            return;
        }
        if (failedMsg.contains("发货记录")) {
            return;
        }
        if (failedMsg.contains("延宝服务购买记录已存在")) {
            return;
        }
        if (listFailed == null) {
            listFailed = Maps.newHashMapWithExpectedSize(5);
        }
        if (listFailed.size() >= 5) {
            return;
        }
        listFailed.put(tradeId, StringUtils.substring(failedMsg, 0, 200));
    }

}
