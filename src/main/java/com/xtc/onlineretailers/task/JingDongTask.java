package com.xtc.onlineretailers.task;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.jd.open.api.sdk.domain.shangjiashouhou.ServiceQueryProvider.response.list.ServiceBrief;
import com.jd.open.api.sdk.response.shangjiashouhou.AscSyncListResponse;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.InvoiceLogDO;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.ServiceInformationDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.enums.InvoiceState;
import com.xtc.onlineretailers.refactor.job.BaseTask;
import com.xtc.onlineretailers.refactor.pojo.entity.InvoiceApplyDO;
import com.xtc.onlineretailers.refactor.repository.InvoiceApplyRepository;
import com.xtc.onlineretailers.service.InvoiceLogService;
import com.xtc.onlineretailers.service.PlatformService;
import com.xtc.onlineretailers.service.ServiceInformationService;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.GsonUtil;
import com.xtc.onlineretailers.util.JingDongUtil;
import com.xtc.onlineretailers.util.MinioManager;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class JingDongTask {

    @Resource
    private MinioManager minioManager;
    @Resource
    private PlatformService platformService;
    @Resource
    private InvoiceLogService invoiceLogService;
    @Resource
    private ServiceInformationService serviceInformationService;
    @Resource
    private InvoiceApplyRepository invoiceApplyRepository;

    @XxlJob("jingDongInvoiceJob")
    public ReturnT<String> bbkOrderInvoiceJob(String param) {
        return BaseTask.run(this::jingDongInvoice);
    }

    @XxlJob("bbkServiceTicketJob")
    public ReturnT<String> bbkServiceTicketJob(String param) {
        return BaseTask.run(this::bbkReadyForDelivery);
    }

    @XxlJob("xtcServiceTicketJob")
    public ReturnT<String> xtcServiceTicketJob(String param) {
        return BaseTask.run(this::xtcReadyForDelivery);
    }

    private void bbkReadyForDelivery() {
        Integer platformId = 2062;
        String buId = "12509410";
        String operatePin = "bbkgd";
        saveServiceTicket(platformId, buId, operatePin);
    }

    private void xtcReadyForDelivery() {
        Integer platformId = 2063;
        String buId = "12509410";
        String operatePin = "xtcgd";
        saveServiceTicket(platformId, buId, operatePin);
    }

    /**
     * 京东小天才保存服务单列表
     *
     * @param platformId 平台id
     * @param buId       买家id
     * @param operatePin 操作人账号
     */
    private void saveServiceTicket(Integer platformId, String buId, String operatePin) {
        PlatformDO platformDO = this.platformService.getPlatformById(platformId);
        Date endDate = new Date();
        Date startDate = DateUtils.addHours(endDate, -16);

        AscSyncListResponse serviceTicketResult = JingDongUtil.getServiceTicketList(platformDO, startDate, endDate, 1, 30, buId, operatePin);
        if ("500".equals(serviceTicketResult.getCode())) {
            log.info("调用查询服务单接口出错!", serviceTicketResult.getMsg());
        } else {
            // 存入数据库中
            List<ServiceBrief> serviceBriefList = serviceTicketResult.getPageResult().getData();
            if (CollectionUtils.isEmpty(serviceBriefList)) {
                return;
            }
            // 记录总数
            int totalCount = serviceTicketResult.getPageResult().getTotalCount();
            if (totalCount < 30) {
                parseOrderInfo(serviceTicketResult);
            } else {
                for (int i = 1; i <= totalCount / 30 + 1; i++) {
                    AscSyncListResponse serviceTicketList = JingDongUtil.getServiceTicketList(platformDO, startDate, endDate, i, 30, buId, operatePin);
                    parseOrderInfo(serviceTicketList);
                }
            }
        }
    }

    /**
     * 解析京东服务列表信息
     *
     * @param serviceTicketResult 服务列表
     */
    private void parseOrderInfo(AscSyncListResponse serviceTicketResult) {
        String messageJson = serviceTicketResult.getMsg().replaceAll("\\\\", "");
        JsonArray asJsonArray = GsonUtil.jsonToObject(messageJson).getAsJsonObject("jingdong_asc_sync_list_responce").get("pageResult").getAsJsonObject().get("data").getAsJsonArray();
        for (JsonElement jsonElement : asJsonArray) {
            ServiceInformationDO serviceInformationDO = GsonUtil.jsonToBean(GsonUtil.objectToJson(jsonElement), ServiceInformationDO.class);
            ServiceInformationDO old = this.serviceInformationService.getOne(Wrappers.<ServiceInformationDO>lambdaQuery().eq(ServiceInformationDO::getServiceId, serviceInformationDO.getServiceId()));
            if (ObjectUtils.isEmpty(old)) {
                this.serviceInformationService.save(serviceInformationDO);
            } else {
                serviceInformationDO.setId(old.getId());
                this.serviceInformationService.updateById(old);
            }
        }
    }

    /**
     * 京东发票上传
     */
    public void jingDongInvoice() {
        // 已确认收货订单才可以上传成功
        List<TradeDO> tradeDOList = invoiceLogService.getJingdongTradeUpdateList();
        for (TradeDO tradeDO : tradeDOList) {
            log.info("发票上传开始 {}", tradeDO.getPlatformTradeId());
            try {
                // 获取平台信息
                PlatformDO platform = platformService.getPlatformById(tradeDO.getPlatformId());
                // 获取发票文件输入流
                InvoiceApplyDO applyDO = invoiceApplyRepository.getByTradeIdAndState(tradeDO.getPlatformTradeId(), InvoiceState.CREATE_BLUE)
                        .orElseThrow(() -> GlobalDefaultException.of("此订单发票未开票"));
                InputStream inputStream = minioManager.getObject(applyDO.getBlueInvoiceUrl());
                // 发票上传
                if (JingDongUtil.uploadBlueInvoice(platform, applyDO, tradeDO, inputStream)) {
                    // 保存数据库中
                    InvoiceLogDO invoiceLogDO = new InvoiceLogDO();
                    invoiceLogDO.setInvoiceAddress(tradeDO.getElectronicInvoiceUrl());
                    invoiceLogDO.setPlatformName(platform.getPlatformName());
                    invoiceLogDO.setCreateTime(DateUtil.localDateTimeToStr(LocalDateTime.now()));
                    invoiceLogDO.setPaymentTime(DateUtil.dateToStr(tradeDO.getPaymentTime()));
                    invoiceLogDO.setPlatformTradeId(tradeDO.getPlatformTradeId());
                    invoiceLogService.save(invoiceLogDO);
                    log.info("发票上传成功:{}", tradeDO.getPlatformTradeId());
                }
            } catch (Exception e) {
                log.warn("发票上传异常 {}", tradeDO.getPlatformTradeId(), e);
            }
        }
    }

}
