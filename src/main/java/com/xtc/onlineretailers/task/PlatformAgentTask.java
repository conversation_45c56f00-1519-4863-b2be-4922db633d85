package com.xtc.onlineretailers.task;

import com.xtc.onlineretailers.refactor.job.BaseTask;
import com.xtc.onlineretailers.service.PlatformAgentService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class PlatformAgentTask {

    @Resource
    private PlatformAgentService platformAgentService;

    /**
     * 代销平台过账统计
     */
    @XxlJob("platformAgentJob")
    public ReturnT<String> platformAgentJob(String param) throws Exception {
        return BaseTask.run(() -> this.platformAgentService.erpPostAmount());
    }

}
