package com.xtc.onlineretailers.task.checktrade.strategy;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.taobao.api.domain.Order;
import com.taobao.api.domain.Refund;
import com.taobao.api.domain.Trade;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.dto.TaobaoParamsDTO;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.service.PlatformService;
import com.xtc.onlineretailers.service.ProductInfoService;
import com.xtc.onlineretailers.task.checktrade.bo.OrderCheckBO;
import com.xtc.onlineretailers.task.checktrade.bo.TradeCheckBO;
import com.xtc.onlineretailers.util.TaoBaoUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Component("tmallCheckOrderStrategy")
public class TmallCheckTradeStrategy implements CheckTradeStrategy {

    private final PlatformService platformService;
    private final ProductInfoService productInfoService;

    @Override
    public TradeCheckBO getTradeCheckBO(TradeDO trade) {
        // 查询平台订单数据
        Trade platformTrade = this.getPlatformTrade(trade);
        // 查看平台信息
        PlatformDO platform = platformService.getPlatformById(trade.getPlatformId());
        // 生成订单核对BO
        TradeCheckBO tradeCheckBO = this.createTradeCheckBO(platformTrade, platform);
        // 生成订单明细核对BO
        List<OrderCheckBO> listOrderCheckBO = this.createOrderCheckBO(platformTrade, platform);
        tradeCheckBO.setOrders(listOrderCheckBO);

        return tradeCheckBO;
    }

    /**
     * 生成订单明细核对BO
     *
     * @param platformTrade 平台订单
     * @return 订单明细核对BO
     */
    private List<OrderCheckBO> createOrderCheckBO(Trade platformTrade, PlatformDO platform) {
        List<OrderCheckBO> listOrderCheckBO = Lists.newArrayList();

        // 虚拟物料
        List<String> specialCodes = productInfoService.listByIsUserAndMaterialType("3","1");

        platformTrade.getOrders().forEach(platformOrder -> {
            // 过滤已退款明细
            if (this.isRefundSuccess(platformOrder, platform)) {
                return;
            }

            // 处理多个物料skuId，用逗号分隔
            List<String> skuIds = Collections.emptyList();
            if (StringUtils.isNotBlank(platformOrder.getOuterSkuId())) {
                skuIds = Arrays.asList(platformOrder.getOuterSkuId().split(","));
            } else if (StringUtils.isNotBlank(platformOrder.getOuterIid())) {
                skuIds = Arrays.asList(platformOrder.getOuterIid().split(","));
            }

            // 金额处理
            BigDecimal payment;
            if (skuIds.size() == 1) {
                // 单一物料，单价等于1则设置为0
                BigDecimal price = new BigDecimal(platformOrder.getPrice());
                payment = price.compareTo(BigDecimal.ONE) == 0 ? BigDecimal.ZERO : price;
            } else {
                // 多个物料实付金额都设置为0
                payment = BigDecimal.ZERO;
            }
            // 过滤一下虚拟物料
            List<OrderCheckBO> orderCheckList = skuIds.stream()
                    .filter(skuId -> !specialCodes.contains(skuId))
                    .map(skuId -> OrderCheckBO.builder()
                            .tradeId(platformTrade.getTid().toString())
                            .orderId(platformOrder.getOid().toString())
                            .erpCode(skuId)
                            .payment(payment)
                            .num(platformOrder.getNum().intValue())
                            .build()).collect(Collectors.toList());
            // 添加到明细
            if (CollectionUtils.isNotEmpty(orderCheckList)) {
                listOrderCheckBO.addAll(orderCheckList);
            }

        });
        return listOrderCheckBO;
    }

    /**
     * 生成订单核对BO
     *
     * @param platformTrade 平台订单
     * @return 订单核对BO
     */
    private TradeCheckBO createTradeCheckBO(Trade platformTrade, PlatformDO platform) {
        // 订单金额减去明细退款金额
        BigDecimal refundAmount = platformTrade.getOrders().stream()
                .filter(platformOrder -> this.isRefundSuccess(platformOrder, platform))
                .map(platformOrder -> new BigDecimal(platformOrder.getPayment()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal orderAmount = new BigDecimal(platformTrade.getPayment()).subtract(refundAmount);

        return TradeCheckBO.builder()
                .tradeId(platformTrade.getTid().toString())
                .orderAmount(orderAmount)
                .orders(Collections.emptyList())
                .build();
    }

    /**
     * 获取平台订单数据
     *
     * @param trade 订单
     * @return 平台订单
     */
    private Trade getPlatformTrade(TradeDO trade) {
        PlatformDO platform = this.platformService.getPlatformById(trade.getPlatformId());

        TaobaoParamsDTO params = new TaobaoParamsDTO(platform);
        params.setTid(new Long(trade.getPlatformTradeId()));
        params.setOaid(trade.getReceiverOaid());
        Trade platformTrade = TaoBaoUtil.getFullTradeInfo(params);
        if (platformTrade == null) {
            String message = String.format("获取平台订单信息失败 platformName: %1$s, tradeId: %2$s",
                    platform.getPlatformName(), trade.getPlatformTradeId());
            throw new GlobalDefaultException(message);
        }
        return platformTrade;
    }

    /**
     * 判断退款成功
     *
     * @param order 订单
     * @return 执行结果
     */
    private boolean isRefundSuccess(Order order, PlatformDO platform) {
        if (StringUtils.isNotBlank(order.getRefundId())) {
            if (order.getRefundId() != null) {
                Refund refund = TaoBaoUtil.getRefundByRefundId(platform, order.getRefundId());
                if (ObjectUtils.isNotEmpty(refund)) {
                    BigDecimal refundPayment = "SUCCESS".equals(refund.getStatus()) ? new BigDecimal(refund.getRefundFee()) : BigDecimal.ZERO;
                    BigDecimal orderPayment = new BigDecimal(order.getTotalFee());
                    return orderPayment.compareTo(refundPayment) == 0 ;
                }
            }
        }
        return false;
    }
}