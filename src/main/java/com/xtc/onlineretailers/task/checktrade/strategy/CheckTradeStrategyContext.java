package com.xtc.onlineretailers.task.checktrade.strategy;

import com.google.common.base.CaseFormat;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.task.checktrade.enums.PlatformEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class CheckTradeStrategyContext {

    private final Map<String, CheckTradeStrategy> strategies;

    @Autowired
    public CheckTradeStrategyContext(Map<String, CheckTradeStrategy> strategies) {
        this.strategies = strategies;
    }

    public CheckTradeStrategy getStrategy(int platformId) {
        PlatformEnum platformEnum = PlatformEnum.getEnum(platformId);
        if (platformEnum == null) {
            throw new GlobalDefaultException("No CheckOrderStrategy strategy defined. platformId: " + platformId);
        }

        // 将大写下划线命名，转换为小写驼峰命名
        String component = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, platformEnum.name()) + "CheckOrderStrategy";
        log.debug("CheckOrderStrategyContext getStrategy component: {}", component);

        CheckTradeStrategy strategy = this.strategies.get(component);
        if (strategy == null) {
            throw new GlobalDefaultException("No CheckOrderStrategy strategy defined. component: " + component);
        }
        return strategy;
    }

}
