package com.xtc.onlineretailers.task.checktrade.bo;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单核对BO
 */
@Data
@Builder
public class TradeCheckBO {

    /**
     * 订单号
     */
    private String tradeId;

    /**
     * 订单金额
     */
    private BigDecimal orderAmount;

    /**
     * 子订单列表
     */
    private List<OrderCheckBO> orders;

}
