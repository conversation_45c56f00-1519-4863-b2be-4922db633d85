package com.xtc.onlineretailers.task.checktrade.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 子订单核对BO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderCheckBO {

    /**
     * 订单号
     */
    private String tradeId;

    /**
     * 子订单号
     */
    private String orderId;

    /**
     * 物料代码
     */
    private String erpCode;

    /**
     * 子订单金额
     */
    private BigDecimal payment;

    /**
     * 数量
     */
    private Integer num;

}
