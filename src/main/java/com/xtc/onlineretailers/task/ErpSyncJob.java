package com.xtc.onlineretailers.task;

import com.xtc.onlineretailers.pojo.entity.ReturnMasterDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.refactor.job.BaseTask;
import com.xtc.onlineretailers.repository.ReturnMasterDao;
import com.xtc.onlineretailers.repository.TradeDao;
import com.xtc.onlineretailers.rpc.erp.ErpRpc;
import com.xtc.onlineretailers.rpc.erp.dto.ErpOrderHeaderDTO;
import com.xtc.onlineretailers.rpc.erp.qry.ErpOrderHeaderQry;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * ERP同步任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ErpSyncJob {

    private final ErpRpc erpRpc;
    private final TradeDao tradeDao;
    private final ReturnMasterDao returnMasterDao;

    /**
     * 发货同步ERP单号
     */
    @XxlJob("shippingSyncEprId")
    public ReturnT<String> shippingSyncEprId(String param) {
        return BaseTask.run(() -> {
            // 查看未过账订单
            List<TradeDO> trades = tradeDao.listBySyncErpId();
            trades.forEach(trade -> {
                try {
                    log.info("订单 {}，同步ERP单号", trade.getPlatformTradeId());
                    ErpOrderHeaderQry erpOrderHeaderQry = new ErpOrderHeaderQry();
                    erpOrderHeaderQry.setErpId(trade.getPlatformTradeId());
                    PaginationVO<ErpOrderHeaderDTO> erpOrderHeaders = erpRpc.pageByOrderHeader(erpOrderHeaderQry);
                    List<ErpOrderHeaderDTO> items = erpOrderHeaders.getItems();
                    if (CollectionUtils.isEmpty(items)) {
                        log.warn("订单 {}，ERP过账数据不存在", trade.getPlatformTradeId());
                        return;
                    }
                    if (items.size() > 1) {
                        log.warn("订单 {}，ERP过账数据存在多条，请核实", trade.getPlatformTradeId());
                        return;
                    }
                    ErpOrderHeaderDTO erpOrderHeaderDTO = items.get(0);
                    if (erpOrderHeaderDTO.getErpOrderNumber() == null) {
                        log.warn("订单 {}，ERP过账单号为空", trade.getPlatformTradeId());
                        return;
                    }
                    // 更新过账单号
                    TradeDO updateTrade = new TradeDO();
                    updateTrade.setId(trade.getId());
                    updateTrade.setErpId(erpOrderHeaderDTO.getErpOrderNumber());
                    tradeDao.updateById(updateTrade);
                    log.info("订单 {}，ERP单号 {}，同步成功", trade.getPlatformTradeId(), erpOrderHeaderDTO.getErpOrderNumber());
                } catch (Exception e) {
                    log.warn("订单 {}，ERP单号同步失败 {}", trade.getPlatformTradeId(), e.getMessage(), e);
                }
            });
        });
    }

    /**
     * 退货同步ERP单号
     */
    @XxlJob("refundSyncEprId")
    public ReturnT<String> refundSyncEprId(String param) {
        return BaseTask.run(() -> {
            // 查看未过账订单
            List<ReturnMasterDO> returnMaster = returnMasterDao.listBySyncErpId();
            returnMaster.forEach(returnMasterDO -> {
                try {
                    log.info("退货单 {}，退货同步ERP单号", returnMasterDO.getRefundId());
                    ErpOrderHeaderQry erpOrderHeaderQry = new ErpOrderHeaderQry();
                    erpOrderHeaderQry.setErpId(returnMasterDO.getRefundId());
                    PaginationVO<ErpOrderHeaderDTO> erpOrderHeaders = erpRpc.pageByOrderHeader(erpOrderHeaderQry);
                    List<ErpOrderHeaderDTO> items = erpOrderHeaders.getItems();
                    if (CollectionUtils.isEmpty(items)) {
                        log.warn("退货单 {}，ERP过账数据不存在", returnMasterDO.getRefundId());
                        return;
                    }
                    if (items.size() > 1) {
                        log.warn("退货单 {}，ERP过账数据存在多条，请核实", returnMasterDO.getRefundId());
                        return;
                    }
                    ErpOrderHeaderDTO erpOrderHeaderDTO = items.get(0);
                    if (erpOrderHeaderDTO.getErpOrderNumber() == null) {
                        log.warn("退货单 {}，ERP过账单号为空", returnMasterDO.getRefundId());
                        return;
                    }
                    // 更新过账单号
                    ReturnMasterDO updateReturnMaster = new ReturnMasterDO();
                    updateReturnMaster.setId(returnMasterDO.getId());
                    updateReturnMaster.setErpId(erpOrderHeaderDTO.getErpOrderNumber());
                    returnMasterDao.updateById(updateReturnMaster);
                    log.info("退货单 {}，ERP单号 {}，同步成功", returnMasterDO.getRefundId(), erpOrderHeaderDTO.getErpOrderNumber());
                } catch (Exception e) {
                    log.warn("退货单 {}，ERP单号同步失败 {}", returnMasterDO.getRefundId(), e.getMessage(), e);
                }
            });
        });
    }

}
