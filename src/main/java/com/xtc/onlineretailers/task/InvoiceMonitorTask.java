package com.xtc.onlineretailers.task;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.constant.InvoiceConstant;
import com.xtc.onlineretailers.enums.InvoiceStatus;
import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.enums.TradeTypeEnum;
import com.xtc.onlineretailers.mapper.master.InvoiceMainMapper;
import com.xtc.onlineretailers.mapper.master.TradeMapper;
import com.xtc.onlineretailers.pojo.dto.InvoiceSwitchToggleDTO;
import com.xtc.onlineretailers.pojo.entity.OrdinaryInvoiceRegisterDO;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.SystemParamDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.query.TradeErpQuery;
import com.xtc.onlineretailers.refactor.job.BaseTask;
import com.xtc.onlineretailers.service.OrdinaryInvoiceRegisterService;
import com.xtc.onlineretailers.service.PlatformService;
import com.xtc.onlineretailers.service.SystemParamService;
import com.xtc.onlineretailers.service.TradeService;
import com.xtc.onlineretailers.service.impl.InvoiceServiceImpl;
import com.xtc.onlineretailers.util.GsonUtil;
import com.xtc.onlineretailers.util.XtcMessageSender;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class InvoiceMonitorTask {

    @Resource
    private InvoiceServiceImpl invoiceService;
    @Resource
    private TradeService tradeService;
    @Resource
    private SystemParamService systemParamService;
    @Resource
    private InvoiceMainMapper invoiceMainMapper;
    @Resource
    private PlatformService platformService;
    @Resource
    private OrdinaryInvoiceRegisterService ordinaryInvoiceRegisterServicel;
    @Resource
    private TradeMapper tradeMapper;

    /**
     * 订单已发货但发票已冲红状态或订单已发货未开票企业提醒
     */
    @XxlJob("tradeInvoiceRedWarnJob")
    public ReturnT<String> tradeInvoiceRedWarnJob(String param) {
        return BaseTask.run(this::InvoiceWarnJob);
    }

    /**
     * 发票开关切换
     */
    @XxlJob("invoiceStatusMonitor")
    public ReturnT<String> monitorInvoiceStatus(String param) {
        return BaseTask.run(this::monitorInvoiceStatus);
    }

    /**
     * 开票失败和冲红失败的自动开票
     */
    @XxlJob("createInvoice")
    public ReturnT<String> createInvoice(String param) {
        return BaseTask.run(this::createInvoice);
    }

    private void returnOrderInvoiceMonitor() {
        try {
            LocalDateTime startTime = LocalDateTime.now().minusDays(2);
            LocalDateTime endTime = LocalDateTime.now().minusHours(2);
            String sendUsers = this.systemParamService.getSystemParamByName(GlobalConstant.INVOICE_ERP_IT_ADMIN);
            List<String> tradeIds = this.tradeMapper.listTradeIdByOrderStatusAndInvoiceStatus(startTime,endTime);
            String collect = String.join(",", tradeIds);
            if (StringUtils.isNotBlank(collect)) {
                XtcMessageSender.cardTextMessage(sendUsers, "订单号" + collect + ",超过两天,发票状态还是已回传,请核实");

            }
        } catch (Exception e) {
            log.error("发票状态已回传监控异常{}", e);
        }

    }

    private void regularInvoiceRegisterMonitor() {
        try {
            ArrayList<String> invoiceStatus = Lists.newArrayList("1", "2");
            LocalDateTime startTime = LocalDateTime.now().minusDays(3);
            LocalDateTime endTime = LocalDateTime.now().minusMinutes(240);
            List<OrdinaryInvoiceRegisterDO> ordinaryInvoiceRegisterList = ordinaryInvoiceRegisterServicel.list(Wrappers.<OrdinaryInvoiceRegisterDO>lambdaQuery()
                    .in(OrdinaryInvoiceRegisterDO::getStatus, invoiceStatus)
                    .ge(OrdinaryInvoiceRegisterDO::getConfirmTime, startTime)
                    .le(OrdinaryInvoiceRegisterDO::getConfirmTime, endTime)
                    .orderByDesc(OrdinaryInvoiceRegisterDO::getConfirmTime)
                    .last("limit 200"));
            String platformTradeIds = ordinaryInvoiceRegisterList.stream().map(OrdinaryInvoiceRegisterDO::getPlatformTradeId).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(platformTradeIds)) {
                String sendUsers = this.systemParamService.getSystemParamByName(GlobalConstant.INVOICE_ERP_IT_ADMIN);
                XtcMessageSender.cardTextMessage(sendUsers, "订单号" + platformTradeIds + ",超过六小时，普票还是已登记 未开票状态,请核实");
            }

        } catch (Exception e) {
            log.error("普票登记未开票监控异常{}", e);
        }
    }

    private void createInvoice() {
        TradeErpQuery presentQuery = new TradeErpQuery();

        List<String> presentType = new ArrayList<>();
        presentType.add(TradeTypeEnum.NORMAL.name());
        presentType.add(TradeTypeEnum.ADJUST_ORDER.name());
        presentQuery.setTradeTypeList(presentType);

        List<String> presentStatus = new ArrayList<>();
        presentStatus.add(OrderStatus.HAS_SHIP.name());
        presentStatus.add(OrderStatus.RETURNED.name());
        presentQuery.setOrderStatusList(presentStatus);

        List<String> presentInvoiceStatus = new ArrayList<>();
        presentInvoiceStatus.add(InvoiceStatus.FAIL_MAKE.name());
        presentInvoiceStatus.add(InvoiceStatus.WASHED_RED_FAIL.name());
        presentQuery.setInvoiceStatus(presentInvoiceStatus);

        String limit = BaseTask.getShardingLimitSql(1000);
        List<TradeDO> trades = this.tradeService.getTradeListByErp(presentQuery, limit);
        if (CollectionUtils.isNotEmpty(trades)) {
            this.invoiceService.createInvoiceByTask(trades, "blue");
        }
    }

    /**
     * 监控发票开关状态
     */
    private void monitorInvoiceStatus() {
        // 开票开关
        SystemParamDO systemParamDO = systemParamService.getOne(Wrappers.<SystemParamDO>lambdaQuery().eq(SystemParamDO::getParamName, GlobalConstant.INVOICE_SWITCH));
        String paramValue = systemParamDO.getParamValue();
        InvoiceSwitchToggleDTO invoiceSwitchToggle = GsonUtil.jsonToBean(paramValue, InvoiceSwitchToggleDTO.class);
        // 开
        if (Boolean.TRUE.equals(invoiceSwitchToggle.getSwitchStatus())) {
            return;
        }
        TradeDO trade = tradeService.getTradeByTradeId(invoiceSwitchToggle.getPlatformTradeId());
        // 查看平台信息
        PlatformDO platform = platformService.getPlatformById(trade.getPlatformId());

        // 请求开票接口
        this.invoiceService.retryInvoice(invoiceSwitchToggle.getPlatformTradeId(), InvoiceConstant.INVOICE_TYPE_BLUE, platform, systemParamDO, invoiceSwitchToggle);

    }

    /**
     * 订单已发货但发票已冲红状态或订单已发货未开票企业提醒
     */
    private void InvoiceWarnJob() {
        // 订单已发货,已冲红提醒
        tradeInvoiceRedWarnJob();
        // 订单已发货 未开票提醒
        shipmentsInvoiceWarnJob();
        //已发货，已退货，发票已回传，但状态还是已开票处理
        tradeStatusCheckJoB();
        // 普票状态 （已登记 超过 七天 提醒）
        regularInvoiceRegisterMonitor();
        // 已退货 已回传状态（两天）
        returnOrderInvoiceMonitor();
    }

    private void tradeStatusCheckJoB() {
        try {
            LocalDateTime startTime = LocalDateTime.now().minusDays(5);
            LocalDateTime entTime = LocalDateTime.now().minusHours(6);
            List<String> tradeStatus = Lists.newArrayList(OrderStatus.HAS_SHIP.name(), OrderStatus.RETURNED.name());
            List<TradeDO> tradeList = tradeService.list(Wrappers.<TradeDO>lambdaQuery()
                    .in(TradeDO::getStatusOrder, tradeStatus)
                    .eq(TradeDO::getInvoiceStatus, InvoiceStatus.HAS_MADE.name())
                    .ge(TradeDO::getPaymentTime, startTime)
                    .le(TradeDO::getPaymentTime, entTime)
                    .isNotNull(TradeDO::getElectronicInvoiceNo)
                    .orderByDesc(TradeDO::getPaymentTime)
                    .last("limit 30"));
            if (CollectionUtils.isNotEmpty(tradeList)) {
                String sendUsers = this.systemParamService.getSystemParamByName(GlobalConstant.SYSTEM_SHIPPING_NOT_COLLECT_NOTIFY);
                String tradeIds = tradeList.stream().map(TradeDO::getPlatformTradeId).collect(Collectors.joining(","));
                XtcMessageSender.cardTextMessage(sendUsers, "订单号" + tradeIds + ",超过五天,发票状态还是已开票,请核实");
            }
        } catch (Exception e) {
            log.error("订单已发货,已退货,发票状态已开票监控异常{}", e);
        }
    }

    private void tradeInvoiceRedWarnJob() {
        try {
            List<String> invoiceStatus = Lists.newArrayList(InvoiceStatus.SECOND_WASH_RED.name(), InvoiceStatus.WASHED_RED_SUCCESS.name());
            LocalDateTime startTime = LocalDateTime.now().minusDays(1);
            LocalDateTime endTime = LocalDateTime.now().minusMinutes(120);
            List<TradeDO> tradeList = tradeService.list(Wrappers.<TradeDO>lambdaQuery()
                    .eq(TradeDO::getStatusOrder, OrderStatus.HAS_SHIP)
                    .in(TradeDO::getInvoiceStatus, invoiceStatus)
                    .ge(TradeDO::getElectronicInvoiceRedTime, startTime)
                    .le(TradeDO::getElectronicInvoiceRedTime, endTime)
                    .orderByDesc(TradeDO::getElectronicInvoiceRedTime)
                    .last("limit 2000"));
            if (CollectionUtils.isNotEmpty(tradeList)) {
                String sendUsers = this.systemParamService.getSystemParamByName(GlobalConstant.SYSTEM_SHIPPING_NOT_COLLECT_NOTIFY);
                if (tradeList.size() > 30) {
                    String content = "你有批量已冲红,已发货的订单，请核实 是否开专票和多次开票";
                    XtcMessageSender.cardTextMessage(sendUsers, content);
                } else {
                    String tradeIds = tradeList.stream().map(TradeDO::getPlatformTradeId).collect(Collectors.joining(","));
                    XtcMessageSender.cardTextMessage(sendUsers, "订单号" + tradeIds + ",超过六个小时,订单还是已发货,已冲红,请核实");
                }

            }
        } catch (Exception e) {
            log.error("订单已发货,已冲红定时器监控异常{}", e);
        }
    }

    /**
     * 发货未开票监控
     */
    private void shipmentsInvoiceWarnJob() {
        try {
            // 发货往前减去三天
            LocalDateTime startTime = LocalDateTime.now().minusDays(3);
            LocalDateTime endTime = LocalDateTime.now().minusMinutes(120);
            List<TradeDO> tradeList = tradeService.list(Wrappers.<TradeDO>lambdaQuery()
                    .eq(TradeDO::getStatusOrder, OrderStatus.HAS_SHIP)
                    .eq(TradeDO::getInvoiceStatus, InvoiceStatus.NOT_MAKE)
                    .ge(TradeDO::getShippingTime, startTime)
                    .le(TradeDO::getShippingTime, endTime)
                    .orderByDesc(TradeDO::getShippingTime)
                    .last("limit 2000"));
            if (CollectionUtils.isNotEmpty(tradeList)) {
                String sendUsers = this.systemParamService.getSystemParamByName(GlobalConstant.INVOICE_ERP_IT_ADMIN);
                String tradeIds = tradeList.stream().map(TradeDO::getPlatformTradeId).collect(Collectors.joining(","));
                XtcMessageSender.cardTextMessage(sendUsers, "订单号" + tradeIds + ",超过六个小时,订单还是已发货 未开票状态,请核实");

            }
        } catch (Exception e) {
            log.error("订单已发货 未开票监控异常{}", e);
        }
    }

}
