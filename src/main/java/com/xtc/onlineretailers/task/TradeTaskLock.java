package com.xtc.onlineretailers.task;

import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;

public class TradeTaskLock {

    //天猫订单详细信息同步锁
    private static boolean xtc_tianMaoFullOrderSync = false;
    private static boolean bbk_tianMaoFullOrderSync = false;

    //天猫订单简要信息同步锁
    private static boolean xtc_tianMaoProfileTradeSync = false;
    private static boolean bbk_tianMaoProfileTradeSync = false;

    //APP商城订单同步锁
    private static boolean app_orderSync = false;

    //内部购机订单同步锁
    private static boolean app_inside_orderSync = false;

    //步步高精品课堂订单同步锁
    private static boolean bbk_excellent_lecture_orderSync = false;

    //抖音小天才
    private static boolean xtc_tictokProfileTradeSync_orderSync = false;

    //抖音步步高
    private static boolean bbk_tictokProfileTradeSync_orderSync = false;

    //京东小天才
    private static boolean xtc_jingdongProfileTradeSync_orderSync = false;

    //京东步步高
    private static boolean bbk_jingdongProfileTradeSync_orderSync = false;

    //快手小天才
    private static boolean xtc_kuaishouProfileTradeSync_orderSync = false;

    //快手步步高
    private static boolean bbk_kuaishouProfileTradeSync_orderSync = false;

    // 美团旗舰店
    private static boolean meitaunProfileTradeSync_orderSync = false;

    // 小红书小天才平台
    private static boolean xtc_XiaoHongShuTradeSync = false;

    // 小红书步步高平台
    private static boolean bbk_XiaoHongShuTradeSync = false;

    public static void lock(String lockName) {
        if (lockName.equals("xtc_tianMaoProfileTradeSync")) {
            xtc_tianMaoProfileTradeSync = true;
        } else if (lockName.equals("bbk_tianMaoProfileTradeSync")) {
            bbk_tianMaoProfileTradeSync = true;
        } else if (lockName.equals("xtc_tianMaoFullOrderSync")) {
            xtc_tianMaoFullOrderSync = true;
        } else if (lockName.equals("bbk_tianMaoFullOrderSync")) {
            bbk_tianMaoFullOrderSync = true;
        } else if (lockName.equals("app_orderSync")) {
            app_orderSync = true;
        } else if (lockName.equals("app_inside_orderSync")) {
            app_inside_orderSync = true;
        } else if (lockName.equals("bbk_excellent_lecture_orderSync")) {
            bbk_excellent_lecture_orderSync = true;
        } else if (lockName.equals("xtc_tictokProfileTradeSync")) {
            bbk_tictokProfileTradeSync_orderSync = true;
        } else if (lockName.equals("bbk_tictokProfileTradeSync")) {
            xtc_tictokProfileTradeSync_orderSync = true;
        } else if (lockName.equals("xtc_kuaishouProfileTradeSync")) {
            xtc_kuaishouProfileTradeSync_orderSync = true;
        } else if (lockName.equals("bbk_kuaishouProfileTradeSync")) {
            bbk_kuaishouProfileTradeSync_orderSync = true;
        } else if (lockName.equals("bbk_jingdongProfileTradeSync")) {
            bbk_jingdongProfileTradeSync_orderSync = true;
        } else if (lockName.equals("xtc_jingdongProfileTradeSync")) {
            xtc_jingdongProfileTradeSync_orderSync = true;
        } else if (lockName.equals("meitaunProfileTradeSync")) {
            meitaunProfileTradeSync_orderSync = true;
        } else if (lockName.equals("xtc_XiaoHongShuTradeSync")) {
            xtc_XiaoHongShuTradeSync = true;
        }else if (lockName.equals("bbk_XiaoHongShuTradeSync")) {
            bbk_XiaoHongShuTradeSync = true;
        }  else {
            throw new GlobalDefaultException(ResponseEnum.LOCK_NOT_FOUND);
        }
    }

    public static void release(String lockName) {
        if (lockName.equals("xtc_tianMaoProfileTradeSync")) {
            xtc_tianMaoProfileTradeSync = false;
        } else if (lockName.equals("bbk_tianMaoProfileTradeSync")) {
            bbk_tianMaoProfileTradeSync = false;
        } else if (lockName.equals("xtc_tianMaoFullOrderSync")) {
            xtc_tianMaoFullOrderSync = false;
        } else if (lockName.equals("bbk_tianMaoFullOrderSync")) {
            bbk_tianMaoFullOrderSync = false;
        } else if (lockName.equals("app_orderSync")) {
            app_orderSync = false;
        } else if (lockName.equals("app_inside_orderSync")) {
            app_inside_orderSync = false;
        } else if (lockName.equals("bbk_excellent_lecture_orderSync")) {
            bbk_excellent_lecture_orderSync = false;
        } else if (lockName.equals("xtc_tictokProfileTradeSync")) {
            bbk_tictokProfileTradeSync_orderSync = false;
        } else if (lockName.equals("bbk_tictokProfileTradeSync")) {
            xtc_tictokProfileTradeSync_orderSync = false;
        } else if (lockName.equals("xtc_kuaishouProfileTradeSync")) {
            xtc_kuaishouProfileTradeSync_orderSync = false;
        } else if (lockName.equals("bbk_kuaishouProfileTradeSync")) {
            bbk_kuaishouProfileTradeSync_orderSync = false;
        } else if (lockName.equals("bbk_jingdongProfileTradeSync")) {
            bbk_jingdongProfileTradeSync_orderSync = false;
        } else if (lockName.equals("xtc_jingdongProfileTradeSync")) {
            xtc_jingdongProfileTradeSync_orderSync = false;
        } else if (lockName.equals("meitaunProfileTradeSync")) {
            meitaunProfileTradeSync_orderSync = false;
        } else if (lockName.equals("xtc_XiaoHongShuTradeSync")) {
            xtc_XiaoHongShuTradeSync = false;
        } else if (lockName.equals("bbk_XiaoHongShuTradeSync")) {
            bbk_XiaoHongShuTradeSync = false;
        } else {
            throw new GlobalDefaultException(ResponseEnum.LOCK_NOT_FOUND);
        }
    }

    public static boolean isLock(String lockName) {
        if (lockName.equals("xtc_tianMaoProfileTradeSync")) {
            return xtc_tianMaoProfileTradeSync;
        } else if (lockName.equals("bbk_tianMaoProfileTradeSync")) {
            return bbk_tianMaoProfileTradeSync;
        } else if (lockName.equals("xtc_tianMaoFullOrderSync")) {
            return xtc_tianMaoFullOrderSync;
        } else if (lockName.equals("bbk_tianMaoFullOrderSync")) {
            return bbk_tianMaoFullOrderSync;
        } else if (lockName.equals("app_orderSync")) {
            return app_orderSync;
        } else if (lockName.equals("app_inside_orderSync")) {
            return app_inside_orderSync;
        } else if (lockName.equals("bbk_excellent_lecture_orderSync")) {
            return bbk_excellent_lecture_orderSync;
        } else if (lockName.equals("xtc_tictokProfileTradeSync")) {
            return bbk_tictokProfileTradeSync_orderSync;
        } else if (lockName.equals("bbk_tictokProfileTradeSync")) {
            return xtc_tictokProfileTradeSync_orderSync;
        } else if (lockName.equals("xtc_kuaishouProfileTradeSync")) {
            return xtc_kuaishouProfileTradeSync_orderSync;
        } else if (lockName.equals("bbk_kuaishouProfileTradeSync")) {
            return bbk_kuaishouProfileTradeSync_orderSync;
        } else if (lockName.equals("bbk_jingdongProfileTradeSync")) {
            return bbk_jingdongProfileTradeSync_orderSync;
        } else if (lockName.equals("xtc_jingdongProfileTradeSync")) {
            return xtc_jingdongProfileTradeSync_orderSync;
        } else if (lockName.equals("meitaunProfileTradeSync")) {
            return meitaunProfileTradeSync_orderSync;
        } else if (lockName.equals("xtc_XiaoHongShuTradeSync")) {
            return xtc_XiaoHongShuTradeSync;
        }  else if (lockName.equals("bbk_XiaoHongShuTradeSync")) {
            return bbk_XiaoHongShuTradeSync;
        }else {
            throw new GlobalDefaultException(ResponseEnum.LOCK_NOT_FOUND);
        }
    }

}
