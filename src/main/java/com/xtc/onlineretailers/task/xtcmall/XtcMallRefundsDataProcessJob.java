package com.xtc.onlineretailers.task.xtcmall;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xtc.marketing.adapterservice.shop.AdapterShopFeignClient;
import com.xtc.marketing.adapterservice.shop.dto.OrderDTO;
import com.xtc.marketing.adapterservice.shop.dto.RefundDTO;
import com.xtc.marketing.adapterservice.shop.dto.query.OrderGetQry;
import com.xtc.marketing.adapterservice.shop.dto.query.RefundPageQry;
import com.xtc.marketing.marketingcomponentdto.dto.PageResponse;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import com.xtc.marketing.orderservice.order.enums.OrderType;
import com.xtc.marketing.orderservice.serviceorder.ServiceOrderFeignClient;
import com.xtc.marketing.orderservice.serviceorder.enums.ServiceState;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.constant.OrderStateConstant;
import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.enums.WarehouseStatus;
import com.xtc.onlineretailers.pojo.entity.OrderDO;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.PlatformRefundDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.job.BaseTask;
import com.xtc.onlineretailers.service.*;
import com.xtc.onlineretailers.task.xtcmall.check.XtcMallRefundCheck;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.MD5Util;
import com.xtc.onlineretailers.util.RedisUtil;
import com.xtc.onlineretailers.util.XtcMessageSender;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class XtcMallRefundsDataProcessJob {

    @Resource
    private PlatformRefundService platformRefundService;
    @Resource
    private TradeService tradeService;
    @Resource
    private PlatformService platformService;
    @Resource
    private AdapterShopFeignClient adapterShopFeignClient;
    @Resource
    private OrderService orderService;

    @Resource
    private SystemParamService systemParamService;

    @Resource
    private XtcMallRefundCheck xtcMallRefundCheck;

    @Resource
    private ServiceOrderFeignClient serviceOrderFeignClient;

    @Resource
    private RedisUtil redisUtil;

    @XxlJob("xtcMallRefundsDataProcessJob")
    public ReturnT<String> xtcMallRefundsDataProcessJob(String param) throws Exception {
        return BaseTask.run(() -> {
            // 获取会员商城退款数据
            List<PlatformRefundDO> refunds = platformRefundService.list(Wrappers.<PlatformRefundDO>lambdaQuery()
                    .in(PlatformRefundDO::getPlatformId, GlobalConstant.XTC_MALL_IOT, GlobalConstant.PLATFORM_XTC_MALL_AGENT)
                    .eq(PlatformRefundDO::getIsProcess, 0)
                    .ge(PlatformRefundDO::getUpdateTime, LocalDateTime.now().minusDays(15))
                    .orderByDesc(PlatformRefundDO::getUpdateTime)
                    .last(BaseTask.getShardingLimitSql(1000)));
            String systemParamValue = systemParamService.getSystemParamByName(GlobalConstant.MEMBER_SHOP_ORDER_NOTICE);

            refunds.forEach(refundDO -> {
                try {
                    log.info("处理会员商城售后数据处理 订单 {}", refundDO.getPlatformTradeId());
                    // 获取订单
                    TradeDO trade = tradeService.getTradeByTradeId(refundDO.getPlatformTradeId());
                    if (ObjectUtils.isEmpty(trade)) {
                        return;
                    }
                    // 待处理不处理退款信息
                    if (trade.getStatusOrder().equals(OrderStatus.WAIT_DEAL.name())) {
                        return;
                    }

                    PlatformDO platformDO = platformService.getPlatformById(trade.getPlatformId());
                    TradeDO updateTrade = new TradeDO();
                    updateTrade.setPlatformTradeId(trade.getPlatformTradeId());
                    if (trade.getPlatformId() == GlobalConstant.XTC_MALL_IOT) {
                        log.info("订单 {} 处理补货单退款", trade.getPlatformTradeId());
                        this.handleXtcMallIotRefund(trade, updateTrade);
                        return;
                    }
                    // 订单已发货或者仓库状态已分拣状态 发送企业预警
                    if (trade.getWarehouseStatus().equals(WarehouseStatus.HAVE_SORTED.name()) ||
                            trade.getStatusOrder().equals(OrderStatus.HAS_SHIP.name())) {
                        // 更新退款处理状态
                        PlatformRefundDO updatePlatformRefund = new PlatformRefundDO();
                        updatePlatformRefund.setId(refundDO.getId());
                        updatePlatformRefund.setIsProcess(1);
                        platformRefundService.updateById(updatePlatformRefund);
                        String messages = "订单 %s，存在发货后退款，请迅速核实";
                        this.sendNotification(String.format(messages, trade.getPlatformTradeId()), systemParamValue);
                        return;
                    }
                    // 获取退款列表
                    RefundPageQry refundPageQry = RefundPageQry.builder()
                            .shopCode(platformDO.getShopCode())
                            .pageIndex(1)
                            .pageSize(10)
                            .orderNo(trade.getPlatformTradeId())
                            .build();
                    PageResponse<RefundDTO> refunPageResponse = adapterShopFeignClient.pageRefunds(refundPageQry);
                    if (BooleanUtils.isFalse(refunPageResponse.isSuccess()) || CollectionUtils.isEmpty(refunPageResponse.getData())) {
                        log.info("订单 {}，查询服务单列表异常", trade.getPlatformTradeId());
                        return;
                    }
                    // 过滤退款成功的物料代码
                    List<String> skuErpCodes = refunPageResponse.getData().stream()
                            .filter(serviceOrderDTO -> serviceOrderDTO.getServiceState().equals(ServiceState.COMPLETED.name()))
                            .map(RefundDTO::getSkuErpCode)
                            .collect(Collectors.toList());

                    List<OrderDO> orders = orderService.getOrderByTid(trade.getPlatformTradeId());
                    // 过滤退款成功的订单明细
                    List<OrderDO> updateOrders = orders.stream()
                            .filter(orderDO -> orderDO.getMaterialType() == 1)
                            .filter(orderDO -> skuErpCodes.contains(orderDO.getErpCode()))
                            .map(orderDO -> {
                                OrderDO order = new OrderDO();
                                order.setRefundNum(orderDO.getNum());
                                order.setId(orderDO.getId());
                                return order;
                            })
                            .collect(Collectors.toList());
                    // 更新明细退货数量（退货数量=发货数量）
                    if (CollectionUtils.isNotEmpty(updateOrders)) {
                        this.orderService.updateBatchById(updateOrders);
                    }

                    // 退款成功机器和配件明细数量
                    int refundNum = orders.stream()
                            .filter(orderDO -> orderDO.getMaterialType() != 3)
                            .filter(orderDO -> skuErpCodes.contains(orderDO.getErpCode()))
                            .map(OrderDO::getNum)
                            .reduce(Integer::sum)
                            .orElse(0);

                    // 订单机器和配件明细数量
                    int orderNum = orders.stream()
                            .filter(orderDO -> orderDO.getMaterialType() != 3)
                            .map(OrderDO::getNum)
                            .reduce(Integer::sum)
                            .orElse(0);

                    // 没有退款信息，不做处理
                    if (refundNum == 0) {
                        log.info("订单 {}，退款数量 {} 电商数量 {} 没有匹配到退款信息，不做处理", trade.getPlatformTradeId(), refundNum, orderNum);
                        return;
                    }

                    //  全额退款
                    if (orderNum == refundNum) {
                        log.info("平台 {} 订单 {} 已申请售后", trade.getPlatformName(), trade.getPlatformTradeId());
                        // 更新订单
                        updateTrade.setStatusOrder(OrderStatus.REJECT.name());
                        updateTrade.setWarehouseStatus(WarehouseStatus.HAVE_REJECTED.name());
                        updateTrade.setOrderReturnTime(DateUtil.dateToStr(new Date()));
                        this.tradeService.updateByTradeId(updateTrade);
                    }
                    // 部分退款
                    if (orderNum != refundNum) {
                        String message = "平台 %s 订单 %s 存在部分退款明细 请核实！";
                        XtcMessageSender.cardTextMessage(systemParamValue, String.format(message, trade.getPlatformName(), trade.getPlatformTradeId()));
                        // 更新订单
                        updateTrade.setStatusOrder(OrderStatus.REJECT.name());
                        updateTrade.setWarehouseStatus(WarehouseStatus.HAVE_REJECTED.name());
                        updateTrade.setOrderReturnTime(DateUtil.dateToStr(new Date()));
                        this.tradeService.updateByTradeId(updateTrade);
                    }
                    // 更新退款处理状态
                    PlatformRefundDO updatePlatformRefund = new PlatformRefundDO();
                    updatePlatformRefund.setId(refundDO.getId());
                    updatePlatformRefund.setIsProcess(1);
                    platformRefundService.updateById(updatePlatformRefund);
                    log.info("订单 {}，退款信息，处理成功", refundDO.getPlatformTradeId());
                } catch (Exception e) {
                    String message = "平台 %s 订单 %s 售后数据处理异常: %s";
                    String errorMessage = String.format(message, refundDO.getPlatformName(), refundDO.getPlatformTradeId(), e.getMessage());
                    this.sendNotification(errorMessage, systemParamValue);
                    log.warn("订单 {} 售后数据处理异常 {}", refundDO.getPlatformTradeId(), e.getMessage(), e);
                }
            });
        });
    }

    /**
     * 发送企业通知
     *
     * @param errorMessage     信息
     * @param systemParamValue 工号
     */
    public void sendNotification(String errorMessage, String systemParamValue) {
        String contentKey = MD5Util.encode(errorMessage);
        Duration duration = Duration.ofHours(2);
        Long incr = redisUtil.incr(contentKey, duration);
        if (incr != null && incr > 1) {
            log.warn("发送企业微信通知失败 - 已发送过通知 {} 秒内只能发送一次", duration.getSeconds());
            return;
        }
        XtcMessageSender.cardTextMessage(systemParamValue, errorMessage);
    }

    /**
     * iot订单处理退款
     *
     * @param trade       订单
     * @param updateTrade 更新订单
     */
    private void handleXtcMallIotRefund(TradeDO trade, TradeDO updateTrade) {
        OrderGetQry orderGetQry = OrderGetQry.builder()
                .shopCode(GlobalConstant.XTC_MALL_IOT_CODE)
                .orderNo(trade.getPlatformTradeId())
                .build();

        SingleResponse<OrderDTO> order = adapterShopFeignClient.getOrder(orderGetQry);
        OrderDTO orderDTO = order.getData();
        if (BooleanUtils.isTrue(order.isSuccess()) || ObjectUtils.isEmpty(orderDTO)) {
            return;
        }
        // 过账前退款
        if (OrderType.RESTOCK.name().equals(orderDTO.getOrderType()) && OrderStateConstant.ORDER_STATUS_CLOSE.contains(orderDTO.getOrderState())) {
            updateTrade.setStatusOrder(OrderStatus.HAS_CANCEL.name());
            this.tradeService.updateByTradeId(updateTrade);
        }
    }

}
