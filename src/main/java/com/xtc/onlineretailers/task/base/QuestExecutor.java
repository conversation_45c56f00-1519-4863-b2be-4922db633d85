package com.xtc.onlineretailers.task.base;

import com.google.common.base.Joiner;
import com.xtc.onlineretailers.executor.command.WorkWechatAlertSenderCmdExe;
import com.xtc.onlineretailers.pojo.entity.TaskLogDO;
import com.xtc.onlineretailers.service.TaskLogService;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.onlineretailers.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * 任务处理器，执行任务并保存任务记录
 */
@Slf4j
public abstract class QuestExecutor<P> {

    /**
     * 任务状态
     */
    private static final String TASK_STATE_SUCCESS = "success";
    public static final String TASK_STATE_FAILED = "fail";
    private static final String TASK_STATE_RETRY = "retry";
    /**
     * 任务名称分隔符
     */
    private static final String TASK_NAME_SEPARATOR = "_";
    /**
     * 拉取分页数据，最大循环页数
     */
    private static final int MAX_LOOP = 1000;

    /**
     * 任务记录
     */
    protected ThreadLocal<TaskLogDO> taskLogHolder = new ThreadLocal<>();
    /**
     * 任务参数
     */
    protected ThreadLocal<P> jobParamHolder = new ThreadLocal<>();

    private TaskLogService taskLogService;

    private WorkWechatAlertSenderCmdExe workWechatAlertSenderCmdExe;

    @Autowired
    public final void setTaskLogService(TaskLogService taskLogRepository) {
        this.taskLogService = taskLogRepository;
    }

    @Autowired
    public final void setSendWechatWorkNotifyCmdExe(WorkWechatAlertSenderCmdExe workWechatAlertSenderCmdExe) {
        this.workWechatAlertSenderCmdExe = workWechatAlertSenderCmdExe;
    }

    /**
     * 任务逻辑，需要子类实现
     */
    public abstract void job();

    /**
     * 执行任务
     *
     * @param name 任务名称
     */
    public final void taskExecute(String name) {
        this.taskExecute(name, null, null, null);
    }

    /**
     * 执行任务
     *
     * @param name  任务名称
     * @param param 任务参数
     */
    public final void taskExecute(String name, String param) {
        this.taskExecute(name, param, null, null);
    }

    /**
     * 执行任务
     *
     * @param taskLog 任务记录
     */
    public final void taskExecute(TaskLogDO taskLog) {
        // 任务重试时，需要处理任务名称
        String name = taskLog.getTaskName().split(TASK_NAME_SEPARATOR)[0];
        this.taskExecute(name, taskLog.getParams(), taskLog.getSyncStartTime(), taskLog.getSyncEndTime());
    }

    /**
     * 执行任务
     *
     * @param name          任务名称
     * @param param         任务参数
     * @param syncStartTime 业务开始时间
     * @param syncEndTime   业务结束时间
     */
    public final void taskExecute(String name, String param, Date syncStartTime, Date syncEndTime) {
        // 参数初始化
        P jobParam = GsonUtil.jsonToGenericSuperclassBean(param, this);

        // 生成任务记录
        TaskLogDO newTaskLog = new TaskLogDO();
        newTaskLog.setTaskName(Joiner.on(TASK_NAME_SEPARATOR).skipNulls().join(name, ""));
        newTaskLog.setExecuteTime(new Date());
        newTaskLog.setSyncStartTime(syncStartTime);
        newTaskLog.setSyncEndTime(syncEndTime);
        newTaskLog.setParams(Optional.ofNullable(jobParam).map(GsonUtil::objectToJson).orElse(null));
        newTaskLog.setUpdateTime(DateUtil.nowDateTimeStr());
        newTaskLog.setCreateTime(DateUtil.nowDateTimeStr());
        this.taskLogService.save(newTaskLog);

        this.taskLogHolder.set(newTaskLog);
        this.jobParamHolder.set(jobParam);

        boolean taskSuccess = false;
        String taskRemark = null;
        try {
            // 执行任务逻辑
            this.job();
            taskSuccess = true;
        } catch (Exception e) {
            taskSuccess = e instanceof QuestCannotProceedException;
            taskRemark = e.getMessage();
            throw e;
        } finally {
            // 记录任务结果
            if (taskSuccess) {
                this.taskSuccess(newTaskLog.getId(), taskRemark);
            } else {
                this.taskFailed(newTaskLog.getId(), taskRemark);
            }
            // 清除缓存
            this.taskLogHolder.remove();
            this.jobParamHolder.remove();
        }
    }

    /**
     * 重试任务
     *
     * @param jobName         任务名称
     * @param retryOffsetDays 任务重试偏移天数
     */
    protected final void taskRetry(String jobName, int retryOffsetDays) {
        LocalDateTime endTime = LocalDateTime.now().minusHours(1);
        LocalDateTime startTime = endTime.minusDays(retryOffsetDays);
        List<TaskLogDO> tasks = this.listFailedTaskByExecuteTime(jobName, startTime, endTime);
        tasks.forEach(task -> {
            if (!TASK_STATE_FAILED.equals(task.getResult())) {
                log.info("重试任务 - 任务状态无法进行重试，任务id: {} 状态: {}", task.getId(), task.getResult());
                return;
            }
            try {
                log.info("重试任务 - 开始重试，任务id: {}", task.getId());
                // 先更新任务状态为重试，避免后续任务重复执行
                this.taskRetry(task.getId());
                // 重新执行任务
                this.taskExecute(task);
                log.info("重试任务 - 重试完成，任务id: {}", task.getId());
            } catch (Exception e) {
                log.warn("重试任务 - 重试失败，任务id: {}", task.getId(), e);
                String content = "重试任务 - 重试失败，请检查任务是否正常。如后续无需重试则设置状态为【ignore】" +
                        "\n任务：%s\n异常信息：%s";
                workWechatAlertSenderCmdExe.execute(content, GsonUtil.objectToJson(task), e.getMessage());
            }
        });
    }

    /**
     * 计算任务时间
     *
     * @param initTime 数据初始时间
     * @param offset   偏移量
     */
    protected final void calculateTaskTime(LocalDateTime initTime, Duration offset) {
        TaskLogDO taskLog = taskLogHolder.get();
        // 如果已经设置任务时间，则不再计算
        if (taskLog.getSyncStartTime() != null && taskLog.getSyncEndTime() != null) {
            log.info("已经设置任务时间，不再计算");
            return;
        }

        // 任务终止时间，当前时间前移 10 分钟，避免平台接口不接受，或者用户 10 分钟内进行退款操作而重复处理数据
        LocalDateTime jobTerminateTime = LocalDateTime.now().minusMinutes(10);
        // 任务初始化时间，上一次执行成功的结束时间 > 数据初始化时间 > 任务终止时间
        LocalDateTime jobInitTime = taskLogService.getLastByNameAndState(taskLog.getTaskName(), TASK_STATE_SUCCESS)
                .map(TaskLogDO::getSyncEndTime).map(DateUtil::dateToLocalDateTime)
                .orElseGet(() -> Optional.ofNullable(initTime).orElse(jobTerminateTime));
        // 计算同步时间范围，每次获取的起始时间前移 10 分钟（双11大促时建议前移 30 分钟左右），防止极端情况下由于平台压力而导致延迟更新到数据库而产生的漏单。
        LocalDateTime syncStartTime = jobInitTime.minusMinutes(10);
        LocalDateTime syncEndTime = jobInitTime.plus(offset);

        // 分片任务时间计算，每个分片任务执行不同的时间范围
        int shardingIndex = BaseQuest.getShardingIndex();
        int shardingTotal = BaseQuest.getShardingTotal();
        if (shardingTotal > 1) {
            // 分片时间 = 任务时间 / 分片总数
            Duration shardingDuration = Duration.between(syncStartTime, syncEndTime).dividedBy(shardingTotal);
            // 分片开始时间 = 任务开始时间 + 分片时间 * 分片索引（第几个分片）
            syncStartTime = syncStartTime.plus(shardingDuration.multipliedBy(shardingIndex));
            // 分片结束时间 = 分片开始时间 + 分片时间
            syncEndTime = syncStartTime.plus(shardingDuration);
        }

        // 开始时间，除了第一个任务其他都默认前移 1 分钟，降低漏单风险
        syncStartTime = shardingIndex == 0 ? syncStartTime : syncStartTime.minusMinutes(1);
        // 结束时间，不能超过终止时间
        syncEndTime = syncEndTime.isAfter(jobTerminateTime) ? jobTerminateTime : syncEndTime;
        // 计算后的同步时间不合法无法执行任务时，标识任务完成，并结束任务
        if (syncStartTime.isAfter(jobTerminateTime) || syncStartTime.isAfter(syncEndTime)) {
            throw new QuestCannotProceedException("任务开始时间在未来，无法执行任务 platform: " + taskLog.getPlatformName());
        }

        // 更新任务记录
        taskLog.setSyncStartTime(DateUtil.localDateTimeToDate(syncStartTime));
        taskLog.setSyncEndTime(DateUtil.localDateTimeToDate(syncEndTime));
        taskLogService.updateById(taskLog);
    }

    /**
     * 查询失败的任务记录列表，默认最多 10 条数据
     *
     * @param taskNamePrefix 任务名称前缀
     * @param startTime      执行时间开始
     * @param endTime        执行时间结束
     * @return 任务记录列表
     */
    protected final List<TaskLogDO> listFailedTaskByExecuteTime(String taskNamePrefix,
                                                                LocalDateTime startTime, LocalDateTime endTime) {
        String limitSql = BaseQuest.getShardingLimitSql(1);
        return taskLogService.listByExecuteTimeAndState(taskNamePrefix, TASK_STATE_FAILED, startTime, endTime, limitSql);
    }

    /**
     * 任务成功
     *
     * @param taskId 任务id
     * @param remark 备注（过长会被截断为 200 字符）
     */
    protected final void taskSuccess(long taskId, String remark) {
        TaskLogDO updateTask = new TaskLogDO();
        updateTask.setId(taskId);
        updateTask.setResult(TASK_STATE_SUCCESS);
        updateTask.setRemark(StringUtils.substring(remark, 0, 200));
        taskLogService.updateById(updateTask);
    }

    /**
     * 任务失败
     *
     * @param taskId 任务id
     * @param remark 备注（过长会被截断为 200 字符）
     */
    protected final void taskFailed(long taskId, String remark) {
        TaskLogDO updateTask = new TaskLogDO();
        updateTask.setId(taskId);
        updateTask.setResult(TASK_STATE_FAILED);
        updateTask.setRemark(StringUtils.substring(remark, 0, 200));
        taskLogService.updateById(updateTask);
    }

    /**
     * 任务重试
     *
     * @param taskId 任务id
     */
    protected final void taskRetry(long taskId) {
        TaskLogDO updateTask = new TaskLogDO();
        updateTask.setId(taskId);
        updateTask.setResult(TASK_STATE_RETRY);
        taskLogService.updateById(updateTask);
    }

}
