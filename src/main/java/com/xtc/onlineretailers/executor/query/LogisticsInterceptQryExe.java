package com.xtc.onlineretailers.executor.query;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xtc.onlineretailers.pojo.entity.LogisticsInterceptDO;
import com.xtc.onlineretailers.pojo.query.LogisticsInterceptPageQry;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.repository.LogisticsInterceptRepository;
import com.xtc.onlineretailers.util.DateUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 分页查询物流拦截列表
 */
@Component
@RequiredArgsConstructor
public class LogisticsInterceptQryExe {

    private final LogisticsInterceptRepository logisticsInterceptRepository;

    /**
     * 分页查询物流拦截列表
     *
     * @param qry 参数
     * @return 拦截列表
     */
    public PaginationVO<LogisticsInterceptDO> execute(LogisticsInterceptPageQry qry) {
        IPage<LogisticsInterceptDO> logisticsInterceptDOIPage = this.pageBy(qry);
        return PaginationVO.newVO(logisticsInterceptDOIPage);
    }

    /**
     * 构建分页条件
     *
     * @param qry 参数
     * @return 分页条件
     */
    public IPage<LogisticsInterceptDO> pageBy(LogisticsInterceptPageQry qry) {
        LocalDateTime createStartTime = DateUtil.minTimeOfLocalDate(qry.getCreateStartTime());
        LocalDateTime createEndTime = DateUtil.maxTimeOfLocalDate(qry.getCreateEndTime());
        Wrapper<LogisticsInterceptDO> wrapper = Wrappers.<LogisticsInterceptDO>lambdaQuery()
                .eq(qry.getRefundStatus() != null, LogisticsInterceptDO::getRefundStatus, qry.getRefundStatus())
                .eq(qry.getPlatformId() != null, LogisticsInterceptDO::getPlatformId, qry.getPlatformId())
                .eq(qry.getTradeId() != null, LogisticsInterceptDO::getTradeId, qry.getTradeId())
                .eq(qry.getExpressCompany() != null, LogisticsInterceptDO::getExpressCompany, qry.getExpressCompany())
                .eq(qry.getExpressTrackingNo() != null, LogisticsInterceptDO::getExpressTrackingNo, qry.getExpressTrackingNo())
                .between(ObjectUtils.isNotNull(createStartTime, createEndTime),
                        LogisticsInterceptDO::getCreateTime, createStartTime, createEndTime)
                .orderByDesc(LogisticsInterceptDO::getCreateTime);
        return logisticsInterceptRepository.page(qry.createPage(), wrapper);
    }

}
