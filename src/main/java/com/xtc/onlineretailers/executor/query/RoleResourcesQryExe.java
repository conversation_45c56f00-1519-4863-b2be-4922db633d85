package com.xtc.onlineretailers.executor.query;

import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.dto.ResourcesDTO;
import com.xtc.onlineretailers.pojo.entity.CatalogsDO;
import com.xtc.onlineretailers.pojo.entity.ResourcesDO;
import com.xtc.onlineretailers.pojo.entity.RoleResourceDO;
import com.xtc.onlineretailers.pojo.entity.RolesDO;
import com.xtc.onlineretailers.repository.CatalogsDao;
import com.xtc.onlineretailers.repository.ResourceDao;
import com.xtc.onlineretailers.repository.RoleResourceDao;
import com.xtc.onlineretailers.repository.RolesDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 获取角色的资源权限列表
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class RoleResourcesQryExe {

    private final RoleResourceDao roleResourceDao;

    private final RolesDao rolesDao;

    private final ResourceDao resourceDao;

    private final CatalogsDao catalogsDao;

    public List<ResourcesDTO> execute(String roleCode) {
        // 数据初始化
        RolesDO rolesDO = rolesDao.getByRoleCode(roleCode)
                .orElseThrow(() -> GlobalDefaultException.of("角色编码 %s 不存在", roleCode));
        List<RoleResourceDO> roleResources = roleResourceDao.listByRoleId(rolesDO.getId());
        if (CollectionUtils.isEmpty(roleResources)) {
            throw GlobalDefaultException.of("角色编码 %s 资源权限为空", roleCode);
        }
        // 角色的资源id集合
        List<String> resourceId = roleResources.stream().map(RoleResourceDO::getResourceId).collect(Collectors.toList());
        // 获取完整的资源数据
        List<ResourcesDO> resources = resourceDao.listByResourceId(resourceId);
        // 资源目录id分组 <资源目录id, 资源集合>
        Map<String, List<ResourcesDO>> catalogMap = resources.stream().collect(Collectors.groupingBy(ResourcesDO::getCatalogId));
        // 组装接口返回值：资源目录、资源目录对应所有资源名称
        return catalogMap.entrySet().stream()
                .map(catalog -> {
                    ResourcesDTO resourcesDTO = new ResourcesDTO();
                    // 确定资源目录
                    CatalogsDO catalogsDO = catalogsDao.getById(catalog.getKey());
                    resourcesDTO.setMenuName(catalogsDO.getName());
                    // 确定目录下的所有资源名称
                    List<String> resourceNames = catalog.getValue().stream().map(ResourcesDO::getName).collect(Collectors.toList());
                    resourcesDTO.setResourceNames(resourceNames);
                    return resourcesDTO;
                })
                .collect(Collectors.toList());
    }
}
