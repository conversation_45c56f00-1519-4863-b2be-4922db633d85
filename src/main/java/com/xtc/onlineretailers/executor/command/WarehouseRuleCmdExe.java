package com.xtc.onlineretailers.executor.command;

import com.xtc.onlineretailers.enums.SFStatus;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.entity.WarehouseDO;
import com.xtc.onlineretailers.repository.WarehouseDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.xtc.onlineretailers.constant.GlobalConstant.WAREHOUSE_LOCAL;
import static com.xtc.onlineretailers.constant.GlobalConstant.WAREHOUSE_SF;

/**
 * 仓库储位规则
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class WarehouseRuleCmdExe {

    private final WarehouseDao warehouseDao;

    /**
     * 快递公司
     */
    private static final String EXPRESS_COMPANY = "顺丰速运";

    public void execute(TradeDO tradeDO) {
        if (!EXPRESS_COMPANY.equals(tradeDO.getExpressCompany())) {
            log.info("订单 {}，快递公司 {}，不满足抛转顺丰规则", tradeDO.getPlatformTradeId(), tradeDO.getExpressType());
            return;
        }
        List<WarehouseDO> warehouses = warehouseDao.listByWarehouses();
        for (WarehouseDO warehouse : warehouses) {
            if (!warehouse.getArea().contains(tradeDO.getReceiverProvince())) {
                continue;
            }
            String warehouseCode = WAREHOUSE_LOCAL.equals(warehouse.getWarehouseStorage()) ? WAREHOUSE_LOCAL : WAREHOUSE_SF;
            String sfStatus = WAREHOUSE_LOCAL.equals(warehouse.getWarehouseStorage()) ? SFStatus.NOT_SF.name() : SFStatus.WAIT_THROW.name();
            tradeDO.setWarehouseCode(warehouseCode);
            tradeDO.setWarehouseStorage(warehouse.getWarehouseStorage());
            tradeDO.setStatusSf(sfStatus);
            return;
        }
    }

}
