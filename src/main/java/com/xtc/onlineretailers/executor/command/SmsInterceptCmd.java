package com.xtc.onlineretailers.executor.command;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 短信拦截
 */
@Getter
@Setter
@ToString
public class SmsInterceptCmd {

    /**
     * 短信id
     */
    @NotNull
    private Long id;
    /**
     * 拦截
     */
    @NotNull
    private Boolean intercept;
    /**
     * 备注
     */
    @NotBlank
    @Length(max = 200)
    private String remake;

}
