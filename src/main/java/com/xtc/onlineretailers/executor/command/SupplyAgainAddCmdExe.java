package com.xtc.onlineretailers.executor.command;

import com.xtc.onlineretailers.enums.PostSaleStatus;
import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.enums.SupplySyncStatus;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.bo.AgainGiftBO;
import com.xtc.onlineretailers.pojo.bo.SupplyAgainGiftBO;
import com.xtc.onlineretailers.pojo.command.SupplyAgainAddCmd;
import com.xtc.onlineretailers.pojo.entity.SupplyAgainDO;
import com.xtc.onlineretailers.pojo.entity.SupplyGiftDO;
import com.xtc.onlineretailers.repository.SupplyAgainRepository;
import com.xtc.onlineretailers.repository.SupplyGiftRepository;
import com.xtc.onlineretailers.util.BeanCopier;
import com.xtc.onlineretailers.util.DateUtil;
import com.xtc.springboot.pojo.entity.GlobalContext;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
@Slf4j
public class SupplyAgainAddCmdExe {

    private final SupplyAgainRepository supplyAgainRepository;
    private final SupplyGiftRepository supplyGiftRepository;

    /**
     * 新增补寄
     *
     * @param cmd 参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void execute(SupplyAgainAddCmd cmd) {
        // 校验补寄赠品
        this.isMultipleReshipment(cmd);
        cmd.getSupplyAgainGifts().forEach(supplyAgainGiftAddCmd -> {
            // 生成补寄
            SupplyAgainDO supplyAgainDO = BeanCopier.copy(cmd, SupplyAgainDO::new);
            // 当前客服姓名
            supplyAgainDO.setServiceName(GlobalContext.getUser().getName());
            // 下单时间
            supplyAgainDO.setCreateTime(DateUtil.dateToStr(new Date()));
            // 补寄确认 : 默认未确认
            supplyAgainDO.setIsSure(-1);
            // 抛单 : 默认未抛单
            supplyAgainDO.setIsThrow(-1);
            // 状态 : 默认客服下单
            supplyAgainDO.setStatus(PostSaleStatus.SERVICE_ORDER.name());
            // 非系统物料改成未同步状态，系统物料改成未同步状态
            SupplySyncStatus syncState = cmd.getIsSysMateriel() == 1 ? SupplySyncStatus.NOT_SYNC : SupplySyncStatus.SYNCED;
            supplyAgainDO.setSyncState(syncState);
            // 补寄数量
            Integer sum = supplyAgainGiftAddCmd.getAgainGifts().stream()
                    .map(AgainGiftBO::getNum)
                    .reduce(Integer::sum)
                    .orElse(0);
            supplyAgainDO.setNum(String.valueOf(sum));
            supplyAgainRepository.save(supplyAgainDO);
            this.saveSupplyGift(supplyAgainDO, supplyAgainGiftAddCmd.getAgainGifts());
        });
    }

    /**
     * 保存补寄赠品
     *
     * @param supplyAgainDO    补寄
     * @param supplyAgainGifts 补寄赠品列表
     */
    private void saveSupplyGift(SupplyAgainDO supplyAgainDO, List<AgainGiftBO> supplyAgainGifts) {
        // 补寄赠品列表
        List<SupplyGiftDO> saveSupplyGifts = supplyAgainGifts.stream()
                .map(againGiftBO -> {
                    SupplyGiftDO supplyGiftDO = BeanCopier.copy(againGiftBO, SupplyGiftDO::new);
                    supplyGiftDO.setSupplyAgainId(supplyAgainDO.getSupplyAgainId());
                    supplyGiftDO.setCreateTime(new Date());
                    return supplyGiftDO;
                })
                .collect(Collectors.toList());
        // 保存补寄赠品
        this.supplyGiftRepository.saveBatch(saveSupplyGifts);
    }

    /**
     * 校验是否为多次补寄
     *
     * @param cmd 参数
     */
    private void isMultipleReshipment(SupplyAgainAddCmd cmd) {
        if (BooleanUtils.isNotTrue(cmd.getIsMoreTimes())) {
            supplyAgainRepository.listByTradeIdAndSupplyType(cmd.getPlatformTradeId(), cmd.getSupplyType()).stream()
                    .map(supplyAgainDO -> supplyGiftRepository.listBySupplyAgainId(supplyAgainDO.getSupplyAgainId()))
                    .flatMap(Collection::stream)
                    .filter(gift -> checkGiftItems(cmd, gift))
                    .findAny()
                    .ifPresent(elem -> {
                        throw new GlobalDefaultException(ResponseEnum.SUPPLYAGAIN_EXIST);
                    });
        }
    }

    /**
     * 校验赠品明细
     *
     * @param cmd  参数
     * @param gift 赠品明细
     * @return 结果
     */
    private boolean checkGiftItems(SupplyAgainAddCmd cmd, SupplyGiftDO gift) {
        return cmd.getSupplyAgainGifts().stream()
                .map(SupplyAgainGiftBO::getAgainGifts)
                .flatMap(Collection::stream)
                .anyMatch(againGiftBO -> againGiftBO.getErpCode().equals(gift.getErpCode()));
    }

}
