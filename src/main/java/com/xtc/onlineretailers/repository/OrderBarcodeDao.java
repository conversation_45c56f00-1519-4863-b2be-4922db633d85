package com.xtc.onlineretailers.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.mapper.master.OrderBarcodeMapper;
import com.xtc.onlineretailers.pojo.entity.OrderBarcodeDO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
public class OrderBarcodeDao extends ServiceImpl<OrderBarcodeMapper, OrderBarcodeDO> {

    /**
     * 获取发货条码列表
     *
     * @param tradeId 订单号
     * @return 发货条码列表
     */
    public List<OrderBarcodeDO> listByTradeId(String tradeId) {
        if (tradeId == null) {
            return Collections.emptyList();
        }
        Wrapper<OrderBarcodeDO> wrapper = Wrappers.<OrderBarcodeDO>lambdaQuery()
                .eq(OrderBarcodeDO::getPlatformTradeId, tradeId)
                .orderByDesc(OrderBarcodeDO::getId)
                .last(BaseDaoConstant.DEFAULT_LIST_SIZE_LIMIT);
        return this.list(wrapper);
    }

    /**
     * 统计发货条码数量
     *
     * @param barcode 条码
     * @return 发货条码数量
     */
    public long countByBarcode(String barcode) {
        if (barcode == null) {
            return 0;
        }
        Wrapper<OrderBarcodeDO> wrapper = Wrappers.<OrderBarcodeDO>lambdaQuery()
                .eq(OrderBarcodeDO::getBarcode, barcode)
                .orderByDesc(OrderBarcodeDO::getId)
                .last(BaseDaoConstant.DEFAULT_LIST_SIZE_LIMIT);
        return this.count(wrapper);
    }

}
