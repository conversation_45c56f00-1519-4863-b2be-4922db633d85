package com.xtc.onlineretailers.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.mapper.master.AgencyTradeImportMapper;
import com.xtc.onlineretailers.pojo.entity.TradeUploadLogDO;
import com.xtc.onlineretailers.pojo.query.AgencyTradeImportPageQry;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Optional;

@Component
public class TradeUploadLogRepository extends ServiceImpl<AgencyTradeImportMapper, TradeUploadLogDO> {

    /**
     * 分页查询条码记录
     *
     * @param qry 参数
     * @return 分页条码记录
     */
    public IPage<TradeUploadLogDO> pageBy(AgencyTradeImportPageQry qry) {
        // 日期转时间格式 yyyy-MM-dd -> yyyy-MM-dd HH:mm:ss
        LocalDateTime createTimeStart = Optional.ofNullable(qry.getCreateDateStart())
                .map(t -> t.atTime(LocalTime.MIN)).orElse(null);
        LocalDateTime createTimeEnd = Optional.ofNullable(qry.getCreateDateEnd())
                .map(t -> t.atTime(LocalTime.MAX)).orElse(null);
        Wrapper<TradeUploadLogDO> wrapper = Wrappers.<TradeUploadLogDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(qry.getTradeId()), TradeUploadLogDO::getTradeId, qry.getTradeId())
                .eq(StringUtils.isNotBlank(qry.getExpressTrackingNo()), TradeUploadLogDO::getExpressTrackingNo, qry.getExpressTrackingNo())
                .eq(StringUtils.isNotBlank(qry.getPlatformName()), TradeUploadLogDO::getPlatformName, qry.getPlatformName())
                .between(ObjectUtils.allNotNull(createTimeStart, createTimeEnd),
                        TradeUploadLogDO::getCreateTime, createTimeStart, createTimeEnd)
                .orderByDesc(TradeUploadLogDO::getCreateTime);
        return this.page(qry.createPage(), wrapper);
    }

    /**
     * 根据订单号查询代发代发记录
     *
     * @param tradeId 订单号
     * @return 代发记录
     */
    public Optional<TradeUploadLogDO> getByTradeId(String tradeId) {
        if (StringUtils.isBlank(tradeId)) {
            return Optional.empty();
        }
        Wrapper<TradeUploadLogDO> wrapper = Wrappers.<TradeUploadLogDO>lambdaQuery()
                .eq(TradeUploadLogDO::getTradeId, tradeId)
                .last("limit 1");
        return Optional.ofNullable(this.getOne(wrapper));
    }

}
