package com.xtc.onlineretailers.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.enums.TradeExtendType;
import com.xtc.onlineretailers.mapper.master.TradeExtendMapper;
import com.xtc.onlineretailers.pojo.entity.TradeExtendDO;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

@Component
public class TradeExtendRepository extends ServiceImpl<TradeExtendMapper, TradeExtendDO> {

    /**
     * 根据订单号和订单类型查询扩展表
     *
     * @param tradeId 订单号
     * @param type    操作类型
     * @return 扩展列表
     */
    public List<TradeExtendDO> listByTradeIdAndType(List<String> tradeId, TradeExtendType type) {
        if (tradeId == null || type == null) {
            return Collections.emptyList();
        }
        Wrapper<TradeExtendDO> wrapper = Wrappers.<TradeExtendDO>lambdaQuery()
                .in(TradeExtendDO::getTradeId, tradeId)
                .eq(TradeExtendDO::getType, type)
                .last("limit 100");
        return this.list(wrapper);
    }

}
