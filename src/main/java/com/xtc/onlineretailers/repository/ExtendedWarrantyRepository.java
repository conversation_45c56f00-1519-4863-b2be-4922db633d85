package com.xtc.onlineretailers.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.mapper.master.ExtendedWarrantyMapper;
import com.xtc.onlineretailers.pojo.entity.ExtendedWarrantyDO;
import com.xtc.onlineretailers.pojo.query.ExtendedWarrantyQry;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Component
public class ExtendedWarrantyRepository extends ServiceImpl<ExtendedWarrantyMapper, ExtendedWarrantyDO> {

    /**
     * 根据条码和订单号保存或者更新延保记录
     *
     * @param extendedWarrantyDO 延保
     */
    public void saveOrUpdateByBarcodeAndTradeId(ExtendedWarrantyDO extendedWarrantyDO) {
        if (extendedWarrantyDO == null || extendedWarrantyDO.getPlatformTradeId() == null) {
            return;
        }
        Wrapper<ExtendedWarrantyDO> wrapper = Wrappers.<ExtendedWarrantyDO>lambdaUpdate()
                .eq(ExtendedWarrantyDO::getPlatformTradeId, extendedWarrantyDO.getPlatformTradeId())
                .eq(ExtendedWarrantyDO::getBarcode, extendedWarrantyDO.getBarcode())
                .orderByDesc(ExtendedWarrantyDO::getCreateTime)
                .last(BaseDaoConstant.LIMIT_ONE);
        this.saveOrUpdate(extendedWarrantyDO, wrapper);
    }

    /**
     * 根据订单号查询延保服务记录列表
     *
     * @param tradeId 订单号
     * @return 延保服务记录列表
     */
    public List<ExtendedWarrantyDO> listByTradeId(String tradeId) {
        if (tradeId == null) {
            return Collections.emptyList();
        }
        Wrapper<ExtendedWarrantyDO> wrapper = Wrappers.<ExtendedWarrantyDO>lambdaQuery()
                .eq(ExtendedWarrantyDO::getPlatformTradeId, tradeId)
                .last(BaseDaoConstant.DEFAULT_LIST_SIZE_LIMIT);
        return this.list(wrapper);
    }

    /**
     * 延宝记录数量
     *
     * @param tradeId 订单号
     * @return 延宝记录数量
     */
    public long countByTradeId(String tradeId) {
        Wrapper<ExtendedWarrantyDO> wrapper = Wrappers.<ExtendedWarrantyDO>lambdaQuery()
                .eq(ExtendedWarrantyDO::getPlatformTradeId, tradeId)
                .last(BaseDaoConstant.DEFAULT_LIST_SIZE_LIMIT);
        return this.count(wrapper);
    }

    /**
     * 获取最后一个延保记录
     *
     * @param barcode 条码
     * @param tradeId 订单号
     * @return 延保记录
     */
    public Optional<ExtendedWarrantyDO> getLastBuy(String barcode, String tradeId) {
        Wrapper<ExtendedWarrantyDO> wrapper = Wrappers.<ExtendedWarrantyDO>lambdaQuery()
                .eq(ExtendedWarrantyDO::getBarcode, barcode)
                .eq(ExtendedWarrantyDO::getPlatformTradeId, tradeId)
                .eq(ExtendedWarrantyDO::getIsRefund, -1)
                .last(BaseDaoConstant.LIMIT_ONE);
        ExtendedWarrantyDO one = this.getOne(wrapper);
        return Optional.ofNullable(one);
    }

    /**
     * 延保分页列表
     *
     * @param query 参数
     * @return 延保分页列表
     */
    public IPage<ExtendedWarrantyDO> pageExtendedWarranty(ExtendedWarrantyQry query) {
        Wrapper<ExtendedWarrantyDO> wrapper = Wrappers.<ExtendedWarrantyDO>lambdaQuery()
                .eq(StringUtils.isNotBlank(query.getPlatformTradeId()), ExtendedWarrantyDO::getPlatformTradeId, query.getPlatformTradeId())
                .eq(StringUtils.isNotBlank(query.getProductDetail()), ExtendedWarrantyDO::getProductDetail, query.getProductDetail())
                .eq(StringUtils.isNotBlank(query.getPlatformName()), ExtendedWarrantyDO::getPlatformName, query.getPlatformName())
                .eq(StringUtils.isNotBlank(query.getBarcode()), ExtendedWarrantyDO::getBarcode, query.getBarcode())
                .eq(StringUtils.isNotBlank(query.getBuyerNickname()), ExtendedWarrantyDO::getBuyerNickname, query.getBuyerNickname())
                .between(ObjectUtils.isNotNull(query.getBuyTimeStart(), query.getBuyTimeEnd()), ExtendedWarrantyDO::getCreateTime, query.getBuyTimeStart(), query.getBuyTimeEnd());
        return this.page(query.createPage(), wrapper);
    }

}

