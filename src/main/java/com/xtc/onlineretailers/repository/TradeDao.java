package com.xtc.onlineretailers.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.enums.OrderStatus;
import com.xtc.onlineretailers.enums.SFStatus;
import com.xtc.onlineretailers.enums.TradeTypeEnum;
import com.xtc.onlineretailers.enums.WarehouseStatus;
import com.xtc.onlineretailers.mapper.master.TradeMapper;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.job.BaseTask;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Component
public class TradeDao extends ServiceImpl<TradeMapper, TradeDO> {

    /**
     * 根据订单号获取订单
     *
     * @param tradeId 订单号
     * @return 订单
     */
    public Optional<TradeDO> getByTradeId(String tradeId) {
        if (tradeId == null) {
            return Optional.empty();
        }
        Wrapper<TradeDO> wrapper = Wrappers.<TradeDO>lambdaQuery()
                .eq(TradeDO::getPlatformTradeId, tradeId)
                .orderByDesc(TradeDO::getId)
                .last(BaseDaoConstant.LIMIT_ONE);
        TradeDO one = this.getOne(wrapper);
        return Optional.ofNullable(one);
    }

    /**
     * 获取最后一个订单
     *
     * @param newPlatformTradeId 新订单号
     * @return 订单
     */
    public Optional<TradeDO> getByLastLikeRightTradeId(String newPlatformTradeId) {
        if (newPlatformTradeId == null) {
            return Optional.empty();
        }
        Wrapper<TradeDO> wrapper = Wrappers.<TradeDO>lambdaQuery()
                .likeRight(TradeDO::getPlatformTradeId, newPlatformTradeId)
                .orderByDesc(TradeDO::getPlatformTradeId)
                .last(BaseDaoConstant.LIMIT_ONE);
        TradeDO one = this.getOne(wrapper);
        return Optional.ofNullable(one);
    }

    /**
     * 同步未过账订单列表
     *
     * @return 未过账订单列表
     */
    public List<TradeDO> listBySyncErpId() {
        Wrapper<TradeDO> wrapper = Wrappers.<TradeDO>lambdaQuery()
                .eq(TradeDO::getErpPostStatus, 1)
                .eq(TradeDO::getErpId, 0)
                .ne(TradeDO::getPlatformId, GlobalConstant.PLATFORM_XTC_MALL_AGENT)
                .orderByDesc(TradeDO::getPaymentTime)
                .last(BaseTask.getShardingLimitSql(1000));
        return this.list(wrapper);
    }

    /**
     * 根据订单号获取原单
     *
     * @param oldTradeId 订单号
     * @return 订单
     */
    public Optional<TradeDO> getByOldTradeId(String oldTradeId) {
        if (oldTradeId == null) {
            return Optional.empty();
        }
        Wrapper<TradeDO> wrapper = Wrappers.<TradeDO>lambdaQuery()
                .eq(TradeDO::getOldPlatformTradeId, oldTradeId)
                .orderByDesc(TradeDO::getId)
                .last(BaseDaoConstant.LIMIT_ONE);
        TradeDO one = this.getOne(wrapper);
        return Optional.ofNullable(one);
    }

    /**
     * 京东仓库订单过账
     * <p>筛选：已发货，未过账，京东平台，京东仓库订单</p>
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 京东仓库订单列表
     */
    public List<TradeDO> jdWarehouseTradePost(LocalDateTime startTime, LocalDateTime endTime) {
        Wrapper<TradeDO> wrapper = Wrappers.<TradeDO>lambdaQuery()
                .eq(TradeDO::getErpPostStatus, 0)
                .eq(TradeDO::getWarehouseCode, GlobalConstant.WAREHOUSE_SF)
                .eq(TradeDO::getWarehouseStorage, GlobalConstant.WAREHOUSE_JD)
                .in(TradeDO::getStatusOrder, OrderStatus.HAS_SHIP, OrderStatus.RETURNED)
                .in(TradeDO::getPlatformId, GlobalConstant.PLATFORM_JINGDONG)
                .between(TradeDO::getPaymentTime, startTime, endTime)
                .orderByDesc(TradeDO::getPaymentTime)
                .last(BaseDaoConstant.DEFAULT_LIST_SIZE_LIMIT);
        return this.list(wrapper);
    }

    /**
     * 京东POP订单列表仓库条码同步
     * <p>筛选：已发货，京东平台，京东仓库订单</p>
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 京东POP订单列表
     */
    public List<TradeDO> jdWarehouseBarcodeSync(LocalDateTime startTime, LocalDateTime endTime) {
        Wrapper<TradeDO> wrapper = Wrappers.<TradeDO>lambdaQuery()
                .eq(TradeDO::getWarehouseCode, GlobalConstant.WAREHOUSE_SF)
                .eq(TradeDO::getWarehouseStorage, GlobalConstant.WAREHOUSE_JD)
                .eq(TradeDO::getStatusOrder, OrderStatus.HAS_SHIP)
                .in(TradeDO::getPlatformId, GlobalConstant.PLATFORM_JINGDONG)
                .between(TradeDO::getShippingTime, startTime, endTime)
                .orderByDesc(TradeDO::getShippingTime)
                .last(BaseTask.getShardingLimitSql(200));
        return this.list(wrapper);
    }

    /**
     * 查询需要核对发货数量的订单列表
     *
     * @param shippingTimeStart 发货开始时间
     * @param shippingTimeEnd   发货结束时间
     * @param lastId            上次最后一条记录id
     * @param size              列表数量
     * @param platformId        平台id
     * @return 订单列表
     */
    public List<TradeDO> checkShopShippingNum(LocalDateTime shippingTimeStart, LocalDateTime shippingTimeEnd,
                                              long lastId, int size, Integer platformId) {
        Wrapper<TradeDO> wrapper = Wrappers.<TradeDO>lambdaQuery()
                .select(TradeDO::getPlatformTradeId, TradeDO::getPlatformName, TradeDO::getPlatformId)
                .in(TradeDO::getStatusOrder, OrderStatus.WAIT_SHIP, OrderStatus.REJECT)
                .eq(TradeDO::getType, TradeTypeEnum.NORMAL)
                // where (warehouse_status in(HAVE_SORTED,HAVE_HANDED_OVER,HAVE_REJECTED) or status_sf in (HAS_TURN_LEFT,HAVE_OUTBOUND))
                .and(query -> query
                        .in(TradeDO::getWarehouseStatus, WarehouseStatus.HAVE_SORTED, WarehouseStatus.HAVE_HANDED_OVER, WarehouseStatus.HAVE_REJECTED)
                        .or()
                        .in(TradeDO::getStatusSf, SFStatus.HAS_TURN_LEFT, SFStatus.HAVE_OUTBOUND))
                .eq(TradeDO::getPlatformId, platformId)
                .lt(lastId > 0, TradeDO::getId, lastId)
                .between(TradeDO::getShippingTime, shippingTimeStart, shippingTimeEnd)
                .orderByDesc(TradeDO::getShippingTime, TradeDO::getId)
                .last("limit " + size);
        return this.list(wrapper);
    }

}
