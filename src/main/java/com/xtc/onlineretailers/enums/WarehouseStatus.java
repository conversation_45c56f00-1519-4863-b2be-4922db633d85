package com.xtc.onlineretailers.enums;

import lombok.Getter;

/**
 * 仓库状态
 */
@Getter
public enum WarehouseStatus {

    HAVE_CONFIRMED("已确认"),

    HAVE_DISTRIBUTED("已配货"),

    HAVE_PRINTED("已打印清单"),

    HAVE_SORTED("已分拣发货"),

    HAVE_HANDED_OVER("已交接"),

    HAVE_REJECTED("已打回");

    private final String description;

    WarehouseStatus(String description) {
        this.description = description;
    }

    /**
     * 判断不允许订单同步
     *
     * @param warehouseStatus 仓库状态
     * @return 执行结果
     */
    public static boolean notAllowSync(String warehouseStatus) {
        WarehouseStatus status = getEnum(warehouseStatus);
        return status == HAVE_SORTED || status == HAVE_HANDED_OVER;
    }

    /**
     * 判断不允许顺丰出库
     *
     * @param warehouseStatus 仓库状态
     * @return 执行结果
     */
    public static boolean notAllowSfOutbound(String warehouseStatus) {
        return !isAllowSfOutbound(warehouseStatus);
    }

    /**
     * 判断允许顺丰出库
     *
     * @param warehouseStatus 仓库状态
     * @return 执行结果
     */
    public static boolean isAllowSfOutbound(String warehouseStatus) {
        WarehouseStatus status = getEnum(warehouseStatus);
        return status == HAVE_CONFIRMED || status == HAVE_REJECTED;
    }

    /**
     * 判断未发货
     *
     * @param warehouseStatus 仓库状态
     * @return 执行结果
     */
    public static boolean isNoShip(String warehouseStatus) {
        WarehouseStatus status = getEnum(warehouseStatus);
        return status == HAVE_CONFIRMED || status == HAVE_REJECTED
                || status == HAVE_DISTRIBUTED || status == HAVE_PRINTED;
    }

    /**
     * 状态字符串转枚举
     *
     * @param warehouseStatus 状态字符串
     * @return 枚举
     */
    public static WarehouseStatus getEnum(String warehouseStatus) {
        try {
            return WarehouseStatus.valueOf(warehouseStatus);
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("仓库状态转换异常");
        }
    }
}
