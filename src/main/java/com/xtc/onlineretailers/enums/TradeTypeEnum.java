package com.xtc.onlineretailers.enums;

import lombok.Getter;

/**
 * 订单类型枚举
 */
@Getter
public enum TradeTypeEnum {

    NORMAL("正常"),

    GIFT("赠品"),

    ADJUST_ORDER("调单"),

    EXCHANGE_GOODS("换货");

    private final String description;

    TradeTypeEnum(String description) {
        this.description = description;
    }

    /**
     * 字符串转枚举
     *
     * @param str 字符串
     * @return 枚举
     */
    public static TradeTypeEnum getEnum(String str) {
        try {
            return TradeTypeEnum.valueOf(str);
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("订单类型转换异常");
        }
    }
}
