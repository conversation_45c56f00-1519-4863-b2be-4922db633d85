package com.xtc.onlineretailers.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.security.MessageDigest;

public class WatchUtil {

    public static String doPost(String url, String body) {
        HttpClient hc = HttpClientBuilder.create().build();
        HttpPost post = new HttpPost(url);
        byte[] bodyBytes = body.getBytes(Charset.forName("utf-8"));
        post.addHeader("Content-Type", "application/json;charset=UTF-8");
        String key = "shouhouwatchchange";
        String secret = "q1hkl3@(zcui12#dfas&t32";
        post.addHeader("key", key);
        String signKey = sign(key, null, secret.substring(0, 6));
        String sign = sign(signKey, body, secret);
        post.addHeader("sign", sign);
        ByteArrayEntity arrayEntity = new ByteArrayEntity(bodyBytes);
        post.setEntity(arrayEntity);
        HttpResponse response = null;
        try {
            response = hc.execute(post);
            response.getStatusLine().getStatusCode();
            return EntityUtils.toString(response.getEntity());
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private static String sign(String key, String body, String secret) {
        ByteArrayOutputStream outSteam = new ByteArrayOutputStream();
        byte[] keyBytes = key.getBytes(Charset.forName("utf-8"));
        outSteam.write(keyBytes, 0, keyBytes.length);
        if (StringUtils.isNotBlank(body)) {
            byte[] bodyBytes = body.getBytes(Charset.forName("utf-8"));
            outSteam.write(bodyBytes, 0, bodyBytes.length);
        }
        byte[] secretBytes = secret.getBytes(Charset.forName("utf-8"));
        outSteam.write(secretBytes, 0, secretBytes.length);
        try {
            outSteam.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        byte[] signData = outSteam.toByteArray();
        return encode(signData);
    }

    private static String encode(byte[] btInput) {
        char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
                'A', 'B', 'C', 'D', 'E', 'F'};
        try {
            // 获得MD5摘要算法的 MessageDigest 对象
            MessageDigest mdInst = MessageDigest.getInstance("MD5");
            // 使用指定的字节更新摘要
            mdInst.update(btInput);
            // 获得密文
            byte[] md = mdInst.digest();
            // 把密文转换成十六进制的字符串形式
            int j = md.length;
            char[] str = new char[j * 2];
            int k = 0;
            for (byte byte0 : md) {
                str[k++] = hexDigits[byte0 >>> 4 & 0xf];
                str[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(str);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

}
