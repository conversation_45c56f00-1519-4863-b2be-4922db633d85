package com.xtc.onlineretailers.util;

import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.*;

@Slf4j
public class SMSUtil {

    /**
     * 国内短信接口
     */
    private static final String API_CHINA = "http://sms.okii.com/sms/send";

    /**
     * 国外短信接口
     */
    private static final String API_GLOBAL = "http://sms.okii.com/sms/globalsend";

    /**
     * 发短信的账户
     */
    private static final Map<String, MultiValueMap<String, Object>> SMS_USER = new HashMap<>();

    /**
     * 国家区号
     */
    private static final Map<String, String> COUNTRY_ID = new HashMap<>();

    /**
     * 接口签名的参数顺序
     */
    private static final List<String> API_ARG_ORDER = new ArrayList<>();

    /**
     * 成功调用接口返回的标识
     */
    private static final String API_SUCCESS = "成功";

    private static String API_ID = "supplychain";
    private static String SECRET = "27f74faec42944d09d7c5449d979b86c";
    private static String URL = "http://esb.bbkedu.com:8000/api/messages/sms";

    // 初始化数据
    static {
        // 账号密码
        MultiValueMap<String, Object> okii = new LinkedMultiValueMap<>();
        okii.add("username", "xtcds_tz_market");
        okii.add("password", "xtc@dstzm");
        SMS_USER.put("okii", okii);

        MultiValueMap<String, Object> eebbk = new LinkedMultiValueMap<>();
        eebbk.add("username", "bbkds_tz_market");
        eebbk.add("password", "bbk@dstzm");
        SMS_USER.put("eebbk", eebbk);

        // 国家区号
        COUNTRY_ID.put("id", "62");

        // 接口参数，要按字母从a到z排序
        API_ARG_ORDER.add("username");
        API_ARG_ORDER.add("password");
        API_ARG_ORDER.add("sender");
        API_ARG_ORDER.add("content");
        API_ARG_ORDER.add("channel");
        API_ARG_ORDER.add("countryid");
        API_ARG_ORDER.add("timestamp");
        Collections.sort(API_ARG_ORDER);
    }

    /**
     * 发送国内短信
     *
     * @param brand   品牌
     * @param phone   手机号
     * @param content 内容
     * @return 发送结果
     */
    public static boolean send(String brand, String phone, String content) {
        return send(brand, null, phone, content);
    }

    /**
     * 发送短信
     *
     * @param brand   品牌
     * @param country 国家
     * @param phone   手机号
     * @param content 内容
     * @return 发送结果
     */
    public static boolean send(String brand, String country, String phone, String content) {
        if (SMS_USER.get(brand) == null) {
            return false;
        }

        // 参数
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>(SMS_USER.get(brand));
        body.add("sender", phone);
        body.add("content", content);
        body.add("timestamp", System.currentTimeMillis());
        if (COUNTRY_ID.get(country) != null) {
            body.add("countryid", COUNTRY_ID.get(country));
        } else {
            body.add("channel", 1);
        }
        body.add("sign", sign(body));

        // 调用接口
        String url = body.get("countryid") == null ? API_CHINA : API_GLOBAL;
        String result = HttpUtil.postForForm(url, body);
        log.debug("url: {}, body: {}, result: {}", url, body, result);

        // 判断结果
        Map<String, Object> resultMap = GsonUtil.jsonToMap(result);
        Object status = resultMap.get("status");
        return status != null && API_SUCCESS.equals(status.toString());
    }

    /**
     * 签名
     *
     * @param obj 参数
     * @return MD5加密的签名
     */
    private static String sign(MultiValueMap<String, Object> obj) {
        StringBuilder sb = new StringBuilder();
        API_ARG_ORDER.forEach(arg -> {
            List<Object> list = obj.get(arg);
            if (list != null && list.size() > 0) {
                Object value = list.get(0);
                if ("content".equals(arg) && value != null) {
                    // 需要对内容进行UrlEncode编码
                    value = HttpUtil.urlEncode(value.toString());
                }
                sb.append("&").append(arg).append("=").append(value);
            }
        });
        String str = sb.substring(1, sb.length());
        return MD5Util.encodeUTF8(str, true);
    }

    /**
     * 伟哥接口
     */
    public static String getAuthorization() {
        MultiValueMap<String, Object> tokeMap = new LinkedMultiValueMap<>();
        tokeMap.add("client_id", API_ID);
        tokeMap.add("client_secret", SECRET);
        tokeMap.add("grant_type", "client_credentials");
        tokeMap.add("scope", "ApiGateway");
        return HttpUtil.postForForm("http://esb.bbkedu.com:8000/connect/token", tokeMap);
    }

    /**
     * 伟哥接口(发送短信)
     *
     * @param phones 电话号码
     * @param body   内容
     * @return 结果
     */
    public static ResponseEntity<String> sendSms(List<String> phones, String body) {
        try {
            String authorization = getAuthorization();
            JsonObject jsonObject = GsonUtil.jsonToObject(authorization);
            String accessToken = jsonObject.get("access_token").getAsString();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
            // 例如你需要发送一个认证信息
            headers.set("Authorization", "Bearer " + accessToken);
            // 例如你的body是一个JSON字符串
            Map<String, Object> tokeMap = new HashMap<>();
            tokeMap.put("numbers", phones);
            tokeMap.put("content", body);
            String json = GsonUtil.objectToJson(tokeMap);
            HttpEntity<String> entity = new HttpEntity<>(json, headers);
            RestTemplate restTemplate = new RestTemplate();
            return restTemplate.exchange(URL, HttpMethod.POST, entity, String.class);
        } catch (Exception e) {
            log.error("调用短信服务失败:{}", body);
            return null;
        }

    }
}
