package com.xtc.onlineretailers.util;

import com.google.common.collect.Lists;
import com.taobao.api.DefaultTaobaoClient;
import com.taobao.api.TaobaoClient;
import com.taobao.api.request.AlibabaEinvoiceCreateResultGetRequest;
import com.taobao.api.request.AlibabaEinvoiceCreatereqRequest;
import com.taobao.api.request.AlibabaEinvoiceCreatereqRequest.InvoiceItem;
import com.taobao.api.response.AlibabaEinvoiceCreateResultGetResponse;
import com.taobao.api.response.AlibabaEinvoiceCreatereqResponse;
import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.constant.PlatformConstant;
import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.enums.TradeTypeEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.pojo.entity.InvoiceItemDO;
import com.xtc.onlineretailers.pojo.entity.InvoiceMainDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.pojo.vo.InvoiceParams;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
public class InvoiceUtil {

    // 异步开票
    public static boolean createInvoice(InvoiceParams params) {
        params.setUrl("https://eco.taobao.com/router/rest");
        TaobaoClient client = new DefaultTaobaoClient(params.getUrl(), params.getApp_key(), params.getApp_secret());
        AlibabaEinvoiceCreatereqRequest req = new AlibabaEinvoiceCreatereqRequest();
        InvoiceMainDO invoiceApply = params.getInvoiceApply();
        BeanUtils.copyProperties(invoiceApply, req);
        req.setInvoiceItems(params.getInvoiceItems());
//        req.setPlatformTid("222222222222");
//        req.setSerialNo(params.getInvoiceApply().getSerialNo().substring(0, params.getInvoiceApply().getSerialNo().length() - 2) + "m");
        try {
            AlibabaEinvoiceCreatereqResponse rsp = client.execute(req, params.getSessionKey());
            log.info("createInvoice body: {}, msg: {}", rsp.getBody(), rsp.getMsg());
            return rsp.getIsSuccess();
        } catch (Exception e) {
            log.error("电子发票开票异常，订单号：{}，开票流水号：{}", req.getPlatformTid(), req.getSerialNo(), e);
            return false;
        }
    }

    // 获取基本信息
    public static InvoiceParams getInvoiceParams(TradeDO trade, String invoiceType, List<InvoiceItemDO> itemList) {
        String platform_code = "OTHER";
        if (GlobalConstant.PLATFORM_TAOBAO.contains(trade.getPlatformId())
                && TradeTypeEnum.NORMAL.name().equals(trade.getType())) {
            // 天猫步步高，天猫小天才，只有正常订单设置为TM
            platform_code = "TM";
        }
        // 步步高母婴旗舰店
        String payee_name = "广东天才星网络科技有限公司";// 开票方名称
        String payee_address = "东莞市长安镇霄边社区东门中路168号第十九层西侧";// 开票地址
        String payee_phone = "0769-********-8602";// 开票方电话
        String payee_operator = "周春曲";// 开票人
        String payee_register_no = "91441900324794417T";// 收款方税务登记
        String payee_bank_account = "东莞建设银行股份有限公司东莞长安支行44001779108053029059";
        String payee_checker = "周春曲";// 复核人
        String payee_receiver = "周春曲";// 收款人
        String organization = "天才星";

        InvoiceMainDO invoiceApply = new InvoiceMainDO();
        invoiceApply.setBusinessType(0L);
        invoiceApply.setOrganization(organization);
        invoiceApply.setPlatformCode(platform_code);
        invoiceApply.setPayeeName(payee_name);
        invoiceApply.setPayeeAddress(payee_address);
        invoiceApply.setPayeePhone(payee_phone);
        invoiceApply.setPayeeOperator(payee_operator);
        invoiceApply.setPayeeRegisterNo(payee_register_no.trim());
        invoiceApply.setPayeeBankaccount(payee_bank_account);
        invoiceApply.setPayeeChecker(payee_checker);
        invoiceApply.setPayeeReceiver(payee_receiver);
        invoiceApply.setPlatformTid(trade.getPlatformTradeId());
        invoiceApply.setPlatformId(trade.getPlatformId());
        invoiceApply.setPlatformName(trade.getPlatformName());
        invoiceApply.setHasCallBack(-1);
        invoiceApply.setIsDel(-1);

        if (MyToolUtil.notEmpty(trade.getInvoiceRegisterNo())) {
            invoiceApply.setPayerRegisterNo(trade.getInvoiceRegisterNo().trim());
        }
        invoiceApply.setPayerBankaccount(trade.getInvoiceBankAccount());

        invoiceApply.setInvoiceTime(new Date());
        invoiceApply.setInvoiceType(invoiceType);
        String payAddress = ".";
        // 客服备注的地址、电话
        if (MyToolUtil.notEmpty(trade.getInvoiceAddress())) {
            payAddress = trade.getInvoiceAddress();
        }
        invoiceApply.setPayerAddress(payAddress);
        invoiceApply.setPayerName(MyToolUtil.notEmpty(trade.getInvoiceTitle()) ? trade.getInvoiceTitle() : trade.getReceiverName());
        //只有用户发票抬头为空，一律开个人
        if (StringUtils.isAllBlank(trade.getInvoiceTitle(), trade.getReceiverName())) {
            invoiceApply.setPayerName("个人");
        }
        // 红票冲红时传入原来的发票号和发票代码
        double tradeAmount = trade.getPayment().doubleValue();
        if (invoiceType.equals("red")) {
            invoiceApply.setNormalInvoiceCode(trade.getElectronicInvoiceCode());
            invoiceApply.setNormalInvoiceNo(trade.getElectronicInvoiceNo());
            invoiceApply.setRedInvoiceCode(trade.getElectronicInvoiceCode());
            invoiceApply.setRedInvoiceNo(trade.getElectronicInvoiceNo());
            tradeAmount = -trade.getPayment().doubleValue();
        }
        double tradeSumTax = 0;
        double tradeSumPrice = 0;

        for (InvoiceItemDO invoiceItem : itemList) {
            tradeSumTax += Double.parseDouble(invoiceItem.getTax());
            tradeSumPrice += Double.parseDouble(invoiceItem.getSumPrice());
        }

        DecimalFormat df2 = new DecimalFormat("#.00");
        InvoiceParams params = new InvoiceParams();
        if (!df2.format(tradeAmount).equals(df2.format(tradeSumPrice + tradeSumTax))) {
            log.error("发票开票金额有误，订单：{}", trade.getPlatformTradeId());
            throw new GlobalDefaultException(ResponseEnum.INVOICE_CREATE_AMOUNT);
        }
        invoiceApply.setSumPrice(changeReq(df2.format(tradeSumPrice)));
        invoiceApply.setSumTax(changeReq(df2.format(tradeSumTax)));
        invoiceApply.setInvoiceAmount(changeReq(df2.format(tradeAmount)));
        params.setInvoiceApply(invoiceApply);
        params.setInvoiceItems(toTaobaoItem(itemList));
        return params;
    }

    public static List<InvoiceItem> toTaobaoItem(List<InvoiceItemDO> itemDOList) {
        List<InvoiceItem> invoiceItemList = new ArrayList<>();
        for (InvoiceItemDO itemDO : itemDOList) {
            InvoiceItem invoiceItem = new InvoiceItem();
            BeanUtils.copyProperties(itemDO, invoiceItem);
            invoiceItemList.add(invoiceItem);
        }
        return invoiceItemList;
    }

    /**
     * 流水号最长20位
     */
    public static String getSerialNo(List<InvoiceMainDO> invoiceMainLogs, String tid, String invoiceType, String platformName) {
        // 内部购机，APP商城，这两个平台与天猫的截取规则不一样
        String invoice_tid = "内部购机|APP商城".contains(platformName) ? tid.substring(2) : tid.substring(4);
        if (tid.length() >= 22) {
            invoice_tid = tid.substring(6);
        }
        if (PlatformConstant.PLATFORM_JINGDONG_XTC_NAME.equals(platformName) || PlatformConstant.PLATFORM_JINGDONG_BBK_NAME.equals(platformName)) {
            invoice_tid = tid;
        }
        int blue_count = 0;
        int red_count = 0;
        if (invoiceMainLogs != null && invoiceMainLogs.size() != 0) {
            for (InvoiceMainDO invoice : invoiceMainLogs) {
                if (invoice.getInvoiceType().equals("blue")) {
                    blue_count++;
                } else if (invoice.getInvoiceType().equals("red")) {
                    red_count++;
                }
            }
        }
        if (invoiceType.equals("blue")) {
            blue_count++;
            return "b" + blue_count + "_" + invoice_tid;
        } else if (invoiceType.equals("red")) {
            red_count++;
            return "r" + red_count + "_" + invoice_tid;
        } else {
            throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR);
        }
    }

    /**
     * 开票失败不换流水号
     *
     * @param invoiceMainLogs 开票列表
     * @param invoiceType     发票类型
     * @param tid             订单号
     * @return platformName 平台名称
     */
    public static String getNotSerialNo(List<InvoiceMainDO> invoiceMainLogs, String tid, String invoiceType, String platformName) {
        // 内部购机，APP商城，这两个平台与天猫的截取规则不一样
        String invoice_tid = "内部购机|APP商城".contains(platformName) ? tid.substring(2) : tid.substring(4);
        if (tid.length() >= 22) {
            invoice_tid = tid.substring(6);
        }
        if (PlatformConstant.PLATFORM_JINGDONG_XTC_NAME.equals(platformName) || PlatformConstant.PLATFORM_JINGDONG_BBK_NAME.equals(platformName)) {
            invoice_tid = tid;
        }

        if (PlatformConstant.PLATFORM_IOT_NAME.equals(platformName) && tid.length() >= 17) {
            invoice_tid = tid.substring(0, 17);
        }

        int blue_count = 0;
        int red_count = 0;
        if (invoiceMainLogs != null && invoiceMainLogs.size() != 0) {
            for (InvoiceMainDO invoice : invoiceMainLogs) {
                if (invoice.getInvoiceType().equals("blue") && invoice.getInvoiceStatus().equals("已开票")) {
                    blue_count++;
                } else if (invoice.getInvoiceType().equals("red") && invoice.getInvoiceStatus().equals("已冲红")) {
                    red_count++;
                }
            }
        }
        if (invoiceType.equals("blue")) {
            blue_count++;
            return "b" + blue_count + "_" + invoice_tid;
        } else if (invoiceType.equals("red")) {
            red_count++;
            return "r" + red_count + "_" + invoice_tid;
        } else {
            throw new GlobalDefaultException(ResponseEnum.PARAM_ERROR);
        }
    }

    public static String changeReq(String string) {
        if (string != null && string != "") {
            string = string.replace("-.", "-0.");
            if (string.startsWith(".")) {
                string = "0" + string;
            }
        }
        return string;
    }

    public static List<AlibabaEinvoiceCreateResultGetResponse.InvoiceResult> getInvoiceResult(InvoiceParams params) {
        params.setUrl("https://eco.taobao.com/router/rest");
        TaobaoClient client = new DefaultTaobaoClient(params.getUrl(), params.getApp_key(), params.getApp_secret());
        AlibabaEinvoiceCreateResultGetRequest req = new AlibabaEinvoiceCreateResultGetRequest();
        if (params.getInvoiceApply().getSerialNo() != null) {
            req.setSerialNo(params.getInvoiceApply().getSerialNo());
        }
        req.setPlatformCode(params.getInvoiceApply().getPlatformCode());
        req.setPlatformTid(params.getInvoiceApply().getPlatformTid());
        req.setPayeeRegisterNo(params.getInvoiceApply().getPayeeRegisterNo());
        try {
            AlibabaEinvoiceCreateResultGetResponse rsp = client.execute(req, params.getSessionKey());
            log.info("rsp body: {}", rsp.getBody());
            return rsp.getInvoiceResultList();
        } catch (Exception e) {
            log.error("电子发票查询异常，订单号：{}，开票流水号：{}", req.getPlatformTid(), req.getSerialNo(), e);
        }
        return Lists.newArrayList();
    }

    public static List<InvoiceItemDO> change(List<InvoiceItemDO> itemList) {
        for (InvoiceItemDO item : itemList) {
            if (StringUtils.isNotBlank(item.getTaxRate())) {
                if (item.getTaxRate().startsWith(".")) {
                    item.setTaxRate("0" + item.getTaxRate());
                }
            }
            if (StringUtils.isNotBlank(item.getPrice())) {
                item.setPrice(item.getPrice().replace("-.", "-0."));
                if (item.getPrice().startsWith(".")) {
                    item.setPrice("0" + item.getPrice());
                }
            }
            if (StringUtils.isNotBlank(item.getSumPrice())) {
                item.setSumPrice(item.getSumPrice().replace("-.", "-0."));
                if (item.getSumPrice().startsWith(".")) {
                    item.setSumPrice("0" + item.getSumPrice());
                }
            }
            if (StringUtils.isNotBlank(item.getAmount())) {
                item.setAmount(item.getAmount().replace("-.", "-0."));
                if (item.getAmount().startsWith(".")) {
                    item.setAmount("0" + item.getAmount());
                }
            }
            if (StringUtils.isNotBlank(item.getTax())) {
                item.setTax(item.getTax().replace("-.", "-0."));
                if (item.getTax().startsWith(".")) {
                    item.setTax("0" + item.getTax());
                }
            }
        }
        return itemList;
    }

}
