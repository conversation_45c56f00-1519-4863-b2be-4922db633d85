package com.xtc.onlineretailers.util.suiPingBao;

import java.io.Serializable;

/**
 * 代理表
 *
 * <AUTHOR>
 */
public class Insurance implements Serializable {

    private static final long serialVersionUID = 1L;
    private String id;
    private String tid;
    private String sn;
    private String type;
    private int isRefund;
    private String statusStr;
    private String addTime;
    private String operateTime;
    private String buyerNick;

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getOperateTime() {
        String temp = "";
        if (this.operateTime != null && !this.operateTime.equals("")) {
            temp = operateTime.substring(0, 19);
        }
        return temp;
    }

    public void setOperateTime(String operateTime) {
        this.operateTime = operateTime;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getIsRefund() {
        return isRefund;
    }

    public void setIsRefund(int isRefund) {
        this.isRefund = isRefund;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public void setStatusStr(String statusStr) {
        this.statusStr = statusStr;
    }

    public String getAddTime() {
        String temp = "";
        if (this.addTime != null && !this.addTime.equals("")) {
            temp = addTime.substring(0, 19);
        }
        return temp;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime;
    }

}
