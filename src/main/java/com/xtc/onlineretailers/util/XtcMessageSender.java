package com.xtc.onlineretailers.util;

import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.HashMap;

/**
 * 小天才消息提醒
 * <p>
 * esb:
 * clientId: SRM
 * secret: d6882c537202439aac2a0496c554f7fd
 * <p>
 * 企业微信接口文档 https://work.weixin.qq.com/api/doc/90000/90135/90236
 * <p>
 * 一、请求接口步骤:
 * 1.获取apid与Secret,请联系系统管理部架构及IT可得到调用所需的ClientId与Secret
 * 2.获取token
 * POST: http://esb.bbkedu.com:8000/connect/token
 * {
 * client_id:步骤1中获得,
 * client_secret:步骤1中获得,
 * grant_type:"client_credentials",
 * scope:"ApiGateway"
 * }
 * 注：body使用x-www-form-urlencoded
 * <p>
 * 返回内容:
 * {
 * "access_token":"",
 * "expires_in": 3600,
 * "token_type":"Bearer"
 * }
 * 3.使用步骤2中得到的Bearer Token请求资源.
 * <p>
 * 二、发送微信消息接口说明:
 * 1. 发送图文信息
 * POST: http://esb.bbkedu.com:8000/api/wechat/news
 * {
 * "toUser" : "工号1,工号2....",
 * "agentId" : 0,
 * "news" : {
 * "articles" : [
 * {
 * "title" : "中秋节礼品领取",
 * "description" : "今年中秋节公司有豪礼相送",
 * "url" : "URL",
 * "picurl" : "http://res.mail.qq.com/node/ww/wwopenmng/images/independent/doc/test_pic_msg1.png",
 * "btntxt":"更多"
 * }
 * ]
 * }
 * }
 * <p>
 * 2. 发送文本消息
 * POST: http://esb.bbkedu.com:8000/api/wechat/text
 * {
 * "toUser" : "工号1,工号2,....",
 * "agentId" : 0,
 * "content":"xxxxxx"
 * }
 * <p>
 * 3. 文本卡片消息
 * POST: http://esb.bbkedu.com:8000/api/wechat/textcard
 * {
 * "toUser" : "工号1|工号2,....",
 * "agentId" : 0,
 * "title" : "领奖通知",
 * "description" : "<div class=\"gray\">2016年9月26日</div> <div class=\"normal\">恭喜你抽中iPhone 7一台，领奖码：xxxx</div><div class=\"highlight\">请于2016年10月10日前联系行政同事领取</div>",
 * "url":"http://www.baidu.com",
 * "btntxt":"更多"
 * }
 * <p>
 * 卡片消息的展现形式非常灵活，支持使用br标签或者空格来进行换行处理，也支持使用div标签来使用不同的字体颜色，目前内置了3种文字颜色：灰色(gray)、高亮(highlight)、默认黑色(normal)，将其作为div标签的class属性即可
 * <p>
 * 4. 发送邮件
 * POST: http://esb.bbkedu.com:8000/api/messages/mail
 * {
 * "targetSender":"发送人",
 * "receiver":[{"displayName":"显示名称","code":"编号"}.......],
 * "targetReceiver":[工号1|工号2.....],//receiver和targetReceiver只能写一个
 * "subject":"主题",
 * "htmlBody":"HTML内容",
 * "redirectUrl":"跳转URL"
 * }
 */
@Slf4j
public class XtcMessageSender {

    private static final String CLIENT_ID = "SRM";
    private static final String CLIENT_SECRET = "18de183448d04288b7c0cc9a483c7615";

    // 获取token
    private static final String API_TOKEN = "http://esb.bbkedu.com:8000/connect/token";
    // 发送图文信息
    private static final String API_SEND_PIC_AND_TEXT = "http://esb.bbkedu.com:8000/api/wechat/news";
    // 发送文本消息
    private static final String API_SEND_TEXT = "http://esb.bbkedu.com:8000/api/wechat/text";
    // 文本卡片消息
    private static final String API_SEND_CARD = "http://esb.bbkedu.com:8000/api/wechat/textcard";
    // 发送邮件
    private static final String API_SEND_EMAIL = "http://esb.bbkedu.com:8000/api/messages/mail";

    /**
     * 文本消息
     *
     * @param users 工号1|工号2|工号3|...
     * @return 发送结果
     */
    public static boolean cardTextMessage(String users, String content) {
        try {
            JsonObject body = new JsonObject();
            body.addProperty("toUser", users);
            // 4：我的提醒
            body.addProperty("agentId", 4);
            body.addProperty("content", content);
            return send(API_SEND_TEXT, body.toString());
        } catch (Exception e) {
            log.error("发送企业微信异常{}",e);
            return false;
        }

    }

    /**
     * 文本卡片消息
     *
     * @param users       工号1|工号2|工号3|...
     * @param title       标题
     * @param description 描述
     * @param url         链接
     * @return 发送结果
     */
    public static boolean cardMessage(String users, String title, String description, String url) {
        JsonObject body = new JsonObject();
        body.addProperty("toUser", users);
        body.addProperty("agentId", 0);// 4：我的提醒
        body.addProperty("title", title);
        body.addProperty("description", description);
        body.addProperty("url", url);
        return send(API_SEND_CARD, body.toString());
    }

    /**
     * 发送消息
     *
     * @param api  接口地址
     * @param body 内容
     * @return 发送结果
     */
    private static boolean send(String api, Object body) {
        HashMap<String, String> headers = new HashMap<>();
        String token;
        try {
            token = getToken();
        } catch (Exception e) {
            log.error("XtcMessageSender 获取access_token失败", e);
            return false;
        }
        headers.put("Authorization", "Bearer " + token);

        String response = HttpUtil.post(api, body, headers);
        return response.contains("\"result\":true");
    }

    /**
     * 获取access_token
     *
     * @return access_token
     * @throws Exception 所有异常
     */
    private static String getToken() throws Exception {
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("client_id", CLIENT_ID);
        body.add("client_secret", CLIENT_SECRET);
        body.add("grant_type", "client_credentials");
        body.add("scope", "ApiGateway");

        String response = HttpUtil.postForForm(API_TOKEN, body);
        JsonObject object = GsonUtil.jsonToBean(response, JsonObject.class);
        return object.get("access_token").getAsString();
    }

    public static void main(String[] args) {
        String description = "<div class=\"gray\">" + DateUtil.nowDateStr("yyyy年M月d日") + "</div>" +
                "<br>" +
                "<div class=\"normal\">代理：广州</div>" +
                "<div class=\"highlight\">网点：清远客户服务中心</div>" +
                "<div class=\"highlight\">停业时间：2021-08-20至2021-08-30</div>" +
                "<div class=\"normal\">停业说明：停业搬迁</div>";
        String url = "";
        log.info("cardMessage: {}", cardMessage("20247120", "网点停业消息", description, url));
    }

}
