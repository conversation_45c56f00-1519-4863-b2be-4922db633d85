package com.xtc.onlineretailers.util;

import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;

import java.util.concurrent.TimeUnit;

/**
 * 远程调用重试工具
 */
public class RpcRetryer {

    private RpcRetryer() {
    }

    /**
     * 生成重试机制，执行 2 次，每次间隔时间递增 1 秒（第一次 1 秒，第二次 2 秒）
     *
     * @param exceptionClass 异常类型
     * @param <T>            业务逻辑返回值类型
     * @return 重试机制
     */
    public static <T> Retryer<T> buildWithExceptionOfType(Class<? extends Throwable> exceptionClass) {
        // 执行 2 次，每次间隔时间递增 1 秒（第一次 1 秒，第二次 2 秒）
        return RetryerBuilder.<T>newBuilder()
                .retryIfExceptionOfType(exceptionClass)
                .withWaitStrategy(WaitStrategies.incrementingWait(1, TimeUnit.SECONDS, 1, TimeUnit.SECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(2))
                .build();
    }

}
