package com.xtc.onlineretailers.util;

import com.google.gson.JsonObject;
import com.xtc.onlineretailers.constant.GlobalConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class GlobalUtil {

    /**
     * 获取请求的url
     *
     * @param request 请求
     * @return url
     */
    public static String getRequestUrl(HttpServletRequest request) {
        StringBuilder url = new StringBuilder();
        url.append(request.getScheme()).append("://").append(request.getServerName());
        if (request.getServerPort() != 80) {
            url.append(":").append(request.getServerPort());
        }
        url.append(request.getServletPath());
        if (request.getQueryString() != null) {
            url.append("?").append(request.getQueryString());
        }
        return url.toString();
    }

    /**
     * 生成日志id
     */
    public static String putLogId() {
        return GlobalUtil.putLogId(null);
    }

    /**
     * 生成日志id
     */
    public static String putLogId(String logId) {
        if (logId == null) {
            logId = GlobalUtil.getUUID();
        } else {
            String subLogId = GlobalUtil.getUUID();
            logId = logId + " " + subLogId;
        }
        MDC.put(GlobalConstant.LOG_KEY_TRACE_ID, logId);
        log.info("put log id {}", logId);
        return logId;
    }

    /**
     * 获取方法或者类声明的注解，方法优先级高于类
     *
     * @param method          方法
     * @param annotationClass 注解类
     * @return 注解实例
     */
    public static <A extends Annotation> A getAnnotationByMethodAndType(Method method, Class<A> annotationClass) {
        A annotation = AnnotationUtils.getAnnotation(method, annotationClass);
        if (annotation == null) {
            annotation = AnnotationUtils.getAnnotation(method.getDeclaringClass(), annotationClass);
        }
        return annotation;
    }

    /**
     * 根据属性名获取属性值
     *
     * @param fieldName 字段名
     * @param bean      实体
     * @return 属性值
     */
    public static Object getFieldValueByName(String fieldName, Object bean) {
        String firstLetter = fieldName.substring(0, 1).toUpperCase();
        String getter = "get" + firstLetter + fieldName.substring(1);
        try {
            Method method = bean.getClass().getMethod(getter);
            return method.invoke(bean);
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 获取UUID；
     * 可用用途：用户需要唯一标记的地方，如数据库的id。
     */
    public static String getUUID() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 将集合数据拷贝到新的目标类型集合
     *
     * @param sources 源数据
     * @param target  生成目标类型实例的方法
     * @param <S>     源类型
     * @param <T>     目标类型
     * @return 目标类型集合
     */
    public static <S, T> List<T> copyCollection(Collection<S> sources, Supplier<T> target) {
        return sources.stream().map(elem -> {
            T vo = target.get();
            BeanUtils.copyProperties(elem, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取枚举所有值的属性
     *
     * @param clazz
     * @return
     */
    public static <T> List<Map<String, String>> getEnumValues(Class<T> clazz) {
        if (!clazz.isEnum()) {
            return null;
        }

        // 获取 枚举的属性字段 和 get方法
        Map<String, String> propData = new HashMap<>();
        for (Field field : clazz.getDeclaredFields()) {
            if (field.getModifiers() == Modifier.PRIVATE
                    || field.getModifiers() == (Modifier.PRIVATE | Modifier.FINAL)) {
                propData.put(field.getName(), "get" + firstLetterUpper(field.getName()));
            }
        }

        // 生成map paramValue
        List<Map<String, String>> dataList = new ArrayList<>();
        T[] objs = clazz.getEnumConstants();
        for (int i = 0; i < objs.length; i++) {
            Map<String, String> data = new HashMap<>();
            //data.put("ordinal", i);
            data.put("code", objs[i].toString());
            for (Map.Entry<String, String> entry : propData.entrySet()) {
                try {
                    Method method = clazz.getMethod(entry.getValue());
                    //data.put(objs[i].toString() + "", method.invoke(objs[i]) + "");
                    data.put(entry.getKey(), method.invoke(objs[i]) + "");
                } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException | NoSuchMethodException
                         | SecurityException e) {
                    e.printStackTrace();
                }
            }
            dataList.add(data);
        }
        return dataList;
    }

    /**
     * 字符串首字母 转大写
     *
     * @param str
     * @return
     */
    public static String firstLetterUpper(String str) {
        Assert.hasText(str);
        char[] ch = str.toCharArray();
        if (ch[0] >= 'a' && ch[0] <= 'z') {
            ch[0] = (char) (ch[0] - 32);
        }
        return String.valueOf(ch);
    }

    /**
     * 字符串地址的解析
     *
     * @param address 字符串地址
     * @return 拆分后的省市区地址信息
     */
    public static List<Map<String, String>> addressResolutionNew(String address) {
        List<Map<String, String>> table = new ArrayList<Map<String, String>>();
        String json = HttpUtil.get("https://scrmapis.okii.com/address/purification?text=" + address);
        Map<String, String> row = new LinkedHashMap<String, String>();
        if (json != null && !json.equals("")) {
            String province = "";
            String city = "";
            String district = "";
            String town = "";
            String addr_info = "";
            JsonObject jsonObject = GsonUtil.jsonToBean(json, JsonObject.class);
            String code = jsonObject.get("code").getAsString();
            if (code.equals("000001")) {
                province = jsonObject.get("data").getAsJsonObject().get("prov").getAsString();
                city = jsonObject.get("data").getAsJsonObject().get("city").getAsString();
                district = jsonObject.get("data").getAsJsonObject().get("district").getAsString();
                town = jsonObject.get("data").getAsJsonObject().get("town").getAsString();
                //road = jsonObject.get("data").getAsJsonObject().get("road").getAsString();
                addr_info = jsonObject.get("data").getAsJsonObject().get("addr_info").getAsString();

                if (!StringUtils.isNotBlank(city) && StringUtils.isNotBlank(district)) {
                    city = district;
                }
                row.put("province", province == null ? "" : province.trim());
                row.put("city", city == null ? "" : city.trim());
                row.put("county", district == null ? "" : district.trim());
                row.put("village", town + addr_info == null ? "" : town + addr_info.trim());
                table.add(row);
            } else {
                table = addressResolution(address);
            }
        } else {
            table = addressResolution(address);
        }
        return table;
    }

    /**
     * 字符串地址的解析
     *
     * @param address 字符串地址
     * @return 拆分后的省市区地址信息
     */
    public static List<Map<String, String>> addressResolution(String address) {
        String regex = "(?<province>[^省]+自治区|.*?省|.*?行政区|.*?市)(?<city>[^市]+自治州|.*?市|.*?地区|.+盟|市辖区)?(?<village>.*)";
        Matcher m = Pattern.compile(regex).matcher(address);
        String province = null, city = null, county = null, village = null;
        List<Map<String, String>> table = new ArrayList<Map<String, String>>();
        Map<String, String> row = null;
        while (m.find()) {
            row = new LinkedHashMap<String, String>();
            province = m.group("province");
            if (province != null) {
                if (province.equals("北京市")) {
                    province = "北京";
                    row.put("province", province);
                } else if (province.equals("上海市")) {
                    province = "上海";
                    row.put("province", province);
                } else if (province.equals("重庆市")) {
                    province = "重庆";
                    row.put("province", province);
                } else if (province.equals("天津市")) {
                    province = "天津";
                    row.put("province", province);
                } else {
                    row.put("province", province == null ? "" : province.trim());
                }
            }
            city = m.group("city");
            row.put("city", city == null ? "" : city.trim());

            village = m.group("village");
            if (village != null) {
                int count = 0;
                village = village.trim();
                if (village.contains("市")) {
                    county = village.split("市")[0] + "市";
                    if (county.length() <= 6) {
                        if (!county.contains(city)) {
                            row.put("county", county);
                            village = village.replaceFirst(county, "");
                            row.put("village", village);
                            count++;
                        }
                    }
                }
                if (count == 0) {
                    if (village.contains("区")) {
                        county = village.split("区")[0] + "区";
                        if (county.length() <= 6) {
                            row.put("county", county);
                            village = village.replaceFirst(county, "");
                            row.put("village", village);
                            count++;
                        }
                    }
                }
                if (count == 0) {
                    if (village.contains("县")) {
                        county = village.split("县")[0] + "县";
                        if (county.length() <= 6) {
                            row.put("county", county);
                            village = village.replaceFirst(county, "");
                            row.put("village", village);
                            count++;
                        }
                    }
                }
                if (count == 0) {
                    if (village.contains("镇")) {
                        county = village.split("镇")[0] + "镇";
                        if (county.length() <= 6) {
                            row.put("county", county);
                            village = village.replaceFirst(county, "");
                            row.put("village", village);
                            count++;
                        }
                    }
                }
                if (count == 0) {
                    if (village.contains("旗")) {
                        county = village.split("旗")[0] + "旗";
                        if (county.length() <= 8) {
                            row.put("county", county);
                            village = village.replaceFirst(county, "");
                            row.put("village", village);
                            count++;
                        }
                    }
                }
                if (count == 0) {
                    if (village.contains("岛")) {
                        county = village.split("岛")[0] + "岛";
                        if (county.length() <= 8) {
                            row.put("county", county);
                            village = village.replaceFirst(county, "");
                            row.put("village", village);
                            count++;
                        }
                    }
                }
                if (village != null && !village.equals("")) {
                    row.put("village", village);
                }
            }
            table.add(row);
        }
        return table;
    }

    /**
     * 省份解析
     *
     * @return
     */
    public static String provinceResolution(String province) {
        if (province != null) {
            if (province.contains("北京")) {
                province = "北京市";
            } else if (province.contains("上海")) {
                province = "上海市";
            } else if (province.contains("重庆")) {
                province = "重庆市";
            } else if (province.contains("天津")) {
                province = "天津市";
            } else if (province.contains("广西")) {
                province = "广西壮族自治区";
            } else if (province.contains("西藏")) {
                province = "西藏自治区";
            } else if (province.contains("内蒙古")) {
                province = "内蒙古自治区";
            } else if (province.contains("新疆")) {
                province = "新疆维吾尔自治区";
            } else if (province.contains("宁夏")) {
                province = "宁夏回族自治区";
            }
        }
        return province;
    }

    /**
     * 对字符串进行base64编码
     *
     * @param str 字符串
     * @return 编码后的字符串
     */
    public static String base64Encode(String str) {
        return Base64.getEncoder().encodeToString(str.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 对base64字符串进行解码
     *
     * @param str base64字符串
     * @return 解码后的字符串
     */
    public static String base64Decode(String str) {
        return new String(Base64.getDecoder().decode(str.getBytes(StandardCharsets.UTF_8)));
    }

    /**
     * 获取快递公司简称
     *
     * @param expressCompany
     * @return
     */
    public static String getExpressShortName(String expressCompany) {
        String expressShortName = "";
        if (expressCompany.contains("圆通")) {
            expressShortName = "YTO";
        } else if (expressCompany.contains("EMS")) {
            expressShortName = "EMS";
        } else if (expressCompany.contains("顺丰")) {
            expressShortName = "SF";
        } else if (expressCompany.contains("京东")) {
            expressShortName = "JD";
        } else {
            log.warn("不知道的快递公司");
        }
        return expressShortName;
    }

    /**
     * 将输入流转为Base64字符串
     *
     * @param inputStream 文件输入流
     * @return Base64 字符串
     * @throws Exception
     */
    public static String parseBase64(InputStream inputStream) throws Exception {
        // 将图片文件转化为字节数组字符串，并对其进行Base64编码处理
        byte[] data = null;

        // 读取图片字节数组
        try {
            ByteArrayOutputStream swapStream = new ByteArrayOutputStream();
            byte[] buff = new byte[100];
            int rc = 0;
            while ((rc = inputStream.read(buff, 0, 100)) > 0) {
                swapStream.write(buff, 0, rc);
            }
            data = swapStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    throw new Exception("输入流关闭异常");
                }
            }
        }

        return com.alibaba.druid.util.Base64.byteArrayToBase64(data);
    }
}
