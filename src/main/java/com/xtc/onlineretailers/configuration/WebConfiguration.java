package com.xtc.onlineretailers.configuration;

import com.xtc.onlineretailers.interceptor.LogbackInterceptor;
import com.xtc.onlineretailers.interceptor.OptionsInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

@Configuration
public class WebConfiguration implements WebMvcConfigurer {

    @Resource
    private OptionsInterceptor optionsInterceptor;

    @Resource
    private LogbackInterceptor logbackInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 需要排除的接口
        String[] excludePath = new String[]{"/swagger-ui.html", "/swagger-resources/**", "/v2/api-docs", "/error"};

        // 按顺序从上到下执行拦截器
        registry.addInterceptor(this.optionsInterceptor).addPathPatterns("/**").excludePathPatterns(excludePath);
        registry.addInterceptor(this.logbackInterceptor).addPathPatterns("/**").excludePathPatterns(excludePath);
    }

}
