package com.xtc.onlineretailers.rpc.scrm;

import com.xtc.onlineretailers.enums.ResponseEnum;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.rpc.scrm.dto.AddressDTO;
import com.xtc.onlineretailers.rpc.scrm.dto.PurificationDTO;
import com.xtc.onlineretailers.util.GsonUtil;
import com.xtc.onlineretailers.util.HttpUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 地址解析
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ScrmApiRpc {

    /**
     * 收件人信息解析
     *
     * @param address 地址
     * @return 收件人信息
     */
    public PurificationDTO purification(String address) {
        try {
            String result = HttpUtil.get("https://scrmapis.okii.com/address/purification?text=" + address);
            AddressDTO response = GsonUtil.jsonToBean(result, AddressDTO.class);
            if (StringUtils.isBlank(result) || !"000001".equals(response.getCode()) || ObjectUtils.isEmpty(response.getData())) {
                throw new GlobalDefaultException(ResponseEnum.ERROR_ADDRESS_PARSE_FAIL);
            }
            return response.getData();
        } catch (Exception e) {
            throw new GlobalDefaultException(ResponseEnum.ERROR_ADDRESS_PARSE_FAIL);
        }
    }

}
