package com.xtc.onlineretailers.rpc.erp.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 获取erp物料
 */
@Getter
@Setter
@ToString
public class ErpEcItemVolumeDTO {

    /**
     * ERP单号
     */
    @SerializedName(value = "productId", alternate = "ITEMID")
    private String productId;

    /**
     * 物料名称
     */
    @SerializedName(value = "productName", alternate = "DESCRIPTION")
    private String productName;

    /**
     * 单位
     */
    @SerializedName(value = "unit", alternate = "UNIT")
    private String unit;

    /**
     * 品类
     */
    @SerializedName(value = "category", alternate = "CATEGORY")
    private String category;

    /**
     * 重量
     */
    @SerializedName(value = "weight", alternate = "WEIGHT")
    private BigDecimal weight;

    /**
     * 体积
     */
    @SerializedName(value = "volume", alternate = "VOLUME")
    private BigDecimal volume;

    /**
     * 盒子
     */
    @SerializedName(value = "box", alternate = "BOX")
    private String box;

    /**
     * 备注
     */
    @SerializedName(value = "remark", alternate = "REMARK")
    private String remark;

    /**
     * 创建时间
     */
    @SerializedName(value = "createTime", alternate = "ADDDATE")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @SerializedName(value = "updateTime", alternate = "LASTUPDATEDATE")
    private LocalDateTime updateTime;

    /**
     * 更新时间
     */
    @SerializedName(value = "lastUpdateBy", alternate = "LASTUPDATEBY")
    private String lastUpdateBy;

    /**
     * 最后更新登录人
     */
    @SerializedName(value = "lastUpdateLogin", alternate = "LASTUPDATELOGIN")
    private String lastUpdateLogin;

    /**
     * 创建时间
     */
    @SerializedName(value = "creation", alternate = "CREATION")
    private String creation;

    /**
     * 创建时间
     */
    @SerializedName(value = "createdBy", alternate = "CREATEDBY")
    private String createdBy;

}
