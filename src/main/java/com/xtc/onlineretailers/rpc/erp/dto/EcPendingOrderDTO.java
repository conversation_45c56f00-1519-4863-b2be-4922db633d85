package com.xtc.onlineretailers.rpc.erp.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 未过账订单
 */
@Getter
@Setter
@ToString
public class EcPendingOrderDTO {

    /**
     * 组织
     */
    @SerializedName(value = "organization", alternate = "ORGANIZATION")
    private String organization;

    /**
     * 订单号
     */
    @SerializedName(value = "platformTradeId", alternate = "PLATFORMTRADEID")
    private String platformTradeId;

    /**
     * ERP单号
     */
    @SerializedName(value = "erpId", alternate = "ERPID")
    private String erpId;

    /**
     * 客户代码
     */
    @SerializedName(value = "customerNumber", alternate = "CUSTOMERNUMBER")
    private String customerNumber;

    /**
     * 客户名称
     */
    @SerializedName(value = "customerName", alternate = "CUSTOMERNAME")
    private String customerName;

    /**
     * 物料代码
     */
    @SerializedName(value = "productId", alternate = "PRODUCTID")
    private String productId;

    /**
     * 物料名称
     */
    @SerializedName(value = "shortName", alternate = "SHORTNAME")
    private String shortName;

    /**
     * 物料名称
     */
    @SerializedName(value = "num", alternate = "NUM")
    private Integer num;

    /**
     * 过账状态
     */
    @SerializedName(value = "status", alternate = "STATUS")
    private String status;

}
