package com.xtc.onlineretailers.rpc.erp.qry;

import com.xtc.onlineretailers.rpc.erp.annotation.ErpQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 查询库存
 */
@Getter
@Setter
@ToString
public class ErpInventoryQry extends ErpPaginationQry {

    /**
     * 组织
     */
    @ErpQueryParam("organization")
    private String organization;

    /**
     * 子库存
     */
    @ErpQueryParam("subinventory")
    private String subInventory;

    /**
     * 储位
     */
    @ErpQueryParam("storagePlace")
    private String storagePlace;

    /**
     * 物料代码
     */
    @ErpQueryParam("productId")
    private String productId;

    /**
     * 物料描述
     */
    @ErpQueryParam("shortnamefuzzy")
    private String shortNameFuzzy;

}
