package com.xtc.onlineretailers.rpc.erp.qry;

import com.xtc.onlineretailers.rpc.erp.annotation.ErpQueryParam;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDate;

/**
 * 获取过账明细
 */
@Getter
@Setter
@ToString
public class ErpPostDetailQry extends ErpPaginationQry {

    /**
     * 订单号
     */
    @ErpQueryParam("platformTradeId")
    private String platformTradeId;

    /**
     * erp单号
     */
    @ErpQueryParam("erpId")
    private String erpId;

    /**
     * 过账开始时间
     */
    @ErpQueryParam("addDateFrom")
    private LocalDate erpPostStartTime;

    /**
     * 过账结束时间
     */
    @ErpQueryParam("addDateTo")
    private LocalDate erpPostEndTime;

    /**
     * 库位
     */
    @ErpQueryParam("subinvCode")
    private String subInvCode;

    /**
     * 物料代码
     */
    @ErpQueryParam("productId")
    private String productId;

}
