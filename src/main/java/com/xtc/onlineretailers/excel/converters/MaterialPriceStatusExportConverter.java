package com.xtc.onlineretailers.excel.converters;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.xtc.onlineretailers.enums.PriceHidingStatus;
import org.apache.commons.lang3.StringUtils;

public class MaterialPriceStatusExportConverter implements Converter<String> {

    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (StringUtils.isNotBlank(value)) {
            return PriceHidingStatus.ACCOMPLISH.name().equals(value) ? new WriteCellData<>("完成") : new WriteCellData<>("未完成");
        }
        return new WriteCellData<>("");
    }

    @Override
    public Class<?> supportJavaTypeKey() {
        return String.class;
    }

}
