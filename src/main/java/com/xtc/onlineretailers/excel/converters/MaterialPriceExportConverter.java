package com.xtc.onlineretailers.excel.converters;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;

public class MaterialPriceExportConverter implements Converter<BigDecimal> {

    @Override
    public Class<?> supportJavaTypeKey() {
        return BigDecimal.class;
    }

    @Override
    public WriteCellData<?> convertToExcelData(BigDecimal value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (ObjectUtils.isNotEmpty(value) && value.compareTo(BigDecimal.ZERO) == 0) {
            return new WriteCellData<>("*****");
        }
        return new WriteCellData<>(value);
    }

}
