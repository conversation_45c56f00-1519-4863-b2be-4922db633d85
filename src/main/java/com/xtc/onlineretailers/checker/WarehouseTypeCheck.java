package com.xtc.onlineretailers.checker;

import com.xtc.onlineretailers.constant.GlobalConstant;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 校验仓库类型
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class WarehouseTypeCheck {

    /**
     * 校验京东仓库类型
     *
     * @param tradeDO 订单
     * @return 结果
     */
    public boolean checkJdWarehouseType(TradeDO tradeDO) {
        if (tradeDO == null) {
            return false;
        }
        return GlobalConstant.WAREHOUSE_SF.equals(tradeDO.getWarehouseCode())
                && GlobalConstant.WAREHOUSE_JD.equals(tradeDO.getWarehouseStorage());
    }

}
