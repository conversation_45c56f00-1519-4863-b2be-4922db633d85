package com.xtc.onlineretailers.checker;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xtc.onlineretailers.exception.GlobalDefaultException;
import com.xtc.onlineretailers.mapper.master.TradeMapper;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.vo.PaginationVO;
import com.xtc.onlineretailers.rpc.erp.ErpRpc;
import com.xtc.onlineretailers.rpc.erp.dto.ErpRemainCreditDTO;
import com.xtc.onlineretailers.rpc.erp.qry.ErpRemainCreditQry;
import com.xtc.onlineretailers.service.PlatformService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 代销金额校验
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class AgencyPlatformAmountCheck {

    private final PlatformService platformService;

    private final TradeMapper tradeMapper;

    private final ErpRpc erpRpc;

    /**
     * 校验代理平台金额
     *
     * @param platformAmount 订单总金额
     * @param platformDO     平台
     */
    public void checkPlatformAmount(BigDecimal platformAmount, PlatformDO platformDO) {
        // 代理订单导入需要查询账号余额
        if (ObjectUtils.isNotEmpty(platformDO.getIsBalance()) && platformDO.getIsBalance() == 1 && "AGENT".equals(platformDO.getPlatformType())) {
            if (StringUtils.isEmpty(platformDO.getInvoiceTitle())) {
                throw new GlobalDefaultException("该平台没有维护公司名称,请联系管理员");
            }
            // 查询该公司的下账号
            List<PlatformDO> companyPlatforms = platformService.list(Wrappers.<PlatformDO>lambdaQuery().in(PlatformDO::getInvoiceTitle, platformDO.getInvoiceTitle()));
            // 获取该公司下的客户代码
            String companyCustomerCodes = companyPlatforms.stream().map(PlatformDO::getCustomerCode)
                    .collect(Collectors.joining(","));
            // 获取该公司平台下的平台id
            List<Integer> platformIdList = companyPlatforms.stream().map(PlatformDO::getPlatformId).collect(Collectors.toList());
            if (StringUtils.isBlank(companyCustomerCodes)) {
                throw new GlobalDefaultException("平台维护有误,请联系管理员");
            }
            // 获取代理账号余额
            ErpRemainCreditQry qry = new ErpRemainCreditQry();
            qry.setCustomerCodes(companyCustomerCodes);
            PaginationVO<ErpRemainCreditDTO> pageByErpRemainCredit = erpRpc.getEcRemainCredit(qry);
            if (CollectionUtils.isEmpty(pageByErpRemainCredit.getItems())) {
                throw new GlobalDefaultException("找不到代理账号信息,请联系管理员");
            }
            BigDecimal agentSum = pageByErpRemainCredit.getItems().stream()
                    .map(ErpRemainCreditDTO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (agentSum.compareTo(BigDecimal.ZERO) > 0) {
                throw new GlobalDefaultException("你的账号余额不足,请及时充值,并与工厂运营人员联系!");
            }
            // 待处理 已确认 已发货 待发货,已打回 未过账订单
            List<String> orderStatusList = Arrays.asList("ORDER_CONFIRMED", "REJECT", "WAIT_SHIP", "HAS_SHIP", "WAIT_DEAL");
            // 计算当前公司下的 已确认 已发货 待发货 未过账的订单总金额
            BigDecimal platformTradAmount = tradeMapper.getPlatformAmount(platformIdList, orderStatusList, 0);
            if (ObjectUtils.isEmpty(platformTradAmount)) {
                platformTradAmount = BigDecimal.ZERO;
            }
            // 订单支付总金额
            BigDecimal tradeAmount = platformTradAmount.add(platformAmount);
            // 代理总金额+发货总金额
            BigDecimal resultAmount = agentSum.add(tradeAmount);
            if (resultAmount.compareTo(BigDecimal.ZERO) > 0) {
                throw new GlobalDefaultException("你的账号余额不足,请及时充值,并与工厂运营人员联系!");
            }
        }
    }

}
