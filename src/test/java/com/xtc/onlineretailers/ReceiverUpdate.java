package com.xtc.onlineretailers;

import com.taobao.api.domain.Trade;
import com.xtc.onlineretailers.pojo.dto.TaobaoParamsDTO;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.service.PlatformService;
import com.xtc.onlineretailers.service.TradeService;
import com.xtc.onlineretailers.util.BatchProcessor;
import com.xtc.onlineretailers.util.TaoBaoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
public class ReceiverUpdate {

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private TradeService tradeService;

    @Resource
    private PlatformService platformService;

    void execute() {
        String sql = "select platform_trade_id from t_trade where status_order = 'WAIT_DEAL' and receiver_province is null";
        List<String> ids = this.jdbcTemplate.queryForList(sql, String.class);
        BatchProcessor.execute(ids, platformTradeId -> {
            TradeDO tradeDO = this.tradeService.getTradeByTradeId(platformTradeId);
            PlatformDO platformDO = this.platformService.getPlatformById(tradeDO.getPlatformId());
            TaobaoParamsDTO taobaoParamsDTO = new TaobaoParamsDTO(platformDO, tradeDO.getPlatformTradeId());
            Trade trade = TaoBaoUtil.getFullTradeInfo(taobaoParamsDTO);
            if (trade != null) {
                TradeDO tradeDO1 = new TradeDO();
                tradeDO1.setPlatformTradeId(tradeDO.getPlatformTradeId());
                tradeDO1.setReceiverCity(trade.getReceiverCity() != null ? trade.getReceiverCity() : "");
                tradeDO1.setReceiverDistrict(trade.getReceiverCity() != null ? trade.getReceiverDistrict() : "");
                tradeDO1.setReceiverAddress(trade.getReceiverAddress());
                tradeDO1.setReceiverProvince(trade.getReceiverState());
                tradeDO1.setReceiverName(trade.getReceiverName());
                tradeDO1.setReceiverPhone(trade.getReceiverPhone());
                tradeDO1.setReceiverMobile(trade.getReceiverMobile());
                tradeDO1.setReceiverOaid(trade.getOaid());
                this.tradeService.updateTrade(tradeDO1);
            }
        });
    }

}
