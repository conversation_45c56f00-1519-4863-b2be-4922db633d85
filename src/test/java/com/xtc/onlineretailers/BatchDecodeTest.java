package com.xtc.onlineretailers;

import com.xtc.onlineretailers.refactor.executor.command.DecryptTextCmdExe;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 添加批量解密的数据脚本
 */
@Component
public class BatchDecodeTest {
    @Resource
    private DecryptTextCmdExe decryptTextCmdExe;
    @Resource
    private JdbcTemplate jdbcTemplate;

    /**
     * 批量解密脚本
     */
    void batchDecode() {
        String sql = "select tid from test_tid_tiao";
        List<String> list = this.jdbcTemplate.queryForList(sql, String.class);
        for (String tradeId : list) {
            decryptTextCmdExe.execute(tradeId);
        }
    }
}
