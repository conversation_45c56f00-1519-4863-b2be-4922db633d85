package com.xtc.onlineretailers;

import com.xtc.onlineretailers.controller.TradeController;
import com.xtc.onlineretailers.pojo.query.BatchUpdateExpressCompany;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class ExpressCompanyChange {

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private TradeController tradeController;

    public void execute() {
        String sql = "SELECT tid FROM test_tid";
        List<String> tradeIds = this.jdbcTemplate.queryForList(sql, String.class);

        BatchUpdateExpressCompany qry = new BatchUpdateExpressCompany();
        qry.setExpressCompany("EMS");
        qry.setPlatformTradeIds(tradeIds);
        this.tradeController.batchUpdateExpressCompany(qry);
    }

}
