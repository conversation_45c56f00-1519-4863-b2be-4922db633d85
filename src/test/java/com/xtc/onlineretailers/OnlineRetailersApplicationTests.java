package com.xtc.onlineretailers;

import com.xtc.marketing.orderservice.order.OrderFeignClient;
import com.xtc.onlineretailers.service.impl.*;
import com.xtc.onlineretailers.task.ErpPostTask;
import com.xtc.onlineretailers.task.TradeDealTask;
import com.xtc.onlineretailers.task.checktrade.CheckTradeMaterialNumJob;
import com.xtc.onlineretailers.task.checktrade.strategy.TmallCheckTradeStrategy;
import com.xtc.onlineretailers.task.xtcmall.executor.MemberTradeCmdExe;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.annotation.Resource;

@Slf4j
@SpringBootTest
class OnlineRetailersApplicationTests {

    @Resource
    private TradeServiceImpl tradeService;
    @Resource
    private OrderServiceImpl orderService;
    @Resource
    SupplyAgainServiceImpl supplyAgainService;
    @Resource
    MemberTradeCmdExe memberTradeCmdExe;
    @Resource
    OrderFeignClient orderFeignClient;
    @Resource
    InvoiceServiceImpl invoiceService;
    @Resource
    TradeDealTask tradeDealTask;
    @Resource
    ConfirmTrade confirmTrade;
    @Resource
    PlatformServiceImpl platformService;
    @Resource
    JdbcTemplate jdbcTemplate;
    @Resource
    ProductInfoServiceImpl productInfoService;
    @Resource
    ErpPostTask erpPostTask;
    @Resource
    WarehouseServiceImpl warehouseService;
    @Resource
    CheckTradeMaterialNumJob checkTradeMaterialNumJob;
    @Resource
    TmallCheckTradeStrategy tmallCheckTradeStrategy;
    @Resource
    PlatformRefundServiceImpl platformRefundService;
    @Resource
    BatchOrder batchOrder;
    @Test
    void contextLoads() throws Exception {
        String platformTradeId = "3606898680915727018";
        String expressCompany = "圆通速递";
        this.tradeService.rePrintNewExpress(platformTradeId, expressCompany);
    }

}
