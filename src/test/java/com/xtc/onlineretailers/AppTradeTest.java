package com.xtc.onlineretailers;

import com.google.gson.JsonObject;
import com.xtc.onlineretailers.util.GsonUtil;
import com.xtc.onlineretailers.util.HttpUtil;

import java.util.List;

public class AppTradeTest {

    public static void main(String[] args) {
        String url = "http://apiseller.tiancaixing.com/seller/trade/orders/getOrderByShop?seller_id=19&key=1613703207&order_status=ALL&page_no=14&page_size=100&last_update_start_time=16136161860&last_update_end_time=16137031860&sign=6287670e7e6d6e135c0f91c55e751589";
        String response = HttpUtil.get(url);
        JsonObject object = GsonUtil.jsonToBean(response, JsonObject.class);
        if (!object.get("code").getAsString().equals("000001")) {
            System.out.println("获取订单失败");
        }

        String json = object.get("data").toString();
        JsonObject dataObject = GsonUtil.jsonToBean(json, JsonObject.class);
        List<JsonObject> array = GsonUtil.jsonToList(dataObject.get("data").toString(), JsonObject.class);
        System.out.println(array.size());
    }

}
