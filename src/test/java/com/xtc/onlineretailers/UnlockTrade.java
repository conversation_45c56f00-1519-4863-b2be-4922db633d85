package com.xtc.onlineretailers;

import com.xtc.onlineretailers.controller.TradeController;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class UnlockTrade {

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private TradeController tradeController;

    public void execute() {
        String sql = "select tid from test_tid";
        List<String> tradeIds = this.jdbcTemplate.queryForList(sql, String.class);

        this.tradeController.unlock(tradeIds);
    }

}
