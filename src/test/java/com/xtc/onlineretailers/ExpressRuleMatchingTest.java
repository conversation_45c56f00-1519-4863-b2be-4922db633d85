package com.xtc.onlineretailers;

import com.xtc.onlineretailers.pojo.entity.ProductInfoDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.refactor.enums.LogisticsCompany;
import com.xtc.onlineretailers.refactor.executor.command.ExpressRuleGetExe;
import com.xtc.onlineretailers.refactor.executor.command.ExpressRuleMatchCmdExe;
import com.xtc.onlineretailers.refactor.pojo.dto.ExpressRuleDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

class ExpressRuleMatchCmdExeTest {

    @Mock
    private ExpressRuleGetExe expressRuleGetExe;

    @InjectMocks
    private ExpressRuleMatchCmdExe expressRuleMatchCmdExe;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    void testJingdongExpressRule() {
        // 准备测试数据
        TradeDO trade = new TradeDO();
        trade.setPlatformId(2063); // 京东平台ID
        trade.setIsProduct(0); // 非商品订
        trade.setReceiverAddress("北京市朝阳区");
        trade.setReceiverProvince("北京市");
        trade.setReceiverCity("朝阳区");

        ProductInfoDO productInfo = new ProductInfoDO();
        productInfo.setProductId("product123");
        productInfo.setModelSort("modelX");

        // 准备快递规则
        ExpressRuleDTO rule = new ExpressRuleDTO();
        rule.setPlatformIds(Collections.singletonList("2063")); // 京东平台
        rule.setIsProduct(false);
        rule.setStartTime(LocalDateTime.of(2025, 6, 23, 0, 0));
        rule.setEndTime(LocalDateTime.of(2029, 6, 23, 23, 59, 59));
        rule.setExpressCompany(LogisticsCompany.YTO);
        rule.setDefaultExpressCompany(LogisticsCompany.EMS);
        rule.setRules(Collections.emptyList());

        when(expressRuleGetExe.execute()).thenReturn(Collections.singletonList(rule));

        // 执行测试
        ExpressRuleDTO result = expressRuleMatchCmdExe.execute(trade, productInfo);

        // 验证结果
        assertNotNull(result);
        assertEquals(LogisticsCompany.YTO, result.getExpressCompany());
    }

    @Test
    void testJingdongExpressRuleWithSpecialAddress() {
        // 测试特殊地址（乡、镇、村）应返回EMS
        TradeDO trade = new TradeDO();
        trade.setPlatformId(2063);
        trade.setIsProduct(0);
        trade.setReceiverAddress("北京市朝阳区XX村");
        trade.setReceiverProvince("北京市");
        trade.setReceiverCity("朝阳区");

        ProductInfoDO productInfo = new ProductInfoDO();

        ExpressRuleDTO rule = new ExpressRuleDTO();
        rule.setPlatformIds(Collections.singletonList("2063"));
        rule.setIsProduct(false);
        rule.setStartTime(LocalDateTime.of(2025, 6, 23, 0, 0));
        rule.setEndTime(LocalDateTime.of(2029, 6, 23, 23, 59, 59));
        rule.setExpressCompany(LogisticsCompany.YTO);
        rule.setDefaultExpressCompany(LogisticsCompany.EMS);
        rule.setRules(Collections.emptyList());

        when(expressRuleGetExe.execute()).thenReturn(Collections.singletonList(rule));

        ExpressRuleDTO result = expressRuleMatchCmdExe.execute(trade, productInfo);

        assertNotNull(result);
        assertEquals(LogisticsCompany.EMS, result.getExpressCompany());
    }

    @Test
    void testJingdongExpressRuleWithProduct() {
        // 测试商品订单应返回顺丰标快
        TradeDO trade = new TradeDO();
        trade.setPlatformId(2063);
        trade.setIsProduct(1); // 商品订单
        trade.setReceiverAddress("北京市朝阳区");

        ProductInfoDO productInfo = new ProductInfoDO();

        ExpressRuleDTO rule = new ExpressRuleDTO();
        rule.setPlatformIds(Collections.singletonList("2063"));
        rule.setIsProduct(false);
        rule.setStartTime(LocalDateTime.of(2025, 6, 23, 0, 0));
        rule.setEndTime(LocalDateTime.of(2029, 6, 23, 23, 59, 59));
        rule.setExpressCompany(LogisticsCompany.YTO);
        rule.setDefaultExpressCompany(LogisticsCompany.EMS);
        rule.setRules(Collections.emptyList());

        when(expressRuleGetExe.execute()).thenReturn(Collections.singletonList(rule));

        ExpressRuleDTO result = expressRuleMatchCmdExe.execute(trade, productInfo);

        assertNotNull(result);
        assertEquals(LogisticsCompany.SF, result.getExpressCompany());
        assertEquals("顺丰标快", result.getExpressType());
    }

}
