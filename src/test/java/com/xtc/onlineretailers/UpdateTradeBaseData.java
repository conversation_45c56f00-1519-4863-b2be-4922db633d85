package com.xtc.onlineretailers;

import com.taobao.api.domain.Trade;
import com.xtc.onlineretailers.pojo.dto.TaobaoParamsDTO;
import com.xtc.onlineretailers.pojo.entity.PlatformDO;
import com.xtc.onlineretailers.pojo.entity.TradeDO;
import com.xtc.onlineretailers.service.TradeService;
import com.xtc.onlineretailers.util.GsonUtil;
import com.xtc.onlineretailers.util.TaoBaoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class UpdateTradeBaseData {

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private TradeService tradeService;

    /**
     * 更新订单的基础数据
     */
    void execute() {
        String sql = "select t.platform_trade_id from t_trade t where t.create_time >= '2021-08-17' and t.receiver_mobile like '%***%'";
        List<String> list = this.jdbcTemplate.queryForList(sql, String.class);

        int num = 10;
        int threadSize = list.size() / num + 1;
        ExecutorService fixedThreadPool = Executors.newFixedThreadPool(threadSize);
        for (int i = 0; i < threadSize; i++) {
            int fromIndex = num * i;
            int toIndex = i == threadSize - 1 ? list.size() : fromIndex + num;
            List<String> subList = list.subList(fromIndex, toIndex);
            fixedThreadPool.execute(() -> subList.forEach(tradeId -> {
                try {
                    TradeDO trade = this.tradeService.getTradeByTradeId(tradeId);
                    Trade taobaoTrade = this.getTaobaoTrade(trade.getPlatformTradeId(), trade.getPlatformName());
                    this.updateReceiverInfo(trade.getId(), taobaoTrade);
                    log.info("更新完成，订单号：{}", tradeId);
                } catch (Exception e) {
                    log.info("异常，订单号：{}", tradeId);
                }
            }));
        }

        // 当任务执行完成后关闭线程
        fixedThreadPool.shutdown();
        try {
            while (!fixedThreadPool.awaitTermination(1, TimeUnit.SECONDS)) {
                log.info("线程池正在运行中");
            }
        } catch (InterruptedException e) {
            log.error("线程池异常", e);
        }
        log.info("线程池已经关闭");
    }

    private void updateReceiverInfo(Long id, Trade trade) {
        TradeDO update = new TradeDO();
        update.setId(id);
        update.setReceiverName(trade.getReceiverName());
        update.setReceiverMobile(trade.getReceiverMobile());
        update.setReceiverAddress(trade.getReceiverAddress());
        log.info("更新数据：{}", GsonUtil.objectToJson(update));
        this.tradeService.updateById(update);
    }

    private Trade getTaobaoTrade(String tradeId, String platformName) {
        PlatformDO platform = getPlatform(platformName);
        TaobaoParamsDTO taobaoParams = new TaobaoParamsDTO(platform, tradeId);
        Trade taobaoTrade = TaoBaoUtil.getFullTradeInfo(taobaoParams);
        log.info(GsonUtil.objectToJson(taobaoTrade));
        return taobaoTrade;
    }

    private PlatformDO getPlatform(String brand) {
        PlatformDO platform = new PlatformDO();
        platform.setApiUrl("http://gw.api.taobao.com/router/rest");
        if ("天猫小天才".equals(brand)) {
            platform.setAppKey("21004858");
            platform.setAppSecret("b9f3d0ef57649d71cf2743885f358958");
            platform.setSessionKey("620210378c3ZZ3c427e007f68f9054d91a48b92c088e2c8760711426");
        } else if ("天猫步步高".equals(brand)) {
            platform.setAppKey("21135797");
            platform.setAppSecret("9528a49aecc91fdc780ac650e418f8a4");
            platform.setSessionKey("6102a22e77f10f0ce7fa7c8d8455321c573f5b735ee626b1024694017");
        }
        return platform;
    }

}
