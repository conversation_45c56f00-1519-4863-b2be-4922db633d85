#!/bin/bash
set +e  # Continue on errors

# Set colors
COLOR_BLUE="\033[0;94m"
COLOR_GREEN="\033[0;92m"
COLOR_RESET="\033[0m"

# Print banner
banner="${COLOR_BLUE}

            %########%
            %###########%       ____                 _____                            __  __ _____  ____
                %#########%    |  _ \   ___ __   __ / ___/  ____    ____   ____ ___   \ \/ /(_   _)/ ___)
                %#########%    | | | | / _ \\\\\ \ / / \___ \ |  _ \  / _  | / __// _ \\   \  /   | | | |
            %#############%    | |_| |(  __/ \ V /  ____) )| |_) )( (_| |( (__(  __/   /  \   | | | |___
            %#############%    |____/  \___|  \_/   \____/ |  __/  \__,_| \___\\\\\___|  /_/\_\  |_|  \____)
        %###############%                                  |_|
        %###########%

${COLOR_RESET}"

# Include project's bin/ folder in PATH
export PATH="./bin:$PATH"

# Create symbolic links for the stdout and stderr of the pod
ln -sf /proc/1/fd/1 /dev/stdout_pod
ln -sf /proc/1/fd/2 /dev/stderr_pod

# Deploy application and print logs
if pgrep -afx "java.*app\.jar" > /dev/null
then
  app_message="\n${COLOR_GREEN}Application is running...${COLOR_RESET}\n"
else
  echo -e "${banner}\n${COLOR_GREEN}Deploying application...${COLOR_RESET}\n"
  nohup java $JAVA_OPTS $HOTSWAP_AGENT $REMOTE_DEBUG -jar app.jar >/dev/stdout_pod 2>/dev/stderr_pod </dev/null &
#  nohup java $JAVA_OPTS $HOTSWAP_AGENT $REMOTE_DEBUG -jar app.jar > >(tee -a /dev/stdout_pod app.log >/dev/null) 2> >(tee -a /dev/stderr_pod app.log >/dev/null) </dev/null &
  devspace run logs
fi

# Print useful output for user
echo -e "${banner}${app_message}
Welcome to your development container!

This is how you can work with it:
- Files will be synchronized between your local machine and this container
- Some ports will be forwarded, so you can access this container via localhost
- Run \`${COLOR_GREEN}devspace list commands${COLOR_RESET}\` to list all commands you can run
- Run \`${COLOR_GREEN}devspace run start${COLOR_RESET}\` to start the application
- Run \`${COLOR_GREEN}devspace run logs${COLOR_RESET}\` to print logs of the application
"

# Set terminal prompt
export PS1="\[${COLOR_BLUE}\]devspace\[${COLOR_RESET}\] ./\W \[${COLOR_BLUE}\]\\$\[${COLOR_RESET}\] "
if [ -z "$BASH" ]; then export PS1="$ "; fi

# Open shell
bash --norc
