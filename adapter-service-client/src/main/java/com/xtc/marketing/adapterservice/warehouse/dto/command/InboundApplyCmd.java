package com.xtc.marketing.adapterservice.warehouse.dto.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xtc.marketing.adapterservice.warehouse.enums.WarehouseEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 入库申请单
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InboundApplyCmd {

    /**
     * 仓库
     */
    @NotNull
    private WarehouseEnum warehouse;

    /**
     * 仓库编码
     */
    @NotBlank
    @Length(max = 50)
    private String warehouseCode;

    /**
     * 入库单号（业务方单号）
     */
    @NotBlank
    @Length(max = 50)
    private String orderId;

    /**
     * 预计收货时间（格式：2023-01-01 00:00:00）
     */
    @NotNull
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime scheduledReceiptTime;

    /**
     * 货物集合，最多 200 个
     */
    @Valid
    @NotEmpty
    @Size(min = 1, max = 200)
    private List<WarehouseItemCmd> items;

}
