package com.xtc.marketing.adapterservice.shop.dto.command;

import com.xtc.marketing.adapterservice.shop.enums.ShopLogisticsCompanyEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 店铺电子面单，生成面单参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopLogisticsOrderCreateCmd {

    /**
     * 店铺代码
     */
    @NotBlank
    @Length(max = 50)
    private String shopCode;
    /**
     * 店铺订单号
     */
    @NotBlank
    @Length(max = 50)
    private String shopOrderNo;
    /**
     * 电子面单id，不传则默认生成UUID
     */
    @Length(max = 50)
    private String logisticsOrderId;
    /**
     * 物流公司
     */
    @NotNull
    private ShopLogisticsCompanyEnum logisticsCompany;
    /**
     * 物流类型（普快、特快）
     */
    @Length(max = 50)
    private String logisticsType;
    /**
     * 寄件人姓名
     */
    @NotBlank
    @Length(max = 50)
    private String senderName;
    /**
     * 寄件人电话
     */
    @NotBlank
    @Length(max = 50)
    private String senderPhone;
    /**
     * 寄件人省份
     */
    @NotBlank
    @Length(max = 50)
    private String senderProvince;
    /**
     * 寄件人城市
     */
    @NotBlank
    @Length(max = 50)
    private String senderCity;
    /**
     * 寄件人区县
     */
    @NotBlank
    @Length(max = 50)
    private String senderDistrict;
    /**
     * 寄件人乡镇
     */
    @Length(max = 50)
    private String senderTown;
    /**
     * 寄件人详细地址
     */
    @NotBlank
    @Length(max = 200)
    private String senderAddress;
    /**
     * 收件人密文数据
     */
    @Length(max = 2000)
    private String receiverOaid;
    /**
     * 收件人姓名
     */
    @NotBlank
    @Length(max = 200)
    private String receiverName;
    /**
     * 收件人手机号
     */
    @NotBlank
    @Length(max = 200)
    private String receiverMobile;
    /**
     * 收件人省份
     */
    @NotBlank
    @Length(max = 50)
    private String receiverProvince;
    /**
     * 收件人城市
     */
    @NotBlank
    @Length(max = 50)
    private String receiverCity;
    /**
     * 收件人区县
     */
    @Length(max = 50)
    private String receiverDistrict;
    /**
     * 收件人乡镇
     */
    @Length(max = 50)
    private String receiverTown;
    /**
     * 收件人详细地址
     */
    @NotBlank
    @Length(max = 500)
    private String receiverAddress;
    /**
     * 主要货物名称
     */
    @Length(max = 500)
    private String mainCargoName;
    /**
     * 货物总金额
     */
    @Range(min = 0, max = 99999999)
    private Integer cargoPriceTotal;
    /**
     * 货物集合，最多 200 个
     */
    @Valid
    @Size(max = 200)
    private List<ShopLogisticsCargoCmd> cargos;
    /**
     * 条码集合，最多 200 个
     */
    @Valid
    @Size(max = 200)
    private List<ShopLogisticsBarcodeCmd> barcodes;
    /**
     * 预约上门参数，不传则不预约上门
     */
    @Valid
    private ShopLogisticsReserveCmd reserve;

}
