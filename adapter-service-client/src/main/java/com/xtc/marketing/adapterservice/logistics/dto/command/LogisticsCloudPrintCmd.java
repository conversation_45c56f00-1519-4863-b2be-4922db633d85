package com.xtc.marketing.adapterservice.logistics.dto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * 面单云打印参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsCloudPrintCmd {

    /**
     * 业务账号
     */
    @NotBlank
    @Length(max = 50)
    private String bizAccount;
    /**
     * 运单号
     */
    @NotBlank
    @Length(max = 50)
    private String waybillNo;

}
