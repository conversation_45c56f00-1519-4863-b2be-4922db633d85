package com.xtc.marketing.adapterservice.invoice.dto.command;

import com.xtc.marketing.adapterservice.invoice.enums.InvoicePlatform;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceSerialNoCmd {

    /**
     * 店铺代码
     */
    @NotBlank
    @Length(max = 50)
    private String shopCode;

    /**
     * 发票平台
     */
    @NotNull
    private InvoicePlatform invoicePlatform;

}
