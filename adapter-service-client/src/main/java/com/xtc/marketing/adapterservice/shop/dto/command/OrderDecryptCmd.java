package com.xtc.marketing.adapterservice.shop.dto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 订单解密
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderDecryptCmd {

    /**
     * 店铺代码
     */
    @NotBlank
    @Length(max = 50)
    private String shopCode;

    /**
     * 订单号
     */
    @NotBlank
    @Length(max = 50)
    private String orderNo;

    /**
     * 密文集合
     */
    @NotEmpty
    @Size(min = 1, max = 1000)
    private List<String> ciphers;

}
