package com.xtc.marketing.adapterservice.notify.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 仓库出库通知
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseOutboundNotifyDTO {

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 出库单号（业务方单号）
     */
    private String orderId;

    /**
     * 平台出库单号
     */
    private String shipmentId;

    /**
     * 运单号
     */
    private String waybillNo;

    /**
     * 出库单类型
     */
    private String orderType;

    /**
     * 平台出库单类型
     */
    private String shipmentOrderType;

    /**
     * 出库单状态
     */
    private String orderStatus;

    /**
     * 发货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime shippingTime;

    /**
     * 货物集合
     */
    private List<WarehouseOutboundNotifyCargoDTO> cargos;

}
