package com.xtc.marketing.adapterservice.invoice.enums;

import lombok.Getter;

/**
 * 开票状态
 */
@Getter
public enum InvoiceStatus {

    /**
     * 开票中
     */
    WAITING("waiting", "开票中"),
    /**
     * 开票成功
     */
    CREATE_SUCCESS("create_success", "开票成功"),
    /**
     * 开票失败
     */
    CREATE_FAILED("create_failed", "开票失败"),
    /**
     * 未知
     */
    UNKNOWN("unknown", "未知状态");

    private final String code;
    private final String desc;

    InvoiceStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 查询开票状态枚举
     *
     * @param code 开票代码
     * @return 开票状态枚举
     */
    public static InvoiceStatus getInvoiceStatus(String code) {
        for (InvoiceStatus status : InvoiceStatus.values()) {
            if (code.equals(status.getCode())) {
                return status;
            }
        }
        return InvoiceStatus.UNKNOWN;
    }

}
