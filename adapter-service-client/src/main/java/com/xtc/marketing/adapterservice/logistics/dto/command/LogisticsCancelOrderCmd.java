package com.xtc.marketing.adapterservice.logistics.dto.command;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogisticsCancelOrderCmd {

    /**
     * 业务账号
     */
    @NotBlank
    @Length(max = 50)
    private String bizAccount;
    /**
     * 订单id
     */
    @NotBlank
    @Length(max = 50)
    private String orderId;
    /**
     * 取消原因
     */
    @Length(max = 30)
    private String cancelReason;
    /**
     * 平台参数
     */
    @Length(max = 1000)
    private String platformJson;

}
