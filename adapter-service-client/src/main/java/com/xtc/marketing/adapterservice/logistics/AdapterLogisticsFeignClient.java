package com.xtc.marketing.adapterservice.logistics;

import com.xtc.marketing.adapterservice.logistics.dto.LogisticsCloudPrintDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsOrderDTO;
import com.xtc.marketing.adapterservice.logistics.dto.LogisticsRouteDTO;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCancelOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCloudPrintCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsCreateOrderCmd;
import com.xtc.marketing.adapterservice.logistics.dto.command.LogisticsInterceptCmd;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsOrderGetQry;
import com.xtc.marketing.adapterservice.logistics.dto.query.LogisticsRouteListQry;
import com.xtc.marketing.marketingcomponentdto.dto.MultiResponse;
import com.xtc.marketing.marketingcomponentdto.dto.Response;
import com.xtc.marketing.marketingcomponentdto.dto.SingleResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 对接服务 - 物流模块
 */
@FeignClient(
        contextId = "adapterLogisticsFeignClient",
        name = "adapter-logistics-feign",
        url = "${xtc.feign.client.adapter-service.url}"
)
public interface AdapterLogisticsFeignClient {

    /**
     * 查询快递路由
     *
     * @param qry 参数
     * @return 快递路由
     */
    @GetMapping("/api/logistics/routes")
    MultiResponse<LogisticsRouteDTO> routes(@SpringQueryMap LogisticsRouteListQry qry);

    /**
     * 下物流单并生成运单号
     *
     * @param cmd 参数
     * @return 运单号
     * @apiNote 默认每次都使用新的【业务订单号】向物流公司【下单】生成新的【运单号】
     */
    @PostMapping("/api/logistics/create-order")
    SingleResponse<LogisticsOrderDTO> createOrder(@RequestBody LogisticsCreateOrderCmd cmd);

    /**
     * 查询订单详情
     *
     * @param qry 参数
     * @return 执行结果
     */
    @GetMapping("/api/logistics/order")
    SingleResponse<String> getOrder(@SpringQueryMap LogisticsOrderGetQry qry);

    /**
     * 查询运单号
     *
     * @param qry 参数
     * @return 运单号
     */
    @GetMapping("/api/logistics/waybill-no")
    SingleResponse<String> getWaybillNo(@SpringQueryMap LogisticsOrderGetQry qry);

    /**
     * 取消订单
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/api/logistics/cancel-order")
    SingleResponse<Boolean> cancelOrder(@RequestBody LogisticsCancelOrderCmd cmd);

    /**
     * 拦截快递
     *
     * @param cmd 参数
     * @return 执行结果
     */
    @PostMapping("/api/logistics/intercept")
    Response intercept(@RequestBody LogisticsInterceptCmd cmd);

    /**
     * 面单云打印
     *
     * @param cmd 参数
     * @return 云打印数据
     */
    @PostMapping("/api/logistics/cloud-print")
    SingleResponse<LogisticsCloudPrintDTO> cloudPrint(@RequestBody LogisticsCloudPrintCmd cmd);

}
