package com.xtc.marketing.adapterservice.shop.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单明细
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderItemDTO {

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 子订单单号
     */
    private String itemNo;
    /**
     * 子订单类型
     */
    private String itemType;
    /**
     * 子订单状态
     */
    private String itemState;
    /**
     * 退款状态
     */
    private String refundState;
    /**
     * 商品id
     */
    private String productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品的最小库存单位sku的id
     */
    private String skuId;
    /**
     * sku名称
     */
    private String skuName;
    /**
     * 商品数量
     */
    private Integer num;
    /**
     * 商品价格（单位：分）
     */
    private Integer unitPrice;
    /**
     * 子订单金额（单位：分）
     */
    private Integer priceTotal;
    /**
     * 折扣金额（单位：分）
     */
    private Integer discount;
    /**
     * 支付金额（单位：分）
     */
    private Integer payment;
    /**
     * 商品物料代码
     */
    private String productErpCode;
    /**
     * sku物料代码
     */
    private String skuErpCode;
    /**
     * 达人id
     */
    private String authorId;
    /**
     * 达人名称
     */
    private String authorName;

}
