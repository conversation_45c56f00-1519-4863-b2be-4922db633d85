package com.xtc.marketing.adapterservice.notify.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 仓库入库通知
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseInboundNotifyDTO {

    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 入库单号（业务方单号）
     */
    private String orderId;

    /**
     * 平台入库单号
     */
    private String receiptId;

    /**
     * 入库单类型
     */
    private String orderType;

    /**
     * 平台入库单类型
     */
    private String receiptOrderType;

    /**
     * 入库单状态
     */
    private String orderStatus;

    /**
     * 货物集合
     */
    private List<WarehouseInboundNotifyCargoDTO> cargos;

}
