package com.xtc.marketing.adapterservice.shop.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 评价
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CommentDTO {

    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 买家名称
     */
    private String buyerName;
    /**
     * skuId
     */
    private String skuId;
    /**
     * sku名称
     */
    private String skuName;
    /**
     * 评价内容
     */
    private String content;
    /**
     * 好评
     */
    private Boolean isGoodRating;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

}
