package com.xtc.marketing.invoiceservice.invoice.dto;

import com.xtc.marketing.invoiceservice.support.dto.BaseDTO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 销售方
 */
@Getter
@Setter
@ToString
public class SellerDTO extends BaseDTO {

    /**
     * 销售方代码
     */
    private String sellerCode;
    /**
     * 销售方税号
     */
    private String sellerIdentifyNo;
    /**
     * 销售方名称
     */
    private String sellerName;
    /**
     * 销售方电话
     */
    private String sellerPhone;
    /**
     * 销售方银行
     */
    private String sellerBankName;
    /**
     * 销售方银行账号
     */
    private String sellerBankAccount;
    /**
     * 销售方地址
     */
    private String sellerAddress;
    /**
     * 开票人
     */
    private String operator;
    /**
     * 启用标识
     */
    private Boolean enabled;

}
