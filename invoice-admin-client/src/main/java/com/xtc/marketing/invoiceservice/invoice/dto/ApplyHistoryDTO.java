package com.xtc.marketing.invoiceservice.invoice.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 发票申请记录
 */
@Getter
@Setter
@ToString
public class ApplyHistoryDTO {

    /**
     * 处理时间
     */
    private LocalDateTime handleTime;
    /**
     * 处理人
     */
    private String handler;
    /**
     * 处理描述
     */
    private String handleDesc;
    /**
     * 处理备注
     */
    private String handleRemark;

}
