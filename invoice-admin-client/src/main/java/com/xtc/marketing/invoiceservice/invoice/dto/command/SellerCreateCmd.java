package com.xtc.marketing.invoiceservice.invoice.dto.command;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

/**
 * 销售方新增参数
 */
@Getter
@Setter
@ToString
public class SellerCreateCmd {

    /**
     * 销售方代码
     */
    @NotBlank
    @Length(max = 50)
    private String sellerCode;
    /**
     * 销售方税号
     */
    @NotBlank
    @Length(max = 50)
    private String sellerIdentifyNo;
    /**
     * 销售方名称
     */
    @NotBlank
    @Length(max = 50)
    private String sellerName;
    /**
     * 销售方电话
     */
    @NotBlank
    @Length(max = 50)
    private String sellerPhone;
    /**
     * 销售方银行
     */
    @NotBlank
    @Length(max = 50)
    private String sellerBankName;
    /**
     * 销售方银行账号
     */
    @NotBlank
    @Length(max = 50)
    private String sellerBankAccount;
    /**
     * 销售方地址
     */
    @NotBlank
    @Length(max = 200)
    private String sellerAddress;
    /**
     * 开票人
     */
    @NotBlank
    @Length(max = 20)
    private String operator;
    /**
     * 启用标识
     */
    @NotNull
    private Boolean enabled;

}
