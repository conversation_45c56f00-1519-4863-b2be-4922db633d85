package com.xtc.marketing.invoiceservice.config;

import com.xtc.marketing.invoiceservice.constant.SystemConstant;
import com.xtc.marketing.invoiceservice.exception.SysErrorCode;
import com.xtc.marketing.invoiceservice.exception.SysException;
import com.xtc.marketing.invoiceservice.util.GsonUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.*;

/**
 * 基础任务执行器
 */
@Slf4j
public class BaseJobExe {

    /**
     * 默认超时秒数
     */
    private static final int DEFAULT_TIMEOUT_SECONDS = 300;
    /**
     * 默认最大并发数
     */
    private static final int DEFAULT_MAX_CONCURRENT = 100;

    private BaseJobExe() {
    }

    /**
     * 执行任务
     *
     * @param param              任务参数
     * @param dataIdFunc         获取数据id函数
     * @param collectionSupplier 获取数据集合函数
     * @param consumer           处理数据函数
     * @param <P>                参数类型
     * @param <T>                数据类型
     */
    public static <P extends BaseJobBO, T> void execute(P param,
                                                        Function<T, String> dataIdFunc,
                                                        Supplier<Collection<T>> collectionSupplier,
                                                        Consumer<T> consumer) {
        execute(param, dataIdFunc, collectionSupplier, consumer, null);
    }

    /**
     * 执行任务
     *
     * @param param              任务参数
     * @param dataIdFunc         获取数据id函数
     * @param collectionSupplier 获取数据集合函数
     * @param consumer           处理数据函数
     * @param errorConsumer      异常处理函数
     * @param <P>                参数类型
     * @param <T>                数据类型
     */
    public static <P extends BaseJobBO, T> void execute(P param,
                                                        Function<T, String> dataIdFunc,
                                                        Supplier<Collection<T>> collectionSupplier,
                                                        Consumer<T> consumer,
                                                        BiConsumer<T, Exception> errorConsumer) {
        // 初始化任务参数：时间范围、超时时间、最大并发数
        initParamTime(param);
        int timeoutMinutes = param.getTimeoutSeconds() != null && param.getTimeoutSeconds() > 0
                ? param.getTimeoutSeconds() : DEFAULT_TIMEOUT_SECONDS;
        param.setTimeoutSeconds(timeoutMinutes);
        int maxConcurrent = param.getMaxConcurrent() != null && param.getMaxConcurrent() > 0
                ? param.getMaxConcurrent() : DEFAULT_MAX_CONCURRENT;
        param.setMaxConcurrent(maxConcurrent);
        log.info("任务执行参数: {}", GsonUtil.objectToJson(param));
        // 获取数据集合
        Collection<T> collection = collectionSupplier.get();
        if (CollectionUtils.isEmpty(collection)) {
            log.info("没有数据需要处理");
            return;
        }
        // 使用虚拟线程处理数据
        virtualThreadProcess(
                param,
                collection,
                dataIdFunc,
                data -> {
                    Exception bizException;
                    // 业务数据处理
                    String dataId = dataIdFunc.apply(data);
                    try {
                        log.info("业务数据处理开始 dataId: {}", dataId);
                        consumer.accept(data);
                        return true;
                    } catch (Exception e) {
                        log.warn("业务数据处理失败 message: {}", e.getMessage(), e);
                        bizException = e;
                    }
                    // 业务异常处理
                    if (errorConsumer != null) {
                        try {
                            log.info("业务异常处理开始 dataId: {}", dataId);
                            errorConsumer.accept(data, bizException);
                        } catch (Exception e) {
                            log.warn("业务异常处理失败 message: {}", e.getMessage(), e);
                        }
                    }
                    return false;
                }
        );
    }

    /**
     * 虚拟线程处理数据
     *
     * @param collection 数据集合
     * @param dataIdFunc 获取数据id函数
     * @param processor  处理数据函数
     * @param <T>        数据类型
     */
    private static <P extends BaseJobBO, T> void virtualThreadProcess(P param,
                                                                      Collection<T> collection,
                                                                      Function<T, String> dataIdFunc,
                                                                      Predicate<T> processor) {
        log.info("开始处理数据 总数: {}", collection.size());
        // 初始化任务运行时，设置任务处理并发数
        JobRuntime jobRuntime = new JobRuntime(param.getMaxConcurrent());
        // 获取主线程的 trace.id
        final String traceId = StringUtils.defaultIfBlank(MDC.get(SystemConstant.MDC_TRACE_ID), uuid());
        log.info("设置主线程 trace.id: {}", traceId);
        // 使用虚拟线程执行器
        ThreadFactory threadFactory = Thread.ofVirtual().name("virtual-", 0).factory();
        try (ExecutorService executor = Executors.newThreadPerTaskExecutor(threadFactory)) {
            collection.forEach(data -> {
                // 数据id
                final String dataId = StringUtils.defaultIfBlank(dataIdFunc.apply(data), uuid());
                // 任务执行id
                String executeId = traceId + " " + dataId;
                log.info("设置虚拟线程 trace.id: {}", executeId);
                // 提交虚拟线程任务
                executor.submit(() -> dataProcessTask(executeId, jobRuntime, data, processor));
            });
            try {
                // 等待所有任务完成
                executor.shutdown();
                if (!executor.awaitTermination(param.getTimeoutSeconds(), TimeUnit.SECONDS)) {
                    log.error("任务执行超时");
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                log.warn("等待所有任务完成逻辑被中断", e);
                Thread.currentThread().interrupt();
            }
        } finally {
            log.info("任务执行结束 已处理 {} (成功 {} 失败 {}) 未完成 {}",
                    jobRuntime.getTotalCount(), jobRuntime.getSuccessCount(), jobRuntime.getFailureCount(),
                    collection.size() - jobRuntime.getTotalCount());
        }
    }

    /**
     * 处理数据任务
     *
     * @param executeId  任务执行id
     * @param jobRuntime 任务运行时
     * @param data       数据
     * @param processor  处理数据函数
     * @param <T>        数据类型
     */
    private static <T> void dataProcessTask(String executeId, JobRuntime jobRuntime, T data, Predicate<T> processor) {
        boolean acquireSemaphore = false;
        // 用 executeId 重新设置 trace.id 包含 traceId dataId 用于日志追踪
        try (MDC.MDCCloseable ignored = MDC.putCloseable(SystemConstant.MDC_TRACE_ID, executeId)) {
            // 获取信号量
            acquireSemaphore = jobRuntime.acquireSemaphore();
            // 处理数据
            boolean success = false;
            if (acquireSemaphore) {
                success = processor.test(data);
            }
            // 计数器计数
            jobRuntime.incrementCounter(success);
        } finally {
            // 释放信号量
            if (acquireSemaphore) {
                jobRuntime.releaseSemaphore();
            }
        }
    }

    /**
     * 初始化任务时间参数
     *
     * @param param 任务参数
     * @param <P>   参数类型
     */
    private static <P extends BaseJobBO> void initParamTime(P param) {
        // 计算任务开始时间和结束时间
        LocalDateTime startTime = param.getStartTime();
        LocalDateTime endTime = param.getEndTime();
        // 优先使用指定开始时间和结束时间，否则使用偏移量进行计算
        if (ObjectUtils.anyNull(startTime, endTime)
                && ObjectUtils.allNotNull(param.getOffsetMinutes(), param.getLimitDays())) {
            endTime = LocalDateTime.now().minusMinutes(param.getOffsetMinutes());
            startTime = endTime.minusDays(param.getLimitDays());
        }
        if (ObjectUtils.anyNull(startTime, endTime)) {
            throw SysException.of(SysErrorCode.S_JOB_ERROR, "任务开始时间和结束时间不能为空");
        }
        if (startTime.isAfter(endTime)) {
            throw SysException.of(SysErrorCode.S_JOB_ERROR, "任务开始时间不能晚于结束时间");
        }
        param.setStartTime(startTime);
        param.setEndTime(endTime);
    }

    /**
     * 生成 uuid
     *
     * @return uuid
     */
    private static String uuid() {
        return java.util.UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 任务运行时
     */
    @Getter
    @Setter
    @ToString
    private static class JobRuntime {

        /**
         * 并发信号量
         */
        private Semaphore semaphore;
        /**
         * 成功计数器
         */
        private AtomicInteger successCounter;
        /**
         * 失败计数器
         */
        private AtomicInteger failureCounter;

        /**
         * 生成任务运行时
         *
         * @param maxConcurrent 最大并发数
         */
        JobRuntime(int maxConcurrent) {
            this.semaphore = new Semaphore(maxConcurrent);
            this.successCounter = new AtomicInteger(0);
            this.failureCounter = new AtomicInteger(0);
        }

        /**
         * 获取信号量
         *
         * @return 执行结果
         */
        public boolean acquireSemaphore() {
            try {
                this.semaphore.acquire();
                return true;
            } catch (InterruptedException e) {
                log.warn("获取信号量许可被中断", e);
                Thread.currentThread().interrupt();
                return false;
            }
        }

        /**
         * 释放信号量
         */
        public void releaseSemaphore() {
            this.semaphore.release();
        }

        /**
         * 计数器计数
         *
         * @param success 成功标识
         */
        public void incrementCounter(boolean success) {
            if (success) {
                successCounter.incrementAndGet();
            } else {
                failureCounter.incrementAndGet();
            }
        }

        /**
         * 获取成功计数
         *
         * @return 成功计数
         */
        public int getSuccessCount() {
            return successCounter.get();
        }

        /**
         * 获取失败计数
         *
         * @return 失败计数
         */
        public int getFailureCount() {
            return failureCounter.get();
        }

        /**
         * 获取总计数 = 成功计数 + 失败计数
         *
         * @return 总计数
         */
        public int getTotalCount() {
            return successCounter.get() + failureCounter.get();
        }

    }

}
