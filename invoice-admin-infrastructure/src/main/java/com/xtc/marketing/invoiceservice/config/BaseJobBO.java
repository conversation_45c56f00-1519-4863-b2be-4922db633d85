package com.xtc.marketing.invoiceservice.config;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.time.LocalDateTime;

/**
 * 基础任务配置
 */
@Getter
@Setter
@ToString
public class BaseJobBO {

    /**
     * 往前偏移分钟（最新提交的数据可能会变更，可以暂不处理）
     */
    private Integer offsetMinutes;
    /**
     * 数据天数限制
     */
    private Integer limitDays;
    /**
     * 数据量限制
     */
    private Integer limit;
    /**
     * 开始时间（可指定且优先使用），默认算法：LocalDateTime.now().minusMinutes(offsetMinutes).minusDays(limitDays)
     */
    private LocalDateTime startTime;
    /**
     * 结束时间（可指定且优先使用），默认算法：LocalDateTime.now().minusMinutes(offsetMinutes)
     */
    private LocalDateTime endTime;
    /**
     * 任务处理超时秒数
     */
    private Integer timeoutSeconds;
    /**
     * 任务处理并发数
     */
    private Integer maxConcurrent;

}
