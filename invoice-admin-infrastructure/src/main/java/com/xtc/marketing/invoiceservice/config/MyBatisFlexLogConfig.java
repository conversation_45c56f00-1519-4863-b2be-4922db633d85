package com.xtc.marketing.invoiceservice.config;

import com.mybatisflex.core.audit.AuditManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * MyBatis-Flex 日志配置类
 * <p>禁止在正式环境开启</p>
 */
@Slf4j
@Configuration
@Profile({"dev", "test"})
public class MyBatisFlexLogConfig {

    public MyBatisFlexLogConfig() {
        // 开启审计功能
        AuditManager.setAuditEnable(true);
        // 设置 SQL 审计收集器
        AuditManager.setMessageCollector(audit -> log.info("Query at: {} | Duration: {} ms | Records: {} | SQL: {}",
                audit.getQueryTime(), audit.getElapsedTime(), audit.getQueryCount(), audit.getFullSql()));
    }

}