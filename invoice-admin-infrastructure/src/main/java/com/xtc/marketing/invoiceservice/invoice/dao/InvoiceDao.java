package com.xtc.marketing.invoiceservice.invoice.dao;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryWrapper;
import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.invoice.dao.mapper.InvoiceMapper;
import com.xtc.marketing.invoiceservice.invoice.dataobject.InvoiceDO;
import com.xtc.marketing.invoiceservice.invoice.dto.command.InvoiceExportCmd;
import com.xtc.marketing.invoiceservice.invoice.dto.query.InvoicePageQry;
import com.xtc.marketing.invoiceservice.util.DateUtil;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.xtc.marketing.invoiceservice.invoice.dataobject.table.InvoiceDOTableDef.INVOICE_DO;

/**
 * 发票数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class InvoiceDao extends BaseDao<InvoiceMapper, InvoiceDO> {

    /**
     * 查询发票分页列表
     *
     * @param qry 参数
     * @return 发票分页列表
     */
    public Page<InvoiceDO> pageBy(InvoicePageQry qry) {
        Page<InvoiceDO> page = Page.of(qry.getPageIndex(), qry.getPageSize());
        return queryChain()
                .where(INVOICE_DO.BIZ_CODE.eq(qry.getBizCode(), If::hasText))
                .and(INVOICE_DO.INVOICE_ID.eq(qry.getInvoiceId(), If::hasText))
                .and(INVOICE_DO.BIZ_ORDER_ID.eq(qry.getBizOrderId(), If::hasText))
                .and(INVOICE_DO.SERIAL_NO.eq(qry.getSerialNo(), If::hasText))
                .and(INVOICE_DO.BUYER_IDENTIFY_NO.eq(qry.getBuyerIdentifyNo(), If::hasText))
                .and(INVOICE_DO.BLUE_INVOICE_NO.eq(qry.getBlueInvoiceNo(), If::hasText))
                .and(INVOICE_DO.RED_INVOICE_NO.eq(qry.getRedInvoiceNo(), If::hasText))
                .and(INVOICE_DO.INVOICE_TIME.between(DateUtil.startOfDay(qry.getInvoiceDateStart()), DateUtil.endOfDay(qry.getInvoiceDateEnd()),
                        If.notNull(qry.getInvoiceDateStart()) && If.notNull(qry.getInvoiceDateEnd())))
                .orderBy(INVOICE_DO.ID.desc())
                .page(page);
    }

    /**
     * 查询导出的发票列表
     *
     * @param cmd    参数
     * @param lastId 上次查询的最后一条记录id
     * @return 发票列表
     */
    public List<InvoiceDO> listForExport(InvoiceExportCmd cmd, Long lastId) {
        QueryWrapper query = buildExportQuery(cmd)
                .and(INVOICE_DO.ID.lt(lastId, If::notNull))
                .orderBy(INVOICE_DO.ID.desc())
                .limit(LIMIT_LIST);
        return list(query);
    }

    /**
     * 统计导出的发票数量
     *
     * @param cmd 参数
     * @return 导出的发票数量
     */
    public long countForExport(InvoiceExportCmd cmd) {
        QueryWrapper query = buildExportQuery(cmd);
        return count(query);
    }

    /**
     * 判断发票不存在
     *
     * @param invoiceId 发票id
     * @return 执行结果
     */
    public boolean notExistsByInvoiceId(String invoiceId) {
        if (If.noText(invoiceId)) {
            return true;
        }
        return !queryChain().where(INVOICE_DO.INVOICE_ID.eq(invoiceId)).exists();
    }

    /**
     * 构建发票导出的查询条件
     *
     * @param cmd 参数
     * @return 查询条件
     */
    private QueryWrapper buildExportQuery(InvoiceExportCmd cmd) {
        return queryChain()
                .where(INVOICE_DO.INVOICE_TIME.between(DateUtil.startOfDay(cmd.getInvoiceDateStart()), DateUtil.endOfDay(cmd.getInvoiceDateEnd())));
    }

}
