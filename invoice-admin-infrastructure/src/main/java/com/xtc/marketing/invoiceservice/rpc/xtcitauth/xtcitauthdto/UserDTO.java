package com.xtc.marketing.invoiceservice.rpc.xtcitauth.xtcitauthdto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 小天才IT鉴权，用户数据
 */
@Getter
@Setter
@ToString
public class UserDTO {

    /**
     * 应用id
     */
    private String appId;
    /**
     * 应用名
     */
    private String appName;
    /**
     * 用户工号
     */
    private String userName;
    /**
     * 用户名
     */
    private String name;
    /**
     * 用户显示名
     */
    private String displayName;
    /**
     * 默认组id
     */
    private String defaultGroupId;
    /**
     * attributes
     */
    private Map<String, Object> attributes;
    /**
     * 角色
     */
    private List<String> roles;
    /**
     * 角色+组织
     */
    private List<String> codes;

}
