package com.xtc.marketing.invoiceservice.support.dao;

import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryCondition;
import com.xtc.marketing.invoiceservice.config.BaseDao;
import com.xtc.marketing.invoiceservice.support.dao.mapper.UserMapper;
import com.xtc.marketing.invoiceservice.support.dataobject.UserDO;
import com.xtc.marketing.invoiceservice.support.dto.query.UserPageQry;
import org.springframework.stereotype.Repository;

import java.util.Optional;

import static com.xtc.marketing.invoiceservice.support.dataobject.table.UserDOTableDef.USER_DO;

/**
 * 用户数据库访问类
 * <p>原则上不写 sql 语句</p>
 * <p>从模板项目中获取 BaseDao 也可以使用 ServiceImpl</p>
 * <p>参数校验使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.If} 包含常用的：非空、非空集合、非空字符串等</p>
 * <p>SQL 函数使用 MyBatis-Flex 提供的工具类 {@link com.mybatisflex.core.query.QueryMethods}</p>
 */
@Repository
public class UserDao extends BaseDao<UserMapper, UserDO> {

    /**
     * 查询用户分页列表
     *
     * @param qry 参数
     * @return 用户分页列表
     */
    public Page<UserDO> pageBy(UserPageQry qry) {
        Page<UserDO> page = Page.of(qry.getPageIndex(), qry.getPageSize());
        return queryChain()
                .where(USER_DO.EMPLOYEE_ID.eq(qry.getEmployeeId(), If::hasText))
                .and(
                        QueryCondition.createEmpty()
                                .and("MATCH (" + USER_DO.USER_NAME.getName() + ") AGAINST (? IN BOOLEAN MODE)", "+" + qry.getUserName())
                                .when(If.hasText(qry.getUserName()))
                )
                .and(USER_DO.BIZ_CODE.eq(qry.getBizCode(), If::hasText))
                .and(USER_DO.ENABLED.eq(qry.getEnabled(), If::notNull))
                .orderBy(USER_DO.ID.desc())
                .page(page);
    }

    /**
     * 查询用户
     *
     * @param employeeId 工号
     * @return 用户
     */
    public Optional<UserDO> getByEmployeeId(String employeeId) {
        if (If.noText(employeeId)) {
            return Optional.empty();
        }
        return queryChain()
                .where(USER_DO.EMPLOYEE_ID.eq(employeeId))
                .orderBy(USER_DO.ID.desc())
                .limit(LIMIT_ONE)
                .oneOpt();
    }

    /**
     * 判断工号是否存在
     *
     * @param employeeId 工号
     * @return 执行结果
     */
    public boolean existsByEmployeeId(String employeeId) {
        if (If.noText(employeeId)) {
            return false;
        }
        return queryChain().where(USER_DO.EMPLOYEE_ID.eq(employeeId)).exists();
    }

    /**
     * 判断工号是否存在（排除指定ID）
     *
     * @param employeeId 工号
     * @param excludeId  排除的ID
     * @return 执行结果
     */
    public boolean existsByEmployeeIdExcludeId(String employeeId, Long excludeId) {
        if (If.noText(employeeId) || If.isNull(excludeId)) {
            return false;
        }
        return queryChain()
                .where(USER_DO.EMPLOYEE_ID.eq(employeeId))
                .and(USER_DO.ID.ne(excludeId))
                .exists();
    }

}
