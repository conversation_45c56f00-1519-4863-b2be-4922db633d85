package com.xtc.marketing.adapterservice.util;

public class AssertUtil {

    private AssertUtil() {
    }

    public static void isTrue(boolean expression, RuntimeException exception) {
        if (!expression) {
            throw exception;
        }
    }

    public static void notNull(Object object, RuntimeException exception) {
        if (object == null) {
            throw exception;
        }
    }

}
