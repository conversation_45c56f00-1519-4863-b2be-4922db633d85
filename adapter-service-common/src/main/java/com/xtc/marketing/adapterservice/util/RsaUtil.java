package com.xtc.marketing.adapterservice.util;

import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * RSA算法工具类
 */
public class RsaUtil {

    private RsaUtil() {
    }

    /**
     * 私钥签名
     *
     * @param data              需要签名的数据
     * @param privateKeyContent 私钥内容
     * @return 签名
     * @throws GeneralSecurityException 异常
     */
    public static String encryptByPrivateKey(String data, String privateKeyContent) throws GeneralSecurityException {
        PrivateKey privateKey = getPrivateKeyByKeyContent(privateKeyContent);
        return encryptByPrivateKey(data, privateKey);
    }

    /**
     * 私钥签名
     *
     * @param data       需要签名的数据
     * @param privateKey 私钥
     * @return 签名
     * @throws GeneralSecurityException 异常
     */
    public static String encryptByPrivateKey(String data, PrivateKey privateKey) throws GeneralSecurityException {
        Signature signature = Signature.getInstance("SHA256WithRSA");
        signature.initSign(privateKey);
        signature.update(data.getBytes(StandardCharsets.UTF_8));
        byte[] signed = signature.sign();
        return HttpUtil.base64Encode(signed);
    }

    /**
     * 公钥验证签名
     *
     * @param data             需要验证签名的数据
     * @param sign             签名
     * @param publicKeyContent 公钥内容
     * @return 验证结果
     * @throws GeneralSecurityException 异常
     */
    public static boolean verifyByPublicKey(String data, String sign, String publicKeyContent) throws GeneralSecurityException {
        PublicKey publicKey = getPublicKeyByKeyContent(publicKeyContent);
        return verifyByPublicKey(data, sign, publicKey);
    }

    /**
     * 公钥验证签名
     *
     * @param data      需要验证签名的数据
     * @param sign      签名
     * @param publicKey 公钥
     * @return 验证结果
     * @throws GeneralSecurityException 异常
     */
    public static boolean verifyByPublicKey(String data, String sign, PublicKey publicKey) throws GeneralSecurityException {
        Signature signature = Signature.getInstance("SHA256WithRSA");
        signature.initVerify(publicKey);
        signature.update(data.getBytes(StandardCharsets.UTF_8));
        return signature.verify(HttpUtil.base64Decode(sign));
    }

    /**
     * 获取私钥
     *
     * @param originalKey 私钥文本内容
     * @return 私钥
     */
    public static PrivateKey getPrivateKeyByKeyContent(String originalKey) {
        String keyContent = originalKey
                .replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s+", "");
        try {
            byte[] decodeKey = HttpUtil.base64Decode(keyContent);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodeKey);
            return KeyFactory.getInstance("RSA").generatePrivate(keySpec);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("无此算法");
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException("私钥非法");
        } catch (NullPointerException e) {
            throw new RuntimeException("私钥数据为空");
        }
    }

    /**
     * 获取公钥
     *
     * @param originalKey 公钥文本内容
     * @return 公钥
     */
    public static PublicKey getPublicKeyByKeyContent(String originalKey) {
        String keyContent = originalKey
                .replace("-----BEGIN CERTIFICATE-----", "")
                .replace("-----END CERTIFICATE-----", "")
                .replaceAll("\\s+", "");
        try {
            byte[] decodeKey = HttpUtil.base64Decode(keyContent);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decodeKey);
            return KeyFactory.getInstance("RSA").generatePublic(keySpec);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("无此算法");
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException("公钥非法");
        } catch (NullPointerException e) {
            throw new RuntimeException("公钥数据为空");
        }
    }

}
