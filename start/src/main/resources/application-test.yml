spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: WakKcUOV3565CVNL6fFv
    url: ******************************************************************************************************************************************************
  redis:
    host: marketing-test-redis-master-0.marketing-test-redis-headless.redis.svc
    port: 6379
    password: bSpcWTb9cmhkn6fXfDlD

xxl:
  job:
    accessToken: sHOvbDODulPB6OTw5rWTR7Hlf9MSlHVul2_q0hvXp263QqDYnF8PjFHEZ6L6B_SK
    admin:
      addresses: http://xxl-job.marketing-component-env-test:8080/xxl-job-admin/
