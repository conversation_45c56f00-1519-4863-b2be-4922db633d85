spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: WakKcUOV3565CVNL6fFv
    url: ******************************************************************************************************************************************************
  data:
    redis:
      host: ************
      port: 30276
      password: bSpcWTb9cmhkn6fXfDlD
minio:
  endpoint: https://eds-prod-2.okii.com:12000
  access-key: BN1KWRIGIBJBWHAV574Z
  secret-key: 0zk6nMfpJrY85uDx8Ahd06DDjLwqwPK9zZNYepDH
xxl:
  job:
    access-token: sHOvbDODulPB6OTw5rWTR7Hlf9MSlHVul2_q0hvXp263QqDYnF8PjFHEZ6L6B_SK
    admin:
      addresses: https://marketing-job-test.okii.com/xxl-job-admin/
xtc:
  feign:
    client:
      invoice-service:
        url: https://marketing-invoice-test.okii.com
