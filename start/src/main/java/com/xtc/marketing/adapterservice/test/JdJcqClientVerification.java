package com.xtc.marketing.adapterservice.test;

import com.xtc.marketing.adapterservice.rpc.jd.JcqClient;
import com.xtc.marketing.adapterservice.rpc.jd.jdjcqdto.AfsStepResultJosDTO;
import com.xtc.marketing.adapterservice.rpc.jd.jdjcqdto.JcqMessageDTO;
import com.xtc.marketing.adapterservice.rpc.jd.jdjcqdto.JcqPullResultDTO;
import com.xtc.marketing.adapterservice.rpc.oms.OmsRpc;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.subscribe.ability.client.JdJcqClient;
import com.xtc.marketing.adapterservice.subscribe.ability.domainservice.SubscribePushOmsDomainService;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * JdJcqClient 验证程序
 */
@Slf4j
@Component
public class JdJcqClientVerification {

    /**
     * 验证JdJcqClient的修复
     */
    public void verifyJdJcqClientFix() {
        log.info("开始验证 JdJcqClient 修复...");

        try {
            // 创建依赖
            OmsRpc omsRpc = new OmsRpc();
            SubscribePushOmsDomainService subscribePushOmsDomainService = new SubscribePushOmsDomainService(omsRpc);
            JdJcqClient jdJcqClient = new JdJcqClient(subscribePushOmsDomainService);

            // 准备测试数据
            ShopDO shop = ShopDO.builder()
                    .shopCode("JD_XTC")
                    .apiUrl("https://api.jd.com/routerjson")
                    .appKey("test_app_key")
                    .appSecret("test_app_secret")
                    .appAccessToken("test_access_token")
                    .build();

            String bizParam = "[{\"shopId\":\"12509410\",\"platformId\":\"2063\"}]";

            // 测试获取JcqClient
            JcqClient jcqClient = jdJcqClient.getJcqClient(shop, bizParam);
            if (jcqClient != null) {
                log.info("✓ JcqClient 创建成功");
            } else {
                log.error("✗ JcqClient 创建失败");
                return;
            }

            // 创建测试消息
            String messageBody = createTestMessageBody();
            JcqMessageDTO message = new JcqMessageDTO();
            message.setMessageId("d362e5eb-dd15-4a0c-90d5-bb421529969b");
            message.setMessageBody(messageBody);
            
            Map<String, String> properties = new HashMap<>();
            properties.put("BUSINESS_ID", "**********");
            properties.put("PROPERTY_RETRY_TIMES", "0");
            message.setProperties(properties);

            JcqPullResultDTO pullResult = new JcqPullResultDTO();
            pullResult.setTopicName("568091687201$Default$open_message_AFS_StepResult_JOS_A44020750E1C3532733F30F5BE74DD23");
            pullResult.setAckIndex("0|1753847219063");
            pullResult.setMessages(Collections.singletonList(message));

            log.info("✓ 测试消息创建成功");

            // 验证消息解析
            AfsStepResultJosDTO afsData = GsonUtil.jsonToBean(messageBody, AfsStepResultJosDTO.class);
            if (afsData != null && "************".equals(afsData.getOrderId())) {
                log.info("✓ 消息解析成功，订单号: {}", afsData.getOrderId());
            } else {
                log.error("✗ 消息解析失败");
                return;
            }

            // 验证业务参数解析
            if (bizParam.contains("12509410") && bizParam.contains("2063")) {
                log.info("✓ 业务参数格式正确");
            } else {
                log.error("✗ 业务参数格式错误");
                return;
            }

            log.info("🎉 JdJcqClient 修复验证完成，所有测试通过！");

        } catch (Exception e) {
            log.error("✗ JdJcqClient 验证失败: {}", e.getMessage(), e);
        }
    }

    private String createTestMessageBody() {
        AfsStepResultJosDTO afsData = new AfsStepResultJosDTO();
        afsData.setOrderId("************");
        afsData.setAfsServiceId("**********");
        afsData.setBuId("12509410");
        afsData.setOperationDate("2025-07-27 11:56:06");
        afsData.setOrderType(22);
        afsData.setStepType("AUDIT");
        afsData.setAfsCategoryId(1555887L);
        afsData.setOperationXID("o*AARehBs8g3UlYH7kpMQsS7ZjNmRiZBBmTZXcb2J31Bu1n80HW_W6lRmM2HrLUnzpvxxXipK5");
        afsData.setOperationPin("系统-0秒极速审核");
        afsData.setChannel(1);
        afsData.setWareName("小天才 电话手表原装充电线 适用Z系列/Q系列/D系列充电线");
        afsData.setOrgId("");
        afsData.setCategorySource(20);
        afsData.setApproveResult(31);
        afsData.setIdentity("");
        afsData.setAfsResultType("PICKWARE_JD");
        afsData.setSkuUuid("1012_F1V3S1E1504913518949961728");
        afsData.setWareId(10136138988767L);
        afsData.setOperationName("系统-0秒极速审核");
        afsData.setAfsApplyId(2587793784L);
        afsData.setCustomerExpect("REFUND");
        afsData.setPlatformSrc(106);
        afsData.setCompanyId(10);
        afsData.setServiceCount(1);
        afsData.setCustomerXID("o*AARehBs8g3UlYH7kpMQsS7ZjNmRiZE-bOoYeNEOJrQvH770uCDA");
        afsData.setCustomerPin("18666101105_pbm");

        return GsonUtil.objectToJson(afsData);
    }
}
