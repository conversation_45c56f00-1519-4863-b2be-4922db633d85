package com.xtc.marketing.adapterservice;

import com.alibaba.nacos.spring.context.annotation.config.EnableNacosConfig;
import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

/**
 * <p>EnableAspectJAutoProxy 注解使用示例
 * <p>获取当前类的代理类，从而实现调用自身方法时进行事务操作
 * <p>UserAddCmdExe exe = (UserAddCmdExe) AopContext.currentProxy();
 * <p>exe.transactionalFunction()
 * <p>配合多数据源插件一起使用时，需要在 @Transactional 注解配置 Propagation.REQUIRES_NEW。
 * 但是处理不了分布式事务，只能各自处理自己的事务
 * <p>@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
 */
@Slf4j
@EnableNacosConfig
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableFeignClients(basePackages = "com.xtc")
@SpringBootApplication(scanBasePackages = {"com.xtc.marketing.adapterservice", "com.alibaba.cola"})
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    @Value("${xxl.job.executor.appname}")
    private String appName;

    @Value("${xxl.job.admin.addresses}")
    private String xxlJobAddresses;

    @Value("${xxl.job.accessToken}")
    private String accessToken;

    @Bean
    public XxlJobSpringExecutor xxlJobExecutor() {
        if (StringUtils.isAnyBlank(appName, xxlJobAddresses)) {
            return null;
        }
        log.info(">>>>>>>>>>> xxl-job config init.");
        XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
        xxlJobSpringExecutor.setAdminAddresses(xxlJobAddresses);
        xxlJobSpringExecutor.setAppname(appName);
        xxlJobSpringExecutor.setAccessToken(accessToken);
        xxlJobSpringExecutor.setPort(9999);
        xxlJobSpringExecutor.setLogPath("/data/applogs/xxl-job/jobhandler");
        xxlJobSpringExecutor.setLogRetentionDays(30);
        return xxlJobSpringExecutor;
    }

    /**
     * 跨域过滤器
     */
    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", this.buildConfig());
        return new CorsFilter(source);
    }

    private CorsConfiguration buildConfig() {
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.addAllowedOrigin("*");
        corsConfiguration.addAllowedHeader("Accept");
        corsConfiguration.addAllowedHeader("Content-Type");
        corsConfiguration.addAllowedHeader("Content-Length");
        corsConfiguration.addAllowedHeader("Accept-Encoding");
        corsConfiguration.addAllowedHeader("Authorization");
        corsConfiguration.addAllowedMethod("OPTIONS");
        corsConfiguration.addAllowedMethod("GET");
        corsConfiguration.addAllowedMethod("POST");
        corsConfiguration.addAllowedMethod("PUT");
        corsConfiguration.addAllowedMethod("DELETE");
        // 小红书消息推送需要的自定义头部
        corsConfiguration.addAllowedHeader("App-Key");
        corsConfiguration.addAllowedHeader("Sign");
        corsConfiguration.addAllowedHeader("Timestamp");
        return corsConfiguration;
    }

}
